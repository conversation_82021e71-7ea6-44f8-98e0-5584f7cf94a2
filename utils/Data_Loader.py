
import os
import json
import jsonlines
from ipdb import set_trace

class DataLoader():
    def __init__(self, file_path) -> None:
        self.file_path = file_path
        assert os.path.exists(self.file_path)

    def read_json(self, filename):
        with open(filename, "r", encoding="utf-8") as fin:
            data_list = json.load(fin)
        return data_list

    def read_jsonl(self, filename):
        question_list = []
        answer_list = []
        with jsonlines.open(filename) as reader:
            for obj in reader:
                question = obj['question']
                answer = obj['answer']
                question_list.append(question)
                answer_list.append(answer)
        return question_list, answer_list

