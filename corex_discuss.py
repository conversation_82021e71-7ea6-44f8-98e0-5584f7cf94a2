import logging
import os
import sys
import time
import json
import random
from openai import OpenAI

api_key = os.getenv('OPENAI_API_KEY')
if api_key is None:
    raise ValueError("Please set your OpenAI API key as an environment variable 'OPENAI_API_KEY'")
client = OpenAI(api_key=api_key)
MODEL_NAME = "gpt-4o"  

DATASETS = {
    'gsm8k': {
        'path': 'dataset/GSM8k/test.jsonl',
        'prompt_path': 'prompts/math/gsm8k_prompt.py',
        'prompt_var_name': 'GSM8K_Prompt'
    },
    'AddSub': {
        'path': 'dataset/AddSub/AddSub.json',
        'prompt_path': 'prompts/math/addsub_prompt.py',
        'prompt_var_name': 'AddSub_Prompt'
    },
    'AQuA': {
        'path': 'dataset/AQuA/test.json',
        'prompt_path': 'prompts/math/aqua_prompt.py',
        'prompt_var_name': 'AQuA_Prompt'
    },
    'ARC-c': {
        'path': 'dataset/ARC-c/ARC-Challenge-Test.jsonl',
        'prompt_path': 'prompts/commonsense/arc_c_prompt.py',
        'prompt_var_name': 'ARC_Prompt'
    },
    'GSM-Hard': {
        'path': 'dataset/GSM-Hard/gsmhardv2_test.jsonl',
        'prompt_path': 'prompts/math/gsmhard_prompt.py',
        'prompt_var_name': 'GSMHard_Prompt'
    },
    'MultiArith': {
        'path': 'dataset/MultiArith/MultiArith.json',
        'prompt_path': 'prompts/math/multiarith_prompt.py',
        'prompt_var_name': 'MultiArith_Prompt'
    }
}


OUTPUT_DIR = "outputs"

class OpenAIChat:
    """
    A class for interacting with the OpenAI API, allowing for chat completion requests.
    """

    def __init__(self):
        """
        Initializes the OpenAIChat object with the given configuration.
        """
        self.model_name = MODEL_NAME

    def chat(self, messages, temperature=0):
        """
        Sends a chat completion request to the OpenAI API using the specified messages and parameters.
        """
        response = client.chat.completions.create(
            model=self.model_name,
            messages=messages,
            temperature=temperature
        )
        logging.info(f"Response: {response.choices[0].message.content}")
        return response.choices[0].message.content

class Agent:
    """
    Represents an LLM-based agent that can solve problems independently.
    """

    def __init__(self, agent_id, prompt):
        self.agent_id = agent_id
        self.llm = OpenAIChat()
        self.system_prompt = "You are a helpful AI assistant."
        self.prompt = prompt

    def generate_solution(self, problem, previous_discussion=None):
        """
        Generates a reasoning chain and prediction for the given problem.
        If previous_discussion is provided, uses it to refine the solution.
        """
        if previous_discussion:
            formatted_prompt = self.prompt.format(problem, previous_discussion)
        else:
            formatted_prompt = self.prompt.format(problem)
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": formatted_prompt}
        ]
        response = self.llm.chat(messages)
        return response

class Team:
    """
    Represents a team of agents in the discuss mode.
    """

    def __init__(self, team_name, agents):
        self.team_name = team_name
        self.agents = agents  # List of Agent instances

    def discuss(self, problem, previous_discussion=None):
        """
        Agents in the team discuss and refine their solutions.
        """
        team_solutions = []
        for agent in self.agents:
            solution = agent.generate_solution(problem, previous_discussion)
            team_solutions.append({
                'agent_id': agent.agent_id,
                'solution': solution
            })
        final_solution = team_solutions[0]['solution']
        return final_solution

class Judge:
    """
    Represents the judge agent who evaluates the teams' outputs.
    """

    def __init__(self, agent_id, judge_prompt):
        self.agent_id = agent_id
        self.llm = OpenAIChat()
        self.system_prompt = "You are an expert judge who evaluates solutions."
        self.judge_prompt_template = judge_prompt

    def evaluate(self, problem, blue_outputs, green_outputs):
        """
        Evaluates the outputs from both teams and decides the final answer.
        """
        evaluation_prompt = self.judge_prompt_template.format(
            question=problem,
            blue_outputs=blue_outputs,
            green_outputs=green_outputs
        )
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": evaluation_prompt}
        ]
        evaluation_response = self.llm.chat(messages)
        return evaluation_response

def load_dataset(dataset_name):
    """
    Loads the dataset specified by dataset_name.
    """
    dataset_info = DATASETS[dataset_name]
    dataset_path = dataset_info['path']
    data = []

    if dataset_path.endswith('.jsonl'):
        with open(dataset_path, 'r', encoding='utf-8') as f:
            data = [json.loads(line) for line in f]
    elif dataset_path.endswith('.json'):
        with open(dataset_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    else:
        raise ValueError(f"Unsupported dataset file format: {dataset_path}")

    return data

def load_agent_prompt(prompt_path, prompt_var_name):
    """
    Loads the agent prompt from the given prompt_path using the specified prompt_var_name.
    """
    prompt_dir = os.path.dirname(prompt_path)
    sys.path.append(prompt_dir)
    prompt_module_name = os.path.basename(prompt_path).replace('.py', '')
    prompt_module = __import__(prompt_module_name)
    agent_prompt = getattr(prompt_module, prompt_var_name, None)
    if agent_prompt is None:
        raise ValueError(f"No '{prompt_var_name}' variable found in {prompt_path}")
    return agent_prompt

def load_discuss_prompts():
    """
    Loads the discuss prompts from corex_prompts/discuss_prompts.py
    """
    prompt_file = 'corex_prompts/discuss_prompts.py'
    prompt_dir = os.path.dirname(prompt_file)
    sys.path.append(prompt_dir)
    prompt_module_name = os.path.basename(prompt_file).replace('.py', '')
    prompt_module = __import__(prompt_module_name)
    discuss_prompts = getattr(prompt_module, 'discuss_prompts', None)
    if discuss_prompts is None:
        raise ValueError(f"No 'discuss_prompts' variable found in {prompt_file}")
    return discuss_prompts

def extract_final_answer(solution_text):
    """
    Extracts the final answer from the agent's solution text, matching the style of the few-shot prompts.
    """
    lower_text = solution_text.lower()
    idx = lower_text.rfind("so the answer is")
    if idx != -1:
        answer = solution_text[idx + len("so the answer is"):].strip()
        answer = answer.strip('.').strip()
        # Return the formatted final answer
        return "So the answer is " + answer
    else:
        idx_answer = lower_text.rfind("answer:")
        if idx_answer != -1:
            answer = solution_text[idx_answer + len("answer:"):].strip()
            answer = answer.strip('.').strip()
            return "So the answer is " + answer
        else:
            return solution_text.strip()

def run_discussion(problem, blue_team, green_team, max_rounds=3):
    """
    Runs the discussion between the blue and green teams.
    """
    discussion_history = []
    blue_solution = None
    green_solution = None

    for round_num in range(1, max_rounds + 1):
        print(f"\n--- Round {round_num} ---")

        # Each team discusses and refines their solution
        previous_discussion = discussion_history[-1] if discussion_history else None

        blue_output = blue_team.discuss(problem, previous_discussion)
        green_output = green_team.discuss(problem, previous_discussion)

        blue_pred = extract_final_answer(blue_output)
        green_pred = extract_final_answer(green_output)

        print(f"\nBlue Team's Prediction: {blue_pred}")
        print(f"Green Team's Prediction: {green_pred}")

        discussion_history.append({
            'round': round_num,
            'blue_output': blue_output,
            'green_output': green_output,
            'blue_pred': blue_pred,
            'green_pred': green_pred
        })

        if blue_pred == green_pred:
            print("\nTeams have reached an agreement.")
            return blue_pred, discussion_history  # Return agreed prediction and history

    # If no agreement, return the last predictions and the discussion history
    print("\nTeams did not reach an agreement.")
    return None, discussion_history

def main():
    start_time = time.time()
    start_time_str = time.strftime("%Y%m%d_%H%M%S", time.localtime(start_time))

    # Choose the dataset you want to process
    dataset_name = 'gsm8k'  # Change this to process a different dataset

    if dataset_name not in DATASETS:
        raise ValueError(f"Dataset '{dataset_name}' is not supported.")

    dataset_output_dir = os.path.join(OUTPUT_DIR, f"{dataset_name}_discuss_{start_time_str}")
    os.makedirs(dataset_output_dir, exist_ok=True)
    output_file = os.path.join(dataset_output_dir, "output.jsonl")

    dataset = load_dataset(dataset_name)

    prompt_path = DATASETS[dataset_name]['prompt_path']
    prompt_var_name = DATASETS[dataset_name]['prompt_var_name']
    agent_prompt = load_agent_prompt(prompt_path, prompt_var_name)

    discuss_prompts = load_discuss_prompts()
    judge_prompt = discuss_prompts.get('judge_prompt', discuss_prompts['default_judge_prompt'])

    # Open the output file in append mode
    with open(output_file, 'a', encoding='utf-8') as outfile:
        for idx, problem_data in enumerate(dataset):
            # Extract the problem and solution based on dataset format
            if dataset_name == 'ARC-c':
                problem = problem_data['question']['stem']
                choices = problem_data['question']['choices']
                choices_text = '\n'.join([f"{choice['label']}: {choice['text']}" for choice in choices])
                problem_full = f"{problem}\n\nChoices:\n{choices_text}"
                solution = problem_data.get('answerKey', '')
                problem_to_solve = problem_full
            elif dataset_name == 'AQuA':
                # AQuA dataset has multiple-choice questions
                problem = problem_data['question']
                options = problem_data['options']
                choices_text = '\n'.join(options)
                problem_full = f"{problem}\n\nOptions:\n{choices_text}"
                solution = problem_data.get('correct', '')
                problem_to_solve = problem_full
            elif dataset_name == 'AddSub':
                problem = problem_data['sQuestion']
                solution_list = problem_data.get('lSolutions', [])
                solution = solution_list[0] if solution_list else ''
                problem_to_solve = problem
            elif dataset_name == 'GSM-Hard':
                problem = problem_data['input']
                solution = problem_data.get('target', '')
                problem_to_solve = problem
            elif dataset_name == 'MultiArith':
                problem = problem_data['sQuestion']
                solution_list = problem_data.get('lSolutions', [])
                solution = solution_list[0] if solution_list else ''
                problem_to_solve = problem
            else:  # gsm8k
                problem = problem_data['question']
                solution = problem_data.get('answer', '')
                problem_to_solve = problem

            print(f"\nProcessing {dataset_name} Problem {idx + 1}:")
            print(problem)
            print("\nCorrect Solution:")
            print(solution)

            # Number of agents
            num_agents = 5  # Fixed to 5 agents

            # Create agents
            all_agents = [Agent(agent_id=i, prompt=agent_prompt) for i in range(num_agents)]

            # Randomly select a judge
            judge_index = random.randint(0, num_agents - 1)
            judge_agent = Judge(agent_id=judge_index, judge_prompt=judge_prompt)
            print(f"\nAgent {judge_index} is selected as the judge.\n")

            # Remove judge from agent list
            remaining_agents = [agent for agent in all_agents if agent.agent_id != judge_index]

            # Divide remaining agents into two teams
            random.shuffle(remaining_agents)
            mid_index = len(remaining_agents) // 2
            blue_agents = remaining_agents[:mid_index]
            green_agents = remaining_agents[mid_index:]

            blue_team = Team(team_name="Blue", agents=blue_agents)
            green_team = Team(team_name="Green", agents=green_agents)

            max_rounds = 3  # Maximum number of discussion rounds
            agreed_prediction, discussion_history = run_discussion(problem_to_solve, blue_team, green_team, max_rounds)

            if agreed_prediction:
                final_answer = agreed_prediction
                judge_evaluation = None
            else:
                # If no agreement, let the judge decide
                print("\nJudge is evaluating the teams' outputs...")
                blue_outputs = "\n\n".join([f"Round {item['round']}:\n{item['blue_output']}" for item in discussion_history])
                green_outputs = "\n\n".join([f"Round {item['round']}:\n{item['green_output']}" for item in discussion_history])

                judge_decision = judge_agent.evaluate(problem_to_solve, blue_outputs, green_outputs)
                final_answer = extract_final_answer(judge_decision)
                judge_evaluation = judge_decision

            print("\nFinal Answer:")
            print(final_answer)

            # Prepare the data to be saved
            output_data = {
                'question': problem,
                'discussion_history': discussion_history,
                'final_answer': final_answer,
                'answer': solution,
                'judge_evaluation': judge_evaluation
            }

            # Write the data as a JSON line
            outfile.write(json.dumps(output_data, ensure_ascii=False) + '\n')

            time.sleep(1)

    end_time = time.time()
    execution_time = end_time - start_time
    print(f"\nProgram execution time: {execution_time} seconds")

if __name__ == '__main__':
    main()
