{"examples": [{"question": "Take the last letters of each words in \"Whitney Erika Tj Benito\" and concatenate them.", "answer": "yajo"}, {"question": "Take the last letters of each words in \"Lucky Mireya Jj <PERSON>c\" and concatenate them.", "answer": "yajc"}, {"question": "Take the last letters of each words in \"Caleb Chase Eleazar <PERSON>\" and concatenate them.", "answer": "berl"}, {"question": "Take the last letters of each words in \"Silvia Carolina Stan Chuck\" and concatenate them.", "answer": "aank"}, {"question": "Take the last letters of each words in \"Breanna Trey Omar Patrice\" and concatenate them.", "answer": "ayre"}, {"question": "Take the last letters of each words in \"<PERSON>ian <PERSON>\" and concatenate them.", "answer": "nney"}, {"question": "Take the last letters of each words in \"<PERSON>yla <PERSON>\" and concatenate them.", "answer": "rayn"}, {"question": "Take the last letters of each words in \"Angelina Layla Jenny Zane\" and concatenate them.", "answer": "aaye"}, {"question": "Take the last letters of each words in \"Lizzy Juany Aisha Brenda\" and concatenate them.", "answer": "yyaa"}, {"question": "Take the last letters of each words in \"Elise Lupe <PERSON>\" and concatenate them.", "answer": "eeei"}, {"question": "Take the last letters of each words in \"Jesse Roderick Travis Rita\" and concatenate them.", "answer": "eksa"}, {"question": "Take the last letters of each words in \"Cinthia Lloyd Jacqueline Jc\" and concatenate them.", "answer": "adec"}, {"question": "Take the last letters of each words in \"Sterling Jenifer Patsy <PERSON>\" and concatenate them.", "answer": "grye"}, {"question": "Take the last letters of each words in \"<PERSON>riz <PERSON>\" and concatenate them.", "answer": "znon"}, {"question": "Take the last letters of each words in \"Prince <PERSON>\" and concatenate them.", "answer": "eelk"}, {"question": "Take the last letters of each words in \"<PERSON>ae Dennis Cris Bern<PERSON>\" and concatenate them.", "answer": "esse"}, {"question": "Take the last letters of each words in \"Hank Janine Frankie Isa\" and concatenate them.", "answer": "keea"}, {"question": "Take the last letters of each words in \"Max Mikey Cynthia Holly\" and concatenate them.", "answer": "xyay"}, {"question": "Take the last letters of each words in \"Christy Rey Michelle Dolly\" and concatenate them.", "answer": "yyey"}, {"question": "Take the last letters of each words in \"Annie Toño <PERSON>\" and concatenate them.", "answer": "eons"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON> Danielle Red\" and concatenate them.", "answer": "yyed"}, {"question": "Take the last letters of each words in \"<PERSON>\" and concatenate them.", "answer": "eatn"}, {"question": "Take the last letters of each words in \"Yan Eunice <PERSON>\" and concatenate them.", "answer": "nehh"}, {"question": "Take the last letters of each words in \"<PERSON> Ramiro <PERSON>\" and concatenate them.", "answer": "yoob"}, {"question": "Take the last letters of each words in \"Domingo Briana Michael Joan\" and concatenate them.", "answer": "oaln"}, {"question": "Take the last letters of each words in \"Kristopher Deb Jake Tammy\" and concatenate them.", "answer": "rbey"}, {"question": "Take the last letters of each words in \"Morgan Perla Joao Marta\" and concatenate them.", "answer": "naoa"}, {"question": "Take the last letters of each words in \"Penny Harry Jessica Ho<PERSON>io\" and concatenate them.", "answer": "yyao"}, {"question": "Take the last letters of each words in \"Billie Paloma Tanner Raul\" and concatenate them.", "answer": "earl"}, {"question": "Take the last letters of each words in \"Rena Devon Rosalinda Paulina\" and concatenate them.", "answer": "anaa"}, {"question": "Take the last letters of each words in \"<PERSON>do <PERSON>\" and concatenate them.", "answer": "loda"}, {"question": "Take the last letters of each words in \"Emiliano Jasmin Wade <PERSON>ilma\" and concatenate them.", "answer": "onea"}, {"question": "Take the last letters of each words in \"Lino Mariel Aditya Elisabeth\" and concatenate them.", "answer": "olah"}, {"question": "Take the last letters of each words in \"Jacky Socorro Mark Wanda\" and concatenate them.", "answer": "yoka"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON>cio <PERSON>\" and concatenate them.", "answer": "oeie"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON>\" and concatenate them.", "answer": "noly"}, {"question": "Take the last letters of each words in \"Phillip Ajay <PERSON>\" and concatenate them.", "answer": "pyeo"}, {"question": "Take the last letters of each words in \"Ari Jasmine <PERSON>\" and concatenate them.", "answer": "ietk"}, {"question": "Take the last letters of each words in \"Mar<PERSON>ita Anabel Shaun <PERSON>\" and concatenate them.", "answer": "alna"}, {"question": "Take the last letters of each words in \"Geo Kody <PERSON>\" and concatenate them.", "answer": "oysi"}, {"question": "Take the last letters of each words in \"Kennedy Ginny Iliana Sky\" and concatenate them.", "answer": "yyay"}, {"question": "Take the last letters of each words in \"Billy Kassandra <PERSON>\" and concatenate them.", "answer": "yaye"}, {"question": "Take the last letters of each words in \"Nubia Sarah Jalen Kris\" and concatenate them.", "answer": "ahns"}, {"question": "Take the last letters of each words in \"Ale Gaspar Sonny Simon\" and concatenate them.", "answer": "eryn"}, {"question": "Take the last letters of each words in \"Skylar Chris<PERSON>\" and concatenate them.", "answer": "ryye"}, {"question": "Take the last letters of each words in \"Gavin Neha Asha Baltazar\" and concatenate them.", "answer": "naar"}, {"question": "Take the last letters of each words in \"Camilo Becky Eliza Rebecca\" and concatenate them.", "answer": "oyaa"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON>\" and concatenate them.", "answer": "aeyd"}, {"question": "Take the last letters of each words in \"Meg Andrey Gerard Lilia\" and concatenate them.", "answer": "gyda"}, {"question": "Take the last letters of each words in \"Denny Carlo Re<PERSON>do <PERSON>\" and concatenate them.", "answer": "yooi"}, {"question": "Take the last letters of each words in \"Cheri Rico Teo Jesus\" and concatenate them.", "answer": "ioos"}, {"question": "Take the last letters of each words in \"Alexandria Meghan Autumn Robert\" and concatenate them.", "answer": "annt"}, {"question": "Take the last letters of each words in \"Jr Meredith Zoe Robby\" and concatenate them.", "answer": "rhey"}, {"question": "Take the last letters of each words in \"Pretty Jada Sarita Allen\" and concatenate them.", "answer": "yaan"}, {"question": "Take the last letters of each words in \"Shari Bella Liza Maira\" and concatenate them.", "answer": "iaaa"}, {"question": "Take the last letters of each words in \"Lazaro Ana Charlotte Precious\" and concatenate them.", "answer": "oaes"}, {"question": "Take the last letters of each words in \"Imelda Andi Mack Rigoberto\" and concatenate them.", "answer": "aiko"}, {"question": "Take the last letters of each words in \"Ashish Tracey Varun Emil\" and concatenate them.", "answer": "hynl"}, {"question": "Take the last letters of each words in \"Marian Joanne <PERSON>\" and concatenate them.", "answer": "nent"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON>\" and concatenate them.", "answer": "aydn"}, {"question": "Take the last letters of each words in \"Vicente <PERSON>\" and concatenate them.", "answer": "e<PERSON>n"}, {"question": "Take the last letters of each words in \"Craig Dillon Troy Griselda\" and concatenate them.", "answer": "gnya"}, {"question": "Take the last letters of each words in \"Rubi Daisy Yadira Santa\" and concatenate them.", "answer": "iyaa"}, {"question": "Take the last letters of each words in \"Dave Ernesto <PERSON>\" and concatenate them.", "answer": "eoma"}, {"question": "Take the last letters of each words in \"Ira Paola Jose <PERSON> Maria\" and concatenate them.", "answer": "a<PERSON>a"}, {"question": "Take the last letters of each words in \"Manish Lu Karl Don\" and concatenate them.", "answer": "huln"}, {"question": "Take the last letters of each words in \"Irving Hans <PERSON>\" and concatenate them.", "answer": "gsse"}, {"question": "Take the last letters of each words in \"Micaela Kevin Diamond Ty\" and concatenate them.", "answer": "andy"}, {"question": "Take the last letters of each words in \"<PERSON>ura Selvin Tabitha Gino\" and concatenate them.", "answer": "anao"}, {"question": "Take the last letters of each words in \"Rosendo Shayla Erica Georgia\" and concatenate them.", "answer": "oaaa"}, {"question": "Take the last letters of each words in \"Lorena Shana Priscilla Summer\" and concatenate them.", "answer": "aaar"}, {"question": "Take the last letters of each words in \"Noelle Byron Jane Darin\" and concatenate them.", "answer": "enen"}, {"question": "Take the last letters of each words in \"Chava Rosalia Kurt Linda\" and concatenate them.", "answer": "aata"}, {"question": "Take the last letters of each words in \"<PERSON>agan Naomi <PERSON> Marie\" and concatenate them.", "answer": "nile"}, {"question": "Take the last letters of each words in \"<PERSON> Karan <PERSON>\" and concatenate them.", "answer": "enmn"}, {"question": "Take the last letters of each words in \"<PERSON>la Audrey Glenn Rhonda\" and concatenate them.", "answer": "ayna"}, {"question": "Take the last letters of each words in \"Regina Joel Justice Eli\" and concatenate them.", "answer": "alei"}, {"question": "Take the last letters of each words in \"Janet Ant <PERSON> Elias\" and concatenate them.", "answer": "ttes"}, {"question": "Take the last letters of each words in \"Russ Berta Mandy Lydia\" and concatenate them.", "answer": "saya"}, {"question": "Take the last letters of each words in \"Tristan Marleny Santiago Viviana\" and concatenate them.", "answer": "nyoa"}, {"question": "Take the last letters of each words in \"Carole <PERSON>\" and concatenate them.", "answer": "emyy"}, {"question": "Take the last letters of each words in \"Amparo Gianna <PERSON>\" and concatenate them.", "answer": "oana"}, {"question": "Take the last letters of each words in \"Lewis Azucena Kai Ravi\" and concatenate them.", "answer": "saii"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON>\" and concatenate them.", "answer": "a<PERSON>y"}, {"question": "Take the last letters of each words in \"Faustino Lamar Fransisco Rina\" and concatenate them.", "answer": "oroa"}, {"question": "Take the last letters of each words in \"Angelique Marissa Phyllis Bonnie\" and concatenate them.", "answer": "ease"}, {"question": "Take the last letters of each words in \"Loren Wes <PERSON>ley\" and concatenate them.", "answer": "nsny"}, {"question": "Take the last letters of each words in \"Maxwell Jose Beto Joe\" and concatenate them.", "answer": "leoe"}, {"question": "Take the last letters of each words in \"Liz <PERSON>\" and concatenate them.", "answer": "zaha"}, {"question": "Take the last letters of each words in \"Chantal Ines Valeria Francesca\" and concatenate them.", "answer": "lsaa"}, {"question": "Take the last letters of each words in \"Ron <PERSON>\" and concatenate them.", "answer": "nlng"}, {"question": "Take the last letters of each words in \"Sally Sadie Christie Ellie\" and concatenate them.", "answer": "yeee"}, {"question": "Take the last letters of each words in \"Tomas Nic Zoila Calvin\" and concatenate them.", "answer": "scan"}, {"question": "Take the last letters of each words in \"Gabe Dora <PERSON>\" and concatenate them.", "answer": "eano"}, {"question": "Take the last letters of each words in \"Victoria Aurora Amalia Princess\" and concatenate them.", "answer": "aaas"}, {"question": "Take the last letters of each words in \"Alec Arianna Co<PERSON>\" and concatenate them.", "answer": "caas"}, {"question": "Take the last letters of each words in \"Carolyn Sasha Mercy Ke<PERSON>\" and concatenate them.", "answer": "nayi"}, {"question": "Take the last letters of each words in \"Selena Keisha Gladys Cedric\" and concatenate them.", "answer": "aasc"}, {"question": "Take the last letters of each words in \"Salma Pj Gladis Monica\" and concatenate them.", "answer": "a<PERSON><PERSON>"}, {"question": "Take the last letters of each words in \"Ralph Jeanne <PERSON>\" and concatenate them.", "answer": "heeo"}, {"question": "Take the last letters of each words in \"Letty Aimee Elvia Ted\" and concatenate them.", "answer": "yead"}, {"question": "Take the last letters of each words in \"April Molly <PERSON>\" and concatenate them.", "answer": "lyen"}, {"question": "Take the last letters of each words in \"Markus Kathryn <PERSON>\" and concatenate them.", "answer": "snat"}, {"question": "Take the last letters of each words in \"Desmond Camille <PERSON>\" and concatenate them.", "answer": "deaa"}, {"question": "Take the last letters of each words in \"Quinton Sam Soledad Becca\" and concatenate them.", "answer": "nmda"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON>\" and concatenate them.", "answer": "enli"}, {"question": "Take the last letters of each words in \"Manolo Bobbie Ash Jaqueline\" and concatenate them.", "answer": "oehe"}, {"question": "Take the last letters of each words in \"Jorge Luis Mo Alexia Jerry\" and concatenate them.", "answer": "soay"}, {"question": "Take the last letters of each words in \"Kali Jeanette Tess Devin\" and concatenate them.", "answer": "iesn"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON>\" and concatenate them.", "answer": "leyn"}, {"question": "Take the last letters of each words in \"Charity Svetlana Jamie Jose A\" and concatenate them.", "answer": "yaeA"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON>\" and concatenate them.", "answer": "ayyd"}, {"question": "Take the last letters of each words in \"Blake Hunter Lou Spencer\" and concatenate them.", "answer": "erur"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON><PERSON>\" and concatenate them.", "answer": "yory"}, {"question": "Take the last letters of each words in \"Hayden Maya Zack Roberto\" and concatenate them.", "answer": "nako"}, {"question": "Take the last letters of each words in \"Jesús Vidal Maxine Gloria\" and concatenate them.", "answer": "slea"}, {"question": "Take the last letters of each words in \"Madeline Mckenzie Louie Dylan\" and concatenate them.", "answer": "eeen"}, {"question": "Take the last letters of each words in \"Anastasia Thelma Sheri <PERSON>\" and concatenate them.", "answer": "aaia"}, {"question": "Take the last letters of each words in \"Guillermina Evelin <PERSON>\" and concatenate them.", "answer": "aney"}, {"question": "Take the last letters of each words in \"Sid Isabelle Jackson Heidy\" and concatenate them.", "answer": "deny"}, {"question": "Take the last letters of each words in \"Margaret Rosi Willy Charlene\" and concatenate them.", "answer": "tiye"}, {"question": "Take the last letters of each words in \"Carla Dolores Cooper Damion\" and concatenate them.", "answer": "asrn"}, {"question": "Take the last letters of each words in \"Davis Jules Fabiola Cherie\" and concatenate them.", "answer": "ssae"}, {"question": "Take the last letters of each words in \"Raven Marisela Ross Angie\" and concatenate them.", "answer": "nase"}, {"question": "Take the last letters of each words in \"Jeff Jen Giselle Noel\" and concatenate them.", "answer": "fnel"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON>\" and concatenate them.", "answer": "iooe"}, {"question": "Take the last letters of each words in \"Alina Al<PERSON>andra <PERSON>\" and concatenate them.", "answer": "aaaa"}, {"question": "Take the last letters of each words in \"Zach Trenton Cary Beth\" and concatenate them.", "answer": "hnyh"}, {"question": "Take the last letters of each words in \"Altagracia <PERSON>\" and concatenate them.", "answer": "adis"}, {"question": "Take the last letters of each words in \"Jordan Yoni Lawrence Aura\" and concatenate them.", "answer": "niea"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON><PERSON>o <PERSON>\" and concatenate them.", "answer": "aoal"}, {"question": "Take the last letters of each words in \"Mickey Dom Lilly Eloy\" and concatenate them.", "answer": "ymyy"}, {"question": "Take the last letters of each words in \"Timmy Katherine Gabriel Nate\" and concatenate them.", "answer": "yele"}, {"question": "Take the last letters of each words in \"Raymundo Jonathon Lexi Rony\" and concatenate them.", "answer": "oniy"}, {"question": "Take the last letters of each words in \"Amy Ella Amilcar Roman\" and concatenate them.", "answer": "yarn"}, {"question": "Take the last letters of each words in \"Bailey Lourdes Brianna Martín\" and concatenate them.", "answer": "ysan"}, {"question": "Take the last letters of each words in \"Daniella Dianne <PERSON>\" and concatenate them.", "answer": "aemn"}, {"question": "Take the last letters of each words in \"Rosie Charlie Mick J<PERSON>\" and concatenate them.", "answer": "eekn"}, {"question": "Take the last letters of each words in \"Ericka Aly Darius Reed\" and concatenate them.", "answer": "aysd"}, {"question": "Take the last letters of each words in \"Glenda Beverly Agustin Igor\" and concatenate them.", "answer": "aynr"}, {"question": "Take the last letters of each words in \"Dino Toby Abigail Manuel<PERSON>\" and concatenate them.", "answer": "oyla"}, {"question": "Take the last letters of each words in \"<PERSON>\" and concatenate them.", "answer": "neya"}, {"question": "Take the last letters of each words in \"Cristian Nik <PERSON>\" and concatenate them.", "answer": "nknh"}, {"question": "Take the last letters of each words in \"Garry <PERSON>le <PERSON>ba Rodney\" and concatenate them.", "answer": "yeay"}, {"question": "Take the last letters of each words in \"Clarissa Shauna <PERSON>\" and concatenate them.", "answer": "aasn"}, {"question": "Take the last letters of each words in \"Barb Gage Kristian Asia\" and concatenate them.", "answer": "bena"}, {"question": "Take the last letters of each words in \"Emilia Jonas Christi Sophia\" and concatenate them.", "answer": "asia"}, {"question": "Take the last letters of each words in \"Sonia Griffin Claude Josephine\" and concatenate them.", "answer": "anee"}, {"question": "Take the last letters of each words in \"Arthur Shan Norman Manny\" and concatenate them.", "answer": "rnny"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON>\" and concatenate them.", "answer": "ieae"}, {"question": "Take the last letters of each words in \"Scotty Edgar Hanna Austin\" and concatenate them.", "answer": "yran"}, {"question": "Take the last letters of each words in \"Denis Gigi <PERSON>\" and concatenate them.", "answer": "sirr"}, {"question": "Take the last letters of each words in \"<PERSON>h <PERSON> Fran<PERSON>\" and concatenate them.", "answer": "hoie"}, {"question": "Take the last letters of each words in \"Orlando Colby Julius Ofelia\" and concatenate them.", "answer": "oysa"}, {"question": "Take the last letters of each words in \"Rogelio Freddy <PERSON>\" and concatenate them.", "answer": "oyne"}, {"question": "Take the last letters of each words in \"Marshall Herman <PERSON>\" and concatenate them.", "answer": "lnet"}, {"question": "Take the last letters of each words in \"Bernard Lidia <PERSON>\" and concatenate them.", "answer": "dany"}, {"question": "Take the last letters of each words in \"Kyra Luciano Ciara Bryan\" and concatenate them.", "answer": "aoan"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON><PERSON>\" and concatenate them.", "answer": "m<PERSON>yl"}, {"question": "Take the last letters of each words in \"Memo Joey Phil Samuel\" and concatenate them.", "answer": "oyll"}, {"question": "Take the last letters of each words in \"Angel <PERSON>\" and concatenate them.", "answer": "lley"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON>\" and concatenate them.", "answer": "aady"}, {"question": "Take the last letters of each words in \"Talia Nicki Tia Divya\" and concatenate them.", "answer": "aiaa"}, {"question": "Take the last letters of each words in \"Franklin Rochelle Brent Sarai\" and concatenate them.", "answer": "neti"}, {"question": "Take the last letters of each words in \"<PERSON> Yanira <PERSON>\" and concatenate them.", "answer": "naay"}, {"question": "Take the last letters of each words in \"Ronnie Kiki Alan Remy\" and concatenate them.", "answer": "einy"}, {"question": "Take the last letters of each words in \"Erwin Chad <PERSON>\" and concatenate them.", "answer": "ndoa"}, {"question": "Take the last letters of each words in \"Connie Elena Tami Stuart\" and concatenate them.", "answer": "eait"}, {"question": "Take the last letters of each words in \"Blaine Syed <PERSON>\" and concatenate them.", "answer": "eday"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON>nda Ma<PERSON>da <PERSON>\" and concatenate them.", "answer": "aaro"}, {"question": "Take the last letters of each words in \"Anand Estefania Stanley Lizette\" and concatenate them.", "answer": "daye"}, {"question": "Take the last letters of each words in \"Sunil Tiana Darla Darnell\" and concatenate them.", "answer": "laal"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON><PERSON>ises <PERSON>\" and concatenate them.", "answer": "osnk"}, {"question": "Take the last letters of each words in \"Marty Justin<PERSON>\" and concatenate them.", "answer": "yeon"}, {"question": "Take the last letters of each words in \"Tucker <PERSON>\" and concatenate them.", "answer": "rlzn"}, {"question": "Take the last letters of each words in \"Sherri Genesis <PERSON>\" and concatenate them.", "answer": "isyr"}, {"question": "Take the last letters of each words in \"Cat Ali<PERSON>\" and concatenate them.", "answer": "tano"}, {"question": "Take the last letters of each words in \"Hi<PERSON>io Magdalena Morris Patricio\" and concatenate them.", "answer": "oaso"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON>\" and concatenate them.", "answer": "ooee"}, {"question": "Take the last letters of each words in \"Gayle Doreen Chelsey Helena\" and concatenate them.", "answer": "enya"}, {"question": "Take the last letters of each words in \"Lucio Víctor <PERSON>\" and concatenate them.", "answer": "orre"}, {"question": "Take the last letters of each words in \"Donnie Alli Terry Krystal\" and concatenate them.", "answer": "eiyl"}, {"question": "Take the last letters of each words in \"Carlos Jackie <PERSON>\" and concatenate them.", "answer": "seen"}, {"question": "Take the last letters of each words in \"<PERSON>bi Raymond <PERSON>\" and concatenate them.", "answer": "idso"}, {"question": "Take the last letters of each words in \"Kelvin Brennan Carina <PERSON>\" and concatenate them.", "answer": "nnay"}, {"question": "Take the last letters of each words in \"Janice Shelly Arnulfo Nestor\" and concatenate them.", "answer": "eyor"}, {"question": "Take the last letters of each words in \"Bob Aman Richie <PERSON>\" and concatenate them.", "answer": "bnea"}, {"question": "Take the last letters of each words in \"Juan Jose <PERSON>ia Destiny Amelia\" and concatenate them.", "answer": "eaya"}, {"question": "Take the last letters of each words in \"Jose Luis Ki<PERSON>\" and concatenate them.", "answer": "sana"}, {"question": "Take the last letters of each words in \"Guillermo Gerry Liz<PERSON>\" and concatenate them.", "answer": "oyhy"}, {"question": "Take the last letters of each words in \"Reginald Franky <PERSON>\" and concatenate them.", "answer": "dyan"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON>\" and concatenate them.", "answer": "tsla"}, {"question": "Take the last letters of each words in \"Peggy Trent Darrell Pamela\" and concatenate them.", "answer": "ytla"}, {"question": "Take the last letters of each words in \"Sophie Gregorio Avery Pooja\" and concatenate them.", "answer": "e<PERSON>"}, {"question": "Take the last letters of each words in \"Yvonne Rafaela Jb Salomon\" and concatenate them.", "answer": "eabn"}, {"question": "Take the last letters of each words in \"Gabi Dante <PERSON>\" and concatenate them.", "answer": "ieaa"}, {"question": "Take the last letters of each words in \"Martin Sage Tanisha Rick\" and concatenate them.", "answer": "neak"}, {"question": "Take the last letters of each words in \"Suzanne Julissa Chino America\" and concatenate them.", "answer": "e<PERSON>a"}, {"question": "Take the last letters of each words in \"Mel <PERSON>\" and concatenate them.", "answer": "lhay"}, {"question": "Take the last letters of each words in \"Candy Megan Ed Nathan\" and concatenate them.", "answer": "yndn"}, {"question": "Take the last letters of each words in \"Diego Val Vincent Stacie\" and concatenate them.", "answer": "olte"}, {"question": "Take the last letters of each words in \"Salvatore Gustavo Jill Celeste\" and concatenate them.", "answer": "eole"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON> Alice <PERSON>rlene\" and concatenate them.", "answer": "yzee"}, {"question": "Take the last letters of each words in \"Dana German Alvin Brad<PERSON>\" and concatenate them.", "answer": "annn"}, {"question": "Take the last letters of each words in \"Paula Irina Laurel Maribel\" and concatenate them.", "answer": "aall"}, {"question": "Take the last letters of each words in \"Andrés Miles <PERSON> Melinda\" and concatenate them.", "answer": "ssoa"}, {"question": "Take the last letters of each words in \"Williams Reza Ashton Lillian\" and concatenate them.", "answer": "sann"}, {"question": "Take the last letters of each words in \"Lacey Nora Debra <PERSON>\" and concatenate them.", "answer": "yaah"}, {"question": "Take the last letters of each words in \"Itzel Anderson Ken<PERSON> Ed<PERSON>\" and concatenate them.", "answer": "lnay"}, {"question": "Take the last letters of each words in \"Forrest Juanito Allan Candice\" and concatenate them.", "answer": "tone"}, {"question": "Take the last letters of each words in \"Nico Lorenzo Johanna Teresita\" and concatenate them.", "answer": "o<PERSON>a"}, {"question": "Take the last letters of each words in \"Paulo Tatyana <PERSON>\" and concatenate them.", "answer": "oael"}, {"question": "Take the last letters of each words in \"Lionel <PERSON>\" and concatenate them.", "answer": "layh"}, {"question": "Take the last letters of each words in \"<PERSON>y Hilda Butch Mahesh\" and concatenate them.", "answer": "yahh"}, {"question": "Take the last letters of each words in \"Deon Lane Everett Lindsay\" and concatenate them.", "answer": "nety"}, {"question": "Take the last letters of each words in \"Ubaldo Katrina <PERSON>\" and concatenate them.", "answer": "oasn"}, {"question": "Take the last letters of each words in \"Lesley Luna Nadia Adriana\" and concatenate them.", "answer": "yaaa"}, {"question": "Take the last letters of each words in \"Maureen <PERSON>\" and concatenate them.", "answer": "nnen"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON> Celia\" and concatenate them.", "answer": "yeha"}, {"question": "Take the last letters of each words in \"Ramona Lucy Gail Octavio\" and concatenate them.", "answer": "aylo"}, {"question": "Take the last letters of each words in \"Sue Cassandra Cody Cali\" and concatenate them.", "answer": "eayi"}, {"question": "Take the last letters of each words in \"<PERSON>fra<PERSON> <PERSON>\" and concatenate them.", "answer": "nyni"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON>ren Rex <PERSON>\" and concatenate them.", "answer": "nxnn"}, {"question": "Take the last letters of each words in \"Bobbi Tamika Zac Lala\" and concatenate them.", "answer": "iaca"}, {"question": "Take the last letters of each words in \"Kendall Matias <PERSON>\" and concatenate them.", "answer": "lsby"}, {"question": "Take the last letters of each words in \"Armando Astrid Anibal Dakota\" and concatenate them.", "answer": "odla"}, {"question": "Take the last letters of each words in \"Lamont Frederick Lance Esperanza\" and concatenate them.", "answer": "tkea"}, {"question": "Take the last letters of each words in \"Lynda Danilo Jonny Judith\" and concatenate them.", "answer": "aoyh"}, {"question": "Take the last letters of each words in \"Deandre Moe Jack Vanessa\" and concatenate them.", "answer": "eeka"}, {"question": "Take the last letters of each words in \"Tere Niko Keith Conner\" and concatenate them.", "answer": "eohr"}, {"question": "Take the last letters of each words in \"Kelly Cheryl Nancy Jojo\" and concatenate them.", "answer": "ylyo"}, {"question": "Take the last letters of each words in \"Héctor Daniela Rossy Jose <PERSON>\" and concatenate them.", "answer": "rayl"}, {"question": "Take the last letters of each words in \"Carissa Paige Consuelo Izzy\" and concatenate them.", "answer": "aeoy"}, {"question": "Take the last letters of each words in \"Wilson Abbey Harold Nelly\" and concatenate them.", "answer": "nydy"}, {"question": "Take the last letters of each words in \"Tiara Araceli Michaela Genaro\" and concatenate them.", "answer": "aiao"}, {"question": "Take the last letters of each words in \"Alexa Pilar Rod Nicola\" and concatenate them.", "answer": "arda"}, {"question": "Take the last letters of each words in \"Yamileth Dane A<PERSON>\" and concatenate them.", "answer": "hene"}, {"question": "Take the last letters of each words in \"Rachel Alisa <PERSON>\" and concatenate them.", "answer": "layy"}, {"question": "Take the last letters of each words in \"Manuel Aurelio India Rosalba\" and concatenate them.", "answer": "loaa"}, {"question": "Take the last letters of each words in \"Garrett Eva Joaquin Monique\" and concatenate them.", "answer": "tane"}, {"question": "Take the last letters of each words in \"Ivy Romeo Jana Ej\" and concatenate them.", "answer": "yoaj"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON><PERSON>rrell Adam Lexie\" and concatenate them.", "answer": "alme"}, {"question": "Take the last letters of each words in \"Virginia Juanita <PERSON>\" and concatenate them.", "answer": "aake"}, {"question": "Take the last letters of each words in \"Mari Ximena Leo Antonia\" and concatenate them.", "answer": "i<PERSON>a"}, {"question": "Take the last letters of each words in \"Salvador Sol Tyler Kareem\" and concatenate them.", "answer": "rlrm"}, {"question": "Take the last letters of each words in \"Allyson Mara Jo Toni\" and concatenate them.", "answer": "naoi"}, {"question": "Take the last letters of each words in \"Rocio Sandy Anahi <PERSON>\" and concatenate them.", "answer": "oyia"}, {"question": "Take the last letters of each words in \"<PERSON>ki <PERSON>\" and concatenate them.", "answer": "itna"}, {"question": "Take the last letters of each words in \"Emma Maryann <PERSON>\" and concatenate them.", "answer": "anay"}, {"question": "Take the last letters of each words in \"Belkis Wendell Lissette Patricia\" and concatenate them.", "answer": "slea"}, {"question": "Take the last letters of each words in \"Ever Gio Elia <PERSON>\" and concatenate them.", "answer": "roah"}, {"question": "Take the last letters of each words in \"Dallas Uriel Brendan Julian\" and concatenate them.", "answer": "slnn"}, {"question": "Take the last letters of each words in \"Sandeep Graciela Jai <PERSON>\" and concatenate them.", "answer": "paia"}, {"question": "Take the last letters of each words in \"Frank Trevor Al Gabriella\" and concatenate them.", "answer": "krla"}, {"question": "Take the last letters of each words in \"Elva Kari <PERSON>\" and concatenate them.", "answer": "aiyo"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON><PERSON>\" and concatenate them.", "answer": "oydz"}, {"question": "Take the last letters of each words in \"Ernest Yuri <PERSON>\" and concatenate them.", "answer": "tiae"}, {"question": "Take the last letters of each words in \"Jim Dwayne <PERSON>mio\" and concatenate them.", "answer": "meyo"}, {"question": "Take the last letters of each words in \"Noah Aubrey Cesar Eli<PERSON>\" and concatenate them.", "answer": "hyra"}, {"question": "Take the last letters of each words in \"Shawn Tracie <PERSON>\" and concatenate them.", "answer": "<PERSON><PERSON>"}, {"question": "Take the last letters of each words in \"Norma <PERSON>\" and concatenate them.", "answer": "ayew"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON><PERSON><PERSON> Alfredo <PERSON>\" and concatenate them.", "answer": "zoyk"}, {"question": "Take the last letters of each words in \"Julia Kirsten Pam Ada<PERSON>\" and concatenate them.", "answer": "anmn"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON><PERSON>\" and concatenate them.", "answer": "olrn"}, {"question": "Take the last letters of each words in \"Nicky Eden James Matt\" and concatenate them.", "answer": "ynst"}, {"question": "Take the last letters of each words in \"Jaime Brad <PERSON>\" and concatenate them.", "answer": "edil"}, {"question": "Take the last letters of each words in \"Kristie <PERSON>nie <PERSON>a Derick\" and concatenate them.", "answer": "eeak"}, {"question": "Take the last letters of each words in \"W<PERSON>fredo <PERSON>\" and concatenate them.", "answer": "oyky"}, {"question": "Take the last letters of each words in \"Pancho Claudio <PERSON>\" and concatenate them.", "answer": "ooey"}, {"question": "Take the last letters of each words in \"Teri Lina Mery Melanie\" and concatenate them.", "answer": "iaye"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON>\" and concatenate them.", "answer": "exna"}, {"question": "Take the last letters of each words in \"Gus Brock Ava Jenna\" and concatenate them.", "answer": "skaa"}, {"question": "Take the last letters of each words in \"Karen Hector Mai Steven\" and concatenate them.", "answer": "nrin"}, {"question": "Take the last letters of each words in \"Alexander Marina Valentina Mila\" and concatenate them.", "answer": "raaa"}, {"question": "Take the last letters of each words in \"Kristine Van <PERSON>\" and concatenate them.", "answer": "enln"}, {"question": "Take the last letters of each words in \"Evan Ray <PERSON> Alonso\" and concatenate them.", "answer": "nyao"}, {"question": "Take the last letters of each words in \"Alfonso Collin Amado Dick\" and concatenate them.", "answer": "onok"}, {"question": "Take the last letters of each words in \"Les Jun Noe Juliana\" and concatenate them.", "answer": "snea"}, {"question": "Take the last letters of each words in \"Damian Crystal Nisha <PERSON>\" and concatenate them.", "answer": "nlan"}, {"question": "Take the last letters of each words in \"Rosario Manoj <PERSON>\" and concatenate them.", "answer": "ojoa"}, {"question": "Take the last letters of each words in \"Marcia Belen Reyna Britney\" and concatenate them.", "answer": "anay"}, {"question": "Take the last letters of each words in \"Idalia Arnoldo Marla Duane\" and concatenate them.", "answer": "aoae"}, {"question": "Take the last letters of each words in \"Katia Mina Cam Ronda\" and concatenate them.", "answer": "aama"}, {"question": "Take the last letters of each words in \"Rashad Savannah Flavio Bert\" and concatenate them.", "answer": "dhot"}, {"question": "Take the last letters of each words in \"Leanne Lulu Lopez Jp\" and concatenate them.", "answer": "euzp"}, {"question": "Take the last letters of each words in \"Lora Alberto <PERSON>\" and concatenate them.", "answer": "aoya"}, {"question": "Take the last letters of each words in \"Roni Nikita <PERSON>\" and concatenate them.", "answer": "iaha"}, {"question": "Take the last letters of each words in \"Edwin Lovely Curt Damon\" and concatenate them.", "answer": "nytn"}, {"question": "Take the last letters of each words in \"Missy Erin Lorna Lenny\" and concatenate them.", "answer": "ynay"}, {"question": "Take the last letters of each words in \"Vijay Sherrie <PERSON>\" and concatenate them.", "answer": "yegy"}, {"question": "Take the last letters of each words in \"Claudia Cole Matthew <PERSON>\" and concatenate them.", "answer": "aewo"}, {"question": "Take the last letters of each words in \"Arturo Dominick Christa Myles\" and concatenate them.", "answer": "okas"}, {"question": "Take the last letters of each words in \"Marc Doris Ernie Gary\" and concatenate them.", "answer": "csey"}, {"question": "Take the last letters of each words in \"Teresa Reid Karin Gracie\" and concatenate them.", "answer": "adne"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON>\" and concatenate them.", "answer": "lnhe"}, {"question": "Take the last letters of each words in \"Miranda Jacques <PERSON>\" and concatenate them.", "answer": "asea"}, {"question": "Take the last letters of each words in \"June Robin Josie Bo\" and concatenate them.", "answer": "eneo"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON>\" and concatenate them.", "answer": "a<PERSON>y"}, {"question": "Take the last letters of each words in \"Louise Mariano <PERSON>\" and concatenate them.", "answer": "eoao"}, {"question": "Take the last letters of each words in \"Star Jude Rosemarie Raquel\" and concatenate them.", "answer": "reel"}, {"question": "Take the last letters of each words in \"Clark Jenn <PERSON>\" and concatenate them.", "answer": "kner"}, {"question": "Take the last letters of each words in \"Lalo Yolanda Elida Ester\" and concatenate them.", "answer": "oaar"}, {"question": "Take the last letters of each words in \"Bethany Rakesh <PERSON>\" and concatenate them.", "answer": "yheh"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON> Cindy Jess Chris\" and concatenate them.", "answer": "yyss"}, {"question": "Take the last letters of each words in \"Miriam Brandy Bertha Renato\" and concatenate them.", "answer": "myao"}, {"question": "Take the last letters of each words in \"Abel Mallory Theresa Quinn\" and concatenate them.", "answer": "lyan"}, {"question": "Take the last letters of each words in \"Celso Tracy Winston Anton\" and concatenate them.", "answer": "oynn"}, {"question": "Take the last letters of each words in \"Lore Erasmo Louis David\" and concatenate them.", "answer": "eosd"}, {"question": "Take the last letters of each words in \"Cristobal Dania <PERSON>\" and concatenate them.", "answer": "laia"}, {"question": "Take the last letters of each words in \"Wally Claire Helen Nacho\" and concatenate them.", "answer": "yeno"}, {"question": "Take the last letters of each words in \"Rosa Lana <PERSON>\" and concatenate them.", "answer": "aase"}, {"question": "Take the last letters of each words in \"Angeles Richard Lucian<PERSON>\" and concatenate them.", "answer": "sdae"}, {"question": "Take the last letters of each words in \"Jodi Judi Nia Raj\" and concatenate them.", "answer": "iiaj"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON>ney <PERSON>\" and concatenate them.", "answer": "nyes"}, {"question": "Take the last letters of each words in \"Guille Lisa <PERSON>\" and concatenate them.", "answer": "eaya"}, {"question": "Take the last letters of each words in \"Renata Mariela Mona <PERSON>\" and concatenate them.", "answer": "aaan"}, {"question": "Take the last letters of each words in \"Johan Damien Serena Grace\" and concatenate them.", "answer": "nnae"}, {"question": "Take the last letters of each words in \"<PERSON>j Jd Maddie Francis<PERSON>\" and concatenate them.", "answer": "j<PERSON>"}, {"question": "Take the last letters of each words in \"Daryl Owen Myra Aaron\" and concatenate them.", "answer": "lnan"}, {"question": "Take the last letters of each words in \"Nick Ada Stephany Suzie\" and concatenate them.", "answer": "kaye"}, {"question": "Take the last letters of each words in \"Elle Alex Irma Stephan\" and concatenate them.", "answer": "exan"}, {"question": "Take the last letters of each words in \"Arnold Aidan Ally Ami\" and concatenate them.", "answer": "dnyi"}, {"question": "Take the last letters of each words in \"Gabino Kayla Laurie Familia\" and concatenate them.", "answer": "oaea"}, {"question": "Take the last letters of each words in \"Logan Ely Abbie Colleen\" and concatenate them.", "answer": "nyen"}, {"question": "Take the last letters of each words in \"Kristy Brandi Lizeth Petra\" and concatenate them.", "answer": "yiha"}, {"question": "Take the last letters of each words in \"Hazel Gabrielle Tre Dalton\" and concatenate them.", "answer": "leen"}, {"question": "Take the last letters of each words in \"Conrad Marcella <PERSON>\" and concatenate them.", "answer": "daen"}, {"question": "Take the last letters of each words in \"Isaac Reyes Carly Tan<PERSON>\" and concatenate them.", "answer": "csya"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON><PERSON>\" and concatenate them.", "answer": "aero"}, {"question": "Take the last letters of each words in \"Sonya Eddy <PERSON>\" and concatenate them.", "answer": "aylg"}, {"question": "Take the last letters of each words in \"Leandro <PERSON>ly\" and concatenate them.", "answer": "o<PERSON>y"}, {"question": "Take the last letters of each words in \"Andy Cecilia <PERSON>\" and concatenate them.", "answer": "yani"}, {"question": "Take the last letters of each words in \"Darwin Colin Cj A<PERSON>\" and concatenate them.", "answer": "nnjk"}, {"question": "Take the last letters of each words in \"Lupita Cyndi Kay Derrick\" and concatenate them.", "answer": "aiyk"}, {"question": "Take the last letters of each words in \"Felipe <PERSON>\" and concatenate them.", "answer": "eioy"}, {"question": "Take the last letters of each words in \"Dorian Mayra Freddie Ma<PERSON>\" and concatenate them.", "answer": "naey"}, {"question": "Take the last letters of each words in \"Flora Warren <PERSON>\" and concatenate them.", "answer": "anna"}, {"question": "Take the last letters of each words in \"Ace Rosy Kimberly Jean\" and concatenate them.", "answer": "e<PERSON>yn"}, {"question": "Take the last letters of each words in \"Jeremy Simone Alondra Wyatt\" and concatenate them.", "answer": "yeat"}, {"question": "Take the last letters of each words in \"Deanna Terri Gabriela Jonah\" and concatenate them.", "answer": "aiah"}, {"question": "Take the last letters of each words in \"Conor Randall Oleg Stephanie\" and concatenate them.", "answer": "rlge"}, {"question": "Take the last letters of each words in \"Chance Valentin Micah Clara\" and concatenate them.", "answer": "enha"}, {"question": "Take the last letters of each words in \"Dusty Yanet Hortencia Lili\" and concatenate them.", "answer": "ytai"}, {"question": "Take the last letters of each words in \"Natalie Gilbert <PERSON>\" and concatenate them.", "answer": "etnz"}, {"question": "Take the last letters of each words in \"Ulises Derek <PERSON>\" and concatenate them.", "answer": "skae"}, {"question": "Take the last letters of each words in \"Carlitos Damaris Nikhil Jennie\" and concatenate them.", "answer": "ssle"}, {"question": "Take the last letters of each words in \"Yessenia Geraldine Minerva Tanya\" and concatenate them.", "answer": "aeaa"}, {"question": "Take the last letters of each words in \"Nicolas Aaliyah Pascual Rob\" and concatenate them.", "answer": "shlb"}, {"question": "Take the last letters of each words in \"<PERSON>ne <PERSON>\" and concatenate them.", "answer": "nena"}, {"question": "Take the last letters of each words in \"Pedro Leopoldo Tee Mar\" and concatenate them.", "answer": "ooer"}, {"question": "Take the last letters of each words in \"Paris Jimmie Andrew Ramón\" and concatenate them.", "answer": "sewn"}, {"question": "Take the last letters of each words in \"Angelica Tariq <PERSON>\" and concatenate them.", "answer": "aqaa"}, {"question": "Take the last letters of each words in \"Luz Terence Elder J<PERSON>min\" and concatenate them.", "answer": "zern"}, {"question": "Take the last letters of each words in \"Roberta Mauro <PERSON>\" and concatenate them.", "answer": "aota"}, {"question": "Take the last letters of each words in \"Adrian Marlon Karla Florence\" and concatenate them.", "answer": "nnae"}, {"question": "Take the last letters of each words in \"Skyler Oliver Cristy Sierra\" and concatenate them.", "answer": "rrya"}, {"question": "Take the last letters of each words in \"Barbie Desiree Yaneth Dre\" and concatenate them.", "answer": "eehe"}, {"question": "Take the last letters of each words in \"Dan Ruth Xavier <PERSON>\" and concatenate them.", "answer": "nhro"}, {"question": "Take the last letters of each words in \"Philip <PERSON>\" and concatenate them.", "answer": "pysa"}, {"question": "Take the last letters of each words in \"Corey <PERSON>\" and concatenate them.", "answer": "ynon"}, {"question": "Take the last letters of each words in \"Russell Mitchell Bee Faith\" and concatenate them.", "answer": "lleh"}, {"question": "Take the last letters of each words in \"Bri Roger Eve Diana\" and concatenate them.", "answer": "irea"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON>la <PERSON>\" and concatenate them.", "answer": "<PERSON>ey"}, {"question": "Take the last letters of each words in \"<PERSON>ny Landon <PERSON>\" and concatenate them.", "answer": "ynln"}, {"question": "Take the last letters of each words in \"Constance Nicholas Will Love\" and concatenate them.", "answer": "esle"}, {"question": "Take the last letters of each words in \"Jeannie Kenneth Porfirio E<PERSON>quiel\" and concatenate them.", "answer": "ehol"}, {"question": "Take the last letters of each words in \"Mario Pierre Amit Nelson\" and concatenate them.", "answer": "oetn"}, {"question": "Take the last letters of each words in \"Mercedes Adela Susana Rose\" and concatenate them.", "answer": "saae"}, {"question": "Take the last letters of each words in \"Fred <PERSON>\" and concatenate them.", "answer": "dnnn"}, {"question": "Take the last letters of each words in \"Caroline Demetrius <PERSON>\" and concatenate them.", "answer": "esln"}, {"question": "Take the last letters of each words in \"Julio Sidney <PERSON>\" and concatenate them.", "answer": "oyny"}, {"question": "Take the last letters of each words in \"Mabel Estela Irene May\" and concatenate them.", "answer": "laey"}, {"question": "Take the last letters of each words in \"<PERSON> Junior <PERSON>ly\" and concatenate them.", "answer": "s<PERSON>ry"}, {"question": "Take the last letters of each words in \"Todd Joni <PERSON>\" and concatenate them.", "answer": "diln"}, {"question": "Take the last letters of each words in \"Gerson Roxanne <PERSON>\" and concatenate them.", "answer": "nehl"}, {"question": "Take the last letters of each words in \"Colton Dexter Katy Brayden\" and concatenate them.", "answer": "nryn"}, {"question": "Take the last letters of each words in \"Tori Mariam Gaby Brayan\" and concatenate them.", "answer": "imyn"}, {"question": "Take the last letters of each words in \"Michel Roy <PERSON>\" and concatenate them.", "answer": "lyye"}, {"question": "Take the last letters of each words in \"<PERSON>il Enrique <PERSON>\" and concatenate them.", "answer": "leyy"}, {"question": "Take the last letters of each words in \"Ángel Carlton <PERSON>\" and concatenate them.", "answer": "lnrz"}, {"question": "Take the last letters of each words in \"Maritza Nana Loretta Eric\" and concatenate them.", "answer": "aaac"}, {"question": "Take the last letters of each words in \"Thomas Cara Nita Frances\" and concatenate them.", "answer": "saas"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON> Shane <PERSON>\" and concatenate them.", "answer": "yenr"}, {"question": "Take the last letters of each words in \"Violeta Clay Janelle Mac\" and concatenate them.", "answer": "ayec"}, {"question": "Take the last letters of each words in \"Guadalupe Ebony Wil Luke\" and concatenate them.", "answer": "eyle"}, {"question": "Take the last letters of each words in \"Jenni Leonel Micheal Kat\" and concatenate them.", "answer": "illt"}, {"question": "Take the last letters of each words in \"Yazmin Lea <PERSON> Sammy\" and concatenate them.", "answer": "naoy"}, {"question": "Take the last letters of each words in \"Emely Chelsea Vladimir Tyrone\" and concatenate them.", "answer": "yare"}, {"question": "Take the last letters of each words in \"Bill Santos Roxy Randi\" and concatenate them.", "answer": "lsyi"}, {"question": "Take the last letters of each words in \"Daphne Lola Joanna Sheena\" and concatenate them.", "answer": "eaaa"}, {"question": "Take the last letters of each words in \"Lara Greg Ethan Terrence\" and concatenate them.", "answer": "agne"}, {"question": "Take the last letters of each words in \"Otto Marjorie Leon<PERSON> Esther\" and concatenate them.", "answer": "oerr"}, {"question": "Take the last letters of each words in \"Malcolm Hussein <PERSON>\" and concatenate them.", "answer": "mnoo"}, {"question": "Take the last letters of each words in \"<PERSON>\" and concatenate them.", "answer": "eyae"}, {"question": "Take the last letters of each words in \"Maryam Susy Trinity Pearl\" and concatenate them.", "answer": "myyl"}, {"question": "Take the last letters of each words in \"<PERSON>\" and concatenate them.", "answer": "nzor"}, {"question": "Take the last letters of each words in \"Juan Manuel <PERSON>\" and concatenate them.", "answer": "lnyl"}, {"question": "Take the last letters of each words in \"Fermin Berenice <PERSON>\" and concatenate them.", "answer": "neer"}, {"question": "Take the last letters of each words in \"Sabrina Pete Mary La\" and concatenate them.", "answer": "aeya"}, {"question": "Take the last letters of each words in \"Estrella Madison Paco Rj\" and concatenate them.", "answer": "anoj"}, {"question": "Take the last letters of each words in \"Niki Graham <PERSON>\" and concatenate them.", "answer": "imnu"}, {"question": "Take the last letters of each words in \"Bernie Melina <PERSON>\" and concatenate them.", "answer": "eayy"}, {"question": "Take the last letters of each words in \"Latoya Eliseo Trina Melisa\" and concatenate them.", "answer": "aoaa"}, {"question": "Take the last letters of each words in \"<PERSON>\" and concatenate them.", "answer": "yook"}, {"question": "Take the last letters of each words in \"Sean Rebeca Marco Sami\" and concatenate them.", "answer": "naoi"}, {"question": "Take the last letters of each words in \"Blanca Monika Ervin Lori\" and concatenate them.", "answer": "aani"}, {"question": "Take the last letters of each words in \"Lorraine Corinne <PERSON>\" and concatenate them.", "answer": "eeed"}, {"question": "Take the last letters of each words in \"Bruce Dena Kyla Robbie\" and concatenate them.", "answer": "eaae"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON><PERSON>\" and concatenate them.", "answer": "esye"}, {"question": "Take the last letters of each words in \"Kristen Herbert Benny El\" and concatenate them.", "answer": "ntyl"}, {"question": "Take the last letters of each words in \"Justin Neal Jt Lucia\" and concatenate them.", "answer": "nlta"}, {"question": "Take the last letters of each words in \"Carmelo Tony Susan Sara\" and concatenate them.", "answer": "oyna"}, {"question": "Take the last letters of each words in \"<PERSON>\" and concatenate them.", "answer": "sann"}, {"question": "Take the last letters of each words in \"Elizabeth Mitch <PERSON>rdy<PERSON>\" and concatenate them.", "answer": "hhen"}, {"question": "Take the last letters of each words in \"Delia Kathleen <PERSON>\" and concatenate them.", "answer": "anon"}, {"question": "Take the last letters of each words in \"Bj <PERSON>igo <PERSON> Christian\" and concatenate them.", "answer": "joln"}, {"question": "Take the last letters of each words in \"Kathy Elsa Alba Ivette\" and concatenate them.", "answer": "yaae"}, {"question": "Take the last letters of each words in \"Cruz Wilber <PERSON>lu Malik\" and concatenate them.", "answer": "zruk"}, {"question": "Take the last letters of each words in \"Sherry Ben <PERSON>\" and concatenate them.", "answer": "ynny"}, {"question": "Take the last letters of each words in \"Seth Dario Anne Jodie\" and concatenate them.", "answer": "hoee"}, {"question": "Take the last letters of each words in \"Leon Payton Stefan Javi\" and concatenate them.", "answer": "nnni"}, {"question": "Take the last letters of each words in \"Marcos Kerri Fabio <PERSON>\" and concatenate them.", "answer": "sion"}, {"question": "Take the last letters of each words in \"Earl Rudy <PERSON>\" and concatenate them.", "answer": "lyne"}, {"question": "Take the last letters of each words in \"Andres Mary Ann Sydney Dina\" and concatenate them.", "answer": "snya"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON>an <PERSON>\" and concatenate them.", "answer": "<PERSON>ia"}, {"question": "Take the last letters of each words in \"Braulio Staci Jocelyn Brittany\" and concatenate them.", "answer": "oiny"}, {"question": "Take the last letters of each words in \"Clayton Edison Debbie Elvira\" and concatenate them.", "answer": "nnea"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON><PERSON> Tamara <PERSON>\" and concatenate them.", "answer": "tasi"}, {"question": "Take the last letters of each words in \"Cristina Saad Bridget Katie\" and concatenate them.", "answer": "adte"}, {"question": "Take the last letters of each words in \"Stella Janis Darren Lena\" and concatenate them.", "answer": "asna"}, {"question": "Take the last letters of each words in \"<PERSON>\" and concatenate them.", "answer": "soke"}, {"question": "Take the last letters of each words in \"Sal Scott Myrna Maximo\" and concatenate them.", "answer": "ltao"}, {"question": "Take the last letters of each words in \"Mia Art Samantha Lety\" and concatenate them.", "answer": "atay"}, {"question": "Take the last letters of each words in \"Miguel Angel Saul Brady Darryl\" and concatenate them.", "answer": "llyl"}, {"question": "Take the last letters of each words in \"Maria Elena <PERSON>\" and concatenate them.", "answer": "aejt"}, {"question": "Take the last letters of each words in \"Antoine <PERSON>\" and concatenate them.", "answer": "ertt"}, {"question": "Take the last letters of each words in \"Amanda Geoff <PERSON>\" and concatenate them.", "answer": "afav"}, {"question": "Take the last letters of each words in \"Krishna Catalina Eileen Teddy\" and concatenate them.", "answer": "aany"}, {"question": "Take the last letters of each words in \"María <PERSON>\" and concatenate them.", "answer": "ayea"}, {"question": "Take the last letters of each words in \"Neil Dani Eddie Marcelino\" and concatenate them.", "answer": "lieo"}, {"question": "Take the last letters of each words in \"Alain Jerome <PERSON>\" and concatenate them.", "answer": "neaa"}, {"question": "Take the last letters of each words in \"Drew Jhon <PERSON>\" and concatenate them.", "answer": "wnnf"}, {"question": "Take the last letters of each words in \"Trish Vero Victor Clemente\" and concatenate them.", "answer": "hore"}, {"question": "Take the last letters of each words in \"Antonio Ronald <PERSON>\" and concatenate them.", "answer": "odnl"}, {"question": "Take the last letters of each words in \"Juliet Ricardo Tita Dianna\" and concatenate them.", "answer": "toaa"}, {"question": "Take the last letters of each words in \"Larissa Shawna Alma Paulette\" and concatenate them.", "answer": "aaae"}, {"question": "Take the last letters of each words in \"Donald Lia Gonzalo Lily\" and concatenate them.", "answer": "daoy"}, {"question": "Take the last letters of each words in \"Evelyn Mason Shelby Aldo\" and concatenate them.", "answer": "nnyo"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON>\" and concatenate them.", "answer": "a<PERSON>r"}, {"question": "Take the last letters of each words in \"King <PERSON>\" and concatenate them.", "answer": "ghir"}, {"question": "Take the last letters of each words in \"Courtney Ann John Fer\" and concatenate them.", "answer": "ynnr"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON> Geoffrey <PERSON>\" and concatenate them.", "answer": "yyae"}, {"question": "Take the last letters of each words in \"Stevie Julie <PERSON>\" and concatenate them.", "answer": "eeda"}, {"question": "Take the last letters of each words in \"Juana Chip Lila Jayme\" and concatenate them.", "answer": "apae"}, {"question": "Take the last letters of each words in \"Jon <PERSON>do <PERSON>\" and concatenate them.", "answer": "nhoe"}, {"question": "Take the last letters of each words in \"Beatrice Taylor Juan <PERSON>\" and concatenate them.", "answer": "ersn"}, {"question": "Take the last letters of each words in \"Christina Edna Il<PERSON> L<PERSON>\" and concatenate them.", "answer": "aaae"}, {"question": "Take the last letters of each words in \"Eugenia <PERSON>llie <PERSON>\" and concatenate them.", "answer": "aene"}, {"question": "Take the last letters of each words in \"Ya<PERSON>in <PERSON>\" and concatenate them.", "answer": "naae"}, {"question": "Take the last letters of each words in \"Oswaldo José <PERSON>\" and concatenate them.", "answer": "osna"}, {"question": "Take the last letters of each words in \"Debora Jayson Donna Sai\" and concatenate them.", "answer": "anai"}, {"question": "Take the last letters of each words in \"Je<PERSON>ine Pat Tam<PERSON>\" and concatenate them.", "answer": "etea"}, {"question": "Take the last letters of each words in \"Sunny Trisha Paul Guy\" and concatenate them.", "answer": "yaly"}, {"question": "Take the last letters of each words in \"Martha Elijah Dominic <PERSON>\" and concatenate them.", "answer": "ahca"}, {"question": "Take the last letters of each words in \"Theo <PERSON>\" and concatenate them.", "answer": "oaaa"}, {"question": "Take the last letters of each words in \"Cristal R<PERSON><PERSON>\" and concatenate them.", "answer": "lahy"}, {"question": "Take the last letters of each words in \"Geo<PERSON>ny <PERSON>\" and concatenate them.", "answer": "ymnr"}, {"question": "Take the last letters of each words in \"Buddy Violet Johana Tina\" and concatenate them.", "answer": "ytaa"}, {"question": "Take the last letters of each words in \"<PERSON> Kelley Josue Veronica\" and concatenate them.", "answer": "hyea"}, {"question": "Take the last letters of each words in \"Cassie Clifton <PERSON>\" and concatenate them.", "answer": "enko"}, {"question": "Take the last letters of each words in \"Pauline Kerry Jeanne<PERSON> Hope\" and concatenate them.", "answer": "eyee"}, {"question": "Take the last letters of each words in \"Brooklyn Dawn Tay Gene\" and concatenate them.", "answer": "nnye"}, {"question": "Take the last letters of each words in \"Chloe <PERSON>co <PERSON>\" and concatenate them.", "answer": "eoee"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON><PERSON>\" and concatenate them.", "answer": "eonr"}, {"question": "Take the last letters of each words in \"<PERSON>te Stacey <PERSON>\" and concatenate them.", "answer": "ey<PERSON>"}, {"question": "Take the last letters of each words in \"Jazmine Carmen Kitty Nina\" and concatenate them.", "answer": "enya"}, {"question": "Take the last letters of each words in \"Donovan Lonnie <PERSON>\" and concatenate them.", "answer": "<PERSON><PERSON>"}, {"question": "Take the last letters of each words in \"Nidia Rachelle Lauren Shelia\" and concatenate them.", "answer": "aena"}, {"question": "Take the last letters of each words in \"<PERSON>\" and concatenate them.", "answer": "aaae"}, {"question": "Take the last letters of each words in \"Chandler Martina <PERSON> Gregg\" and concatenate them.", "answer": "raog"}, {"question": "Take the last letters of each words in \"<PERSON>\" and concatenate them.", "answer": "seyy"}, {"question": "Take the last letters of each words in \"Albert Felicia <PERSON>\" and concatenate them.", "answer": "taoy"}, {"question": "Take the last letters of each words in \"Noelia Cassidy Ashok Francisco\" and concatenate them.", "answer": "ayko"}, {"question": "Take the last letters of each words in \"Charmaine Vic Homero Jeanine\" and concatenate them.", "answer": "ecoe"}, {"question": "Take the last letters of each words in \"Glen Ariana Reggie Polo\" and concatenate them.", "answer": "naeo"}, {"question": "Take the last letters of each words in \"<PERSON><PERSON><PERSON>\" and concatenate them.", "answer": "reaa"}, {"question": "Take the last letters of each words in \"Marco Antonio <PERSON>\" and concatenate them.", "answer": "oedl"}, {"question": "Take the last letters of each words in \"<PERSON>\" and concatenate them.", "answer": "mell"}, {"question": "Take the last letters of each words in \"Hailey A<PERSON>\" and concatenate them.", "answer": "yiea"}, {"question": "Take the last letters of each words in \"Jason Betty Elisa Jay\" and concatenate them.", "answer": "nyay"}, {"question": "Take the last letters of each words in \"Oscar George <PERSON>\" and concatenate them.", "answer": "reye"}, {"question": "Take the last letters of each words in \"Andre Oralia Carrie Bruno\" and concatenate them.", "answer": "eaeo"}, {"question": "Take the last letters of each words in \"Leigh Mindy Rocky Lex\" and concatenate them.", "answer": "hyyx"}, {"question": "Take the last letters of each words in \"Jody Juan <PERSON>\" and concatenate them.", "answer": "ynhe"}, {"question": "Take the last letters of each words in \"Liliana Quincy Bart <PERSON>\" and concatenate them.", "answer": "ayta"}, {"question": "Take the last letters of each words in \"Mya Fernando Bubba Tommy\" and concatenate them.", "answer": "aoay"}, {"question": "Take the last letters of each words in \"Emanuel Cheyenne <PERSON>\" and concatenate them.", "answer": "leze"}, {"question": "Take the last letters of each words in \"Len Marquis Kylie Sandra\" and concatenate them.", "answer": "nsea"}, {"question": "Take the last letters of each words in \"<PERSON>\" and concatenate them.", "answer": "hrds"}, {"question": "Take the last letters of each words in \"Marlen Sonja <PERSON>\" and concatenate them.", "answer": "naae"}]}