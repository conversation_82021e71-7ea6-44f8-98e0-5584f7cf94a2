[{"ID": "chal-1", "Body": "Each pack of dvds costs 76 dollars. If there is a discount of 25 dollars on each pack", "Question": "How much do you have to pay to buy each pack?", "Equation": "( 76.0 - 25.0 )", "Answer": 51.0, "Type": "Subtraction"}, {"ID": "chal-2", "Body": "<PERSON> had $ 3 left with him after he bought a candy bar. If he had $ 4 at the start", "Question": "How much did the candy bar cost?", "Equation": "( 4.0 - 3.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-3", "Body": "<PERSON><PERSON> had 26 salty cookies and 17 sweet cookies. He ate 14 sweet cookies and 9 salty cookies.", "Question": "How many salty cookies did <PERSON><PERSON> have left?", "Equation": "( 26.0 - 9.0 )", "Answer": 17.0, "Type": "Subtraction"}, {"ID": "chal-4", "Body": "43 children were riding on the bus. At the bus stop some children got off the bus. Then there were 21 children left on the bus.", "Question": "How many children got off the bus at the bus stop?", "Equation": "( 43.0 - 21.0 )", "Answer": 22.0, "Type": "Subtraction"}, {"ID": "chal-5", "Body": "28 children were riding on the bus. At the bus stop 82 children got on the bus while some got off the bus. Then there were 30 children altogether on the bus.", "Question": "How many more children got on the bus than those that got off?", "Equation": "( 30.0 - 28.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-6", "Body": "There were 3 dollars in <PERSON>'s wallet. She collected 49 more dollars from an atm. After she visited a supermarket there were 49 dollars left.", "Question": "How much more money did she collect at the atm than she spent at the supermarket?", "Equation": "( 49.0 - 3.0 )", "Answer": 46.0, "Type": "Subtraction"}, {"ID": "chal-7", "Body": "<PERSON> had some action figures on a shelf in his room. Later he added 7 more action figures to the shelf. If there are a total of 10 action figures on his shelf now", "Question": "How many action figures did he have initially on the shelf?", "Equation": "( 10.0 - 7.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-8", "Body": "<PERSON><PERSON> had 41 cookies. He gave 9 cookies to his friend and ate 18 cookies.", "Question": "How many more cookies did he eat than those he gave to his friend?", "Equation": "( 18.0 - 9.0 )", "Answer": 9.0, "Type": "Subtraction"}, {"ID": "chal-9", "Body": "<PERSON> is baking a cake. The recipe calls for 3 cups of sugar 10 cups of flour and 15 cups of salt. She already put in 6 cups of flour.", "Question": "How many more cups of flour does she need to add?", "Equation": "( 10.0 - 6.0 )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-10", "Body": "A waiter had some customers. After 9 customers left he still had 12 customers.", "Question": "How many customers did he have at the start?", "Equation": "( 9.0 + 12.0 )", "Answer": 21.0, "Type": "Addition"}, {"ID": "chal-11", "Body": "3 birds were sitting on the fence. 6 more storks and 2 more birds came to join them.", "Question": "How many more storks than birds are sitting on the fence?", "Equation": "( 6.0 - ( 3.0 + 2.0 ) )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-12", "Body": "They decided to hold the party in their backyard. If they have 11 sets of tables and each set has 13 chairs", "Question": "How many chairs do they have in the backyard?", "Equation": "( 11.0 * 13.0 )", "Answer": 143.0, "Type": "Multiplication"}, {"ID": "chal-13", "Body": "In a school there are 458 more girls than boys. If there are 692 girls", "Question": "How many pupils are there in that school?", "Equation": "( ( 692.0 + 692.0 ) - 458.0 )", "Answer": 926.0, "Type": "Subtraction"}, {"ID": "chal-14", "Body": "After resting they decided to go for a swim. The depth of the water is 15 times <PERSON>'s height. <PERSON> is 4 feet taller than <PERSON>. If <PERSON> stands at 13 feet", "Question": "How deep was the water?", "Equation": "( ( 4.0 + 13.0 ) * 15.0 )", "Answer": 255.0, "Type": "Multiplication"}, {"ID": "chal-15", "Body": "6 birds were sitting on the fence. 4 more birds and 8 more storks came to join them.", "Question": "How many birds are sitting on the fence?", "Equation": "( 6.0 + 4.0 )", "Answer": 10.0, "Type": "Addition"}, {"ID": "chal-16", "Body": "<PERSON> and his dad went strawberry picking. Together they collected strawberries that weighed 36 pounds. On the way back <PERSON> ' dad lost 8 pounds of strawberries. <PERSON>'s strawberries now weighed 12 pounds.", "Question": "How much did his dad's strawberries weigh now?", "Equation": "( ( 36.0 - 12.0 ) - 8.0 )", "Answer": 16.0, "Type": "Subtraction"}, {"ID": "chal-17", "Body": "In a school there are 697 girls and the rest are boys. If there are 228 more girls than boys", "Question": "How many boys are there in that school?", "Equation": "( 697.0 - 228.0 )", "Answer": 469.0, "Type": "Subtraction"}, {"ID": "chal-18", "Body": "In a school there are 732 girls and 761 boys. 682 more girls and 8 more boys joined the school.", "Question": "How many girls are there in the school now?", "Equation": "( 732.0 + 682.0 )", "Answer": 1414.0, "Type": "Addition"}, {"ID": "chal-19", "Body": "<PERSON> collects bottle caps and wrappers. He found 22 bottle caps and 30 wrappers at the park. Now he has 17 bottle caps and 57 wrappers in his collection.", "Question": "How many wrappers did danny have at first?", "Equation": "( 57.0 - 30.0 )", "Answer": 27.0, "Type": "Subtraction"}, {"ID": "chal-20", "Body": "<PERSON> got a box of 457 erasers and 617 crayons for his birthday. At the end of the school year he only had 523 crayons left while not having lost a single eraser.", "Question": "How many more crayons than erasers did he have left?", "Equation": "( 523.0 - 457.0 )", "Answer": 66.0, "Type": "Subtraction"}, {"ID": "chal-21", "Body": "<PERSON> made 121 cakes. He sold 105 of them and bought 170 new cakes.", "Question": "How many cakes does baker still have?", "Equation": "( ( 121.0 - 105.0 ) + 170.0 )", "Answer": 186.0, "Type": "Addition"}, {"ID": "chal-22", "Body": "An industrial machine worked for 5 minutes. It can make 4 shirts a minute.", "Question": "How many shirts did machine make?", "Equation": "( 4.0 * 5.0 )", "Answer": 20.0, "Type": "Multiplication"}, {"ID": "chal-23", "Body": "The bananas in <PERSON>'s collection are organized into groups of size 18. If there are a total of 180 bananas in <PERSON>'s banana collection", "Question": "How many groups are there?", "Equation": "( 180.0 / 18.0 )", "Answer": 10.0, "Type": "Common-Division"}, {"ID": "chal-24", "Body": "There were some birds sitting on the fence. 4 more birds came to join them. If there are a total of 5 birds on the fence now", "Question": "How many birds had been sitting on the fence at the start?", "Equation": "( 5.0 - 4.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-25", "Body": "<PERSON> had 3 action figures on a shelf in his room. Later he added 4 more action figures to the shelf and removed some of the old ones. If there are 6 action figures on his shelf now", "Question": "How many action figures did he remove from the shelf?", "Equation": "( ( 3.0 + 4.0 ) - 6.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-26", "Body": "<PERSON> collects bottle caps and wrappers. He found 65 wrappers and 5 bottle caps at the park. Now he has 31 bottle caps and 9 wrappers in his collection.", "Question": "How many more wrappers than bottle caps did danny find at the park?", "Equation": "( 65.0 - 5.0 )", "Answer": 60.0, "Type": "Subtraction"}, {"ID": "chal-27", "Body": "Because of the decision <PERSON> asked the students to suggest specific types of food from a total of 66 choices. If 450 students suggested adding mashed potatoes 38 suggested adding bacon to the menu and the rest did not participate", "Question": "How many students participated in the suggestion of new food items?", "Equation": "( 450.0 + 38.0 )", "Answer": 488.0, "Type": "Addition"}, {"ID": "chal-28", "Body": "<PERSON> gave equal numbers of crackers and cakes to his 28 friends. If he had 13 crackers and 15 cakes initially", "Question": "How many crackers and cakes did each person eat?", "Equation": "( ( 13.0 + 15.0 ) / 28.0 )", "Answer": 1.0, "Type": "Common-Division"}, {"ID": "chal-29", "Body": "A grocery store had 49 bottles of regular soda, 40 bottles of diet soda and 6 bottles of lite soda.", "Question": "How many bottles of regular soda and diet soda did they have altogether?", "Equation": "( 40.0 + 49.0 )", "Answer": 89.0, "Type": "Addition"}, {"ID": "chal-30", "Body": "<PERSON> has $ 3. He bought 2 candy bar for $ 4, each one costing the same amount of money.", "Question": "How much did each candy bar cost?", "Equation": "( 4.0 / 2.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-31", "Body": "<PERSON> was collecting cans for recycling. On monday she had 7 bags of cans. The next day she found 12 more bags worth of cans.", "Question": "How many more bags did she find on the next day than she had on monday?", "Equation": "( 12.0 - 7.0 )", "Answer": 5.0, "Type": "Subtraction"}, {"ID": "chal-32", "Body": "There were 15 roses and 62 orchids in the vase. <PERSON> cut some more roses and orchids from her flower garden. There are now 17 roses and 96 orchids in the vase.", "Question": "How many roses did she cut?", "Equation": "( 17.0 - 15.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-33", "Body": "There are some baskets of peaches. Each basket has 4 red peaches and 3 green peaches. If there are a total of 7 peaches in all baskets", "Question": "How many baskets of peaches are there?", "Equation": "( 7.0 / ( 4.0 + 3.0 ) )", "Answer": 1.0, "Type": "Common-Division"}, {"ID": "chal-34", "Body": "There were 78 dollars in <PERSON>'s wallet. She spent 15 dollars at a supermarket.", "Question": "How much money does she have left?", "Equation": "( 78.0 - 15.0 )", "Answer": 63.0, "Type": "Subtraction"}, {"ID": "chal-35", "Body": "<PERSON> received 6 emails in the morning, 3 emails in the afternoon and some more in the evening. If he received a total of 10 emails in the day", "Question": "How many emails did <PERSON> receive in the afternoon?", "Equation": "( 10.0 - ( 6.0 + 3.0 ) )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-36", "Body": "<PERSON> has $ 4. He bought a candy bar for $ 8. Then his friend have him $ 5", "Question": "How much money is left?", "Equation": "( ( 4.0 - 8.0 ) + 5.0 )", "Answer": 1.0, "Type": "Addition"}, {"ID": "chal-37", "Body": "After eating a hearty meal they went to see the Buckingham palace. There, <PERSON> learned that 703 visitors came to the Buckingham palace on the previous day. If there were 246 visitors on that day", "Question": "How many visited the Buckingham palace within 25 days?", "Equation": "( 246.0 + 703.0 )", "Answer": 949.0, "Type": "Addition"}, {"ID": "chal-38", "Body": "For <PERSON>'s birthday she received 5 dollars from her dad. Her mom gave her 7 more dollars. If she spent 4 dollars.", "Question": "How much more money did she receive from her mom than she did from her dad?", "Equation": "( 7.0 - 5.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-39", "Body": "There are 142 bananas and 356 oranges in <PERSON>'s collection. If the bananas are organized into 47 groups and oranges are organized into 178 groups", "Question": "How big is each group of oranges?", "Equation": "( 356.0 / 178.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-40", "Body": "There are 8 different books and 5 different movies in the ' crazy silly school ' series. If you read 19 of the movies and watched 16 of the books", "Question": "How many more movies than books have you read?", "Equation": "( 19.0 - 16.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-41", "Body": "<PERSON> is baking a cake. The recipe calls for 12 cups of flour and 5 cups of sugar. She already put in some cups of flour. If she still needs 2 more cups of flour", "Question": "How many cups of flour did she put in?", "Equation": "( 12.0 - 2.0 )", "Answer": 10.0, "Type": "Subtraction"}, {"ID": "chal-42", "Body": "<PERSON> collects bottle caps and wrappers. He found 46 wrappers and 50 bottle caps at the park. Now he has 21 bottle caps and 52 wrappers in his collection.", "Question": "How many more bottle caps than wrappers did danny find at the park?", "Equation": "( 50.0 - 46.0 )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-43", "Body": "<PERSON> played 177 rounds of a trivia game. If he gained 46 points in each round", "Question": "How many points did he score in the game?", "Equation": "( 177.0 * 46.0 )", "Answer": 8142.0, "Type": "Multiplication"}, {"ID": "chal-44", "Body": "3 birds and 4 storks were sitting on the fence. 2 more birds came to join them.", "Question": "How many more birds than storks are sitting on the fence?", "Equation": "( ( 3.0 + 2.0 ) - 4.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-45", "Body": "The grasshopper, the frog and the mouse had a jumping contest. The grasshopper jumped 9 inches. The mouse jumped 3 inches lesser than the frog who jumped 33 inches farther than the grasshopper.", "Question": "How far did the mouse jump?", "Equation": "( ( 9.0 + 33.0 ) - 3.0 )", "Answer": 39.0, "Type": "Subtraction"}, {"ID": "chal-46", "Body": "<PERSON> did 35 push-ups and 3 crunches in gym class today. <PERSON> did 58 more push-ups but 87 less crunches than zach<PERSON>.", "Question": "How many more push-ups than crunches did <PERSON> do?", "Equation": "( 35.0 - 3.0 )", "Answer": 32.0, "Type": "Subtraction"}, {"ID": "chal-47", "Body": "<PERSON> received 4 emails in the morning, 5 emails in the afternoon and 8 emails in the evening.", "Question": "How many emails did <PERSON> receive in the afternoon and evening?", "Equation": "( 5.0 + 8.0 )", "Answer": 13.0, "Type": "Addition"}, {"ID": "chal-48", "Body": "<PERSON> had 7 action figures on a shelf in his room. Later he added some more action figures to the shelf and removed 10 of the old ones. If there are 8 action figures on his shelf now", "Question": "How many action figures did he add to the shelf?", "Equation": "( ( 8.0 + 10.0 ) - 7.0 )", "Answer": 11.0, "Type": "Subtraction"}, {"ID": "chal-49", "Body": "<PERSON> played tag with 12 kids on monday. She played tag with 14 kids on tuesday. She spent a total of 34 hours to play tag on both days.", "Question": "How many kids did she play with altogether?", "Equation": "( 12.0 + 14.0 )", "Answer": 26.0, "Type": "Addition"}, {"ID": "chal-50", "Body": "<PERSON> collects cards. She had 309 baseball cards and 356 Ace cards. She gave some of her cards to <PERSON> and now has 415 baseball cards and Ace cards left.", "Question": "How many more baseball cards than Ace cards does <PERSON> have?", "Equation": "( 415.0 - 149.0 )", "Answer": 266.0, "Type": "Subtraction"}, {"ID": "chal-51", "Body": "At the zoo, a cage had 93 snakes and 97 alligators. If 40 alligators were hiding", "Question": "How many alligators were not hiding?", "Equation": "( 97.0 - 40.0 )", "Answer": 57.0, "Type": "Subtraction"}, {"ID": "chal-52", "Body": "You have read 8 books from the ' crazy silly school ' series. If there are a total of 14 different books in the ' crazy silly school ' series", "Question": "How many more do you still have to read?", "Equation": "( 14.0 - 8.0 )", "Answer": 6.0, "Type": "Subtraction"}, {"ID": "chal-53", "Body": "<PERSON> is making bead necklaces for her 43 friends. She had 20 beads and she was able to make 5 necklaces.", "Question": "How many beads did each necklace need?", "Equation": "( 20.0 / 5.0 )", "Answer": 4.0, "Type": "Common-Division"}, {"ID": "chal-54", "Body": "<PERSON> put 11 pieces of candy in each bag. If he had 22 pieces of candy", "Question": "How many bags would he have?", "Equation": "( 22.0 / 11.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-55", "Body": "A waiter had 12 customers. After some left he still had 14 customers. Then he got 10 new customers", "Question": "How many customers does he have now?", "Equation": "( 14.0 + 10.0 )", "Answer": 24.0, "Type": "Addition"}, {"ID": "chal-56", "Body": "A farmer had 90 tomatoes in his garden. If he picked 154 of them yesterday and 50 today.", "Question": "How many tomatoes did he pick in all?", "Equation": "( 154.0 + 50.0 )", "Answer": 204.0, "Type": "Addition"}, {"ID": "chal-57", "Body": "5 red peaches, 14 yellow peaches and 6 green peaches are in the basket.", "Question": "How many green and yellow peaches are in the basket?", "Equation": "( 6.0 + 14.0 )", "Answer": 20.0, "Type": "Addition"}, {"ID": "chal-58", "Body": "Winter is almost here and most animals are migrating to warmer countries. There were 89 bird families living near the mountain. If 60 bird families flew away for winter", "Question": "How many more bird families flew away for the winter than those that stayed behind?", "Equation": "( 60.0 - ( 89.0 - 60.0 ) )", "Answer": 31.0, "Type": "Subtraction"}, {"ID": "chal-59", "Body": "There were 9 roses and 6 orchids in the vase. <PERSON> cut some more roses and orchids from her flower garden. There are now 13 orchids and 3 roses in the vase.", "Question": "How many more orchids than roses are there in the vase now?", "Equation": "( 13.0 - 3.0 )", "Answer": 10.0, "Type": "Subtraction"}, {"ID": "chal-60", "Body": "After eating a hearty meal they went to see the Buckingham palace. There were 39 paintings in the Buckingham palace. There, <PERSON> learned that 661 visitors came to the Buckingham palace that day. If there were 600 visitors the previous day", "Question": "How many more visitors visited the Buckingham palace on that day than on the previous day?", "Equation": "( 661.0 - 600.0 )", "Answer": 61.0, "Type": "Subtraction"}, {"ID": "chal-61", "Body": "A waiter had 12 customers. While 15 customers left he got 14 new customers.", "Question": "How many customers does he still have?", "Equation": "( ( 12.0 - 15.0 ) + 14.0 )", "Answer": 11.0, "Type": "Addition"}, {"ID": "chal-62", "Body": "<PERSON> is making bead necklaces for her 72 friends. She has 6 beads and each necklace takes 3 beads.", "Question": "How many necklaces can <PERSON> make?", "Equation": "( 6.0 / 3.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-63", "Body": "He also had 26 aquariums for saltwater animals. The aquarium had 52 saltwater animals in total and every aquarium had the same number of animals.", "Question": "How many saltwater animals does each aquarium have?", "Equation": "( 52.0 / 26.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-64", "Body": "<PERSON> is baking a cake. The recipe calls for 11 cups of flour and 7 cups of sugar. She already put in some cups of flour. If she still needs 2 more cups of flour than sugar", "Question": "How many cups of flour did she put in?", "Equation": "( ( 11.0 - 7.0 ) - 2.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-65", "Body": "<PERSON> made 149 cakes and 91 pastries. If he sold 10 cakes and 90 pastries", "Question": "How many cakes would baker still have?", "Equation": "( 149.0 - 10.0 )", "Answer": 139.0, "Type": "Subtraction"}, {"ID": "chal-66", "Body": "<PERSON> had 24 files and 13 apps on his phone. After deleting some apps and files he had 17 apps and 21 files left.", "Question": "How many files did he delete?", "Equation": "( 24.0 - 21.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-67", "Body": "An industrial machine made 9 shirts yesterday and 8 shirts today. It can make 2 shirts a minute.", "Question": "How many minutes did the machine work today?", "Equation": "( 8.0 / 2.0 )", "Answer": 4.0, "Type": "Common-Division"}, {"ID": "chal-68", "Body": "<PERSON> wants to split a collection of eggs into 3 groups. <PERSON> has 4 marbles and 15 eggs.", "Question": "How many eggs will each group have?", "Equation": "( 15.0 / 3.0 )", "Answer": 5.0, "Type": "Common-Division"}, {"ID": "chal-69", "Body": "<PERSON> had some crackers. If <PERSON> gave 2 crackers to each of his 11 friends", "Question": "How many crackers did <PERSON> have?", "Equation": "( 11.0 * 2.0 )", "Answer": 22.0, "Type": "Multiplication"}, {"ID": "chal-70", "Body": "<PERSON> spent 78 dollars at a supermarket. If she has 33 dollars left with her", "Question": "How much money did she have initially?", "Equation": "( 78.0 + 33.0 )", "Answer": 111.0, "Type": "Addition"}, {"ID": "chal-71", "Body": "If <PERSON> earns $ 1357 every week during the 223 weeks of a harvest season.", "Question": "How much money will he earn if he works for 73 harvest seasons?", "Equation": "( ( 1357.0 * 223.0 ) * 73.0 )", "Answer": 22090603.0, "Type": "Multiplication"}, {"ID": "chal-72", "Body": "Because of the decision Sofia asked 310 students to suggest specific types of food. 185 students suggested adding mashed potatoes while others suggested adding bacon to the menu.", "Question": "How many students suggested bacon?", "Equation": "( 310.0 - 185.0 )", "Answer": 125.0, "Type": "Subtraction"}, {"ID": "chal-73", "Body": "<PERSON> received 3 emails and 64 letters in the morning. He then received 5 emails and 54 letters in the afternoon.", "Question": "How many emails did jack receive in the day?", "Equation": "( 3.0 + 5.0 )", "Answer": 8.0, "Type": "Addition"}, {"ID": "chal-74", "Body": "<PERSON><PERSON> had 40 cookies. He ate 2 of them. Then he bought 37 more cookies", "Question": "How many cookies did <PERSON><PERSON> have left?", "Equation": "( ( 40.0 - 2.0 ) + 37.0 )", "Answer": 75.0, "Type": "Addition"}, {"ID": "chal-75", "Body": "Because of the decision <PERSON> asked the students to suggest specific types of food. If 218 students suggested adding mashed potatoes as well as bacon while 351 suggested adding only bacon to the menu", "Question": "How many students suggested adding bacon?", "Equation": "( 218.0 + 351.0 )", "Answer": 569.0, "Type": "Addition"}, {"ID": "chal-76", "Body": "If each bag has 41 cookies and you had 53 bags of cookies", "Question": "How many cookies would you have?", "Equation": "( 53.0 * 41.0 )", "Answer": 2173.0, "Type": "Multiplication"}, {"ID": "chal-77", "Body": "<PERSON> had to complete 2 pages of reading homework and 4 pages of math homework.", "Question": "How many more pages of math homework than reading homework did she have?", "Equation": "( 4.0 - 2.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-78", "Body": "<PERSON> brought 5 balloons and <PERSON> brought 4 balloons to the park. <PERSON> then bought 3 more balloons at the park.", "Question": "How many balloons did <PERSON> bring to the park?", "Equation": "( 5.0 + 3.0 )", "Answer": 8.0, "Type": "Addition"}, {"ID": "chal-79", "Body": "<PERSON> played tag with 8 kids on monday, 11 kids on tuesday and 9 kids on wednesday.", "Question": "How many more kids did she play with on tuesday than on wednesday?", "Equation": "( 11.0 - 9.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-80", "Body": "The Razorback shop makes $ 5 dollars off each jersey and $ 215 off each t-shirt. During the Arkansas and Texas tech game they sold 20 t-shirts and 64 jerseys.", "Question": "How much money did they make from selling the t-shirts?", "Equation": "( 215.0 * 20.0 )", "Answer": 4300.0, "Type": "Multiplication"}, {"ID": "chal-81", "Body": "36 children were riding on the bus. At the bus stop 68 children got off the bus while some more got on the bus. Then there were 12 children altogether on the bus.", "Question": "How many more children got off the bus than those that got on?", "Equation": "( 36.0 - 12.0 )", "Answer": 24.0, "Type": "Subtraction"}, {"ID": "chal-82", "Body": "<PERSON>'s mother made 14 cookies for 2 guests. If each of them had the same number of cookies", "Question": "How many did each of them have?", "Equation": "( 14.0 / 2.0 )", "Answer": 7.0, "Type": "Common-Division"}, {"ID": "chal-83", "Body": "<PERSON> was collecting cans for recycling. On monday she had 4 bags of cans. The next day she found some more bags worth of cans. If she had a total of 6 bags altogether", "Question": "How many bags did she find on the next day?", "Equation": "( 6.0 - 4.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-84", "Body": "In a school there are 632 girls and 410 boys. 465 more girls joined the school.", "Question": "How many more girls than boys does the school have?", "Equation": "( ( 632.0 + 465.0 ) - 410.0 )", "Answer": 687.0, "Type": "Subtraction"}, {"ID": "chal-85", "Body": "<PERSON> is making bead necklaces for her friends where each necklace takes 5 beads. She made 4 necklaces.", "Question": "How many beads did <PERSON> have?", "Equation": "( 4.0 * 5.0 )", "Answer": 20.0, "Type": "Multiplication"}, {"ID": "chal-86", "Body": "<PERSON> has 95 blocks. He uses 20 blocks to build a house and 50 blocks to build a tower.", "Question": "How many more blocks did he use to build the tower than he did to build the house?", "Equation": "( 50.0 - 20.0 )", "Answer": 30.0, "Type": "Subtraction"}, {"ID": "chal-87", "Body": "2 birds and 6 storks were sitting on the fence. 3 more birds came to join them.", "Question": "How many more storks than birds are sitting on the fence?", "Equation": "( 6.0 - ( 2.0 + 3.0 ) )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-88", "Body": "The Razorback t-shirt shop made $ 51 dollars from selling 3 t-shirt during the Arkansas and Texas tech game they.", "Question": "What is the cost of each t-shirt?", "Equation": "( 51.0 / 3.0 )", "Answer": 17.0, "Type": "Common-Division"}, {"ID": "chal-89", "Body": "<PERSON><PERSON> had 12 cookies. He ate 16 cookies and gave 21 of them to his friend.", "Question": "How many more cookies did he give to his friend than those he ate?", "Equation": "( 21.0 - 16.0 )", "Answer": 5.0, "Type": "Subtraction"}, {"ID": "chal-90", "Body": "After eating a hearty meal they went to see the Buckingham palace. There, <PERSON> learned that 317 visitors came to the Buckingham palace that day. If there were 295 visitors the previous day", "Question": "How many more visitors visited the Buckingham palace on that day than on the previous day?", "Equation": "( 317.0 - 295.0 )", "Answer": 22.0, "Type": "Subtraction"}, {"ID": "chal-91", "Body": "<PERSON> had 3 marbles in his collection. He found 6 marbles while he lost 5 marbles.", "Question": "How many more marbles did he find than those he lost?", "Equation": "( 6.0 - 5.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-92", "Body": "Because of the decision <PERSON> asked the students to suggest specific types of food. If 408 students suggested adding mashed potatoes while 42 suggested adding bacon to the menu", "Question": "How many more students suggested mashed potatoes than those that suggested bacon?", "Equation": "( 408.0 - 42.0 )", "Answer": 366.0, "Type": "Subtraction"}, {"ID": "chal-93", "Body": "<PERSON><PERSON> had 40 cookies. He ate 5 cookies and gave 13 of them to his friend.", "Question": "How many cookies did paco have left?", "Equation": "( ( 40.0 - 5.0 ) - 13.0 )", "Answer": 22.0, "Type": "Subtraction"}, {"ID": "chal-94", "Body": "<PERSON><PERSON> had 17 cookies. He ate 14 cookies and gave 13 of them to his friend.", "Question": "How many more cookies did he eat than those he gave to his friend?", "Equation": "( 14.0 - 13.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-95", "Body": "<PERSON> played tag with 19 kids on monday. She played tag with 18 kids on tuesday. She spent a total of 38 hours to play tag on both days.", "Question": "How many more kids did she play with on monday than on tuesday?", "Equation": "( 19.0 - 18.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-96", "Body": "<PERSON> has $ 5. He bought a candy bar for $ 2 and a chocolate for $ 3.", "Question": "How much money did he spend to buy chocolate than he did to buy candy bar?", "Equation": "( 3.0 - 2.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-97", "Body": "<PERSON> had 29 more marbles than <PERSON>. <PERSON> lost 17 of his marbles at the playground.", "Question": "How many more marbles did <PERSON> have than <PERSON> then?", "Equation": "( 29.0 - 17.0 )", "Answer": 12.0, "Type": "Subtraction"}, {"ID": "chal-98", "Body": "The grasshopper, the frog and the mouse had a jumping contest. The grasshopper jumped 19 inches. The frog jumped 10 inches farther than the grasshopper and the mouse jumped 20 inches farther than the frog.", "Question": "How much farther did the mouse jump than the grasshopper?", "Equation": "( 10.0 + 20.0 )", "Answer": 30.0, "Type": "Addition"}, {"ID": "chal-99", "Body": "<PERSON> was reading through his favorite book. It took him 16 days to finish the book. If the book had 112 pages", "Question": "How many pages did he read per day?", "Equation": "( 112.0 / 16.0 )", "Answer": 7.0, "Type": "Common-Division"}, {"ID": "chal-100", "Body": "There are 20 different books in the ' crazy silly school ' series. If you are yet to read 5 of the books", "Question": "How many books have you already read?", "Equation": "( 20.0 - 5.0 )", "Answer": 15.0, "Type": "Subtraction"}, {"ID": "chal-101", "Body": "<PERSON> brought 3 balloons and <PERSON> brought 5 balloons to the park. <PERSON> then bought 2 more balloons at the park.", "Question": "How many balloons did <PERSON> and <PERSON> have in the park?", "Equation": "( ( 3.0 + 5.0 ) + 2.0 )", "Answer": 10.0, "Type": "Addition"}, {"ID": "chal-102", "Body": "<PERSON> lost 11 marbles. If he had 19 marbles in his collection earlier", "Question": "How many marbles does he have now?", "Equation": "( 19.0 - 11.0 )", "Answer": 8.0, "Type": "Subtraction"}, {"ID": "chal-103", "Body": "<PERSON> brought 6 balloons and <PERSON> brought 3 balloons to the park. <PERSON> then bought 4 more balloons at the park.", "Question": "How many more balloons did <PERSON> have than <PERSON> in the park?", "Equation": "( ( 3.0 + 4.0 ) - 6.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-104", "Body": "<PERSON> was placing her pencils into 14 rows with 11 pencils in each row.", "Question": "How many pencils does she have?", "Equation": "( 14.0 * 11.0 )", "Answer": 154.0, "Type": "Multiplication"}, {"ID": "chal-105", "Body": "A grocery store had 22 bottles of regular soda and 61 bottles of diet soda.", "Question": "How many more bottles of diet soda than regular soda did they have?", "Equation": "( 61.0 - 22.0 )", "Answer": 39.0, "Type": "Subtraction"}, {"ID": "chal-106", "Body": "There are 6 baskets of peaches. Each basket has 16 red peaches and 18 green peaches.", "Question": "How many red peaches are in the baskets altogether?", "Equation": "( 16.0 * 6.0 )", "Answer": 96.0, "Type": "Multiplication"}, {"ID": "chal-107", "Body": "During summer break 800059 kids from Lawrence county go to camp and the rest stay home. Lawrence county has 828521 kids in all.", "Question": "About how many kids stayed home?", "Equation": "( 828521.0 - 800059.0 )", "Answer": 28462.0, "Type": "Subtraction"}, {"ID": "chal-108", "Body": "There are 141 pots. Each pot has 71 flowers and 91 sticks in it.", "Question": "How many flowers are there in all?", "Equation": "( 141.0 * 71.0 )", "Answer": 10011.0, "Type": "Multiplication"}, {"ID": "chal-109", "Body": "<PERSON> brought 2 balloons and <PERSON> brought 4 balloons to the park. <PERSON> then bought 3 more balloons at the park.", "Question": "How many more balloons did <PERSON> have than <PERSON> in the park?", "Equation": "( ( 2.0 + 3.0 ) - 4.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-110", "Body": "A chef had 58 apples. After making some pies, he had used 35.", "Question": "How many apples remained?", "Equation": "( 58.0 - 35.0 )", "Answer": 23.0, "Type": "Subtraction"}, {"ID": "chal-111", "Body": "You had 26 bags with equal number of cookies. If you had 15 candies and 52 cookies in total", "Question": "How many cookies does each bag have?", "Equation": "( 52.0 / 26.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-112", "Body": "He then went to see the oranges being harvested. He found out that they harvest 38 sacks per day and that each sack containes 42 oranges.", "Question": "How many sacks of oranges will they have after 49 days of harvest?", "Equation": "( 38.0 * 49.0 )", "Answer": 1862.0, "Type": "Multiplication"}, {"ID": "chal-113", "Body": "There were 16 roses and 3 orchids in the vase. <PERSON> cut some more roses and orchids from her flower garden. There are now 7 orchids and 13 roses in the vase.", "Question": "How many orchids did she cut?", "Equation": "( 7.0 - 3.0 )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-114", "Body": "<PERSON> received 10 emails in the morning, 5 emails in the afternoon and 4 emails in the evening.", "Question": "How many more emails did <PERSON> receive in the afternoon than in the evening?", "Equation": "( 5.0 - 4.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-115", "Body": "<PERSON> was placing her pencils into rows with 22 pencils in each row. She had 6 packs of pencils each one having 14 pencils.", "Question": "How many pencils does she have?", "Equation": "( 6.0 * 14.0 )", "Answer": 84.0, "Type": "Multiplication"}, {"ID": "chal-116", "Body": "A farmer had 160 tomatoes in his garden. If he picked 56 of them yesterday and 41 today.", "Question": "How many did he have left after yesterday's picking?", "Equation": "( 160.0 - 56.0 )", "Answer": 104.0, "Type": "Subtraction"}, {"ID": "chal-117", "Body": "Each pot has 40 flowers in it. There are 400 flowers in total.", "Question": "How many pots are there in all?", "Equation": "( 400.0 / 40.0 )", "Answer": 10.0, "Type": "Common-Division"}, {"ID": "chal-118", "Body": "15 campers went rowing in the morning. Some more campers went rowing in the afternoon. If a total of 32 campers went rowing that day", "Question": "How many campers went rowing in the afternoon?", "Equation": "( 32.0 - 15.0 )", "Answer": 17.0, "Type": "Subtraction"}, {"ID": "chal-119", "Body": "Because of the decision <PERSON> asked the students to suggest specific types of food. If 479 students suggested adding mashed potatoes while 489 suggested adding bacon to the menu", "Question": "How many more students suggested bacon than those that suggested mashed potatoes?", "Equation": "( 489.0 - 479.0 )", "Answer": 10.0, "Type": "Subtraction"}, {"ID": "chal-120", "Body": "<PERSON> had 5 more marbles than <PERSON>. <PERSON> lost 3 of his marbles at the playground. If <PERSON> had 27 marbles", "Question": "How many marbles did <PERSON> have initially?", "Equation": "( 27.0 - 5.0 )", "Answer": 22.0, "Type": "Subtraction"}, {"ID": "chal-121", "Body": "<PERSON> had to complete 7 pages of math homework. If she had to complete 4 more pages of math homework than reading homework", "Question": "How many pages of reading homework did she have to complete?", "Equation": "( 7.0 - 4.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-122", "Body": "<PERSON> the hippo and her friends are preparing for thanksgiving at <PERSON>'s house. <PERSON> baked 19 chocolate chip cookies yesterday and 231 raisin cookies and 237 chocolate chip cookies this morning.", "Question": "How many more chocolate chip cookies than raisin cakes did <PERSON> bake?", "Equation": "( ( 19.0 + 237.0 ) - 231.0 )", "Answer": 25.0, "Type": "Subtraction"}, {"ID": "chal-123", "Body": "<PERSON> was collecting cans for recycling. On monday she had some bags of cans. The next day she found 4 more bags worth of cans. If she had a total of 8 bags altogether", "Question": "How many bags did she have on monday?", "Equation": "( 8.0 - 4.0 )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-124", "Body": "<PERSON> collects bottle caps. He found 30 bottle caps at the park while he threw away 63 old ones. Now he has 42 bottle caps in his collection.", "Question": "How many more bottle caps did danny throw away than those he found at the park?", "Equation": "( 63.0 - 30.0 )", "Answer": 33.0, "Type": "Subtraction"}, {"ID": "chal-125", "Body": "<PERSON> is baking a cake. The recipe calls for 12 cups of sugar and 14 cups of flour. She already put in 10 cups of sugar.", "Question": "How many more cups of flour than cups of sugar does she need to add now?", "Equation": "( 14.0 - ( 12.0 - 10.0 ) )", "Answer": 12.0, "Type": "Subtraction"}, {"ID": "chal-126", "Body": "<PERSON> has 22 nintendo games.", "Question": "How many does she need to buy so that she will have 140 games left?", "Equation": "( 140.0 - 22.0 )", "Answer": 118.0, "Type": "Subtraction"}, {"ID": "chal-127", "Body": "Mom buys 70 white t - shirts in total. If white t - shirts can be purchased in packages and mom buys 14 packages", "Question": "How many white t - shirts does each package have?", "Equation": "( 70.0 / 14.0 )", "Answer": 5.0, "Type": "Common-Division"}, {"ID": "chal-128", "Body": "A farmer had 171 tomatoes in his garden. If he picked 134 of them yesterday and 30 today.", "Question": "How many will he have left after today?", "Equation": "( ( 171.0 - 134.0 ) - 30.0 )", "Answer": 7.0, "Type": "Subtraction"}, {"ID": "chal-129", "Body": "Last week <PERSON> had 47 dollars and <PERSON> had 99 dollars. Over the weekend <PERSON> delivered newspapers earning 111 dollars and washed cars earning 34 dollars.", "Question": "How much money does <PERSON> have now?", "Equation": "( ( 47.0 + 111.0 ) + 34.0 )", "Answer": 192.0, "Type": "Addition"}, {"ID": "chal-130", "Body": "<PERSON> is baking a cake. The recipe calls for 6 cups of sugar and 14 cups of flour. She already put in 7 cups of flour and 60 cups of sugar.", "Question": "How many more cups of flour does she need to add?", "Equation": "( 14.0 - 7.0 )", "Answer": 7.0, "Type": "Subtraction"}, {"ID": "chal-131", "Body": "There were 7 roses and 12 orchids in the vase. <PERSON> cut some more roses and orchids from her flower garden. There are now 11 roses and 20 orchids in the vase.", "Question": "How many more orchids than roses are there in the vase now?", "Equation": "( 20.0 - 11.0 )", "Answer": 9.0, "Type": "Subtraction"}, {"ID": "chal-132", "Body": "After <PERSON> started to go jogging everyday she lost 35 kilograms. She weighed 69 kilograms before beginning jogging.", "Question": "How much does she weigh now?", "Equation": "( 69.0 - 35.0 )", "Answer": 34.0, "Type": "Subtraction"}, {"ID": "chal-133", "Body": "22 children were riding on the bus. At the bus stop 40 children got on the bus while some got off the bus. Then there were 2 children altogether on the bus.", "Question": "How many children got off the bus at the bus stop?", "Equation": "( ( 22.0 + 40.0 ) - 2.0 )", "Answer": 60.0, "Type": "Subtraction"}, {"ID": "chal-134", "Body": "There are 10 different books and 11 different movies in the ' crazy silly school ' series. If you read 13 of the books and watched 12 of the movies", "Question": "How many more books than movies have you read?", "Equation": "( 13.0 - 12.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-135", "Body": "<PERSON> is making bead necklaces for her friends. She made 11 necklaces and each necklace takes 28 beads.", "Question": "How many beads did <PERSON> have?", "Equation": "( 11.0 * 28.0 )", "Answer": 308.0, "Type": "Multiplication"}, {"ID": "chal-136", "Body": "A farmer had 175 tomatoes and 77 potatoes in his garden. If he picked 172 potatoes", "Question": "How many tomatoes and potatoes does he have left?", "Equation": "( ( 175.0 + 77.0 ) - 172.0 )", "Answer": 80.0, "Type": "Subtraction"}, {"ID": "chal-137", "Body": "Last week <PERSON> had 111 dollars and <PERSON> had 40 dollars. <PERSON> washed cars over the weekend and now has 115 dollars. <PERSON> delivered newspapers and now has 44 dollars.", "Question": "How much money did they earn by washing cars?", "Equation": "( 115.0 - 111.0 )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-138", "Body": "A mailman has to give 9 pieces of junk mail to each house in each block. If there are 20 houses on a block", "Question": "How many pieces of junk mail should he give in each block?", "Equation": "( 9.0 * 20.0 )", "Answer": 180.0, "Type": "Multiplication"}, {"ID": "chal-139", "Body": "<PERSON> gave equal numbers of crackers and cakes to his 4 friends. If he had 10 crackers and 8 cakes initially", "Question": "How many cakes did each person eat?", "Equation": "( 8.0 / 4.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-140", "Body": "<PERSON> cut some roses from her flower garden to put in her vase. There are now 19 roses in the vase. If there were 3 roses in the vase initially", "Question": "How many roses did she cut?", "Equation": "( 19.0 - 3.0 )", "Answer": 16.0, "Type": "Subtraction"}, {"ID": "chal-141", "Body": "23 children were riding on the bus. At the bus stop 24 children got on the bus while some got off the bus. Then there were 8 children altogether on the bus.", "Question": "How many more children got off the bus than those that got on?", "Equation": "( 23.0 - 8.0 )", "Answer": 15.0, "Type": "Subtraction"}, {"ID": "chal-142", "Body": "A book has 3 chapters. The first chapter is 66 pages long the second chapter is 35 pages long and the third chapter is 24 pages long.", "Question": "How many pages does the book have altogether?", "Equation": "( ( 66.0 + 35.0 ) + 24.0 )", "Answer": 125.0, "Type": "Addition"}, {"ID": "chal-143", "Body": "7 red peaches, 15 yellow peaches and 8 green peaches are in the basket.", "Question": "How many peaches are in the basket?", "Equation": "( ( 7.0 + 15.0 ) + 8.0 )", "Answer": 30.0, "Type": "Addition"}, {"ID": "chal-144", "Body": "<PERSON> was helping her mom plant flowers and they put 10 seeds in each flower bed. If they planted 60 seeds altogther", "Question": "How many flower beds did they have?", "Equation": "( 60.0 / 10.0 )", "Answer": 6.0, "Type": "Common-Division"}, {"ID": "chal-145", "Body": "Because of the decision <PERSON> asked the students to suggest specific types of food. If 257 students suggested adding mashed potatoes 120 suggested adding bacon to the menu and 97 suggested adding tomatoes", "Question": "How many more students suggested mashed potatoes than those that suggested bacon?", "Equation": "( 257.0 - 120.0 )", "Answer": 137.0, "Type": "Subtraction"}, {"ID": "chal-146", "Body": "In a school there are 135 girls and 214 boys. 496 more girls and 910 more boys joined the school.", "Question": "How many boys are there in the school now?", "Equation": "( 214.0 + 910.0 )", "Answer": 1124.0, "Type": "Addition"}, {"ID": "chal-147", "Body": "After resting they decided to go for a swim. If the depth of the water is 10 times <PERSON>'s height and he stands at 9 feet", "Question": "How much deeper is the water than <PERSON>'s height?", "Equation": "( ( 10.0 * 9.0 ) - 9.0 )", "Answer": 81.0, "Type": "Subtraction"}, {"ID": "chal-148", "Body": "Next on his checklist is wax to stick the feathers together. He needs 159 g of wax more. If the feathers require a total of 628 g of wax", "Question": "How many grams of wax does he already have?", "Equation": "( 628.0 - 159.0 )", "Answer": 469.0, "Type": "Subtraction"}, {"ID": "chal-149", "Body": "<PERSON> was reading through his favorite book. He read 8 pages per day. If the book had 576 pages", "Question": "How many days did he take to finish the book?", "Equation": "( 576.0 / 8.0 )", "Answer": 72.0, "Type": "Common-Division"}, {"ID": "chal-150", "Body": "There are a total of 16 peaches in a basket. If there are 13 red peaches and some more green peaches in the basket.", "Question": "How many green peaches are in the basket?", "Equation": "( 16.0 - 13.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-151", "Body": "After resting they decided to go for a swim. The depth of the water is 2 times <PERSON>'s height. <PERSON> is 8 feet shorter than <PERSON>. If <PERSON> stands at 14 feet", "Question": "How deep was the water?", "Equation": "( ( 14.0 - 8.0 ) * 2.0 )", "Answer": 12.0, "Type": "Multiplication"}, {"ID": "chal-152", "Body": "There are 544 pots in each of the 10 gardens. Each pot has 32 flowers in it.", "Question": "How many flowers are there in all?", "Equation": "( ( 544.0 * 10 ) * 32.0 )", "Answer": 174080.0, "Type": "Multiplication"}, {"ID": "chal-153", "Body": "<PERSON> was helping her mom plant flowers and together they planted some seeds. They put 10 seeds in each flower bed. If there are 45 flowerbeds", "Question": "How many seeds did they plant?", "Equation": "( 10.0 * 45.0 )", "Answer": 450.0, "Type": "Multiplication"}, {"ID": "chal-154", "Body": "<PERSON> and his dad went strawberry picking. Together their strawberries weighed 4 pounds. <PERSON>'s strawberries weighed 19 pounds.", "Question": "How much more did his strawberries weigh than his dad's?", "Equation": "( 19.0 - ( 4.0 - 19.0 ) )", "Answer": 34.0, "Type": "Subtraction"}, {"ID": "chal-155", "Body": "<PERSON> played tag with 4 kids on wednesday. She had played with 6 kids on monday and 17 kids on tuesday.", "Question": "How many more kids did she play with on monday than on wednesday?", "Equation": "( 6.0 - 4.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-156", "Body": "<PERSON> was sending out birthday invitations to 10 friends. If each package of invitations she bought had 2 invitations in it", "Question": "How many packs does she need so that no friend gets left out?", "Equation": "( 10.0 / 2.0 )", "Answer": 5.0, "Type": "Common-Division"}, {"ID": "chal-157", "Body": "A grocery store had 36 apples, 80 bottles of regular soda and 54 bottles of diet soda.", "Question": "How many more bottles than apple did they have?", "Equation": "( ( 80.0 + 54.0 ) - 36.0 )", "Answer": 98.0, "Type": "Subtraction"}, {"ID": "chal-158", "Body": "The grasshopper, the frog and the mouse had a jumping contest. The grasshopper jumped 14 inches. The mouse jumped 16 inches lesser than the frog who jumped 37 inches farther than the grasshopper.", "Question": "How much farther did the mouse jump than the grasshopper?", "Equation": "( 37.0 - 16.0 )", "Answer": 21.0, "Type": "Subtraction"}, {"ID": "chal-159", "Body": "<PERSON> was placing her pencils and crayons into 11 rows with 31 pencils and 27 crayons in each row.", "Question": "How many pencils and crayons does she have altogether?", "Equation": "( ( 31.0 + 27.0 ) * 11.0 )", "Answer": 638.0, "Type": "Multiplication"}, {"ID": "chal-160", "Body": "The school is planning a field trip. The school has 87 classrooms. There are 58 students in the school with each classroom having the same number of students. If there are 2 seats on each school bus.", "Question": "How many buses are needed to take the trip?", "Equation": "( 58.0 / 2.0 )", "Answer": 29.0, "Type": "Common-Division"}, {"ID": "chal-161", "Body": "<PERSON> is making bead necklaces for her friends. She had 2 beads and she was able to make 32 necklaces.", "Question": "How many beads did each necklace need?", "Equation": "( 32.0 / 2.0 )", "Answer": 16.0, "Type": "Common-Division"}, {"ID": "chal-162", "Body": "<PERSON> raised 7 goldfish and 12 catfish in the pond but stray cats loved eating them. Now she has 15 left.", "Question": "How many fishes disappeared?", "Equation": "( ( 7.0 + 12.0 ) - 15.0 )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-163", "Body": "<PERSON><PERSON> had 40 sweet cookies and 25 salty cookies. He ate 28 salty cookies and 15 sweet cookies.", "Question": "How many more salty cookies than sweet cookies did he eat?", "Equation": "( 28.0 - 15.0 )", "Answer": 13.0, "Type": "Subtraction"}, {"ID": "chal-164", "Body": "<PERSON> has 14 peaches. <PERSON> has 6 fewer peaches than <PERSON> and 3 more peaches than <PERSON>.", "Question": "How many peaches does <PERSON> have?", "Equation": "( ( 14.0 - 6.0 ) - 3.0 )", "Answer": 5.0, "Type": "Subtraction"}, {"ID": "chal-165", "Body": "<PERSON> had 115 books. He sold 78 books in a garage sale.", "Question": "How many books does he have left with him?", "Equation": "( 115.0 - 78.0 )", "Answer": 37.0, "Type": "Subtraction"}, {"ID": "chal-166", "Body": "An industrial machine made 13 shirts yesterday and 3 shirts today. It can make 8 shirts a minute.", "Question": "How many minutes did the machine work in all?", "Equation": "( ( 13.0 + 3.0 ) / 8.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-167", "Body": "<PERSON> was sending out birthday invitations to her friends. Each package of invitations she bought had 14 invitations in it and she bought 6 packs. Then she bought 11 extra invitations.", "Question": "How many friends can she invite?", "Equation": "( ( 14.0 * 6.0 ) + 11.0 )", "Answer": 95.0, "Type": "Addition"}, {"ID": "chal-168", "Body": "An industrial machine can make 3 shirts per minute. It made 6 shirts in all.", "Question": "How many minutes was the machine working?", "Equation": "( 6.0 / 3.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-169", "Body": "<PERSON> collects bottle caps. He found 63 bottle caps at the park while he threw away 51 old ones. Now he has 33 bottle caps in his collection.", "Question": "How many bottle caps did danny have at first?", "Equation": "( ( 33.0 + 51.0 ) - 63.0 )", "Answer": 21.0, "Type": "Subtraction"}, {"ID": "chal-170", "Body": "A grocery store had 67 bottles of regular soda and 9 bottles of diet soda.", "Question": "How many more bottles of regular soda than diet soda did they have?", "Equation": "( 67.0 - 9.0 )", "Answer": 58.0, "Type": "Subtraction"}, {"ID": "chal-171", "Body": "During summer break 644997 kids from Lawrence county stayed home and the other 893835 kids went to camp. An additional 78 kids from outside the county attended the camp.", "Question": "How many kids are in Lawrence county?", "Equation": "( 893835.0 + 644997.0 )", "Answer": 1538832.0, "Type": "Addition"}, {"ID": "chal-172", "Body": "<PERSON> was placing 30 pencils into rows with 5 pencils in each row.", "Question": "How many rows could she make?", "Equation": "( 30.0 / 5.0 )", "Answer": 6.0, "Type": "Common-Division"}, {"ID": "chal-173", "Body": "<PERSON> had some pieces of candy. If he put them into 26 bags with 33 pieces in each bag", "Question": "How many pieces of candy did he have?", "Equation": "( 26.0 * 33.0 )", "Answer": 858.0, "Type": "Multiplication"}, {"ID": "chal-174", "Body": "A grocery store had a total of 17 bottles of which 9 were bottles of regular soda and the rest were bottles of diet soda. They also had 29 apple.", "Question": "How many bottles of diet soda did they have?", "Equation": "( 17.0 - 9.0 )", "Answer": 8.0, "Type": "Subtraction"}, {"ID": "chal-175", "Body": "<PERSON> has 3 fewer peaches than <PERSON> who has 8 more peaches than <PERSON>. <PERSON> has 5 peaches.", "Question": "How many more peaches does <PERSON> have than <PERSON>?", "Equation": "( 8.0 - 3.0 )", "Answer": 5.0, "Type": "Subtraction"}, {"ID": "chal-176", "Body": "<PERSON> had 20 marbles in his collection. He gave 2 marbles to <PERSON>.", "Question": "How many marbles does <PERSON> have now?", "Equation": "( 20.0 - 2.0 )", "Answer": 18.0, "Type": "Subtraction"}, {"ID": "chal-177", "Body": "<PERSON> has 12 peaches. <PERSON> has 4 fewer peaches than <PERSON> who has 67 more peaches than <PERSON>.", "Question": "How many peaches does <PERSON> have?", "Equation": "( 12.0 - 4.0 )", "Answer": 8.0, "Type": "Subtraction"}, {"ID": "chal-178", "Body": "The Razorback t-shirt shop makes $ 98 dollars off each t-shirt sold. During the Arkansas game and the Texas tech game they sold a total of 163 t-shirts. If they sold 89 t-shirts during the Arkansas game", "Question": "How much money did they make from selling the t-shirts during the arkansas game?", "Equation": "( 98.0 * 89.0 )", "Answer": 8722.0, "Type": "Multiplication"}, {"ID": "chal-179", "Body": "<PERSON> received 6 emails in the morning, 3 emails in the afternoon and 5 emails in the evening.", "Question": "How many emails did <PERSON> receive in the day?", "Equation": "( ( 6.0 + 3.0 ) + 5.0 )", "Answer": 14.0, "Type": "Addition"}, {"ID": "chal-180", "Body": "<PERSON> has 5 fewer peaches than <PERSON>. <PERSON> has 18 more peaches than <PERSON>. If <PERSON> has 87 peaches", "Question": "How many more peaches does <PERSON> have than <PERSON>?", "Equation": "( 18.0 - 5.0 )", "Answer": 13.0, "Type": "Subtraction"}, {"ID": "chal-181", "Body": "For the walls of the house he would use 13 large planks of wood. If each plank of wood needs 17 pieces of nails to be secured and in addition 8 nails are needed for some smaller planks.", "Question": "How many nails does <PERSON> need for the house wall?", "Equation": "( ( 13.0 * 17.0 ) + 8.0 )", "Answer": 229.0, "Type": "Addition"}, {"ID": "chal-182", "Body": "A grocery store had 54 bottles of diet soda, 83 bottles of regular soda and 12 apples.", "Question": "How many more bottles of regular soda than diet soda did they have?", "Equation": "( 83.0 - 54.0 )", "Answer": 29.0, "Type": "Subtraction"}, {"ID": "chal-183", "Body": "Because of the decision <PERSON> asked the students to suggest specific types of food. If 269 students suggested adding bacon 330 suggested adding mashed potatoes to the menu and 76 suggested adding tomatoes", "Question": "How many more students suggested mashed potatoes than those that suggested bacon?", "Equation": "( 330.0 - 269.0 )", "Answer": 61.0, "Type": "Subtraction"}, {"ID": "chal-184", "Body": "<PERSON>'s mother made cookies for 14. If each of them had 30 cookies", "Question": "How many cookies did she prepare?", "Equation": "( 14.0 * 30.0 )", "Answer": 420.0, "Type": "Multiplication"}, {"ID": "chal-185", "Body": "<PERSON> had 15 apps on his phone. He added 71 new apps. After deleting some he had 14 left.", "Question": "How many more apps did he delete than he added?", "Equation": "( 15.0 - 14.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-186", "Body": "<PERSON> has 106 nintendo games.", "Question": "How many will she have left if she gives away 64 games?", "Equation": "( 106.0 - 64.0 )", "Answer": 42.0, "Type": "Subtraction"}, {"ID": "chal-187", "Body": "<PERSON> had 15 crackers. If <PERSON> gave equal numbers of crackers to his 5 friends and still had 10 crackers left", "Question": "How many crackers did each friend eat?", "Equation": "( ( 15.0 - 10.0 ) / 5.0 )", "Answer": 1.0, "Type": "Common-Division"}, {"ID": "chal-188", "Body": "Last week <PERSON> had 114 dollars and <PERSON> had 22 dollars. They washed cars over the weekend and now <PERSON> has 21 dollars and <PERSON> has 78 dollars.", "Question": "How much money did <PERSON> make over the weekend?", "Equation": "( 78.0 - 22.0 )", "Answer": 56.0, "Type": "Subtraction"}, {"ID": "chal-189", "Body": "<PERSON> had 42 pieces of candy. If he put them equally into 2 bags", "Question": "How many pieces of candy are in each bag?", "Equation": "( 42.0 / 2.0 )", "Answer": 21.0, "Type": "Common-Division"}, {"ID": "chal-190", "Body": "<PERSON> received 6 emails in the morning and 2 emails in the afternoon.", "Question": "How many more emails did <PERSON> receive in the morning than in the afternoon?", "Equation": "( 6.0 - 2.0 )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-191", "Body": "<PERSON> made 43 cakes and 114 pastries. If he sold 154 pastries and 78 cakes", "Question": "How many more pastries than cakes did baker sell?", "Equation": "( 154.0 - 78.0 )", "Answer": 76.0, "Type": "Subtraction"}, {"ID": "chal-192", "Body": "<PERSON> has 20 marbles. In her class 2 boys love to play marbles. If she distributes her marbles equally", "Question": "How many will each of the boys receive?", "Equation": "( 20.0 / 2.0 )", "Answer": 10.0, "Type": "Common-Division"}, {"ID": "chal-193", "Body": "They decided to hold the party in their backyard. They have 4 sets of tables and each set has 3 chairs. If a total of 14 people will attend the party", "Question": "How many more chairs will they have to buy?", "Equation": "( 14.0 - ( 4.0 * 3.0 ) )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-194", "Body": "Last week <PERSON> had 19 dollars and <PERSON> had 16 dollars. They washed cars over the weekend and now <PERSON> has 40 dollars and <PERSON> has 69 dollars.", "Question": "How much money did <PERSON> earn over the weekend?", "Equation": "( 40.0 - 19.0 )", "Answer": 21.0, "Type": "Subtraction"}, {"ID": "chal-195", "Body": "<PERSON><PERSON> had 25 cookies. He ate 5 of them. Then he bought 3 more cookies", "Question": "How many more cookies did he eat than those he bought?", "Equation": "( 5.0 - 3.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-196", "Body": "<PERSON> has 4 apple trees. She picked 7 apples from each of her trees. Now the trees have a total 29 apples still on them.", "Question": "How many apples did <PERSON> pick in all?", "Equation": "( 4.0 * 7.0 )", "Answer": 28.0, "Type": "Multiplication"}, {"ID": "chal-197", "Body": "<PERSON> has 8 fewer peaches and 10 more apples than <PERSON>. <PERSON> has 11 apples and 18 peaches.", "Question": "How many more peaches than apples does <PERSON> have?", "Equation": "( 18.0 - 11.0 )", "Answer": 7.0, "Type": "Subtraction"}, {"ID": "chal-198", "Body": "A grocery store had a total of 30 bottles of which 28 were bottles of regular soda and the rest were bottles of diet soda.", "Question": "How many bottles of diet soda did they have?", "Equation": "( 30.0 - 28.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-199", "Body": "The Ferris wheel in paradise park has 2 small seats and 23 large seats. Each small seat can hold 14 people and large seat can hold 54 people.", "Question": "How many people can ride the Ferris wheel on small seats?", "Equation": "( 2.0 * 14.0 )", "Answer": 28.0, "Type": "Multiplication"}, {"ID": "chal-200", "Body": "<PERSON><PERSON> bought 360 soda bottles and 162 water bottles when they were on sale. If she drank 122 water bottles and 9 soda bottles a day", "Question": "How many days would the soda bottles last?", "Equation": "( 360.0 / 9.0 )", "Answer": 40.0, "Type": "Common-Division"}, {"ID": "chal-201", "Body": "A grocery store had 4 bottles of diet soda. If they had 79 more bottles of regular soda than diet soda", "Question": "How many bottles of regular soda did they have?", "Equation": "( 4.0 + 79.0 )", "Answer": 83.0, "Type": "Addition"}, {"ID": "chal-202", "Body": "During the Arkansas and Texas tech game the Razorback t-shirt shop made $ 60 by selling t-shirts. If they make $ 10 dollars off each t-shirt sold", "Question": "How many t-shirts did they sell?", "Equation": "( 60.0 / 10.0 )", "Answer": 6.0, "Type": "Common-Division"}, {"ID": "chal-203", "Body": "<PERSON> was reading through his favorite book. He read 22 pages per day and it took him 569 days to finish the book.", "Question": "How many pages are there in the book?", "Equation": "( 22.0 * 569.0 )", "Answer": 12518.0, "Type": "Multiplication"}, {"ID": "chal-204", "Body": "Winter is almost here and most animals are migrating to warmer countries. 86 bird families flew away for the winter from near a mountain. If there were 45 bird families living near the mountain initially", "Question": "How many more bird families flew away for the winter than those that stayed behind?", "Equation": "( 86.0 - ( 45.0 - 86.0 ) )", "Answer": 127.0, "Type": "Subtraction"}, {"ID": "chal-205", "Body": "<PERSON> brought 2 balloons to the park and then bought 3 more balloons there. If <PERSON> brought 6 balloons to the park", "Question": "How many more balloons did <PERSON> have than <PERSON> in the park?", "Equation": "( 6.0 - ( 2.0 + 3.0 ) )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-206", "Body": "<PERSON> took a look at his books and magazines. If he has 9 books and 46 magazines in each of his 10 bookshelves", "Question": "How many magazines does he have in total?", "Equation": "( 46.0 * 10.0 )", "Answer": 460.0, "Type": "Multiplication"}, {"ID": "chal-207", "Body": "<PERSON> had to complete 8 pages of math homework, 7 pages of reading homework and 3 more pages of biology homework.", "Question": "How many pages of math and biology homework did she have to complete?", "Equation": "( 8.0 + 3.0 )", "Answer": 11.0, "Type": "Addition"}, {"ID": "chal-208", "Body": "Every day <PERSON> spends 2 hours on learning english, 5 hours on learning chinese and 4 hours on learning spanish.", "Question": "How many more hours does he spend on learning chinese than he does on learning spanish?", "Equation": "( 5.0 - 4.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-209", "Body": "2 birds were sitting on the fence. 5 more birds and 4 more storks came to join them.", "Question": "How many more birds than storks are sitting on the fence?", "Equation": "( ( 2.0 + 5.0 ) - 4.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-210", "Body": "<PERSON><PERSON> had 6 cookies. He gave 30 cookies to his friend and ate 23 cookies.", "Question": "How many more cookies did he give to his friend than those he ate?", "Equation": "( 30.0 - 23.0 )", "Answer": 7.0, "Type": "Subtraction"}, {"ID": "chal-211", "Body": "<PERSON> made 170 cakes. He sold 78 of them and bought 31 new cakes.", "Question": "How many more cakes did baker sell than those he bought?", "Equation": "( 78.0 - 31.0 )", "Answer": 47.0, "Type": "Subtraction"}, {"ID": "chal-212", "Body": "<PERSON> sold 25 cases of boxes of trefoils.", "Question": "How many boxes of trefoils does she need to deliver if each case has 14 boxes?", "Equation": "( 25.0 * 14.0 )", "Answer": 350.0, "Type": "Multiplication"}, {"ID": "chal-213", "Body": "We ordered 17 pizzas. Each pizza has 4 slices. If there are 25 of us", "Question": "How many slices of pizza are there altogether?", "Equation": "( 17.0 * 4.0 )", "Answer": 68.0, "Type": "Multiplication"}, {"ID": "chal-214", "Body": "There were 12 roses and 2 orchids in the vase. <PERSON> cut some more roses and orchids from her flower garden. There are now 10 roses and 21 orchids in the vase.", "Question": "How many orchids did she cut?", "Equation": "( 21.0 - 2.0 )", "Answer": 19.0, "Type": "Subtraction"}, {"ID": "chal-215", "Body": "White t - shirts can be purchased in packages. If mom buys 66 white t - shirts where each package has 3 white t - shirts.", "Question": "How many packages will she have?", "Equation": "( 66.0 / 3.0 )", "Answer": 22.0, "Type": "Common-Division"}, {"ID": "chal-216", "Body": "<PERSON> is baking a cake. The recipe calls for 5 cups of flour and 14 cups of sugar. She already put in 10 cups of flour and 2 cups of sugar.", "Question": "How many more cups of sugar does she need to add?", "Equation": "( 14.0 - 2.0 )", "Answer": 12.0, "Type": "Subtraction"}, {"ID": "chal-217", "Body": "Mom buys 51 white t - shirts. If white t - shirts can be purchased in packages of 3", "Question": "How many packages will she have?", "Equation": "( 51.0 / 3.0 )", "Answer": 17.0, "Type": "Common-Division"}, {"ID": "chal-218", "Body": "<PERSON> could fit 8 action figures and 10 cabinets on each shelf in his room. His room has 4 shelves.", "Question": "How many total items could his shelves hold?", "Equation": "( ( 8.0 + 10.0 ) * 4.0 )", "Answer": 72.0, "Type": "Multiplication"}, {"ID": "chal-219", "Body": "We ordered 7 pizzas. Each pizza has the same number of slices. If there were a total of 14 slices", "Question": "How many slices are there in each pizza?", "Equation": "( 14.0 / 7.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-220", "Body": "<PERSON> made 144 cakes. He sold 71 of them. Then he made 111 more cakes.", "Question": "How many more cakes did baker make than those he sold?", "Equation": "( ( 144.0 + 111.0 ) - 71.0 )", "Answer": 184.0, "Type": "Subtraction"}, {"ID": "chal-221", "Body": "<PERSON> is baking a cake. The recipe calls for 12 cups of flour 5 cups of sugar and 49 cups of salt. She already put in 11 cups of flour.", "Question": "How many more cups of flour does she need to add?", "Equation": "( 12.0 - 11.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-222", "Body": "The Razorback t-shirt shop makes $ 78 dollars off each t-shirt sold. During the Arkansas game and the Texas tech game they sold a total of 186 t-shirts. If they sold 172 t-shirts during the Arkansas game", "Question": "How much money did they make from selling the t-shirts during the Texas tech game?", "Equation": "( 78.0 * ( 186.0 - 172.0 ) )", "Answer": 1092.0, "Type": "Multiplication"}, {"ID": "chal-223", "Body": "There are 62 different movies and 19 different books in the ' crazy silly school ' series. If you read 4 of the books and watched 34 of the movies", "Question": "How many more books do you still have to read?", "Equation": "( 19.0 - 4.0 )", "Answer": 15.0, "Type": "Subtraction"}, {"ID": "chal-224", "Body": "We ordered some pizzas. Each pizza has 2 slices. If there were a total of 28 slices", "Question": "How many pizzas did we order?", "Equation": "( 28.0 / 2.0 )", "Answer": 14.0, "Type": "Common-Division"}, {"ID": "chal-225", "Body": "<PERSON> wants to split a collection of eggs into groups of 3. <PERSON> has 99 bananas 9 eggs and 27 marbles.", "Question": "How many groups will be created?", "Equation": "( 9.0 / 3.0 )", "Answer": 3.0, "Type": "Common-Division"}, {"ID": "chal-226", "Body": "41 children were riding on the bus. At the bus stop some children got off the bus. Then there were 18 children left on the bus.", "Question": "How many more children were on the bus before the bus stop than there are now?", "Equation": "( 41.0 - 18.0 )", "Answer": 23.0, "Type": "Subtraction"}, {"ID": "chal-227", "Body": "In <PERSON>'s class some boys love to play marbles. <PERSON> gives 2 marbles to each boy. If she had 28 marbles", "Question": "How many boys did she give the marbles to?", "Equation": "( 28.0 / 2.0 )", "Answer": 14.0, "Type": "Common-Division"}, {"ID": "chal-228", "Body": "<PERSON> had 14 crackers and 21 cakes. If <PERSON> gave equal numbers of crackers and cakes to his 7 friends", "Question": "How many crackers and cakes did each person eat?", "Equation": "( ( 14.0 + 21.0 ) / 7.0 )", "Answer": 5.0, "Type": "Common-Division"}, {"ID": "chal-229", "Body": "<PERSON> the hippo and her friends are preparing for thanksgiving at <PERSON>'s house. <PERSON> baked 31 cookies yesterday 270 cookies this morning and 419 cookies the day before yesterday.", "Question": "How many cookies did <PERSON> bake till last night?", "Equation": "( 31.0 + 419.0 )", "Answer": 450.0, "Type": "Addition"}, {"ID": "chal-230", "Body": "For the walls of the house he would use 4 nails in all to secure large planks of wood. If each plank of wood needs 2 pieces of nails to be secured", "Question": "How many planks does <PERSON> need for the house wall?", "Equation": "( 4.0 / 2.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-231", "Body": "<PERSON> collects bottle caps and wrappers. He found 30 bottle caps and 14 wrappers at the park. Now he has 7 bottle caps and 86 wrappers in his collection.", "Question": "How many more bottle caps than wrappers did danny find at the park?", "Equation": "( 30.0 - 14.0 )", "Answer": 16.0, "Type": "Subtraction"}, {"ID": "chal-232", "Body": "He then went to see the oranges being harvested. He found out that they harvest 8 sacks per day and that each sack containes 35 oranges.", "Question": "How many oranges do they harvest per day?", "Equation": "( 8.0 * 35.0 )", "Answer": 280.0, "Type": "Multiplication"}, {"ID": "chal-233", "Body": "<PERSON> spent $ 16 to buy books and $ 8 to buy pens. Now he has $ 19.", "Question": "How much more did <PERSON> spend on books than pens?", "Equation": "( 16.0 - 8.0 )", "Answer": 8.0, "Type": "Subtraction"}, {"ID": "chal-234", "Body": "<PERSON> is making bead necklaces for her friends. Each necklace takes 8 beads and she has 16 beads.", "Question": "How many necklaces can <PERSON> make?", "Equation": "( 16.0 / 8.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-235", "Body": "<PERSON> has $ 4. He had $ 3 left with him after he bought a candy bar.", "Question": "How much did the candy bar cost?", "Equation": "( 4.0 - 3.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-236", "Body": "<PERSON> got a box of 253 crayons for his birthday. By the end of the school year he had either lost or given away 70 of them.", "Question": "How many crayons did he have left by the end of the school year?", "Equation": "( 253.0 - 70.0 )", "Answer": 183.0, "Type": "Subtraction"}, {"ID": "chal-237", "Body": "<PERSON> the hippo and her friends are preparing for thanksgiving at <PERSON>'s house. <PERSON> baked 144 chocolate chip cookies and 397 raisin cookies yesterday. And she baked 85 raisin cookies and 403 chocolate chip cookies this morning.", "Question": "How many raisin cookies did <PERSON> bake?", "Equation": "( 397.0 + 85.0 )", "Answer": 482.0, "Type": "Addition"}, {"ID": "chal-238", "Body": "<PERSON> picked 2 ripe apples from her tree. Now the tree has 7 apples still on it. If 6 of those are ripe and the rest are unripe", "Question": "How many ripe apples did the tree have to begin with?", "Equation": "( 2.0 + 6.0 )", "Answer": 8.0, "Type": "Addition"}, {"ID": "chal-239", "Body": "An industrial machine made 9 shirts yesterday and 44 shirts today. It can make 3 shirts a minute.", "Question": "How many minutes did the machine work yesterday?", "Equation": "( 9.0 / 3.0 )", "Answer": 3.0, "Type": "Common-Division"}, {"ID": "chal-240", "Body": "<PERSON> did 51 push-ups in gym class today. <PERSON> did 22 more push-ups than <PERSON>. <PERSON> did 4 push-ups less than <PERSON>.", "Question": "How many push-ups did <PERSON> do?", "Equation": "( ( 51.0 + 22.0 ) - 4.0 )", "Answer": 69.0, "Type": "Subtraction"}, {"ID": "chal-241", "Body": "<PERSON> the hippo and her friends are preparing for thanksgiving at <PERSON>'s house. <PERSON> baked 519 chocolate chip cookies and 300 raisin cookies yesterday. And she baked 280 raisin cookies and 359 chocolate chip cookies this morning.", "Question": "How many more raisin cookies did <PERSON> bake yesterday compared to today?", "Equation": "( 300.0 - 280.0 )", "Answer": 20.0, "Type": "Subtraction"}, {"ID": "chal-242", "Body": "A grocery store had 81 bottles of regular soda, 60 bottles of diet soda and 60 bottles of lite soda.", "Question": "How many more bottles of regular soda did they have than diet soda?", "Equation": "( 81.0 - 60.0 )", "Answer": 21.0, "Type": "Subtraction"}, {"ID": "chal-243", "Body": "<PERSON> has some blocks. He uses 52 blocks to build a tower. If there are 38 blocks left", "Question": "How many blocks did he have at the start?", "Equation": "( 52.0 + 38.0 )", "Answer": 90.0, "Type": "Addition"}, {"ID": "chal-244", "Body": "<PERSON> had $ 13. He spent some money. Now he has $ 3.", "Question": "How much money did <PERSON> spend?", "Equation": "( 13.0 - 3.0 )", "Answer": 10.0, "Type": "Subtraction"}, {"ID": "chal-245", "Body": "<PERSON> had 19 more marbles than <PERSON>. <PERSON> lost some of his marbles at the playground. Now <PERSON> has 8 more marbles than doug.", "Question": "How many marbles did <PERSON> lose?", "Equation": "( 19.0 - 8.0 )", "Answer": 11.0, "Type": "Subtraction"}, {"ID": "chal-246", "Body": "They decided to hold the party in their backyard. They have 12 sets of tables and each set has 14 chairs. There are also 6 chairs extra.", "Question": "How many chairs do they have for the guests?", "Equation": "( ( 12.0 * 14.0 ) + 6.0 )", "Answer": 174.0, "Type": "Addition"}, {"ID": "chal-247", "Body": "The Ferris wheel in paradise park has 3 small seats and 7 large seats. Each small seat can hold 16 people and large seat can hold 12 people.", "Question": "How many people can ride the Ferris wheel on large seats?", "Equation": "( 7.0 * 12.0 )", "Answer": 84.0, "Type": "Multiplication"}, {"ID": "chal-248", "Body": "There are 20 houses in a block. If a mailman has to give 32 pieces of junk mail to each house in each block", "Question": "How many pieces of junk mail should he give each block?", "Equation": "( 32.0 * 20.0 )", "Answer": 640.0, "Type": "Multiplication"}, {"ID": "chal-249", "Body": "A grocery store had 72 bottles of regular soda, 32 bottles of diet soda and 78 apples.", "Question": "How many more bottles than apple did they have?", "Equation": "( ( 72.0 + 32.0 ) - 78.0 )", "Answer": 26.0, "Type": "Subtraction"}, {"ID": "chal-250", "Body": "There are 7 baskets of peaches. Each basket has 10 red peaches and 2 green peaches.", "Question": "How many green peaches are in the baskets altogether?", "Equation": "( 2.0 * 7.0 )", "Answer": 14.0, "Type": "Multiplication"}, {"ID": "chal-251", "Body": "Winter is almost here and most animals are migrating to warmer countries. There are 3 bird families living near the mountain. 26 new bird families came to live near the mountain from the arctic while 2 bird families flew away further south for winter.", "Question": "How many bird families were left near the mountain?", "Equation": "( ( 3.0 - 2.0 ) + 26.0 )", "Answer": 27.0, "Type": "Addition"}, {"ID": "chal-252", "Body": "Because of the decision <PERSON> asked the students to suggest specific types of food. If 144 students suggested adding mashed potatoes 467 suggested adding bacon to the menu and 79 suggested adding tomatoes", "Question": "How many more students suggested mashed potatoes than those that suggested tomatoes?", "Equation": "( 144.0 - 79.0 )", "Answer": 65.0, "Type": "Subtraction"}, {"ID": "chal-253", "Body": "<PERSON>'s rooms are 19 feet long and 18 feet wide. If she has 20 rooms in all", "Question": "How much carpet does she need to cover the floors of all rooms?", "Equation": "( ( 19.0 * 18.0 ) * 20.0 )", "Answer": 6840.0, "Type": "Multiplication"}, {"ID": "chal-254", "Body": "<PERSON> had to complete 10 pages of math homework. If she had to complete 3 more pages of reading homework than math homework", "Question": "How many pages did she have to complete in all?", "Equation": "( ( 10.0 + 10.0 ) + 3.0 )", "Answer": 23.0, "Type": "Addition"}, {"ID": "chal-255", "Body": "In a school there are 700 girls and the rest are boys. If there are 142 more boys than girls", "Question": "How many pupils are there in that school?", "Equation": "( ( 700.0 + 700.0 ) + 142.0 )", "Answer": 1542.0, "Type": "Addition"}, {"ID": "chal-256", "Body": "A waiter had 14 customers. 5 customers left.", "Question": "How many customers does he still have?", "Equation": "( 14.0 - 5.0 )", "Answer": 9.0, "Type": "Subtraction"}, {"ID": "chal-257", "Body": "<PERSON> went to the grocery store. She bought 2 packs of cookie and 12 packs of cake. In the end she had 56 amount of change remaining.", "Question": "How many packs of groceries did she buy in all?", "Equation": "( 2.0 + 12.0 )", "Answer": 14.0, "Type": "Addition"}, {"ID": "chal-258", "Body": "<PERSON> is baking a cake. The recipe calls for 11 cups of sugar and 9 cups of flour. She already put in 12 cups of flour and 10 cups of sugar.", "Question": "How many more cups of sugar does she need to add?", "Equation": "( 11.0 - 10.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-259", "Body": "<PERSON> and his dad went strawberry picking. His strawberries weighed 8 pounds while his dad's strawberries weighed 32 pounds.", "Question": "Together how much did their strawberries weigh?", "Equation": "( 8.0 + 32.0 )", "Answer": 40.0, "Type": "Addition"}, {"ID": "chal-260", "Body": "<PERSON> gives away 91 nintendo games.", "Question": "How many did she have initially if she still has 92 games left?", "Equation": "( 91.0 + 92.0 )", "Answer": 183.0, "Type": "Addition"}, {"ID": "chal-261", "Body": "The Razorback t-shirt shop makes $ 106 dollars off each t-shirt sold. During the Arkansas game and the Texas tech game they sold a total of 242 t-shirts. If they sold 115 t-shirts during the Arkansas game", "Question": "How many t-shirts did they sell during the Texas tech game?", "Equation": "( 242.0 - 115.0 )", "Answer": 127.0, "Type": "Subtraction"}, {"ID": "chal-262", "Body": "The Razorback t-shirt shop makes $ 23 dollars off each t-shirt sold. During the Arkansas and Texas tech game they made $ 230 by selling t-shirts.", "Question": "How many t-shirts did they sell?", "Equation": "( 230.0 / 23.0 )", "Answer": 10.0, "Type": "Common-Division"}, {"ID": "chal-263", "Body": "<PERSON> has 90 blocks. He uses 89 blocks to build a house and 63 blocks to build a tower.", "Question": "How many more blocks did he use to build the house than he did to build the tower?", "Equation": "( 89.0 - 63.0 )", "Answer": 26.0, "Type": "Subtraction"}, {"ID": "chal-264", "Body": "<PERSON> brought 2 balloons and <PERSON> brought 3 balloons to the park.", "Question": "How many more balloons did <PERSON> have than <PERSON> in the park?", "Equation": "( 3.0 - 2.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-265", "Body": "Every day <PERSON> spends 7 hours on learning english, 2 hours on learning chinese and 4 hours on learning spanish.", "Question": "How many more hours does he spend on learning english than he does on learning spanish?", "Equation": "( 7.0 - 4.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-266", "Body": "<PERSON> has 43 packages of gum. There are 23 pieces in each package. <PERSON> has 8 extra pieces of gum.", "Question": "How many pieces of gum does <PERSON> have?", "Equation": "( ( 43.0 * 23.0 ) + 8.0 )", "Answer": 997.0, "Type": "Addition"}, {"ID": "chal-267", "Body": "<PERSON> had 108 books. After selling some books in a garage sale and giving 35 books to his friend he had 62 books left.", "Question": "How many books did he sell in the garage sale?", "Equation": "( ( 108.0 - 35.0 ) - 62.0 )", "Answer": 11.0, "Type": "Subtraction"}, {"ID": "chal-268", "Body": "For the walls of the house he would use 12 large planks of wood and 17 small planks. If each large plank of wood needs 14 pieces of nails to be secured and each small plank needs 25 nails.", "Question": "How many planks does <PERSON> need for the house wall?", "Equation": "( 12.0 + 17.0 )", "Answer": 29.0, "Type": "Addition"}, {"ID": "chal-269", "Body": "<PERSON> played 3 games and scored a total of 81 points scoring the same for each game.", "Question": "How many points did she score in each game?", "Equation": "( 81.0 / 3.0 )", "Answer": 27.0, "Type": "Common-Division"}, {"ID": "chal-270", "Body": "<PERSON> collects cards. She had 246 baseball cards and 214 Ace cards. She gave some of her cards to <PERSON> and now has 404 baseball cards and 495 Ace cards left.", "Question": "How many more Ace cards than baseball cards does <PERSON> have?", "Equation": "( 495.0 - 404.0 )", "Answer": 91.0, "Type": "Subtraction"}, {"ID": "chal-271", "Body": "<PERSON> had 8 action figures on a shelf in his room. Later he added 4 more action figures to the shelf and removed 5 old ones.", "Question": "How many action figures were on his shelf in all?", "Equation": "( ( 8.0 + 4.0 ) - 5.0 )", "Answer": 7.0, "Type": "Subtraction"}, {"ID": "chal-272", "Body": "<PERSON> was helping her mom plant flowers and together they planted 45 seeds in 9 flowerbeds. If they put same number of seeds in each flower bed", "Question": "How many seeds did they plant in each flowerbed?", "Equation": "( 45.0 / 9.0 )", "Answer": 5.0, "Type": "Common-Division"}, {"ID": "chal-273", "Body": "A mailman has to give 4 pieces of junk mail to each house in each of the 81 blocks. If there are 12 houses in each block", "Question": "How many pieces of junk mail should he give in each block?", "Equation": "( 4.0 * 12.0 )", "Answer": 48.0, "Type": "Multiplication"}, {"ID": "chal-274", "Body": "The ring toss game at the carnival made 325 dollars in the first 154 days and 114 dollars in the remaining 57 days.", "Question": "How much did they make per day in the remaining 57 days at the carnival?", "Equation": "( 114.0 / 57.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-275", "Body": "<PERSON>'s mother made cookies for 10 guests but 9 guests did not come. If she prepared 18 cookies and each guest had the same number of cookies", "Question": "How many did each of them have?", "Equation": "( 18.0 / ( 10.0 - 9.0 ) )", "Answer": 18.0, "Type": "Common-Division"}, {"ID": "chal-276", "Body": "<PERSON> is baking a cake. The recipe calls for 9 cups of sugar 7 cups of flour and 4 cups of salt. She already put in 2 cups of flour.", "Question": "How many more cups of flour than cups of salt does she need to add now?", "Equation": "( ( 7.0 - 2.0 ) - 4.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-277", "Body": "<PERSON> made some cakes. He sold 145 of them. If he still has 72 cakes left", "Question": "How many cakes did baker make?", "Equation": "( 145.0 + 72.0 )", "Answer": 217.0, "Type": "Addition"}, {"ID": "chal-278", "Body": "<PERSON> needs a carpet of size 10 square feet to cover her room. If her room is 2 feet wide", "Question": "What is the length of her room?", "Equation": "( 10.0 / 2.0 )", "Answer": 5.0, "Type": "Common-Division"}, {"ID": "chal-279", "Body": "There are some bananas in <PERSON>'s banana collection. If the bananas are organized into 140 groups of size 187", "Question": "How many bananas does he have in his collection?", "Equation": "( 140.0 * 187.0 )", "Answer": 26180.0, "Type": "Multiplication"}, {"ID": "chal-280", "Body": "<PERSON> wants to split a collection of eggs into groups of 6. <PERSON> has 18 eggs 72 bananas and 66 marbles.", "Question": "How many groups will be created?", "Equation": "( 18.0 / 6.0 )", "Answer": 3.0, "Type": "Common-Division"}, {"ID": "chal-281", "Body": "<PERSON> currently weighs 9 kilograms. After she started to go jogging everyday she lost 62 kilograms in the first week and 140 kilograms in the second week.", "Question": "How much did she weigh before starting to jog?", "Equation": "( ( 9.0 + 62.0 ) + 140.0 )", "Answer": 211.0, "Type": "Addition"}, {"ID": "chal-282", "Body": "The school is planning a field trip. The school has 72 classrooms. There are 3 seats on each school bus. If there are a total of 111 students in the school", "Question": "How many buses are needed to take the trip?", "Equation": "( 111.0 / 3.0 )", "Answer": 37.0, "Type": "Common-Division"}, {"ID": "chal-283", "Body": "13 campers went rowing and 59 campers went hiking in the morning. 21 campers went rowing in the afternoon.", "Question": "How many campers went rowing in all?", "Equation": "( 13.0 + 21.0 )", "Answer": 34.0, "Type": "Addition"}, {"ID": "chal-284", "Body": "<PERSON> had 9 action figures on a shelf in his room. Later he added 7 more action figures to the shelf. If he also has 10 books on the shelf", "Question": "How many more action figures than books were on his shelf?", "Equation": "( ( 9.0 + 7.0 ) - 10.0 )", "Answer": 6.0, "Type": "Subtraction"}, {"ID": "chal-285", "Body": "<PERSON> had 22 marbles in his collection. He found 13 marbles ones while he lost 5 marbles.", "Question": "How many marbles does he have now?", "Equation": "( ( 22.0 - 5.0 ) + 13.0 )", "Answer": 30.0, "Type": "Addition"}, {"ID": "chal-286", "Body": "For the walls of the house he would use 12 large planks of wood and 10 small planks. If large planks together need 15 pieces of nails to be secured and small planks together need 5 nails.", "Question": "How many nails does <PERSON> need for the house wall?", "Equation": "( 15.0 + 5.0 )", "Answer": 20.0, "Type": "Addition"}, {"ID": "chal-287", "Body": "<PERSON> got a box of 589 crayons for his birthday. During the school year he gave 571 crayons to his friends while he lost 161 crayons.", "Question": "How many more crayons did he give to his friends than those he lost?", "Equation": "( 571.0 - 161.0 )", "Answer": 410.0, "Type": "Subtraction"}, {"ID": "chal-288", "Body": "<PERSON> received 9 emails in the morning, 10 emails in the afternoon and 7 emails in the evening.", "Question": "How many more emails did <PERSON> receive in the morning than in the evening?", "Equation": "( 9.0 - 7.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-289", "Body": "<PERSON> received 6 emails in the morning and 8 emails in the afternoon.", "Question": "How many more emails did <PERSON> receive in the afternoon than in the morning?", "Equation": "( 8.0 - 6.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-290", "Body": "After eating a hearty meal they went to see the Buckingham palace. There were 71 paintings in the Buckingham palace. There, <PERSON> learned that 557 visitors came to the Buckingham palace that day. If there were 188 visitors the previous day", "Question": "How many visited the Buckingham palace within 57 days?", "Equation": "( 557.0 + 188.0 )", "Answer": 745.0, "Type": "Addition"}, {"ID": "chal-291", "Body": "<PERSON> the hippo and her friends are preparing for thanksgiving at <PERSON>'s house. <PERSON> baked 197 chocolate chip cookies and 46 raisin cookies yesterday. And she baked 75 raisin cookies and 66 chocolate chip cookies this morning.", "Question": "How many more chocolate chip cookies did <PERSON> bake yesterday compared to today?", "Equation": "( 197.0 - 66.0 )", "Answer": 131.0, "Type": "Subtraction"}, {"ID": "chal-292", "Body": "<PERSON> has 18 fewer peaches than <PERSON> who has 13 more peaches than <PERSON>. <PERSON> has 19 peaches.", "Question": "How many peaches does <PERSON> have?", "Equation": "( 19.0 - 13.0 )", "Answer": 6.0, "Type": "Subtraction"}, {"ID": "chal-293", "Body": "<PERSON> went to the grocery store. She bought 23 packs of cookie and some packs of cake. In total she had 27 packs of grocery", "Question": "How many packs of cake did she buy in all?", "Equation": "( 27.0 - 23.0 )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-294", "Body": "Every day <PERSON> spends 6 hours on learning english 3 hours on learning chinese and 58 hours on learning spanish.", "Question": "How many more hours does he spend on learning english than he does on learning chinese?", "Equation": "( 6.0 - 3.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-295", "Body": "A book has 2 chapters. The first chapter is 48 pages long. The second chapter is 11 pages long.", "Question": "How many more pages does the first chapter have than the second chapter?", "Equation": "( 48.0 - 11.0 )", "Answer": 37.0, "Type": "Subtraction"}, {"ID": "chal-296", "Body": "6 green peaches, 60 yellow peaches and 2 red peaches are in the basket.", "Question": "How many more green peaches than red peaches are in the basket?", "Equation": "( 6.0 - 2.0 )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-297", "Body": "<PERSON><PERSON> drinks 6 bottles a day. If she bought a total of 12 water bottles", "Question": "How many days would they last her?", "Equation": "( 12.0 / 6.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-298", "Body": "There were 22 parents in the program and 676 people in total.", "Question": "How many pupils were present in the program?", "Equation": "( 676.0 - 22.0 )", "Answer": 654.0, "Type": "Subtraction"}, {"ID": "chal-299", "Body": "<PERSON> played tag with 17 kids on monday, 15 kids on tuesday and 2 kids on wednesday.", "Question": "How many kids did she play with altogether?", "Equation": "( ( 17.0 + 15.0 ) + 2.0 )", "Answer": 34.0, "Type": "Addition"}, {"ID": "chal-300", "Body": "The grasshopper, the frog and the mouse had a jumping contest. The grasshopper jumped 25 inches. The frog jumped 18 inches farther than the grasshopper and the mouse jumped 2 inches farther than the frog.", "Question": "How far did the mouse jump?", "Equation": "( ( 25.0 + 18.0 ) + 2.0 )", "Answer": 45.0, "Type": "Addition"}, {"ID": "chal-301", "Body": "Next on his checklist is wax to stick the feathers together and colors to paint them. He needs a total of 49 g of colors to paint them. If the feathers require 288 g of wax and right now he just needs 260 g", "Question": "How many grams of wax does he already have?", "Equation": "( 288.0 - 260.0 )", "Answer": 28.0, "Type": "Subtraction"}, {"ID": "chal-302", "Body": "<PERSON> is baking a cake. The recipe calls for 14 cups of flour and 6 cups of sugar. She already put in 5 cups of flour.", "Question": "How many more cups of flour than cups of sugar does she need to add now?", "Equation": "( ( 14.0 - 5.0 ) - 6.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-303", "Body": "<PERSON> has 13 more apples and 17 fewer peaches than <PERSON>. <PERSON> has 8 peaches and 12 apples.", "Question": "How many apples does <PERSON> have?", "Equation": "( 13.0 + 12.0 )", "Answer": 25.0, "Type": "Addition"}, {"ID": "chal-304", "Body": "<PERSON> has 12 fewer peaches than <PERSON> who has 11 more peaches than <PERSON>. <PERSON> has 4 peaches.", "Question": "How many more peaches does <PERSON> have than <PERSON>?", "Equation": "( 12.0 - 11.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-305", "Body": "Last week <PERSON> had 86 dollars and <PERSON> had 5 dollars. <PERSON> washed cars over the weekend and now has 9 dollars and <PERSON> did not work.", "Question": "How much money do they have together now?", "Equation": "( 5.0 + 9.0 )", "Answer": 14.0, "Type": "Addition"}, {"ID": "chal-306", "Body": "<PERSON> has 12 fewer peaches and 79 more apples than <PERSON>. <PERSON> has 19 peaches and 14 apples.", "Question": "How many peaches does <PERSON> have?", "Equation": "( 19.0 - 12.0 )", "Answer": 7.0, "Type": "Subtraction"}, {"ID": "chal-307", "Body": "There are 10 different books and 6 different movies in the ' crazy silly school ' series. If you read 14 of the movies and watched 19 of the books", "Question": "How many more books than movies have you read?", "Equation": "( 19.0 - 14.0 )", "Answer": 5.0, "Type": "Subtraction"}, {"ID": "chal-308", "Body": "<PERSON><PERSON> bought 301 water bottles when they were on sale. If she drank 144 bottles a day for some days. If she has 157 bottles left", "Question": "How many days did she drink for?", "Equation": "( ( 301.0 - 157.0 ) / 144.0 )", "Answer": 1.0, "Type": "Common-Division"}, {"ID": "chal-309", "Body": "He also had 56 aquariums for saltwater animals and 10 aquariums for freshwater animals. Each aquarium has 39 animals in it.", "Question": "How many saltwater animals does <PERSON> have?", "Equation": "( 56.0 * 39.0 )", "Answer": 2184.0, "Type": "Multiplication"}, {"ID": "chal-310", "Body": "<PERSON> was collecting cans for recycling. On monday she had 10 bags of cans. She found 3 bags of cans on the next day and 7 bags of cans the day after that.", "Question": "How many bags did she have altogether?", "Equation": "( ( 10.0 + 3.0 ) + 7.0 )", "Answer": 20.0, "Type": "Addition"}, {"ID": "chal-311", "Body": "The grasshopper and the frog had a jumping contest. The grasshopper jumped 31 inches and the frog jumped 35 inches.", "Question": "How much did they jump altogether?", "Equation": "( 31.0 + 35.0 )", "Answer": 66.0, "Type": "Addition"}, {"ID": "chal-312", "Body": "There were 100 dollars in <PERSON>'s wallet. She collected 148 more dollars from an atm. After she visited a supermarket there were 159 dollars left.", "Question": "How much did she spend?", "Equation": "( ( 100.0 + 148.0 ) - 159.0 )", "Answer": 89.0, "Type": "Subtraction"}, {"ID": "chal-313", "Body": "A waiter had 11 customers. After some left he still had 3 customers.", "Question": "How many more customers left than those that stayed behind?", "Equation": "( ( 11.0 - 3.0 ) - 3.0 )", "Answer": 5.0, "Type": "Subtraction"}, {"ID": "chal-314", "Body": "<PERSON> had 81 ds games and her friends had 59 games.", "Question": "How many more games does <PERSON> have than her friends?", "Equation": "( 81.0 - 59.0 )", "Answer": 22.0, "Type": "Subtraction"}, {"ID": "chal-315", "Body": "<PERSON> put some pieces of candy equally into 2 bags. If he had 16 pieces of candy", "Question": "How many pieces of candy are in each bag?", "Equation": "( 16.0 / 2.0 )", "Answer": 8.0, "Type": "Common-Division"}, {"ID": "chal-316", "Body": "Being his favorite, he saved checking on the grapevines for his last stop. He was told by 266 of the pickers that they fill 90 drums of grapes in 5 days.", "Question": "How many drums of grapes would be filled in each day?", "Equation": "( 90.0 / 5.0 )", "Answer": 18.0, "Type": "Common-Division"}, {"ID": "chal-317", "Body": "<PERSON> played tag with 5 kids on tuesday. She had played tag with 6 kids on monday.", "Question": "How many more kids did she play with on monday than on tuesday?", "Equation": "( 6.0 - 5.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-318", "Body": "<PERSON> played tag with a total of 18 kids. She played with 4 kids on monday and yet with some more kids on tuesday.", "Question": "How many kids did she play with on tuesday?", "Equation": "( 18.0 - 4.0 )", "Answer": 14.0, "Type": "Subtraction"}, {"ID": "chal-319", "Body": "<PERSON> had 3 books and 4 action figures on a shelf in his room. Later he added 2 more action figures to the shelf.", "Question": "How many more action figures than books were on his shelf?", "Equation": "( ( 4.0 + 2.0 ) - 3.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-320", "Body": "There are 6 houses on a block. If a mailman has to give 24 pieces of junk mail to each block", "Question": "How many pieces of junk mail should he give in each house?", "Equation": "( 24.0 / 6.0 )", "Answer": 4.0, "Type": "Common-Division"}, {"ID": "chal-321", "Body": "<PERSON> had 19 apps and 18 files on his phone. After deleting some apps and files he had 6 apps and 15 files left.", "Question": "How many more files than apps does he have left on the phone?", "Equation": "( 15.0 - 6.0 )", "Answer": 9.0, "Type": "Subtraction"}, {"ID": "chal-322", "Body": "<PERSON> made 99 cakes. He bought 167 new cakes and sold 89 cakes.", "Question": "How many more cakes did baker buy than those he sold?", "Equation": "( 167.0 - 89.0 )", "Answer": 78.0, "Type": "Subtraction"}, {"ID": "chal-323", "Body": "3 birds and 4 storks were sitting on the fence. 6 more storks came to join them.", "Question": "How many birds and storks are sitting on the fence?", "Equation": "( ( 3.0 + 4.0 ) + 6.0 )", "Answer": 13.0, "Type": "Addition"}, {"ID": "chal-324", "Body": "<PERSON> did 44 push-ups in gym class today. <PERSON> did 9 more push-ups than <PERSON>.", "Question": "How many push-ups did <PERSON> do?", "Equation": "( 44.0 - 9.0 )", "Answer": 35.0, "Type": "Subtraction"}, {"ID": "chal-325", "Body": "<PERSON> spent $ 6 to buy books and $ 16 to buy pens. Now he has $ 19.", "Question": "How much did <PERSON> have before he spent his money?", "Equation": "( ( 6.0 + 16.0 ) + 19.0 )", "Answer": 41.0, "Type": "Addition"}, {"ID": "chal-326", "Body": "<PERSON> took a look at his books as well. Each bookshelf contains 2 books. If he has a total of 38 books", "Question": "How many bookshelves does he have?", "Equation": "( 38.0 / 2.0 )", "Answer": 19.0, "Type": "Common-Division"}, {"ID": "chal-327", "Body": "There were 7 roses in the vase. <PERSON> cut some more roses from her flower garden which had a total of 59 roses. There are now 20 roses in the vase.", "Question": "How many roses did she cut?", "Equation": "( 20.0 - 7.0 )", "Answer": 13.0, "Type": "Subtraction"}, {"ID": "chal-328", "Body": "<PERSON> played a trivia game and gained 3 points in each round of a game. If he scored 78 points in the trivia game", "Question": "How many rounds did he play?", "Equation": "( 78.0 / 3.0 )", "Answer": 26.0, "Type": "Common-Division"}, {"ID": "chal-329", "Body": "<PERSON> made 13 cakes. He sold 91 of them and bought 154 new cakes.", "Question": "How many more cakes did baker buy than those he sold?", "Equation": "( 154.0 - 91.0 )", "Answer": 63.0, "Type": "Subtraction"}, {"ID": "chal-330", "Body": "He then went to see the oranges being harvested. He found out that they harvest 71 sacks of unripe oranges and 60 sacks of ripe oranges per day.", "Question": "How many more sacks of unripe oranges than ripe oranges are harvested per day?", "Equation": "( 71.0 - 60.0 )", "Answer": 11.0, "Type": "Subtraction"}, {"ID": "chal-331", "Body": "At the zoo, a cage had snakes and alligators. The total number of animals in the cage was 79. If 24 snakes and 51 alligators were hiding", "Question": "How many animals were not hiding in all?", "Equation": "( ( 79.0 - 24.0 ) - 51.0 )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-332", "Body": "<PERSON> has 21 bottle caps in his collection. He found some more bottle caps at the park. If he has 53 bottle caps now", "Question": "How many bottle caps did he find at the park?", "Equation": "( 53.0 - 21.0 )", "Answer": 32.0, "Type": "Subtraction"}, {"ID": "chal-333", "Body": "<PERSON> was reading through his favorite book. The book had 2 chapters each with 405 pages. It took frank 664 days to finish the book.", "Question": "How many chapters did he read per day?", "Equation": "( 664.0 / 2.0 )", "Answer": 332.0, "Type": "Common-Division"}, {"ID": "chal-334", "Body": "There were 2 roses in the vase. <PERSON> threw away 4 roses from the vase and cut some more new roses from her flower garden to put in the vase. There are now 23 roses in the vase.", "Question": "How many roses did she cut?", "Equation": "( ( 23.0 + 4.0 ) - 2.0 )", "Answer": 25.0, "Type": "Subtraction"}, {"ID": "chal-335", "Body": "<PERSON> had to complete 5 pages of math homework, 10 pages of reading homework and 6 more pages of biology homework.", "Question": "How many pages of reading and biology homework did she have to complete?", "Equation": "( 10.0 + 6.0 )", "Answer": 16.0, "Type": "Addition"}, {"ID": "chal-336", "Body": "<PERSON> had 7 action figures and 2 books on a shelf in his room. Later he added 4 more books to the shelf.", "Question": "How many more action figures than books were on his shelf?", "Equation": "( 7.0 - ( 2.0 + 4.0 ) )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-337", "Body": "In a school there are 34 girls and 841 boys.", "Question": "How many more boys than girls does the school have?", "Equation": "( 841.0 - 34.0 )", "Answer": 807.0, "Type": "Subtraction"}, {"ID": "chal-338", "Body": "<PERSON> ate some pieces of candy. Then he ate 25 more. If he ate a total of 43 pieces of candy", "Question": "How many pieces of candy had he eaten at the start?", "Equation": "( 43.0 - 25.0 )", "Answer": 18.0, "Type": "Subtraction"}, {"ID": "chal-339", "Body": "He then went to see the oranges being harvested. He found out that they harvest 8 sacks per day.", "Question": "How many days will it take to harvest 24 sacks of oranges?", "Equation": "( 24.0 / 8.0 )", "Answer": 3.0, "Type": "Common-Division"}, {"ID": "chal-340", "Body": "53 campers went rowing in the morning 48 campers went rowing in the afternoon and 49 campers went rowing in the evening.", "Question": "How many more campers went rowing in the morning than in the evening?", "Equation": "( 53.0 - 49.0 )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-341", "Body": "<PERSON> received 3 emails in the afternoon, 5 emails in the morning and 16 emails in the evening.", "Question": "How many more emails did <PERSON> receive in the morning than in the afternoon?", "Equation": "( 5.0 - 3.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-342", "Body": "<PERSON> has $ 3. For a total of $ 4 he bought 2 candy bar each one costing the same amount of money.", "Question": "How much did each candy bar cost?", "Equation": "( 4.0 / 2.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-343", "Body": "<PERSON> had to complete 3 pages of math homework and 4 pages of reading homework.", "Question": "How many more pages of reading homework than math homework did she have?", "Equation": "( 4.0 - 3.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-344", "Body": "A book has 2 chapters. The first chapter is 37 pages long. The second chapter is 80 pages long.", "Question": "How many more pages does the second chapter have than the first chapter?", "Equation": "( 80.0 - 37.0 )", "Answer": 43.0, "Type": "Subtraction"}, {"ID": "chal-345", "Body": "There are 8 different movies and 21 different books in the ' crazy silly school ' series. If you read 7 of the books and watched 4 of the movies", "Question": "How many more movies do you still have to watch?", "Equation": "( 8.0 - 4.0 )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-346", "Body": "<PERSON> grew some trees in her backyard. After a typhoon 5 died. If 12 trees were left", "Question": "How many trees did she grow?", "Equation": "( 5.0 + 12.0 )", "Answer": 17.0, "Type": "Addition"}, {"ID": "chal-347", "Body": "The Razorback shop makes $ 192 dollars off each t-shirt and $ 34 off each jersey. During the Arkansas and Texas tech game they sold 157 t-shirts and 19 jerseys.", "Question": "How much more does a t-shirt cost than a jersey?", "Equation": "( 192.0 - 34.0 )", "Answer": 158.0, "Type": "Subtraction"}, {"ID": "chal-348", "Body": "<PERSON> the hippo and her friends are preparing for thanksgiving at <PERSON>'s house. <PERSON> baked 527 chocolate chip cookies and 86 raisin cookies yesterday. And she baked 86 raisin cookies and 554 chocolate chip cookies this morning.", "Question": "How many chocolate chip cookies did <PERSON> bake?", "Equation": "( 527.0 + 554.0 )", "Answer": 1081.0, "Type": "Addition"}, {"ID": "chal-349", "Body": "<PERSON> and his dad went strawberry picking. Together they collected strawberries that weighed 22 pounds. On the way back <PERSON> ' dad found 30 more pounds of strawberries. <PERSON>'s strawberries now weighed 36 pounds.", "Question": "How much did his dad's strawberries weigh now?", "Equation": "( ( 22.0 - 36.0 ) + 30.0 )", "Answer": 16.0, "Type": "Addition"}, {"ID": "chal-350", "Body": "3 red peaches, and some more green peaches are in the basket. If there are 10 more green peaches than red peaches in the basket", "Question": "How many green peaches are in the basket?", "Equation": "( 3.0 + 10.0 )", "Answer": 13.0, "Type": "Addition"}, {"ID": "chal-351", "Body": "After a typhoon, 13 trees in <PERSON>'s backyard died. If she had grown 3 trees initially", "Question": "How many more trees died in the typhoon than those that survived?", "Equation": "( 13.0 - ( 3.0 - 13.0 ) )", "Answer": 23.0, "Type": "Subtraction"}, {"ID": "chal-352", "Body": "<PERSON> had 9 marbles in his collection. He lost some marbles. If he has 4 marbles now", "Question": "How many marbles did he lose?", "Equation": "( 9.0 - 4.0 )", "Answer": 5.0, "Type": "Subtraction"}, {"ID": "chal-353", "Body": "They decided to hold the party in their backyard. They have 14 chairs for each set of table. If they have 9 sets of tables", "Question": "How many more chairs than tables do they have?", "Equation": "( ( 9.0 * 14.0 ) - 9.0 )", "Answer": 117.0, "Type": "Subtraction"}, {"ID": "chal-354", "Body": "He then went to see the oranges being harvested. He found out that they harvest 66 sacks per day.", "Question": "How many oranges do they harvest per day if each sack contains 28 oranges?", "Equation": "( 66.0 * 28.0 )", "Answer": 1848.0, "Type": "Multiplication"}, {"ID": "chal-355", "Body": "At the arcade <PERSON> had won some tickets. He used 12 tickets to buy some toys. If he still has 14 tickets left", "Question": "How many tickets did <PERSON> win at the arcade?", "Equation": "( 12.0 + 14.0 )", "Answer": 26.0, "Type": "Addition"}, {"ID": "chal-356", "Body": "<PERSON> could fit 10 action figures on each shelf in his room. His room has could hold 8 action figures.", "Question": "How many total shelves did his room have?", "Equation": "( 8.0 * 10.0 )", "Answer": 80.0, "Type": "Multiplication"}, {"ID": "chal-357", "Body": "<PERSON> and his dad went strawberry picking. Together they collected strawberries that weighed 24 pounds. On the way back <PERSON> lost 9 pounds of strawberries. <PERSON>'s strawberries now weighed 3 pounds.", "Question": "How much did his dad's strawberries weigh?", "Equation": "( 24.0 - ( 3.0 + 9.0 ) )", "Answer": 12.0, "Type": "Subtraction"}, {"ID": "chal-358", "Body": "<PERSON> is baking a cake. The recipe calls for 12 cups of flour and 13 cups of sugar. She already put in 2 cups of sugar.", "Question": "How many more cups of sugar does she need to add?", "Equation": "( 13.0 - 2.0 )", "Answer": 11.0, "Type": "Subtraction"}, {"ID": "chal-359", "Body": "Winter is almost here and most animals are migrating to warmer countries. There are 38 bird families living near the mountain. If 47 bird families flew away to africa and 94 bird families flew away to asia", "Question": "How many more bird families flew away to asia than those that flew away to africa?", "Equation": "( 94.0 - 47.0 )", "Answer": 47.0, "Type": "Subtraction"}, {"ID": "chal-360", "Body": "<PERSON> has 9 fewer peaches than <PERSON> and 18 more peaches than <PERSON>. <PERSON> has 16 peaches.", "Question": "How many more peaches does <PERSON> have than <PERSON>?", "Equation": "( 9.0 + 18.0 )", "Answer": 27.0, "Type": "Addition"}, {"ID": "chal-361", "Body": "<PERSON> did 51 push-ups and <PERSON> did 44 push-ups in gym class today.", "Question": "How many more push-ups did <PERSON> do than <PERSON>?", "Equation": "( 51.0 - 44.0 )", "Answer": 7.0, "Type": "Subtraction"}, {"ID": "chal-362", "Body": "16 people can ride the Ferris wheel in paradise park at the same time. If the Ferris wheel has 4 seats", "Question": "How many people can each seat hold?", "Equation": "( 16.0 / 4.0 )", "Answer": 4.0, "Type": "Common-Division"}, {"ID": "chal-363", "Body": "<PERSON> is baking a cake. The recipe calls for 6 cups of flour 8 cups of sugar and 7 cups of salt. She already put in 5 cups of flour.", "Question": "How many more cups of sugar than cups of salt does she need to add now?", "Equation": "( 8.0 - 7.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-364", "Body": "<PERSON> raised 16 goldfish and 71 catfish in the pond but stray cats loved eating them. Now she has 2 left.", "Question": "How many goldfish disappeared?", "Equation": "( 16.0 - 2.0 )", "Answer": 14.0, "Type": "Subtraction"}, {"ID": "chal-365", "Body": "<PERSON> picked 7 ripe apples from her tree. Now the tree has 5 apples still on it. If 3 of those are ripe and the rest are unripe", "Question": "How many unripe apples does the tree have now?", "Equation": "( 5.0 - 3.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-366", "Body": "<PERSON> did 44 push-ups in gym class today. <PERSON> did 58 more push-ups than zach<PERSON>.", "Question": "How many push-ups did <PERSON> and <PERSON> do altogether?", "Equation": "( ( 44.0 + 44.0 ) + 58.0 )", "Answer": 146.0, "Type": "Addition"}, {"ID": "chal-367", "Body": "If <PERSON> earns a total of $ 460 during 5 weeks of harvest", "Question": "How much money does he earn each week?", "Equation": "( 460.0 / 5.0 )", "Answer": 92.0, "Type": "Common-Division"}, {"ID": "chal-368", "Body": "Because of the decision <PERSON> asked the students to suggest specific types of food. If 228 students suggested adding mashed potatoes 337 suggested adding bacon to the menu and 23 suggested adding tomatoes", "Question": "How many more students suggested bacon than those that suggested tomatoes?", "Equation": "( 337.0 - 23.0 )", "Answer": 314.0, "Type": "Subtraction"}, {"ID": "chal-369", "Body": "<PERSON> spent $ 4 to buy books and $ 3 to buy pens. Now he has $ 12.", "Question": "How much did <PERSON> spend on books and pens?", "Equation": "( 4.0 + 3.0 )", "Answer": 7.0, "Type": "Addition"}, {"ID": "chal-370", "Body": "<PERSON> spent $ 17. Then he received $ 10 from his friend. Now he has $ 7.", "Question": "How much did <PERSON> have before he spent his money?", "Equation": "( ( 17.0 - 10.0 ) + 7.0 )", "Answer": 14.0, "Type": "Addition"}, {"ID": "chal-371", "Body": "<PERSON> played tag with 9 kids on monday, 7 kids on tuesday and 96 kids on wednesday.", "Question": "How many more kids did she play with on monday than on tuesday?", "Equation": "( 9.0 - 7.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-372", "Body": "In a school there are 402 more girls than boys. If there are 739 girls", "Question": "How many boys are there in that school?", "Equation": "( 739.0 - 402.0 )", "Answer": 337.0, "Type": "Subtraction"}, {"ID": "chal-373", "Body": "For <PERSON>'s birthday she received 2 dollars from her mom. Her dad gave her 4 more dollars. If she spent 3 dollars.", "Question": "How much money did she still have?", "Equation": "( ( 2.0 + 4.0 ) - 3.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-374", "Body": "6 red peaches, 90 yellow peaches and 16 green peaches are in the basket.", "Question": "How many red and green peaches are in the basket?", "Equation": "( 6.0 + 16.0 )", "Answer": 22.0, "Type": "Addition"}, {"ID": "chal-375", "Body": "<PERSON> has 37 blocks. He uses 33 blocks to build a tower and 13 blocks to build a house.", "Question": "How many more blocks did he use to build the tower than he did to build the house?", "Equation": "( 33.0 - 13.0 )", "Answer": 20.0, "Type": "Subtraction"}, {"ID": "chal-376", "Body": "The Razorback shop makes $ 76 dollars off each jersey and $ 204 off each t-shirt. During the Arkansas and Texas tech game they sold 158 t-shirts and 2 jerseys.", "Question": "How much money did they make from selling the jerseys?", "Equation": "( 76.0 * 2.0 )", "Answer": 152.0, "Type": "Multiplication"}, {"ID": "chal-377", "Body": "<PERSON> made 8 cakes. He bought 139 new cakes and sold 145 cakes.", "Question": "How many more cakes did baker sell than those he bought?", "Equation": "( 145.0 - 139.0 )", "Answer": 6.0, "Type": "Subtraction"}, {"ID": "chal-378", "Body": "<PERSON> received 6 emails and 8 letters in the morning. He then received 2 emails and 7 letters in the afternoon.", "Question": "How many more letters did <PERSON> receive in the morning than in the afternoon?", "Equation": "( 8.0 - 7.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-379", "Body": "<PERSON> has 3 fewer peaches and 10 more apples than <PERSON>. <PERSON> has 12 peaches and 15 apples.", "Question": "How many more apples than peaches does <PERSON> have?", "Equation": "( 15.0 - 12.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-380", "Body": "<PERSON> had to complete 5 pages of math homework and 2 pages of reading homework.", "Question": "How many more pages of math homework than reading homework did she have?", "Equation": "( 5.0 - 2.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-381", "Body": "<PERSON> was reading through his favorite book. The book had 41 chapters, each with the same number of pages. It has a total of 450 pages. It took <PERSON> 30 days to finish the book.", "Question": "How many pages did he read per day?", "Equation": "( 450.0 / 30.0 )", "Answer": 15.0, "Type": "Common-Division"}, {"ID": "chal-382", "Body": "<PERSON>'s room is 19 feet wide and 20 feet long.", "Question": "How much longer is her room than it is wide?", "Equation": "( 20.0 - 19.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-383", "Body": "<PERSON> was reading through some books. Each book had 249 pages and it took <PERSON> 3 days to finish each book.", "Question": "How many pages did he read per day?", "Equation": "( 249.0 / 3.0 )", "Answer": 83.0, "Type": "Common-Division"}, {"ID": "chal-384", "Body": "<PERSON> got a box of 601 crayons and 406 erasers for his birthday. At the end of the school year he only had 336 crayons left while not having lost a single eraser.", "Question": "How many more erasers than crayons did he have left?", "Equation": "( 406.0 - 336.0 )", "Answer": 70.0, "Type": "Subtraction"}, {"ID": "chal-385", "Body": "The ring toss game at the carnival made 120 dollars in the first 20 days and 66 dollars in the remaining 16 days.", "Question": "How much did they make per day in the first 20 days at the carnival?", "Equation": "( 120.0 / 20.0 )", "Answer": 6.0, "Type": "Common-Division"}, {"ID": "chal-386", "Body": "<PERSON> is baking a cake. The recipe calls for 5 cups of sugar and 14 cups of flour. She already put in 11 cups of flour.", "Question": "How many more cups of sugar than cups of flour does she need to add now?", "Equation": "( 5.0 - ( 14.0 - 11.0 ) )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-387", "Body": "<PERSON> is making bead necklaces for her 44 friends. She made 26 necklaces and each necklace takes 2 beads.", "Question": "How many beads did <PERSON> have?", "Equation": "( 26.0 * 2.0 )", "Answer": 52.0, "Type": "Multiplication"}, {"ID": "chal-388", "Body": "<PERSON> scored a total of 21 points playing some games. Is she scored 7 points in each game.", "Question": "How many games did she play?", "Equation": "( 21.0 / 7.0 )", "Answer": 3.0, "Type": "Common-Division"}, {"ID": "chal-389", "Body": "<PERSON> has 10 apples. <PERSON> has 2 apples.", "Question": "How many more apples does <PERSON> have than <PERSON>?", "Equation": "( 10.0 - 2.0 )", "Answer": 8.0, "Type": "Subtraction"}, {"ID": "chal-390", "Body": "<PERSON><PERSON> bought 200 water bottles and 256 soda bottles when they were on sale. If she drank 312 water bottles and 4 soda bottles a day", "Question": "How many days would the soda bottles last?", "Equation": "( 256.0 / 4.0 )", "Answer": 64.0, "Type": "Common-Division"}, {"ID": "chal-391", "Body": "<PERSON> had 5 action figures and 2 books on a shelf in his room. Later he added 9 more books to the shelf.", "Question": "How many more books than action figures were on his shelf?", "Equation": "( ( 2.0 + 9.0 ) - 5.0 )", "Answer": 6.0, "Type": "Subtraction"}, {"ID": "chal-392", "Body": "<PERSON>'s hair was 19 inches long. If he grew 18 more inches", "Question": "How long is his hair now?", "Equation": "( 19.0 + 18.0 )", "Answer": 37.0, "Type": "Addition"}, {"ID": "chal-393", "Body": "<PERSON> collects bottle caps and wrappers. He found 82 wrappers and 29 bottle caps at the park. Now he has 42 bottle caps and 61 wrappers in his collection.", "Question": "How many bottle caps did danny have at first?", "Equation": "( 42.0 - 29.0 )", "Answer": 13.0, "Type": "Subtraction"}, {"ID": "chal-394", "Body": "If you had 4 bags with equal number of cookies and 36 cookies in total", "Question": "How many cookies does each bag have?", "Equation": "( 36.0 / 4.0 )", "Answer": 9.0, "Type": "Common-Division"}, {"ID": "chal-395", "Body": "White t - shirts can be purchased in packages of 13. If mom buys 39 white t - shirts", "Question": "How many packages will she have?", "Equation": "( 39.0 / 13.0 )", "Answer": 3.0, "Type": "Common-Division"}, {"ID": "chal-396", "Body": "In a school there are 706 girls and 222 boys. 418 more girls joined the school.", "Question": "How many pupils are there in the school now?", "Equation": "( ( 706.0 + 222.0 ) + 418.0 )", "Answer": 1346.0, "Type": "Addition"}, {"ID": "chal-397", "Body": "For the walls of the house <PERSON> would use large planks of wood. Each plank needs 2 pieces of nails to be secured and he would use 16 planks.", "Question": "How many nails does <PERSON> need for the house wall?", "Equation": "( 16.0 * 2.0 )", "Answer": 32.0, "Type": "Multiplication"}, {"ID": "chal-398", "Body": "At the arcade <PERSON> had won 13 tickets. If he used 8 to buy some toys and 18 more to buy some clothes", "Question": "How many more tickets did <PERSON> use to buy clothes than he did to buy toys?", "Equation": "( 18.0 - 8.0 )", "Answer": 10.0, "Type": "Subtraction"}, {"ID": "chal-399", "Body": "<PERSON> and his dad went strawberry picking. Together their strawberries weighed 35 pounds. <PERSON>'s strawberries weighed 13 pounds.", "Question": "How much more did his dad's strawberries weigh than his?", "Equation": "( ( 35.0 - 13.0 ) - 13.0 )", "Answer": 9.0, "Type": "Subtraction"}, {"ID": "chal-400", "Body": "An industrial machine can make 3 shirts a minute. It made 4 shirts yesterday and 8 shirts today.", "Question": "How many minutes did the machine work in all?", "Equation": "( ( 4.0 + 8.0 ) / 3.0 )", "Answer": 4.0, "Type": "Common-Division"}, {"ID": "chal-401", "Body": "19 red peaches, 11 yellow peaches and 12 green peaches are in the basket.", "Question": "How many more red peaches than yellow peaches are in the basket?", "Equation": "( 19.0 - 11.0 )", "Answer": 8.0, "Type": "Subtraction"}, {"ID": "chal-402", "Body": "For <PERSON>'s birthday she received 8 dollars from her mom. Her dad gave her 5 more dollars. If she spent 4 dollars.", "Question": "How much more money did she receive from her mom than she did from her dad?", "Equation": "( 8.0 - 5.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-403", "Body": "The ring toss game at the carnival made the same amount of money each day. In total in 30 days they earned 420 dollars. Together with game they earned 22 dollars.", "Question": "How much did ring toss game make per day?", "Equation": "( 420.0 / 30.0 )", "Answer": 14.0, "Type": "Common-Division"}, {"ID": "chal-404", "Body": "<PERSON> had 16 apps and 77 files on his phone. After deleting some apps and files he had 5 apps and 23 files left.", "Question": "How many apps did he delete?", "Equation": "( 16.0 - 5.0 )", "Answer": 11.0, "Type": "Subtraction"}, {"ID": "chal-405", "Body": "<PERSON><PERSON> had 39 sweet cookies and 6 salty cookies. He ate 23 salty cookies and 32 sweet cookies.", "Question": "How many more sweet cookies than salty cookies did he eat?", "Equation": "( 32.0 - 23.0 )", "Answer": 9.0, "Type": "Subtraction"}, {"ID": "chal-406", "Body": "<PERSON> grew 13 trees in her backyard. After a typhoon 6 died.", "Question": "How many more trees survived the typhoon than those that died?", "Equation": "( ( 13.0 - 6.0 ) - 6.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-407", "Body": "<PERSON> played tag with 18 kids on monday. She played tag with 10 kids on tuesday.", "Question": "How many more kids did she play with on monday than on tuesday?", "Equation": "( 18.0 - 10.0 )", "Answer": 8.0, "Type": "Subtraction"}, {"ID": "chal-408", "Body": "<PERSON> has 46 nintendo games and 132 sony games.", "Question": "How many sony games does she need to give away so that she will have 31 sony games left?", "Equation": "( 132.0 - 31.0 )", "Answer": 101.0, "Type": "Subtraction"}, {"ID": "chal-409", "Body": "5 red peaches and 11 green peaches are in the basket.", "Question": "How many more green peaches than red peaches are in the basket?", "Equation": "( 11.0 - 5.0 )", "Answer": 6.0, "Type": "Subtraction"}, {"ID": "chal-410", "Body": "<PERSON> collects baseball cards. She gave 301 of her cards to <PERSON> and now has 154 cards left..", "Question": "How many cards did <PERSON> have initially?", "Equation": "( 154.0 + 301.0 )", "Answer": 455.0, "Type": "Addition"}, {"ID": "chal-411", "Body": "<PERSON> played tag with 15 kids on monday, 18 kids on tuesday and 97 kids on wednesday.", "Question": "How many kids did she play with on monday and tuesday?", "Equation": "( 15.0 + 18.0 )", "Answer": 33.0, "Type": "Addition"}, {"ID": "chal-412", "Body": "<PERSON> had to complete 9 pages of math homework, 2 pages of reading homework and 96 more pages of biology homework.", "Question": "How many more pages of math homework than reading homework did she have?", "Equation": "( 9.0 - 2.0 )", "Answer": 7.0, "Type": "Subtraction"}, {"ID": "chal-413", "Body": "36 campers went rowing in the morning 13 campers went rowing in the afternoon and 49 campers went rowing in the evening.", "Question": "How many campers went rowing in all?", "Equation": "( ( 36.0 + 13.0 ) + 49.0 )", "Answer": 98.0, "Type": "Addition"}, {"ID": "chal-414", "Body": "<PERSON> had 4 marbles in his collection. He lost 16 marbles and found 8 new ones.", "Question": "How many more marbles did he lose than those he found?", "Equation": "( 16.0 - 8.0 )", "Answer": 8.0, "Type": "Subtraction"}, {"ID": "chal-415", "Body": "<PERSON> collects bottle caps and wrappers. He found 11 bottle caps and 28 wrappers at the park. Now he has 68 bottle caps and 51 wrappers in his collection.", "Question": "How many more wrappers than bottle caps did danny find at the park?", "Equation": "( 28.0 - 11.0 )", "Answer": 17.0, "Type": "Subtraction"}, {"ID": "chal-416", "Body": "<PERSON> had 59 files and 15 apps on his phone. After deleting some apps and files he had 12 apps and 30 files left.", "Question": "How many apps did he delete?", "Equation": "( 15.0 - 12.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-417", "Body": "<PERSON> had 71 books. After selling some in a garage sale he bought 38 new ones. If he has 116 books now", "Question": "How many more books did he buy than he sold?", "Equation": "( 116.0 - 71.0 )", "Answer": 45.0, "Type": "Subtraction"}, {"ID": "chal-418", "Body": "<PERSON> brought 7 balloons and 5 balls while <PERSON> brought 6 balloons and 4 balls to the park.", "Question": "How many balls did <PERSON> and <PERSON> have in the park?", "Equation": "( 5.0 + 4.0 )", "Answer": 9.0, "Type": "Addition"}, {"ID": "chal-419", "Body": "You have 104 dollars.", "Question": "How many packs of dvds can you buy if each pack costs 26 dollars?", "Equation": "( 104.0 / 26.0 )", "Answer": 4.0, "Type": "Common-Division"}, {"ID": "chal-420", "Body": "<PERSON> has 20 nintendo games and 70 sony games.", "Question": "How many nintendo games does she need to give away so that she will have 12 nintendo games left?", "Equation": "( 20.0 - 12.0 )", "Answer": 8.0, "Type": "Subtraction"}, {"ID": "chal-421", "Body": "<PERSON> played tag with 11 kids on monday. She played tag with 12 kids on tuesday.", "Question": "How many more kids did she play with on tuesday than on monday?", "Equation": "( 12.0 - 11.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-422", "Body": "<PERSON> was collecting cans for recycling. On monday she had 8 bags of cans. The next day she found 7 more bags worth of cans.", "Question": "How many more bags did she have on monday than she found on the next day?", "Equation": "( 8.0 - 7.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-423", "Body": "There are 3941 skittles in <PERSON>'s skittles collection. <PERSON> also has 4950 erasers. If the erasers are organized into 495 groups", "Question": "How big is each group?", "Equation": "( 4950.0 / 495.0 )", "Answer": 10.0, "Type": "Common-Division"}, {"ID": "chal-424", "Body": "Because of the decision <PERSON> asked the students to suggest specific types of food. 457 students suggested adding mashed potatoes while others suggested adding bacon to the menu. If 63 more students suggested adding mashed potatoes than those that suggested bacon", "Question": "How many students suggested bacon?", "Equation": "( 457.0 - 63.0 )", "Answer": 394.0, "Type": "Subtraction"}, {"ID": "chal-425", "Body": "The Razorback t-shirt shop sells each t-shirt for $ 51 dollars. During the Arkansas and Texas tech game they offered a discount of $ 8 per t-shirt and sold 130 t-shirts.", "Question": "How much money did they make from selling the t-shirts?", "Equation": "( ( 51.0 - 8.0 ) * 130.0 )", "Answer": 5590.0, "Type": "Multiplication"}, {"ID": "chal-426", "Body": "5 storks and 3 birds were sitting on the fence. 4 more birds came to join them.", "Question": "How many more birds than storks are sitting on the fence?", "Equation": "( ( 3.0 + 4.0 ) - 5.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-427", "Body": "<PERSON> had 29 crackers and 30 cakes. If <PERSON> gave equal numbers of crackers and cakes to his 2 friends", "Question": "How many cakes did each person eat?", "Equation": "( 30.0 / 2.0 )", "Answer": 15.0, "Type": "Common-Division"}, {"ID": "chal-428", "Body": "There were 10 roses in the vase. <PERSON> cut 8 more roses from her flower garden and put them in the vase.", "Question": "How many roses are there in the vase now?", "Equation": "( 10.0 + 8.0 )", "Answer": 18.0, "Type": "Addition"}, {"ID": "chal-429", "Body": "<PERSON> was reading through his favorite book. The book had 555 pages equally distributed over 5 chapters. It took <PERSON> 220 days to finish the book.", "Question": "How many pages are in each chapter?", "Equation": "( 555.0 / 5.0 )", "Answer": 111.0, "Type": "Common-Division"}, {"ID": "chal-430", "Body": "<PERSON><PERSON> had 22 sweet cookies and 75 salty cookies. He ate 15 sweet cookies and 54 salty cookies.", "Question": "How many sweet cookies did <PERSON><PERSON> have left?", "Equation": "( 22.0 - 15.0 )", "Answer": 7.0, "Type": "Subtraction"}, {"ID": "chal-431", "Body": "<PERSON> is baking a cake. The recipe calls for 9 cups of flour and 5 cups of sugar. She already put in 3 cups of flour and 62 cups of sugar.", "Question": "How many more cups of flour does she need to add?", "Equation": "( 9.0 - 3.0 )", "Answer": 6.0, "Type": "Subtraction"}, {"ID": "chal-432", "Body": "Each pack of dvds costs 107 dollars. If there is a discount of 106 dollars on each pack", "Question": "How many packs of dvds can you buy with 93 dollars?", "Equation": "( 93.0 / ( 107.0 - 106.0 ) )", "Answer": 93.0, "Type": "Common-Division"}, {"ID": "chal-433", "Body": "There are 8 different books and 10 different movies in the ' crazy silly school ' series. If you read 19 of the books and watched 61 of the movies", "Question": "How many more movies than books are there in the ' crazy silly school ' series?", "Equation": "( 10.0 - 8.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-434", "Body": "<PERSON> ate 33 pieces of candy. Then he ate 4 more. He also ate 14 pieces of chocolate.", "Question": "How many pieces of candy and chocolate did <PERSON> eat altogether?", "Equation": "( ( 33.0 + 4.0 ) + 14.0 )", "Answer": 51.0, "Type": "Addition"}, {"ID": "chal-435", "Body": "<PERSON> took a look at his books as well. He has 7 bookshelves with each having the same number of books. If he has a total of 28 books", "Question": "How many books are there in each bookshelf?", "Equation": "( 28.0 / 7.0 )", "Answer": 4.0, "Type": "Common-Division"}, {"ID": "chal-436", "Body": "<PERSON> scored 12 points in each game. If she scored a total of 36 points", "Question": "How many games did she play?", "Equation": "( 36.0 / 12.0 )", "Answer": 3.0, "Type": "Common-Division"}, {"ID": "chal-437", "Body": "<PERSON> had to complete 8 pages of math homework. If she had to complete 6 more pages of reading homework than math homework", "Question": "How many pages of reading homework did she have to complete?", "Equation": "( 8.0 + 6.0 )", "Answer": 14.0, "Type": "Addition"}, {"ID": "chal-438", "Body": "<PERSON> received 6 emails and sent 91 letters in the morning. He then received 2 emails and sent 74 letters in the afternoon.", "Question": "How many emails did <PERSON> receive in the day?", "Equation": "( 6.0 + 2.0 )", "Answer": 8.0, "Type": "Addition"}, {"ID": "chal-439", "Body": "At the arcade <PERSON> had won 7 tickets. He used some tickets to buy toys. If he still has 2 tickets left", "Question": "How many tickets did <PERSON> use to buy toys?", "Equation": "( 7.0 - 2.0 )", "Answer": 5.0, "Type": "Subtraction"}, {"ID": "chal-440", "Body": "Every day <PERSON> spends 7 hours on learning english and some more hours on learning chinese. If he spends 2 hours more on learning english than on learning chinese", "Question": "How many hours does he spend on learning chinese?", "Equation": "( 7.0 - 2.0 )", "Answer": 5.0, "Type": "Subtraction"}, {"ID": "chal-441", "Body": "<PERSON> had 91 ds games and her new friends had 88 games and old friends had 53 games.", "Question": "How many games do her friends have in all?", "Equation": "( 88.0 + 53.0 )", "Answer": 141.0, "Type": "Addition"}, {"ID": "chal-442", "Body": "<PERSON> got a box of some crayons for his birthday. During the school year he gave 52 crayons to his friends while he lost 535 crayons. If he only had 492 crayons left", "Question": "How many crayons had been lost or given away?", "Equation": "( 52.0 + 535.0 )", "Answer": 587.0, "Type": "Addition"}, {"ID": "chal-443", "Body": "Being his favorite, he saved checking on the grapevines for his last stop. He was told by 294 of the pickers that they fill 244 drums of grapes per day and 47 drums of raspberries per day.", "Question": "How many drums of grapes would be filled in 146 days?", "Equation": "( 244.0 * 146.0 )", "Answer": 35624.0, "Type": "Multiplication"}, {"ID": "chal-444", "Body": "<PERSON> has 7 fewer peaches than <PERSON> and 8 more peaches than <PERSON>. <PERSON> has 17 peaches.", "Question": "How many peaches does <PERSON> have?", "Equation": "( ( 17.0 - 7.0 ) - 8.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-445", "Body": "2 red peaches, 6 yellow peaches and 14 green peaches are in the basket.", "Question": "How many more green peaches than yellow peaches are in the basket?", "Equation": "( 14.0 - 6.0 )", "Answer": 8.0, "Type": "Subtraction"}, {"ID": "chal-446", "Body": "A book has 31 chapters. Each chapter is 61 pages long.", "Question": "How many pages does the book have altogether?", "Equation": "( 31.0 * 61.0 )", "Answer": 1891.0, "Type": "Multiplication"}, {"ID": "chal-447", "Body": "<PERSON>'s mother made cookies for guests. Each guest had 2 cookies. If she prepared a total of 10 cookies", "Question": "How many guests did she prepare cookies for?", "Equation": "( 10.0 / 2.0 )", "Answer": 5.0, "Type": "Common-Division"}, {"ID": "chal-448", "Body": "Every day <PERSON> spends a total of 3 hours on learning english and chinese. If he spends 2 hours on learning english everyday", "Question": "How many hours does he spend on learning chinese?", "Equation": "( 3.0 - 2.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-449", "Body": "Being his favorite, he saved checking on the grapevines for his last stop. He was told by 235 of the pickers that they fill 100 drums of raspberries per day and 221 drums of grapes per day.", "Question": "How many drums of grapes would be filled in 77 days?", "Equation": "( 221.0 * 77.0 )", "Answer": 17017.0, "Type": "Multiplication"}, {"ID": "chal-450", "Body": "<PERSON> ate 38 pieces of candy. Then he ate 36 more. He also ate 16 pieces of chocolate.", "Question": "How many more pieces of candy than chocolate did <PERSON> eat?", "Equation": "( ( 38.0 + 36.0 ) - 16.0 )", "Answer": 58.0, "Type": "Subtraction"}, {"ID": "chal-451", "Body": "He then went to see the oranges being harvested. He found out that they harvested 54 sacks of oranges.", "Question": "How many days did it take to harvest them if they harvested 18 sacks of oranges per day?", "Equation": "( 54.0 / 18.0 )", "Answer": 3.0, "Type": "Common-Division"}, {"ID": "chal-452", "Body": "A mailman has to give 25 pieces of junk mail to each block. If he gives 5 mails to each house in a block", "Question": "How many houses are there in a block?", "Equation": "( 25.0 / 5.0 )", "Answer": 5.0, "Type": "Common-Division"}, {"ID": "chal-453", "Body": "A waiter had 8 customers. After some left he still had 5 customers. Then he got 99 new customers", "Question": "How many customers left?", "Equation": "( 8.0 - 5.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-454", "Body": "<PERSON> has 3 fewer peaches and 4 more apples than <PERSON>. <PERSON> has 19 apples and 15 peaches.", "Question": "How many more apples than peaches does <PERSON> have?", "Equation": "( 19.0 - 15.0 )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-455", "Body": "Every day <PERSON> spends 6 hours on learning english and 7 hours on learning chinese.", "Question": "How many more hours does he spend on learning chinese than he does on learning english?", "Equation": "( 7.0 - 6.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-456", "Body": "4 birds and 46 storks were sitting on the fence. 6 more birds came to join them.", "Question": "How many birds are sitting on the fence?", "Equation": "( 4.0 + 6.0 )", "Answer": 10.0, "Type": "Addition"}, {"ID": "chal-457", "Body": "<PERSON><PERSON> had 28 cookies. He ate some of them and had 7 cookies left.", "Question": "How many cookies did <PERSON><PERSON> eat?", "Equation": "( 28.0 - 7.0 )", "Answer": 21.0, "Type": "Subtraction"}, {"ID": "chal-458", "Body": "<PERSON> had 8 crackers to give to friends. If <PERSON> gave 2 crackers to each his friends", "Question": "How many friends did he give crackers to?", "Equation": "( 8.0 / 2.0 )", "Answer": 4.0, "Type": "Common-Division"}, {"ID": "chal-459", "Body": "41 campers went rowing and 4 campers went hiking in the morning. 26 campers went rowing in the afternoon.", "Question": "How many campers went rowing and hiking in all?", "Equation": "( ( 41.0 + 26.0 ) + 4.0 )", "Answer": 71.0, "Type": "Addition"}, {"ID": "chal-460", "Body": "<PERSON><PERSON> had 13 cookies. He ate 2 of them. Then he bought 36 more cookies", "Question": "How many more cookies did he buy than those he ate?", "Equation": "( 36.0 - 2.0 )", "Answer": 34.0, "Type": "Subtraction"}, {"ID": "chal-461", "Body": "<PERSON> did 19 push-ups in gym class today. <PERSON> did 39 more push-ups than <PERSON>. <PERSON> did 13 push-ups less than david.", "Question": "How many push-ups did <PERSON> do?", "Equation": "( 19.0 + 39.0 )", "Answer": 58.0, "Type": "Addition"}, {"ID": "chal-462", "Body": "<PERSON> is baking a cake. The recipe calls for 12 cups of flour 14 cups of sugar and 7 cups of salt. She already put in 2 cups of flour.", "Question": "How many more cups of flour than cups of salt does she need to add now?", "Equation": "( ( 12.0 - 2.0 ) - 7.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-463", "Body": "There were 15 roses in the vase. <PERSON> threw away 33 roses from the vase and cut some more new roses from her flower garden to put in the vase. There are now 17 roses in the vase.", "Question": "How many more roses did she cut than those she threw away?", "Equation": "( 17.0 - 15.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-464", "Body": "Last week <PERSON> had 49 dollars and <PERSON> had 3 dollars. <PERSON> washed cars over the weekend and now has 112 dollars. <PERSON> delivered newspapers and now has 63 dollars.", "Question": "How much money did <PERSON> earn by delivering newspapers?", "Equation": "( 63.0 - 3.0 )", "Answer": 60.0, "Type": "Subtraction"}, {"ID": "chal-465", "Body": "<PERSON> has 7 fewer peaches than <PERSON> and 9 more peaches than <PERSON>. <PERSON> has 16 peaches.", "Question": "How many peaches does <PERSON> have?", "Equation": "( 16.0 - 7.0 )", "Answer": 9.0, "Type": "Subtraction"}, {"ID": "chal-466", "Body": "46 campers went rowing on a day. 43 campers went rowing in the morning and some more campers went rowing in the afternoon.", "Question": "How many campers went rowing in the afternoon?", "Equation": "( 46.0 - 43.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-467", "Body": "The ring toss game at the carnival made 382 dollars in the first 44 days and 374 dollars in the remaining 10 days.", "Question": "How much did they make in all?", "Equation": "( 382.0 + 374.0 )", "Answer": 756.0, "Type": "Addition"}, {"ID": "chal-468", "Body": "A book has 2 chapters. The second chapter is 36 pages long. If there are a total of 67 pages in the book", "Question": "How many pages are in the first chapter?", "Equation": "( 67.0 - 36.0 )", "Answer": 31.0, "Type": "Subtraction"}, {"ID": "chal-469", "Body": "<PERSON> was sending out birthday invitations to her friends. Each package of invitations she bought had 3 invitations in it and she bought 2 packs. If she wants to invite 9 friends", "Question": "How many extra invitations will she need to buy?", "Equation": "( 9.0 - ( 3.0 * 2.0 ) )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-470", "Body": "<PERSON> did 15 push-ups in gym class today. <PERSON> did 39 more push-ups than <PERSON>. <PERSON> did 9 push-ups less than <PERSON>.", "Question": "How many more push-ups did <PERSON> do than <PERSON>?", "Equation": "( 39.0 - 9.0 )", "Answer": 30.0, "Type": "Subtraction"}, {"ID": "chal-471", "Body": "<PERSON> had 2 more marbles than <PERSON>. <PERSON> lost some of his marbles at the playground. Now <PERSON> has 19 more marbles than doug.", "Question": "How many marbles did <PERSON> lose?", "Equation": "( 19.0 - 2.0 )", "Answer": 17.0, "Type": "Subtraction"}, {"ID": "chal-472", "Body": "There are 896 skittles in <PERSON>'s skittles collection. <PERSON> also has 517 erasers and 90 scales. If the skittles are organized into 8 groups", "Question": "How big is each group?", "Equation": "( 896.0 / 8.0 )", "Answer": 112.0, "Type": "Common-Division"}, {"ID": "chal-473", "Body": "<PERSON> wants to split a collection of eggs into groups of 5. <PERSON> has 10 marbles and 15 eggs.", "Question": "How many groups will be created?", "Equation": "( 15.0 / 5.0 )", "Answer": 3.0, "Type": "Common-Division"}, {"ID": "chal-474", "Body": "<PERSON> had 95 pens and 153 books. After selling some books and pens in a garage sale he had 13 books and 23 pens left.", "Question": "How many books did he sell in the garage sale?", "Equation": "( 153.0 - 13.0 )", "Answer": 140.0, "Type": "Subtraction"}, {"ID": "chal-475", "Body": "<PERSON> was reading through his favorite book. The book had 612 pages equally distributed over 24 chapters. It took <PERSON> 6 days to finish the book.", "Question": "How many pages did he read per day?", "Equation": "( 612.0 / 6.0 )", "Answer": 102.0, "Type": "Common-Division"}, {"ID": "chal-476", "Body": "There are 10 peaches distributed equally in some baskets. If each basket has 4 red peaches and 6 green peaches", "Question": "How many baskets of peaches are there?", "Equation": "( 10.0 / ( 4.0 + 6.0 ) )", "Answer": 1.0, "Type": "Common-Division"}, {"ID": "chal-477", "Body": "<PERSON> earns $ 2 every week during the harvest. If he earns a total of $ 178", "Question": "How many weeks did the harvest last?", "Equation": "( 178.0 / 2.0 )", "Answer": 89.0, "Type": "Common-Division"}, {"ID": "chal-478", "Body": "<PERSON> collects bottle caps. He threw away 54 of the old ones at the park while he found 48 bottle caps new ones. Now he has 52 bottle caps in his collection.", "Question": "How many more bottle caps did danny throw away than those he found at the park?", "Equation": "( 54.0 - 48.0 )", "Answer": 6.0, "Type": "Subtraction"}, {"ID": "chal-479", "Body": "He then went to see the oranges being harvested. He found out that they harvest 31 sacks of ripe oranges and 24 sacks of unripe oranges per day.", "Question": "How many sacks of unripe oranges will they have after 45 days of harvest?", "Equation": "( 24.0 * 45.0 )", "Answer": 1080.0, "Type": "Multiplication"}, {"ID": "chal-480", "Body": "The grasshopper and the frog had a jumping contest. The grasshopper jumped 35 inches. The grasshopper jumped 4 inches farther than the frog.", "Question": "How much did they jump altogether?", "Equation": "( 35.0 + ( 35.0 - 4.0 ) )", "Answer": 66.0, "Type": "Subtraction"}, {"ID": "chal-481", "Body": "The Razorback shop makes $ 67 dollars off each t-shirt and $ 165 off each jersey. During the Arkansas and Texas tech game they sold 74 t-shirts and 156 jerseys.", "Question": "How much money did they make from selling the jerseys?", "Equation": "( 165.0 * 156.0 )", "Answer": 25740.0, "Type": "Multiplication"}, {"ID": "chal-482", "Body": "After resting they decided to go for a swim. The depth of the water is 5 times <PERSON>'s height. <PERSON> is 11 feet shorter than <PERSON>. If <PERSON> stands at 12 feet", "Question": "How deep was the water?", "Equation": "( 12.0 * 5.0 )", "Answer": 60.0, "Type": "Multiplication"}, {"ID": "chal-483", "Body": "There are 466 pots. Each pot has 53 flowers and 181 sticks in it.", "Question": "How many flowers and sticks are there in all?", "Equation": "( ( 53.0 + 181.0 ) * 466.0 )", "Answer": 109044.0, "Type": "Multiplication"}, {"ID": "chal-484", "Body": "<PERSON> got a box of 110 crayons for his birthday. During the school year he gave 90 crayons to his friends while he lost 412 crayons.", "Question": "How many more crayons did he lose than those he gave to his friends?", "Equation": "( 412.0 - 90.0 )", "Answer": 322.0, "Type": "Subtraction"}, {"ID": "chal-485", "Body": "In a school there are 569 girls and 236 boys.", "Question": "How many more girls than boys does the school have?", "Equation": "( 569.0 - 236.0 )", "Answer": 333.0, "Type": "Subtraction"}, {"ID": "chal-486", "Body": "He then went to see the oranges being harvested. He found out that they harvest 65 sacks of unripe oranges and 46 sacks of ripe oranges per day.", "Question": "How many sacks of unripe oranges will they have after 6 days of harvest?", "Equation": "( 65.0 * 6.0 )", "Answer": 390.0, "Type": "Multiplication"}, {"ID": "chal-487", "Body": "The Ferris wheel in paradise park has 6 seats. Each seat can hold 14 people. If there are 16 people on the wheel right now", "Question": "How many more people can join the wheel?", "Equation": "( ( 6.0 * 14.0 ) - 16.0 )", "Answer": 68.0, "Type": "Subtraction"}, {"ID": "chal-488", "Body": "<PERSON><PERSON> bought 153 water bottles when they were on sale. She drank the same number of bottles each day. If the bottles lasted for 17 days", "Question": "How many bottles did she drink each day?", "Equation": "( 153.0 / 17.0 )", "Answer": 9.0, "Type": "Common-Division"}, {"ID": "chal-489", "Body": "<PERSON> has 21 packages of gum and 45 packages of candy. There are 9 pieces in each package.", "Question": "How many pieces of candies does <PERSON> have?", "Equation": "( 45.0 * 9.0 )", "Answer": 405.0, "Type": "Multiplication"}, {"ID": "chal-490", "Body": "After <PERSON> visited a supermarket there were 29 dollars left. If there were 54 dollars in her wallet initially", "Question": "How much did she spend?", "Equation": "( 54.0 - 29.0 )", "Answer": 25.0, "Type": "Subtraction"}, {"ID": "chal-491", "Body": "<PERSON> grew 20 trees in her backyard. After a typhoon 4 trees were left.", "Question": "How many trees died in the typhoon?", "Equation": "( 20.0 - 4.0 )", "Answer": 16.0, "Type": "Subtraction"}, {"ID": "chal-492", "Body": "<PERSON> has 7 fewer peaches than <PERSON> who has 14 more peaches than <PERSON>. <PERSON> has 15 peaches.", "Question": "How many peaches does <PERSON> have?", "Equation": "( 15.0 - 7.0 )", "Answer": 8.0, "Type": "Subtraction"}, {"ID": "chal-493", "Body": "<PERSON> earns $ 491 every week during the 1181 weeks of harvest. If he has to pay $ 216 rent every week", "Question": "How much money will have at the end of the harvest season?", "Equation": "( ( 491.0 - 216.0 ) * 1181.0 )", "Answer": 324775.0, "Type": "Multiplication"}, {"ID": "chal-494", "Body": "<PERSON> had 51 books and 106 pens. After selling some books and pens in a garage sale he had 82 books and 14 pens left.", "Question": "How many pens did he sell in the garage sale?", "Equation": "( 106.0 - 14.0 )", "Answer": 92.0, "Type": "Subtraction"}, {"ID": "chal-495", "Body": "<PERSON> did 44 push-ups and 17 crunches in gym class today. <PERSON> did 29 more push-ups but 13 less crunches than zach<PERSON>.", "Question": "How many crunches did <PERSON> do?", "Equation": "( 17.0 - 13.0 )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-496", "Body": "<PERSON> had 42 pens and 143 books. After selling some books and pens in a garage sale he had 113 books and 19 pens left.", "Question": "How many pens did he sell in the garage sale?", "Equation": "( 42.0 - 19.0 )", "Answer": 23.0, "Type": "Subtraction"}, {"ID": "chal-497", "Body": "<PERSON> collects bottle caps and wrappers. He found 71 bottle caps and 24 wrappers at the park. Now he has 28 bottle caps and 16 wrappers in his collection.", "Question": "How many more bottle caps than wrappers does danny have now?", "Equation": "( 28.0 - 16.0 )", "Answer": 12.0, "Type": "Subtraction"}, {"ID": "chal-498", "Body": "<PERSON> has $ 4. He bought 99 candy bar for $ 3 each one costing the same amount of money.", "Question": "How much money is left?", "Equation": "( 4.0 - 3.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-499", "Body": "5 children were riding on the bus. At the bus stop 63 children got off the bus while some more got on the bus. Then there were 14 children altogether on the bus.", "Question": "How many more children got on the bus than those that got off?", "Equation": "( 14.0 - 5.0 )", "Answer": 9.0, "Type": "Subtraction"}, {"ID": "chal-500", "Body": "<PERSON><PERSON> bought 95 soda bottles and 180 water bottles when they were on sale. If she drank 15 water bottles and 54 soda bottles a day", "Question": "How many days would the water bottles last?", "Equation": "( 180.0 / 15.0 )", "Answer": 12.0, "Type": "Common-Division"}, {"ID": "chal-501", "Body": "<PERSON> is baking a cake. The recipe calls for 3 cups of sugar and 10 cups of flour. She already put in some cups of flour. If she still needs 5 more cups of flour than sugar", "Question": "How many cups of flour did she put in?", "Equation": "( ( 10.0 - 3.0 ) - 5.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-502", "Body": "<PERSON> had 35 packs of pencils each one having 4 pencils. She was placing her pencils into rows with 2 pencils in each row.", "Question": "How many rows could she make?", "Equation": "( 35.0 * ( 4.0 / 2.0 ) )", "Answer": 70.0, "Type": "Multiplication"}, {"ID": "chal-503", "Body": "Each Ferris wheel in paradise park has 19 seats. Each seat in a Ferris wheel can hold 15 people.", "Question": "How many people can ride 20 Ferris wheels at the same time?", "Equation": "( ( 19.0 * 15.0 ) * 20.0 )", "Answer": 5700.0, "Type": "Multiplication"}, {"ID": "chal-504", "Body": "<PERSON> had 19 pieces of candy. He ate 2 pieces of candy.", "Question": "How many pieces of candy does he still have left?", "Equation": "( 19.0 - 2.0 )", "Answer": 17.0, "Type": "Subtraction"}, {"ID": "chal-505", "Body": "<PERSON> had 20 pieces of candy. He ate 34 pieces of candy. Then he ate 18 more.", "Question": "How many pieces of candy did <PERSON> eat?", "Equation": "( 34.0 + 18.0 )", "Answer": 52.0, "Type": "Addition"}, {"ID": "chal-506", "Body": "After eating a hearty meal they went to see the Buckingham palace. There, <PERSON> learned that 705 visitors came to the Buckingham palace that day. If there were 191 visitors the previous day and 457 visitors the day before that", "Question": "How many more visitors visited the Buckingham palace on that day than on the previous 245 days?", "Equation": "( 705.0 - ( 191.0 + 457.0 ) )", "Answer": 57.0, "Type": "Subtraction"}, {"ID": "chal-507", "Body": "<PERSON> had 108 books and 55 pens. After selling some books and pens in a garage sale he had 66 books and 59 pens left.", "Question": "How many books did he sell in the garage sale?", "Equation": "( 108.0 - 66.0 )", "Answer": 42.0, "Type": "Subtraction"}, {"ID": "chal-508", "Body": "<PERSON> and his dad went strawberry picking. <PERSON>'s dad's strawberries weighed 11 pounds. If together their strawberries weighed 30 pounds.", "Question": "How much did <PERSON>'s strawberries weigh?", "Equation": "( 30.0 - 11.0 )", "Answer": 19.0, "Type": "Subtraction"}, {"ID": "chal-509", "Body": "<PERSON> played tag with 5 kids on monday, 9 kids on tuesday and 15 kids on wednesday.", "Question": "How many kids did she play with on monday and wednesday?", "Equation": "( 5.0 + 15.0 )", "Answer": 20.0, "Type": "Addition"}, {"ID": "chal-510", "Body": "<PERSON> and his dad went strawberry picking. <PERSON>'s strawberries weighed 15 pounds. If together their strawberries weighed 37 pounds.", "Question": "How much did his dad's strawberries weigh?", "Equation": "( 37.0 - 15.0 )", "Answer": 22.0, "Type": "Subtraction"}, {"ID": "chal-511", "Body": "<PERSON> sold 10 boxes of trefoils.", "Question": "How many boxes are in each case if she needs to deliver 5 cases of boxes?", "Equation": "( 10.0 / 5.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-512", "Body": "<PERSON> got a box of 521 crayons and 66 erasers for his birthday. At the end of the school year he only had 154 left while not having lost a single erasers.", "Question": "How many crayons had been lost or given away?", "Equation": "( 521.0 - 154.0 )", "Answer": 367.0, "Type": "Subtraction"}, {"ID": "chal-513", "Body": "<PERSON> did 56 more push-ups than <PERSON> in gym class today. If <PERSON> did 38 push-ups", "Question": "How many push-ups did <PERSON> and <PERSON> do altogether?", "Equation": "( ( 38.0 + 38.0 ) - 56.0 )", "Answer": 20.0, "Type": "Subtraction"}, {"ID": "chal-514", "Body": "<PERSON> collects bottle caps and wrappers. He found 10 bottle caps and 62 wrappers at the park. Now he has 27 bottle caps and 43 wrappers in his collection.", "Question": "How many more wrappers than bottle caps does danny have now?", "Equation": "( 43.0 - 27.0 )", "Answer": 16.0, "Type": "Subtraction"}, {"ID": "chal-515", "Body": "<PERSON> was collecting cans for recycling. On monday she had 8 bags of cans. She found 10 bags of cans on the next day and 4 bags of cans the day after that.", "Question": "How many bags did she find after monday?", "Equation": "( 10.0 + 4.0 )", "Answer": 14.0, "Type": "Addition"}, {"ID": "chal-516", "Body": "<PERSON> was placing her pencils into rows with 16 pencils in each row. She had 28 packs of pencils each one having 24 pencils.", "Question": "How many rows could she make?", "Equation": "( 28.0 * ( 24.0 / 16.0 ) )", "Answer": 42.0, "Type": "Multiplication"}, {"ID": "chal-517", "Body": "4 birds and 3 storks were sitting on the fence. 6 more storks came to join them.", "Question": "How many more storks than birds are sitting on the fence?", "Equation": "( ( 3.0 + 6.0 ) - 4.0 )", "Answer": 5.0, "Type": "Subtraction"}, {"ID": "chal-518", "Body": "<PERSON> weighed 114 kilograms. After she started to go jogging everyday she lost 50 kilograms in the first week and 60 kilograms in the second week.", "Question": "How much does she weigh now?", "Equation": "( ( 114.0 - 50.0 ) - 60.0 )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-519", "Body": "6 packs of dvds can be bought with 120 dollars.", "Question": "How much does each pack cost?", "Equation": "( 120.0 / 6.0 )", "Answer": 20.0, "Type": "Common-Division"}, {"ID": "chal-520", "Body": "<PERSON> played tag with 14 kids on monday, 2 kids on tuesday and 8 kids on wednesday.", "Question": "How many more kids did she play with on monday than on wednesday?", "Equation": "( 14.0 - 8.0 )", "Answer": 6.0, "Type": "Subtraction"}, {"ID": "chal-521", "Body": "There were 73 parents, 724 pupils and 744 teachers in the program.", "Question": "How many people were present in the program?", "Equation": "( ( 73.0 + 724.0 ) + 744.0 )", "Answer": 1541.0, "Type": "Addition"}, {"ID": "chal-522", "Body": "At the stop 8 more people got on the train. Initially there were 11 people on the train.", "Question": "How many people are there on the train now?", "Equation": "( 11.0 + 8.0 )", "Answer": 19.0, "Type": "Addition"}, {"ID": "chal-523", "Body": "<PERSON> had 33 pieces of candy. He lost 24 of them. If he put the remaining pieces into bags with 9 pieces in each bag", "Question": "How many bags would he have?", "Equation": "( ( 33.0 - 24.0 ) / 9.0 )", "Answer": 1.0, "Type": "Common-Division"}, {"ID": "chal-524", "Body": "<PERSON> took a look at his books as well. If he has 34 books distributed equally in 2 bookshelves", "Question": "How many books are there in each bookshelf?", "Equation": "( 34.0 / 2.0 )", "Answer": 17.0, "Type": "Common-Division"}, {"ID": "chal-525", "Body": "A waiter had 3 customers. After some more arrived he had 8 customers.", "Question": "How many new customers arrived?", "Equation": "( 8.0 - 3.0 )", "Answer": 5.0, "Type": "Subtraction"}, {"ID": "chal-526", "Body": "<PERSON>'s mother made cookies for guests. If she prepared 38 cookies and each of them had 19 cookies", "Question": "How many guests did she prepare cookies for?", "Equation": "( 38.0 / 19.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-527", "Body": "They decided to hold the party in their backyard. They have some sets of tables, each having the 3 chairs. If there are a total of 9 chairs in the backyard", "Question": "How many sets of tables do they have?", "Equation": "( 9.0 / 3.0 )", "Answer": 3.0, "Type": "Common-Division"}, {"ID": "chal-528", "Body": "<PERSON> played a trivia game and scored 154 points. If he gained the 11 points in each round", "Question": "How many rounds did he play?", "Equation": "( 154.0 / 11.0 )", "Answer": 14.0, "Type": "Common-Division"}, {"ID": "chal-529", "Body": "<PERSON> received 3 emails in the afternoon, 6 emails in the morning and some more in the evening. If he received a total of 10 emails in the day", "Question": "How many emails did jack receive in the evening?", "Equation": "( 10.0 - ( 6.0 + 3.0 ) )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-530", "Body": "There are 65 baskets of peaches. Each basket has 7 red peaches and 3 green peaches.", "Question": "How many peaches are in each basket?", "Equation": "( 7.0 + 3.0 )", "Answer": 10.0, "Type": "Addition"}, {"ID": "chal-531", "Body": "The school is planning a field trip. The school has 102 classrooms. There are 11 students in the school and 99 seats on each school bus. If 8 students do n't want to go for the trip", "Question": "How many students are going on the trip?", "Equation": "( 11.0 - 8.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-532", "Body": "<PERSON>'s room is 11 feet long and 15 feet wide. If she already has 16 square feet of carpet", "Question": "How much more carpet does she need to cover the whole floor?", "Equation": "( ( 11.0 * 15.0 ) - 16.0 )", "Answer": 149.0, "Type": "Subtraction"}, {"ID": "chal-533", "Body": "<PERSON> has 6 fewer peaches and 8 more apples than <PERSON>. <PERSON> has 17 peaches and 16 apples.", "Question": "How many more peaches than apples does <PERSON> have?", "Equation": "( 17.0 - 16.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-534", "Body": "<PERSON> collects bottle caps. He threw away 60 of the old ones at the park while he found 58 bottle caps new ones. Now he has 67 bottle caps in his collection.", "Question": "How many bottle caps did danny have at first?", "Equation": "( ( 67.0 + 60.0 ) - 58.0 )", "Answer": 69.0, "Type": "Subtraction"}, {"ID": "chal-535", "Body": "<PERSON> collects baseball cards. She had 573 cards. She gave 195 cards to <PERSON> and yet some more to <PERSON>. Now she has 210 cards left.", "Question": "How many cards did <PERSON> give to <PERSON>?", "Equation": "( ( 573.0 - 195.0 ) - 210.0 )", "Answer": 168.0, "Type": "Subtraction"}, {"ID": "chal-536", "Body": "Next on his checklist is wax to stick the feathers together. If he currently has 11 g of wax and the feathers require a total of 492 g of wax", "Question": "How many more grams of wax does he need?", "Equation": "( 492.0 - 11.0 )", "Answer": 481.0, "Type": "Subtraction"}, {"ID": "chal-537", "Body": "Winter is almost here and most animals are migrating to warmer countries. There are 49 bird families living near the mountain. 59 bird families flew away for winter and 24 new bird families came to live near the mountain from the arctic.", "Question": "How many bird families were left near the mountain?", "Equation": "( ( 49.0 - 59.0 ) + 24.0 )", "Answer": 14.0, "Type": "Addition"}, {"ID": "chal-538", "Body": "<PERSON> has 19 peaches. <PERSON> has 12 fewer peaches than <PERSON> and 72 more peaches than <PERSON>.", "Question": "How many peaches does <PERSON> have?", "Equation": "( 19.0 - 12.0 )", "Answer": 7.0, "Type": "Subtraction"}, {"ID": "chal-539", "Body": "<PERSON> had some marbles in his collection. He lost 21 marbles. If he has 12 marbles now", "Question": "How many marbles did he have in his collection?", "Equation": "( 21.0 + 12.0 )", "Answer": 33.0, "Type": "Addition"}, {"ID": "chal-540", "Body": "He had 15 aquariums for saltwater animals and 62 aquariums for freshwater animals. Each aquarium has 19 animals in it.", "Question": "How many animals does he have in total?", "Equation": "( ( 15.0 + 62.0 ) * 19.0 )", "Answer": 1463.0, "Type": "Multiplication"}, {"ID": "chal-541", "Body": "<PERSON> currently weighs 27 kilograms. After she started to go jogging everyday she lost 101 kilograms.", "Question": "How much did she weigh before starting to jog?", "Equation": "( 27.0 + 101.0 )", "Answer": 128.0, "Type": "Addition"}, {"ID": "chal-542", "Body": "<PERSON> has $ 3 left with him after he bought a candy bar for $ 2.", "Question": "How much money did he have initially?", "Equation": "( 3.0 + 2.0 )", "Answer": 5.0, "Type": "Addition"}, {"ID": "chal-543", "Body": "<PERSON> did 53 push-ups and 14 crunches in gym class today. <PERSON> did 17 more push-ups but 10 less crunches than zach<PERSON>.", "Question": "How many push-ups and crunches did <PERSON> do?", "Equation": "( 53.0 + 14.0 )", "Answer": 67.0, "Type": "Addition"}, {"ID": "chal-544", "Body": "A book has 3 chapters. The first chapter is 91 pages long the second chapter is 23 pages long and the third chapter is 25 pages long.", "Question": "How many more pages does the first chapter have than the second chapter?", "Equation": "( 91.0 - 23.0 )", "Answer": 68.0, "Type": "Subtraction"}, {"ID": "chal-545", "Body": "<PERSON> collects bottle caps. He found 36 bottle caps at the park while he threw away 35 old ones. Now he has 22 bottle caps in his collection.", "Question": "How many more bottle caps did danny find at the park than those he threw away?", "Equation": "( 36.0 - 35.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-546", "Body": "<PERSON> has $ 4. For a total of $ 3 he bought 10 candy bar each one costing the same amount of money.", "Question": "How much money is left?", "Equation": "( 4.0 - 3.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-547", "Body": "<PERSON> is baking a cake. The recipe calls for 6 cups of sugar and 9 cups of flour. She already put in 2 cups of flour.", "Question": "How many more cups of flour than cups of sugar does she need to add now?", "Equation": "( ( 9.0 - 2.0 ) - 6.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-548", "Body": "At the zoo, a cage had 95 snakes and 61 alligators. If 64 snakes were hiding", "Question": "How many snakes were not hiding?", "Equation": "( 95.0 - 64.0 )", "Answer": 31.0, "Type": "Subtraction"}, {"ID": "chal-549", "Body": "<PERSON> is making bead necklaces for her friends. She was able to make 6 necklaces and she had 18 beads.", "Question": "How many beads did each necklace need?", "Equation": "( 18.0 / 6.0 )", "Answer": 3.0, "Type": "Common-Division"}, {"ID": "chal-550", "Body": "<PERSON> was collecting cans for recycling. On monday she had 3 bags of cans. The next day she found 7 more bags of cans and 44 bags of bottles.", "Question": "How many bags of cans did she have altogether?", "Equation": "( 3.0 + 7.0 )", "Answer": 10.0, "Type": "Addition"}, {"ID": "chal-551", "Body": "<PERSON> has some packages of gum. There are 7 pieces in each package. <PERSON> has 6 extra pieces of gum. In all the number of pieces of gums robin has is 41.", "Question": "How many packages does <PERSON> have?", "Equation": "( ( 41.0 - 6.0 ) / 7.0 )", "Answer": 5.0, "Type": "Common-Division"}, {"ID": "chal-552", "Body": "<PERSON> made 155 cakes. If his friend bought 140 cakes from him", "Question": "How many cakes would baker still have?", "Equation": "( 155.0 - 140.0 )", "Answer": 15.0, "Type": "Subtraction"}, {"ID": "chal-553", "Body": "2 children were riding on the bus. At the bus stop some more children got on the bus. Then there were 10 children altogether on the bus.", "Question": "How many more children are on the bus now than there were before the bus stop?", "Equation": "( 10.0 - 2.0 )", "Answer": 8.0, "Type": "Subtraction"}, {"ID": "chal-554", "Body": "<PERSON> collects bottle caps and wrappers. He found 22 bottle caps and 8 wrappers at the park. Now he has 28 bottle caps and 63 wrappers in his collection.", "Question": "How many bottle caps did danny have at first?", "Equation": "( 28.0 - 22.0 )", "Answer": 6.0, "Type": "Subtraction"}, {"ID": "chal-555", "Body": "For the walls of the house he would use 8 large planks of wood. If each plank of wood needs 74 pieces of nails to be secured", "Question": "How many planks does <PERSON> need for the house wall?", "Equation": "8.0", "Answer": 8.0, "Type": "Common-Division"}, {"ID": "chal-556", "Body": "<PERSON><PERSON> ate 36 cookies. If he had 37 cookies initially", "Question": "How many cookies did <PERSON><PERSON> have left?", "Equation": "( 37.0 - 36.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-557", "Body": "<PERSON> had 21 pieces of candy. He ate 5 pieces of candy. Then he ate 9 more.", "Question": "How many pieces of candy does he still have left?", "Equation": "( 21.0 - ( 5.0 + 9.0 ) )", "Answer": 7.0, "Type": "Subtraction"}, {"ID": "chal-558", "Body": "<PERSON> made 54 cakes. He sold some of them. If he still has 13 cakes left", "Question": "How many cakes did baker sell?", "Equation": "( 54.0 - 13.0 )", "Answer": 41.0, "Type": "Subtraction"}, {"ID": "chal-559", "Body": "During summer break 819058 kids from Lawrence county go to camp and the other 668278 kids stay home.", "Question": "How many more kids spent their summer break at the camp compared to those who stayed home?", "Equation": "( 819058.0 - 668278.0 )", "Answer": 150780.0, "Type": "Subtraction"}, {"ID": "chal-560", "Body": "<PERSON> has 79 blocks. He uses 14 blocks to build a tower and 11 blocks to build a house.", "Question": "How many blocks are left?", "Equation": "( 79.0 - ( 14.0 + 11.0 ) )", "Answer": 54.0, "Type": "Subtraction"}, {"ID": "chal-561", "Body": "Every day <PERSON> spends 6 hours on learning english 7 hours on learning chinese and 3 hours on learning spanish.", "Question": "How many hours does he spend on learning english, chinese and spanish in all?", "Equation": "( ( 6.0 + 7.0 ) + 3.0 )", "Answer": 16.0, "Type": "Addition"}, {"ID": "chal-562", "Body": "2 birds were sitting on the fence. Some more birds came to join them. If there are a total of 6 birds on the fence now", "Question": "How many more birds had come to sit on the fence?", "Equation": "( 6.0 - 2.0 )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-563", "Body": "In a school there are 635 girls and the rest are boys. If there are 510 more boys than girls", "Question": "How many boys are there in that school?", "Equation": "( 635.0 + 510.0 )", "Answer": 1145.0, "Type": "Addition"}, {"ID": "chal-564", "Body": "<PERSON>'s room is 12 feet long and 11 feet wide.", "Question": "How much longer is her room than it is wide?", "Equation": "( 12.0 - 11.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-565", "Body": "There are 16 different books and 14 different movies in the ' crazy silly school ' series. If you read 19 of the books and watched 13 of the movies", "Question": "How many more books than movies are there in the ' crazy silly school ' series?", "Equation": "( 16.0 - 14.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-566", "Body": "You had 14 bags with equal number of cookies. If you had 28 cookies and 86 candies in total", "Question": "How many bags of cookies do you have?", "Equation": "( 28.0 / 14.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-567", "Body": "<PERSON> had 2 action figures and 10 books on a shelf in his room. Later he added 4 more action figures to the shelf.", "Question": "How many more books than action figures were on his shelf?", "Equation": "( 10.0 - ( 2.0 + 4.0 ) )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-568", "Body": "<PERSON> is baking a cake. The recipe calls for 5 cups of sugar and 13 cups of flour. She already put in some cups of flour. If she still needs 12 more cups of flour", "Question": "How many cups of flour did she put in?", "Equation": "( 13.0 - 12.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-569", "Body": "<PERSON> had to complete 9 pages of math homework, 11 pages of reading homework and 29 more pages of biology homework.", "Question": "How many pages of math and reading homework did she have to complete?", "Equation": "( 9.0 + 11.0 )", "Answer": 20.0, "Type": "Addition"}, {"ID": "chal-570", "Body": "<PERSON> was helping her mom plant flowers in 8 flowerbeds. They planted 32 seeds altogether. If they put same number of seeds in each flower bed", "Question": "How many seeds did they plant in each flowerbed?", "Equation": "( 32.0 / 8.0 )", "Answer": 4.0, "Type": "Common-Division"}, {"ID": "chal-571", "Body": "He then went to see the oranges being harvested. He found out that they harvest 25 sacks of unripe oranges and 5 sacks of ripe oranges per day.", "Question": "How many sacks of ripe oranges will they have after 73 days of harvest?", "Equation": "( 5.0 * 73.0 )", "Answer": 365.0, "Type": "Multiplication"}, {"ID": "chal-572", "Body": "<PERSON> had 134 books. After giving 39 books to his friend and selling some books in a garage sale he had 68 books left.", "Question": "How many books did he sell in the garage sale?", "Equation": "( ( 134.0 - 39.0 ) - 68.0 )", "Answer": 27.0, "Type": "Subtraction"}, {"ID": "chal-573", "Body": "<PERSON><PERSON> had 97 salty cookies and 34 sweet cookies. He ate 15 sweet cookies and 56 salty cookies.", "Question": "How many sweet cookies did <PERSON><PERSON> have left?", "Equation": "( 34.0 - 15.0 )", "Answer": 19.0, "Type": "Subtraction"}, {"ID": "chal-574", "Body": "The school is planning a field trip. The school has 84 classrooms. 95 school buses are fully filled with 118 seats on each school bus.", "Question": "How many students are in the school?", "Equation": "( 95.0 * 118.0 )", "Answer": 11210.0, "Type": "Multiplication"}, {"ID": "chal-575", "Body": "<PERSON> was placing her pencils and crayons into 30 rows with 71 crayons and 24 pencils in each row.", "Question": "How many pencils does she have?", "Equation": "( 30.0 * 24.0 )", "Answer": 720.0, "Type": "Multiplication"}, {"ID": "chal-576", "Body": "<PERSON> was placing 12 pencils equally into 3 rows.", "Question": "How many pencils did she place in each row?", "Equation": "( 12.0 / 3.0 )", "Answer": 4.0, "Type": "Common-Division"}, {"ID": "chal-577", "Body": "<PERSON> played tag with 15 kids on monday, 18 kids on tuesday and 7 kids on wednesday.", "Question": "How many more kids did she play with on monday and tuesday than on wednesday?", "Equation": "( ( 15.0 + 18.0 ) - 7.0 )", "Answer": 26.0, "Type": "Subtraction"}, {"ID": "chal-578", "Body": "<PERSON> made 133 cakes. If he sold 51 of them", "Question": "How many more cakes did baker make than those he sold?", "Equation": "( 133.0 - 51.0 )", "Answer": 82.0, "Type": "Subtraction"}, {"ID": "chal-579", "Body": "There are 18 bananas in <PERSON>'s banana collection. If the bananas are organized into groups of size 2", "Question": "How many groups are there?", "Equation": "( 18.0 / 2.0 )", "Answer": 9.0, "Type": "Common-Division"}, {"ID": "chal-580", "Body": "<PERSON> spent 38 dollars at a supermarket. If there were 128 dollars in her wallet initially", "Question": "How much money does she have left?", "Equation": "( 128.0 - 38.0 )", "Answer": 90.0, "Type": "Subtraction"}, {"ID": "chal-581", "Body": "<PERSON> sold 44 cakes. If he had made 48 cakes initially", "Question": "How many cakes would baker still have?", "Equation": "( 48.0 - 44.0 )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-582", "Body": "Every day <PERSON> spends 5 hours on learning chinese and some more hours on learning english. If he spends 2 hours more on learning english than on learning chinese", "Question": "How many hours does he spend on learning english?", "Equation": "( 5.0 + 2.0 )", "Answer": 7.0, "Type": "Addition"}, {"ID": "chal-583", "Body": "<PERSON> had to complete 8 pages of math homework. If she had to complete 3 more pages of math homework than reading homework", "Question": "How many pages did she have to complete in all?", "Equation": "( ( 8.0 + 8.0 ) - 3.0 )", "Answer": 13.0, "Type": "Subtraction"}, {"ID": "chal-584", "Body": "<PERSON> has $ 5. He bought a chocolate for $ 3 and a candy bar for $ 7.", "Question": "How much money did he spend to buy candy bar than he did to buy chocolate?", "Equation": "( 7.0 - 3.0 )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-585", "Body": "<PERSON> weighed 92 kilograms. After she started to go jogging everyday she lost 56 kilograms in the first week and 99 kilograms in the second week.", "Question": "How much did she weigh after the first week of jogging?", "Equation": "( 92.0 - 56.0 )", "Answer": 36.0, "Type": "Subtraction"}, {"ID": "chal-586", "Body": "<PERSON> played tag with 2 kids on monday, 14 kids on tuesday and 16 kids on wednesday.", "Question": "How many kids did she play with on tuesday and wednesday?", "Equation": "( 14.0 + 16.0 )", "Answer": 30.0, "Type": "Addition"}, {"ID": "chal-587", "Body": "21 children were riding on the bus. At the bus stop 10 children got off the bus while some more got on the bus. Then there were 16 children altogether on the bus.", "Question": "How many children got on the bus at the bus stop?", "Equation": "( ( 16.0 - 21.0 ) + 10.0 )", "Answer": 5.0, "Type": "Addition"}, {"ID": "chal-588", "Body": "<PERSON> made 62 cakes. Then he made 149 more cakes. He sold 144 of them.", "Question": "How many cakes would baker still have?", "Equation": "( ( 62.0 - 144.0 ) + 149.0 )", "Answer": 67.0, "Type": "Addition"}, {"ID": "chal-589", "Body": "<PERSON> got a box of some crayons for his birthday. By the end of the school year he only had 291 crayons left since he had either lost or given away 315 of them.", "Question": "How many crayons did he get for his birthday?", "Equation": "( 315.0 + 291.0 )", "Answer": 606.0, "Type": "Addition"}, {"ID": "chal-590", "Body": "<PERSON> received 5 emails and 6 letters in the morning. He then received 9 emails and 7 letters in the afternoon.", "Question": "How many letters did jack receive in the day?", "Equation": "( 6.0 + 7.0 )", "Answer": 13.0, "Type": "Addition"}, {"ID": "chal-591", "Body": "<PERSON> earns $ 403 every week during the 233 weeks of harvest. If he has to pay $ 49 rent every week", "Question": "How much money does he earn during harvest season?", "Equation": "( 403.0 * 233.0 )", "Answer": 93899.0, "Type": "Multiplication"}, {"ID": "chal-592", "Body": "He then went to see the oranges being harvested. He found out that they harvest 16 sacks per day and that each sack containes 57 oranges.", "Question": "How many days will it take to harvest 80 sacks of oranges?", "Equation": "( 80.0 / 16.0 )", "Answer": 5.0, "Type": "Common-Division"}, {"ID": "chal-593", "Body": "<PERSON> has some packages of gum. There are 3 pieces in each package and a total of 42 pieces of gum.", "Question": "How many packages does <PERSON> have?", "Equation": "( 42.0 / 3.0 )", "Answer": 14.0, "Type": "Common-Division"}, {"ID": "chal-594", "Body": "During the Arkansas and Texas tech game the Razorback t-shirt shop made $ 215 by selling 5 t-shirts.", "Question": "What is the cost of each t-shirt?", "Equation": "( 215.0 / 5.0 )", "Answer": 43.0, "Type": "Common-Division"}, {"ID": "chal-595", "Body": "The grasshopper, the frog and the mouse had a jumping contest. The grasshopper jumped 25 inches. The frog jumped 32 inches farther than the grasshopper and the mouse jumped 26 inches lesser than the frog.", "Question": "How far did the mouse jump?", "Equation": "( ( 25.0 + 32.0 ) - 26.0 )", "Answer": 31.0, "Type": "Subtraction"}, {"ID": "chal-596", "Body": "He then went to see the oranges being harvested. He found out that they harvest 76 sacks per day and discard 64 of them.", "Question": "How many oranges do they harvest per day if each sack contains 50 oranges?", "Equation": "( ( 76.0 - 64.0 ) * 50.0 )", "Answer": 600.0, "Type": "Multiplication"}, {"ID": "chal-597", "Body": "<PERSON>'s room is 7 feet wide. If she needs a carpet of size 14 square feet", "Question": "What is the length of her room?", "Equation": "( 14.0 / 7.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-598", "Body": "Every day <PERSON> spends 6 hours on learning english and 5 hours on learning chinese. If he learns for 2 days", "Question": "How many hours does he spend on learning english in all?", "Equation": "( 6.0 * 2.0 )", "Answer": 12.0, "Type": "Multiplication"}, {"ID": "chal-599", "Body": "<PERSON> had 10 more marbles than <PERSON>. <PERSON> lost 11 of his marbles at the playground. If <PERSON> had 45 marbles", "Question": "How many more marbles did <PERSON> have than <PERSON> then?", "Equation": "( 10.0 + 11.0 )", "Answer": 21.0, "Type": "Addition"}, {"ID": "chal-600", "Body": "<PERSON> was sending out birthday invitations to her friends. Each package of invitations she bought had 10 invitations in it and she bought 7 packs. If she sends invitations to only 2 friends", "Question": "How many invitations will be left?", "Equation": "( ( 10.0 * 7.0 ) - 2.0 )", "Answer": 68.0, "Type": "Subtraction"}, {"ID": "chal-601", "Body": "<PERSON>'s tree had 4 apples initially. She picked some apples and now there are 2 apples left on the tree.", "Question": "How many apples did rachel pick?", "Equation": "( 4.0 - 2.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-602", "Body": "<PERSON> ate 28 pieces of candy. Then he ate 42 more. He also ate 63 pieces of chocolate.", "Question": "How many pieces of candy did <PERSON> eat?", "Equation": "( 28.0 + 42.0 )", "Answer": 70.0, "Type": "Addition"}, {"ID": "chal-603", "Body": "The Razorback shop makes $ 115 dollars off each jersey and $ 25 off each t-shirt. During the Arkansas and Texas tech game they sold 113 t-shirts and 78 jerseys.", "Question": "How much more does a jersey cost than a t-shirt?", "Equation": "( 115.0 - 25.0 )", "Answer": 90.0, "Type": "Subtraction"}, {"ID": "chal-604", "Body": "<PERSON> had 22 pieces of candy. He ate some pieces of candy. Then he ate 5 more. If he still has 8 pieces of candy left", "Question": "How many pieces of candy had he eaten at the start?", "Equation": "( ( 22.0 - 5.0 ) - 8.0 )", "Answer": 9.0, "Type": "Subtraction"}, {"ID": "chal-605", "Body": "If each pack costs 81 dollars", "Question": "How much will it cost to buy 33 packs of dvds?", "Equation": "( 81.0 * 33.0 )", "Answer": 2673.0, "Type": "Multiplication"}, {"ID": "chal-606", "Body": "There were 61 parents in the program and 177 pupils too. The program could seat 44 people", "Question": "How many people were present in the program?", "Equation": "( 61.0 + 177.0 )", "Answer": 238.0, "Type": "Addition"}, {"ID": "chal-607", "Body": "Every day <PERSON> spends 4 hours on learning english and 6 hours on learning chinese. If he learns for 86 days", "Question": "How many hours does he spend on learning english and chinese each day?", "Equation": "( 4.0 + 6.0 )", "Answer": 10.0, "Type": "Addition"}, {"ID": "chal-608", "Body": "<PERSON> made 126 cakes and 48 pastries. If he sold 115 cakes and 81 pastries", "Question": "How many more cakes than pastries did baker make?", "Equation": "( 126.0 - 48.0 )", "Answer": 78.0, "Type": "Subtraction"}, {"ID": "chal-609", "Body": "At the arcade <PERSON> had won 19 tickets. If he used 12 to buy some toys and 7 more to buy some clothes", "Question": "How many more tickets did <PERSON> use to buy toys than he did to buy clothes?", "Equation": "( 12.0 - 7.0 )", "Answer": 5.0, "Type": "Subtraction"}, {"ID": "chal-610", "Body": "A grocery store had 30 bottles of regular soda, 8 bottles of diet soda and 41 apples.", "Question": "How many bottles did they have total?", "Equation": "( 30.0 + 8.0 )", "Answer": 38.0, "Type": "Addition"}, {"ID": "chal-611", "Body": "There are 11 baskets of peaches. Each basket has 10 red peaches and 18 green peaches.", "Question": "How many peaches are in the baskets altogether?", "Equation": "( ( 10.0 + 18.0 ) * 11.0 )", "Answer": 308.0, "Type": "Multiplication"}, {"ID": "chal-612", "Body": "<PERSON> got a box of 440 crayons for his birthday. During the school year he gave 111 crayons to his friends while he lost 106 crayons.", "Question": "How many crayons did he have left?", "Equation": "( 440.0 - ( 111.0 + 106.0 ) )", "Answer": 223.0, "Type": "Subtraction"}, {"ID": "chal-613", "Body": "Some campers went rowing in the morning while 7 campers went rowing in the afternoon. If a total of 60 campers went rowing that day", "Question": "How many campers went rowing in the morning?", "Equation": "( 60.0 - 7.0 )", "Answer": 53.0, "Type": "Subtraction"}, {"ID": "chal-614", "Body": "He then went to see the oranges being harvested. He found out that they harvest 74 sacks per day and discard 71 of them.", "Question": "How many sacks of oranges will they have after 51 days of harvest?", "Equation": "( ( 74.0 - 71.0 ) * 51.0 )", "Answer": 153.0, "Type": "Multiplication"}, {"ID": "chal-615", "Body": "Next on his checklist is wax to stick the feathers together. If he has 557 g of wax and right now he just needs 17 g", "Question": "Total how many grams of wax do the feathers require?", "Equation": "( 557.0 + 17.0 )", "Answer": 574.0, "Type": "Addition"}, {"ID": "chal-616", "Body": "<PERSON> had 7 books and 3 action figures on a shelf in his room. Later he added 2 more action figures to the shelf.", "Question": "How many more books than action figures were on his shelf?", "Equation": "( 7.0 - ( 3.0 + 2.0 ) )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-617", "Body": "<PERSON> gave 6 crackers to each his friends. If he had 36 crackers", "Question": "How many friends did he give crackers to?", "Equation": "( 36.0 / 6.0 )", "Answer": 6.0, "Type": "Common-Division"}, {"ID": "chal-618", "Body": "<PERSON> had 57 new games and 39 old games. Her friends had 34 new games.", "Question": "How many more games does <PERSON> have than her friends?", "Equation": "( ( 57.0 + 39.0 ) - 34.0 )", "Answer": 62.0, "Type": "Subtraction"}, {"ID": "chal-619", "Body": "26 children were riding on the bus. At the bus stop 38 more children got on the bus.", "Question": "How many children are on the bus now?", "Equation": "( 26.0 + 38.0 )", "Answer": 64.0, "Type": "Addition"}, {"ID": "chal-620", "Body": "<PERSON> had 21 marbles in his collection. He found 7 marbles.", "Question": "How many marbles does he have now?", "Equation": "( 21.0 + 7.0 )", "Answer": 28.0, "Type": "Addition"}, {"ID": "chal-621", "Body": "<PERSON> brought 6 balloons to the park. If <PERSON> brought 4 more balloons than the number of balloons that <PERSON> brought", "Question": "How many balloons did <PERSON> bring to the park?", "Equation": "( 6.0 - 4.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-622", "Body": "At the arcade <PERSON> had won 14 tickets and lost 2 tickets. If he used 10 to buy some toys", "Question": "How many tickets did <PERSON> have left?", "Equation": "( ( 14.0 - 2.0 ) - 10.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-623", "Body": "During summer break 610769 kids from Lawrence county go to camp and the other 590796 kids stay home. An additional 22 kids from outside the county attended the camp.", "Question": "About how many kids are in Lawrence county?", "Equation": "( 610769.0 + 590796.0 )", "Answer": 1201565.0, "Type": "Addition"}, {"ID": "chal-624", "Body": "<PERSON> had $ 18. He spent $ 16.", "Question": "How much money does <PERSON> have now?", "Equation": "( 18.0 - 16.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-625", "Body": "For <PERSON>'s birthday she received 5 dollars from her dad. Her mom gave her 2 more dollars. If she spent 6 dollars.", "Question": "How much more money did she receive from her dad than she did from her mom?", "Equation": "( 5.0 - 2.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-626", "Body": "<PERSON>'s hair was 14 inches long. It grew by 8 inches. Then he cut off 20 inches.", "Question": "How long is his hair now?", "Equation": "( ( 14.0 - 20.0 ) + 8.0 )", "Answer": 2.0, "Type": "Addition"}, {"ID": "chal-627", "Body": "<PERSON> spent $ 16 to buy 92 books each book costing him the same amount of money. Now he has $ 6.", "Question": "How much did <PERSON> have before he spent his money?", "Equation": "( 16.0 + 6.0 )", "Answer": 22.0, "Type": "Addition"}, {"ID": "chal-628", "Body": "During summer break 34044 kids from Lawrence county go to camp and the other 134867 kids stay home. An additional 424944 kids from outside the county attended the camp.", "Question": "How many kids in total attended the camp?", "Equation": "( 34044.0 + 424944.0 )", "Answer": 458988.0, "Type": "Addition"}, {"ID": "chal-629", "Body": "<PERSON> made 14 cakes and 153 pastries. If he sold 8 pastries and 97 cakes", "Question": "How many more cakes than pastries did baker sell?", "Equation": "( 97.0 - 8.0 )", "Answer": 89.0, "Type": "Subtraction"}, {"ID": "chal-630", "Body": "After a typhoon, 2 trees in <PERSON>'s backyard died. If she had grown 12 trees initially", "Question": "How many trees does she have left?", "Equation": "( 12.0 - 2.0 )", "Answer": 10.0, "Type": "Subtraction"}, {"ID": "chal-631", "Body": "<PERSON><PERSON> had 8 sweet cookies and 6 salty cookies. He ate 20 sweet cookies and 34 salty cookies.", "Question": "How many more salty cookies than sweet cookies did he eat?", "Equation": "( 34.0 - 20.0 )", "Answer": 14.0, "Type": "Subtraction"}, {"ID": "chal-632", "Body": "A grocery store had 79 bottles of regular soda, 53 bottles of diet soda and 42 apples.", "Question": "How many more bottles of regular soda than diet soda did they have?", "Equation": "( 79.0 - 53.0 )", "Answer": 26.0, "Type": "Subtraction"}, {"ID": "chal-633", "Body": "<PERSON> was sending out birthday invitations to 12 friends. She bought 3 packs each one having the same number of invitations.", "Question": "How many invitations are in each pack?", "Equation": "( 12.0 / 3.0 )", "Answer": 4.0, "Type": "Common-Division"}, {"ID": "chal-634", "Body": "<PERSON> has 13 fewer peaches and 3 more apples than <PERSON>. <PERSON> has 9 peaches and 8 apples.", "Question": "How many apples does <PERSON> have?", "Equation": "( 3.0 + 8.0 )", "Answer": 11.0, "Type": "Addition"}, {"ID": "chal-635", "Body": "<PERSON> had 22 marbles in his collection. <PERSON> gave him 20 marbles.", "Question": "How many marbles does <PERSON> have now?", "Equation": "( 22.0 + 20.0 )", "Answer": 42.0, "Type": "Addition"}, {"ID": "chal-636", "Body": "<PERSON> had 31 packs of pencils each one having 6 pencils. She was placing her pencils into rows with 19 pencils in each row.", "Question": "How many pencils does she have?", "Equation": "( 31.0 * 6.0 )", "Answer": 186.0, "Type": "Multiplication"}, {"ID": "chal-637", "Body": "<PERSON> has 84 more apples and 10 fewer peaches than <PERSON>. <PERSON> has 13 peaches and 52 apples.", "Question": "How many peaches does <PERSON> have?", "Equation": "( 13.0 - 10.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-638", "Body": "<PERSON> wants to split a collection of eggs into 4 groups. <PERSON> has 8 eggs and 6 marbles.", "Question": "How many eggs will each group have?", "Equation": "( 8.0 / 4.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-639", "Body": "<PERSON> received 4 emails and sent 2 letters in the morning. He then received 6 emails and sent 8 letters in the afternoon.", "Question": "How many emails did <PERSON> send in the day?", "Equation": "( 2.0 + 8.0 )", "Answer": 10.0, "Type": "Addition"}, {"ID": "chal-640", "Body": "<PERSON> had to complete 7 pages of math homework, 11 pages of reading homework and 8 more pages of biology homework.", "Question": "How many more pages of reading homework than biology homework did she have?", "Equation": "( 11.0 - 8.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-641", "Body": "<PERSON> has $ 2. He bought some candy bar for $ 6 each one costing $ 3.", "Question": "How many candy bar did he buy?", "Equation": "( 6.0 / 3.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-642", "Body": "<PERSON> went to the grocery store. She bought 4 packs of cookie, 22 packs of cake and 16 packs of chocolate.", "Question": "How many packs of groceries did she buy in all?", "Equation": "( ( 4.0 + 22.0 ) + 16.0 )", "Answer": 42.0, "Type": "Addition"}, {"ID": "chal-643", "Body": "The school has 304 grades and each grade has 75 students", "Question": "How many students were there in total?", "Equation": "( 304.0 * 75.0 )", "Answer": 22800.0, "Type": "Multiplication"}, {"ID": "chal-644", "Body": "The cave is 1218 feet deep and they are already at 849 feet. If they are travelling at speed of 17", "Question": "How much farther until they reach the end of the cave?", "Equation": "( 1218.0 - 849.0 )", "Answer": 369.0, "Type": "Subtraction"}, {"ID": "chal-645", "Body": "<PERSON> had 32 pieces of candy. He ate some pieces of candy. If he has 20 pieces of candy left", "Question": "How many pieces of candy did <PERSON> eat?", "Equation": "( 32.0 - 20.0 )", "Answer": 12.0, "Type": "Subtraction"}, {"ID": "chal-646", "Body": "<PERSON> sold 8 cakes. If he had made 40 cakes initially", "Question": "How many more cakes did baker make than those he sold?", "Equation": "( 40.0 - 8.0 )", "Answer": 32.0, "Type": "Subtraction"}, {"ID": "chal-647", "Body": "After eating a hearty meal they went to see the Buckingham palace. There, <PERSON> learned that 92 visitors came to the Buckingham palace that day. If there were 419 visitors the previous day and 103 visitors the day before that", "Question": "How many visited the Buckingham palace before that day?", "Equation": "( 419.0 + 103.0 )", "Answer": 522.0, "Type": "Addition"}, {"ID": "chal-648", "Body": "After eating a hearty meal they went to see the Buckingham palace. There, <PERSON> learned that 100 visitors came to the Buckingham palace on the previous day. If there were 666 visitors on that day", "Question": "How many more visitors visited the Buckingham palace on that day than on the previous day?", "Equation": "( 666.0 - 100.0 )", "Answer": 566.0, "Type": "Subtraction"}, {"ID": "chal-649", "Body": "In <PERSON>'s class 4 are boys who love to play marbles. If <PERSON> has 23 marbles and wants to keep 15 marbles for herself", "Question": "How many will each of the boys receive?", "Equation": "( ( 23.0 - 15.0 ) / 4.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-650", "Body": "<PERSON> had 22 crackers and 34 cakes. If <PERSON> gave equal numbers of crackers and cakes to his 11 friends", "Question": "How many crackers did each person eat?", "Equation": "( 22.0 / 11.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-651", "Body": "<PERSON> has 3 apple trees. She picked 8 apples from each of her trees. Now the trees have a total 9 apples still on them.", "Question": "How many apples were there on all trees initially?", "Equation": "( ( 3.0 * 8.0 ) + 9.0 )", "Answer": 33.0, "Type": "Addition"}, {"ID": "chal-652", "Body": "<PERSON> is baking a cake. The recipe calls for 9 cups of flour and 11 cups of sugar. She already put in 4 cups of flour.", "Question": "How many more cups of sugar than cups of flour does she need to add now?", "Equation": "( 11.0 - ( 9.0 - 4.0 ) )", "Answer": 6.0, "Type": "Subtraction"}, {"ID": "chal-653", "Body": "The school is planning a field trip. The school has 17 classrooms. There are 46 school buses and a total of 92 students in the school. If all buses are full", "Question": "How many seats are in each bus?", "Equation": "( 92.0 / 46.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-654", "Body": "For <PERSON>'s birthday she received some dollars. She spent 8 dollars and has 6 dollars left with her.", "Question": "How much money did <PERSON> receive for her birthday?", "Equation": "( 8.0 + 6.0 )", "Answer": 14.0, "Type": "Addition"}, {"ID": "chal-655", "Body": "<PERSON> collects bottle caps. He lost 66 bottle caps at the park. Now he has 25 bottle caps in his collection.", "Question": "How many bottle caps did danny have at first?", "Equation": "( 66.0 + 25.0 )", "Answer": 91.0, "Type": "Addition"}, {"ID": "chal-656", "Body": "<PERSON> has 6 fewer peaches than <PERSON>. <PERSON> has 18 more peaches than <PERSON>. If jill has 5 peaches", "Question": "How many peaches does <PERSON> have?", "Equation": "( ( 18.0 + 5.0 ) - 6.0 )", "Answer": 17.0, "Type": "Subtraction"}, {"ID": "chal-657", "Body": "During summer break 907611 kids from Lawrence county stay home and the other 455682 kids go to camp.", "Question": "About how many kids are in Lawrence county?", "Equation": "( 455682.0 + 907611.0 )", "Answer": 1363293.0, "Type": "Addition"}, {"ID": "chal-658", "Body": "<PERSON> had 8 action figures and 9 books on a shelf in his room. Later he added 10 more books to the shelf.", "Question": "How many books were on his shelf in all?", "Equation": "( 9.0 + 10.0 )", "Answer": 19.0, "Type": "Addition"}, {"ID": "chal-659", "Body": "4 birds and 6 storks were sitting on the fence. 2 more storks came to join them.", "Question": "How many storks are sitting on the fence?", "Equation": "( 6.0 + 2.0 )", "Answer": 8.0, "Type": "Addition"}, {"ID": "chal-660", "Body": "<PERSON> brought 6 balloons and <PERSON> brought 2 balloons to the park. <PERSON> then bought 3 more balloons at the park.", "Question": "How many more balloons did <PERSON> have than <PERSON> in the park?", "Equation": "( 6.0 - ( 2.0 + 3.0 ) )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-661", "Body": "<PERSON> was placing her pencils and crayons into 19 rows with 4 pencils and 27 crayons in each row.", "Question": "How many pencils does she have?", "Equation": "( 19.0 * 4.0 )", "Answer": 76.0, "Type": "Multiplication"}, {"ID": "chal-662", "Body": "The Razorback t-shirt shop sells each t-shirt for $ 201 dollars. During the Arkansas and Texas tech game they increased the prices by $ 217 per t-shirt and sold 14 t-shirts.", "Question": "How much money did they make from selling the t-shirts?", "Equation": "( ( 201.0 + 217.0 ) * 14.0 )", "Answer": 5852.0, "Type": "Multiplication"}, {"ID": "chal-663", "Body": "<PERSON> had 12 more marbles than <PERSON>. <PERSON> lost 20 of his marbles at the playground. If <PERSON> had 17 marbles", "Question": "How many marbles does <PERSON> have now?", "Equation": "( 17.0 - 12.0 )", "Answer": 5.0, "Type": "Subtraction"}, {"ID": "chal-664", "Body": "Lawrence county has 313473 kids. During summer break 274865 kids stay home and the rest go to camp.", "Question": "How many kids go to the camp during the break?", "Equation": "( 313473.0 - 274865.0 )", "Answer": 38608.0, "Type": "Subtraction"}, {"ID": "chal-665", "Body": "The Razorback shop makes $ 210 dollars off each jersey and $ 240 off each t-shirt. During the Arkansas and Texas tech game they sold 177 t-shirts and 23 jerseys.", "Question": "How much more does a t-shirt cost than a jersey?", "Equation": "( 240.0 - 210.0 )", "Answer": 30.0, "Type": "Subtraction"}, {"ID": "chal-666", "Body": "<PERSON> has $ 4. He bought a candy bar for $ 7 and a chocolate for $ 6.", "Question": "How much money did he spend buying the candy bar and chocolate?", "Equation": "( 7.0 + 6.0 )", "Answer": 13.0, "Type": "Addition"}, {"ID": "chal-667", "Body": "<PERSON> had 4 action figures and 22 books on a shelf in his room. Later he added 6 more action figures to the shelf.", "Question": "How many action figures were on his shelf in all?", "Equation": "( 4.0 + 6.0 )", "Answer": 10.0, "Type": "Addition"}, {"ID": "chal-668", "Body": "<PERSON> is baking a cake. The recipe calls for 11 cups of sugar and some more cups of flour. She already put in 3 cups of flour. If she still needs to add 6 more cups of flour", "Question": "How many cups of flour did the recipe require?", "Equation": "( 3.0 + 6.0 )", "Answer": 9.0, "Type": "Addition"}, {"ID": "chal-669", "Body": "<PERSON> made 54 cakes and 143 pastries. If he sold 68 cakes and 92 pastries", "Question": "How many more pastries than cakes did baker sell?", "Equation": "( 92.0 - 68.0 )", "Answer": 24.0, "Type": "Subtraction"}, {"ID": "chal-670", "Body": "3 birds and 2 storks were sitting on the fence. 5 more birds came to join them.", "Question": "How many birds and storks are sitting on the fence?", "Equation": "( ( 3.0 + 5.0 ) + 2.0 )", "Answer": 10.0, "Type": "Addition"}, {"ID": "chal-671", "Body": "<PERSON> played tag with 7 kids on monday and 13 kids on tuesday. She played cards wtih 20 kids on wednesday.", "Question": "How many kids did she play tag with altogether?", "Equation": "( 7.0 + 13.0 )", "Answer": 20.0, "Type": "Addition"}, {"ID": "chal-672", "Body": "Being his favorite, he saved checking on the grapevines for his last stop. He was told by 36 of the pickers that they fill 8 drums of grapes per day.", "Question": "How many days will it take to fill 240 drums of grapes?", "Equation": "( 240.0 / 8.0 )", "Answer": 30.0, "Type": "Common-Division"}, {"ID": "chal-673", "Body": "<PERSON> ate 23 pieces of candy. If he initially had 30 pieces of candy", "Question": "How many pieces of candy does he still have left?", "Equation": "( 30.0 - 23.0 )", "Answer": 7.0, "Type": "Subtraction"}, {"ID": "chal-674", "Body": "The Ferris wheel in paradise park has 18 seats. Each seat can hold 15 people. If 10 seats are broken", "Question": "How many people can ride the Ferris wheel at the same time?", "Equation": "( ( 18.0 - 10.0 ) * 15.0 )", "Answer": 120.0, "Type": "Multiplication"}, {"ID": "chal-675", "Body": "<PERSON>'s hair was 16 inches long. He cut off 11 inches. Then it again grew by 12 inches.", "Question": "How long is his hair now?", "Equation": "( ( 16.0 - 11.0 ) + 12.0 )", "Answer": 17.0, "Type": "Addition"}, {"ID": "chal-676", "Body": "<PERSON> was reading through his favorite book. The book had 193 pages equally distributed over 15 chapters. It took <PERSON> 660 days to finish the book.", "Question": "How many chapters did he read per day?", "Equation": "( 660.0 / 15.0 )", "Answer": 44.0, "Type": "Common-Division"}, {"ID": "chal-677", "Body": "The grasshopper, the frog and the mouse had a jumping contest. The grasshopper jumped 19 inches. The grasshopper jumped 4 inches farther than the frog and the mouse jumped 44 inches lesser than the frog.", "Question": "How far did the frog jump?", "Equation": "( 19.0 - 4.0 )", "Answer": 15.0, "Type": "Subtraction"}, {"ID": "chal-678", "Body": "Winter is almost here and most animals are migrating to warmer countries. There are 18 bird families living near the mountain. If 38 bird families flew away to africa and 80 bird families flew away to asia", "Question": "How many bird families flew away for the winter?", "Equation": "( 38.0 + 80.0 )", "Answer": 118.0, "Type": "Addition"}, {"ID": "chal-679", "Body": "<PERSON> had 23 crackers. He has 11 crackers left after he gave equal numbers of crackers to his 2 friends.", "Question": "How many crackers did each friend eat?", "Equation": "( ( 23.0 - 11.0 ) / 2.0 )", "Answer": 6.0, "Type": "Common-Division"}, {"ID": "chal-680", "Body": "<PERSON>'s tree had 4 apples. She picked 2 apples from her tree. Thereafter 3 new apples grew on the tree.", "Question": "How many apples are there on the tree now?", "Equation": "( ( 4.0 - 2.0 ) + 3.0 )", "Answer": 1.0, "Type": "Addition"}, {"ID": "chal-681", "Body": "Summer is almost here and most animals are migrating back to cooler countries. There are 87 bird families living near a mountain. If 18 new bird families joined them", "Question": "How many bird families live near the mountain now?", "Equation": "( 87.0 + 18.0 )", "Answer": 105.0, "Type": "Addition"}, {"ID": "chal-682", "Body": "<PERSON> collects bottle caps and wrappers. He found 15 bottle caps and 18 wrappers at the park. Now he has 67 wrappers and 35 bottle caps in his collection.", "Question": "How many more wrappers than bottle caps does danny have now?", "Equation": "( 67.0 - 35.0 )", "Answer": 32.0, "Type": "Subtraction"}, {"ID": "chal-683", "Body": "<PERSON> scored a total of 91 points in 13 games scoring the same for each game.", "Question": "How many points did she score in each game?", "Equation": "( 91.0 / 13.0 )", "Answer": 7.0, "Type": "Common-Division"}, {"ID": "chal-684", "Body": "<PERSON>'s hair was 17 inches long. He cut off some inches of her hair. If his hair is now 13 inches long", "Question": "How much of his hair did he cut?", "Equation": "( 17.0 - 13.0 )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-685", "Body": "<PERSON> made 56 pastries and 124 cakes. If he sold 104 cakes and 29 pastries", "Question": "How many pastries would baker still have?", "Equation": "( 56.0 - 29.0 )", "Answer": 27.0, "Type": "Subtraction"}, {"ID": "chal-686", "Body": "The school is planning a field trip. The school has 67 classrooms. There are 66 students in each classroom in the school. If there are 6 seats on each school bus", "Question": "How many buses are needed to take the trip?", "Equation": "( 67.0 * ( 66.0 / 6.0 ) )", "Answer": 737.0, "Type": "Multiplication"}, {"ID": "chal-687", "Body": "The Razorback t-shirt shop makes $ 87 dollars off each t-shirt sold. During the Arkansas game and the Texas tech game they sold a total of 95 t-shirts. If they sold 47 t-shirts during the Arkansas game", "Question": "How much money did they make from selling the t-shirts?", "Equation": "( 87.0 * 95.0 )", "Answer": 8265.0, "Type": "Multiplication"}, {"ID": "chal-688", "Body": "3 birds were sitting on the fence. 2 more birds and 6 more storks came to join them.", "Question": "How many more storks than birds are sitting on the fence?", "Equation": "( 6.0 - ( 3.0 + 2.0 ) )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-689", "Body": "The school is planning a field trip. The school has 21 classrooms. There are 98 students in the school and 7 school buses. If all buses are full", "Question": "How many seats are in each bus?", "Equation": "( 98.0 / 7.0 )", "Answer": 14.0, "Type": "Common-Division"}, {"ID": "chal-690", "Body": "<PERSON> has 58 blocks. He uses 27 blocks to build a tower and 53 blocks to build a house.", "Question": "How many blocks did he use to build the tower and the house altogether?", "Equation": "( 27.0 + 53.0 )", "Answer": 80.0, "Type": "Addition"}, {"ID": "chal-691", "Body": "Every day <PERSON> spends 5 hours on learning english and 4 hours on learning chinese. If he learns for 6 days", "Question": "How many hours does he spend on learning chinese in all?", "Equation": "( 4.0 * 6.0 )", "Answer": 24.0, "Type": "Multiplication"}, {"ID": "chal-692", "Body": "The Razorback shop makes $ 86 dollars off each t-shirt and $ 232 off each jersey. During the Arkansas and Texas tech game they sold 254 t-shirts and 43 jerseys.", "Question": "How much more does a jersey cost than a t-shirt?", "Equation": "( 232.0 - 86.0 )", "Answer": 146.0, "Type": "Subtraction"}, {"ID": "chal-693", "Body": "<PERSON> collects bottle caps and wrappers. He found 66 wrappers and 39 bottle caps at the park. Now he has 16 bottle caps and 68 wrappers in his collection.", "Question": "How many wrappers did danny have at first?", "Equation": "( 68.0 - 66.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-694", "Body": "<PERSON> grew 14 trees in her backyard. After a typhoon 9 died.", "Question": "How many more trees died in the typhoon than those that survived?", "Equation": "( 9.0 - ( 14.0 - 9.0 ) )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-695", "Body": "He has 9 apples more than <PERSON> and <PERSON> together do. <PERSON> and <PERSON> have 7 apples between them.", "Question": "How many apples does He have?", "Equation": "( 7.0 + 9.0 )", "Answer": 16.0, "Type": "Addition"}, {"ID": "chal-696", "Body": "<PERSON> had 11 apps and 3 files on his phone. After deleting some apps and files he had 24 files and 2 apps left.", "Question": "How many more files than apps does he have left on the phone?", "Equation": "( 24.0 - 2.0 )", "Answer": 22.0, "Type": "Subtraction"}, {"ID": "chal-697", "Body": "<PERSON> wants to split a collection of eggs into groups of 11. If each group has 2 eggs", "Question": "How many eggs does <PERSON> have?", "Equation": "( 11.0 * 2.0 )", "Answer": 22.0, "Type": "Multiplication"}, {"ID": "chal-698", "Body": "<PERSON> received 10 emails in the morning, 7 emails in the afternoon and 17 emails in the evening.", "Question": "How many more emails did <PERSON> receive in the morning than in the afternoon?", "Equation": "( 10.0 - 7.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-699", "Body": "<PERSON> could fit 11 action figures on each shelf in his room. His room has 4 shelves and 40 cabinets.", "Question": "How many total action figures could his shelves hold?", "Equation": "( 11.0 * 4.0 )", "Answer": 44.0, "Type": "Multiplication"}, {"ID": "chal-700", "Body": "<PERSON> went to the grocery store. She bought 10 packs of cookie and 4 packs of cake.", "Question": "How many more packs of cookie did she buy over cake?", "Equation": "( 10.0 - 4.0 )", "Answer": 6.0, "Type": "Subtraction"}, {"ID": "chal-701", "Body": "Every day <PERSON> spends 6 hours on learning english and 2 hours on learning chinese.", "Question": "How many more hours does he spend on learning english than he does on learning chinese?", "Equation": "( 6.0 - 2.0 )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-702", "Body": "<PERSON> did 40 more push-ups but 17 less crunches than <PERSON> in gym class today. If <PERSON> did 34 push-ups and 62 crunches", "Question": "How many crunches did <PERSON> do?", "Equation": "( 62.0 - 17.0 )", "Answer": 45.0, "Type": "Subtraction"}, {"ID": "chal-703", "Body": "The grasshopper and the frog had a jumping contest. The frog jumped 39 inches and the grasshopper jumped 17 inches.", "Question": "How much farther did the frog jump than the grasshopper?", "Equation": "( 39.0 - 17.0 )", "Answer": 22.0, "Type": "Subtraction"}, {"ID": "chal-704", "Body": "<PERSON> had 50 books. After buying some in a garage sale he had 151 left.", "Question": "How many books did he buy?", "Equation": "( 151.0 - 50.0 )", "Answer": 101.0, "Type": "Subtraction"}, {"ID": "chal-705", "Body": "If they are already at 659 feet and the cave is 762 feet deep", "Question": "How much farther until they reach the end of the cave?", "Equation": "( 762.0 - 659.0 )", "Answer": 103.0, "Type": "Subtraction"}, {"ID": "chal-706", "Body": "<PERSON> had 78 new games and 86 old games. Her friends had 48 new games.", "Question": "How many new games do they have together?", "Equation": "( 78.0 + 48.0 )", "Answer": 126.0, "Type": "Addition"}, {"ID": "chal-707", "Body": "<PERSON> had 19 marbles in his collection. He lost 11 marbles and found 5 new ones.", "Question": "How many marbles does he have now?", "Equation": "( ( 19.0 - 11.0 ) + 5.0 )", "Answer": 13.0, "Type": "Addition"}, {"ID": "chal-708", "Body": "<PERSON> had 4 action figures on a shelf in his room. Later he added some more action figures to the shelf. If there are a total of 8 action figures on his shelf now", "Question": "How many action figures did he add to the shelf?", "Equation": "( 8.0 - 4.0 )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-709", "Body": "<PERSON> has 52 apple trees. She picked 2 apples from 1 of her trees. Now the tree has 7 apples still on it.", "Question": "How many apples did the tree have to begin with?", "Equation": "( 2.0 + 7.0 )", "Answer": 9.0, "Type": "Addition"}, {"ID": "chal-710", "Body": "We ordered 9 pizzas. Each pizza has 10 slices. If there are 2 of us", "Question": "How many slices of pizza does each of us get if distributed equally?", "Equation": "( 9.0 * ( 10.0 / 2.0 ) )", "Answer": 45.0, "Type": "Multiplication"}, {"ID": "chal-711", "Body": "<PERSON> received a total of 9 emails in the day. If he received 7 emails in the morning and some more in the afternoon", "Question": "How many emails did <PERSON> receive in the afternoon?", "Equation": "( 9.0 - 7.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-712", "Body": "A grocery store had 19 bottles of diet soda and 60 bottles of regular soda.", "Question": "How many more bottles of regular soda than diet soda did they have?", "Equation": "( 60.0 - 19.0 )", "Answer": 41.0, "Type": "Subtraction"}, {"ID": "chal-713", "Body": "A book has 3 chapters. The first chapter is 35 pages long the second chapter is 18 pages long and the third chapter is 3 pages long.", "Question": "How many more pages does the second chapter have than the third chapter?", "Equation": "( 18.0 - 3.0 )", "Answer": 15.0, "Type": "Subtraction"}, {"ID": "chal-714", "Body": "<PERSON> has 97 blocks. He uses some blocks to build a tower. If there are 72 blocks left", "Question": "How many blocks did he use to build the tower?", "Equation": "( 97.0 - 72.0 )", "Answer": 25.0, "Type": "Subtraction"}, {"ID": "chal-715", "Body": "Because of the decision <PERSON> asked the students to suggest specific types of food. 182 students suggested adding mashed potatoes while others suggested adding bacon to the menu. If 166 more students suggested adding bacon than those that suggested mashed potatoes", "Question": "How many students suggested bacon?", "Equation": "( 182.0 + 166.0 )", "Answer": 348.0, "Type": "Addition"}, {"ID": "chal-716", "Body": "A waiter had 13 customers. After some left he got 4 new customers. If he still had 9 customers", "Question": "How many customers left?", "Equation": "( 13.0 - ( 9.0 - 4.0 ) )", "Answer": 8.0, "Type": "Subtraction"}, {"ID": "chal-717", "Body": "Every day <PERSON> spends 3 hours on learning english and some more hours on learning chinese. If he spends a total of 4 hours on learning english and chinese everyday", "Question": "How many hours does he spend on learning chinese?", "Equation": "( 4.0 - 3.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-718", "Body": "A book has 2 chapters across 81 pages. The second chapter is 68 pages long.", "Question": "How many pages are in the second chapter?", "Equation": "( 81.0 - 68.0 )", "Answer": 13.0, "Type": "Subtraction"}, {"ID": "chal-719", "Body": "White t - shirts can be purchased in packages. If mom buys 28 packages which contain 56 white t - shirts in total", "Question": "How many white t - shirts does each package have?", "Equation": "( 56.0 / 28.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-720", "Body": "<PERSON> made 52 pastries and 84 cakes. If he sold 102 cakes and 94 pastries", "Question": "How many more cakes than pastries did baker make?", "Equation": "( 84.0 - 52.0 )", "Answer": 32.0, "Type": "Subtraction"}, {"ID": "chal-721", "Body": "<PERSON> weighed 71 kilograms. After she started to go jogging everyday she lost 20 kilograms.", "Question": "How much does she weigh now?", "Equation": "( 71.0 - 20.0 )", "Answer": 51.0, "Type": "Subtraction"}, {"ID": "chal-722", "Body": "Lawrence county has 898051 kids. During summer break 629424 kids go to camp and the rest stay home.", "Question": "How many kids stay home?", "Equation": "( 898051.0 - 629424.0 )", "Answer": 268627.0, "Type": "Subtraction"}, {"ID": "chal-723", "Body": "<PERSON> had 17 apps and 21 files on his phone. After deleting some apps and files he had 3 apps and 7 files left.", "Question": "How many files did he delete?", "Equation": "( 21.0 - 7.0 )", "Answer": 14.0, "Type": "Subtraction"}, {"ID": "chal-724", "Body": "If you had 33 cookies and each bag has 11 cookies", "Question": "How many bags of cookies do you have?", "Equation": "( 33.0 / 11.0 )", "Answer": 3.0, "Type": "Common-Division"}, {"ID": "chal-725", "Body": "He then went to see the oranges being harvested. He found out that they harvest 66 sacks per day and that each sack containes 25 oranges.", "Question": "How many oranges will they have after 87 days of harvest?", "Equation": "( ( 66.0 * 87.0 ) * 25.0 )", "Answer": 143550.0, "Type": "Multiplication"}, {"ID": "chal-726", "Body": "<PERSON> collects baseball cards. She had 242 cards. She gave 136 of her cards to <PERSON>.", "Question": "How many cards does <PERSON> have left?", "Equation": "( 242.0 - 136.0 )", "Answer": 106.0, "Type": "Subtraction"}, {"ID": "chal-727", "Body": "<PERSON> the hippo and her friends are preparing for thanksgiving at <PERSON>'s house. <PERSON> baked 90 chocolate chip cookies yesterday and 51 raisin cookies and 484 chocolate chip cookies this morning.", "Question": "How many chocolate chip cookies did <PERSON> bake?", "Equation": "( 90.0 + 484.0 )", "Answer": 574.0, "Type": "Addition"}, {"ID": "chal-728", "Body": "There were 13 roses in the vase. <PERSON> cut some more roses from her flower garden which had a total of 12 roses. There are now 21 roses in the vase.", "Question": "How many roses are left in the garden?", "Equation": "( 12.0 - ( 21.0 - 13.0 ) )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-729", "Body": "<PERSON> had some action figures on a shelf in his room. Later he added 2 more action figures to the shelf and removed 7 of the old ones. If there are 10 action figures on his shelf now", "Question": "How many action figures did he have initially on the shelf?", "Equation": "( ( 10.0 + 7.0 ) - 2.0 )", "Answer": 15.0, "Type": "Subtraction"}, {"ID": "chal-730", "Body": "A book has 2 chapters across 23 pages. The first chapter is 10 pages long.", "Question": "How many pages are in the first chapter?", "Equation": "( 23.0 - 10.0 )", "Answer": 13.0, "Type": "Subtraction"}, {"ID": "chal-731", "Body": "<PERSON> had 21 books. After selling some in a garage sale he bought 42 new ones. If he has 15 books now", "Question": "How many more books did he sell than he bought?", "Equation": "( 21.0 - 15.0 )", "Answer": 6.0, "Type": "Subtraction"}, {"ID": "chal-732", "Body": "<PERSON> and <PERSON> brought 3 balloons to the park. If <PERSON> brought 2 balloons", "Question": "How many balloons did <PERSON> bring to the park?", "Equation": "( 3.0 - 2.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-733", "Body": "There are 270 students in a school. If the school has 5 students in each grades and each grade has the same number of students", "Question": "How many grades are there in total?", "Equation": "( 270.0 / 5.0 )", "Answer": 54.0, "Type": "Common-Division"}, {"ID": "chal-734", "Body": "After finding some bottle caps at the park danny has 32 bottle caps in his collection. If he had 25 bottle caps in his collection earlier", "Question": "How many bottle caps did he find at the park?", "Equation": "( 32.0 - 25.0 )", "Answer": 7.0, "Type": "Subtraction"}, {"ID": "chal-735", "Body": "A mailman has to give 32 pieces of junk mail to each of the 55 blocks. If he gives 8 mails to each house in a block", "Question": "How many houses are there in a block?", "Equation": "( 32.0 / 8.0 )", "Answer": 4.0, "Type": "Common-Division"}, {"ID": "chal-736", "Body": "Winter is almost here and most animals are migrating to warmer countries. There are 41 bird families living near the mountain. If 35 bird families flew away to asia and 62 bird families flew away to africa", "Question": "How many more bird families flew away to africa than those that flew away to asia?", "Equation": "( 62.0 - 35.0 )", "Answer": 27.0, "Type": "Subtraction"}, {"ID": "chal-737", "Body": "<PERSON> got a box of 65 crayons for his birthday. During the school year he gave 213 crayons to his friends while he lost 16 crayons.", "Question": "How many crayons had been lost or given away?", "Equation": "( 213.0 + 16.0 )", "Answer": 229.0, "Type": "Addition"}, {"ID": "chal-738", "Body": "<PERSON> had to complete 5 pages of math homework and yet more pages of reading homework. If she had to complete a total of 7 pages of homework", "Question": "How many pages of reading homework did she have to complete?", "Equation": "( 7.0 - 5.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-739", "Body": "<PERSON>'s mother made cookies for 5. She prepared 22 cookies but had to throw away 17 cookies. If each of them had the same number of cookies", "Question": "How many did each of them have?", "Equation": "( ( 22.0 - 17.0 ) / 5.0 )", "Answer": 1.0, "Type": "Common-Division"}, {"ID": "chal-740", "Body": "There were 6 roses and 20 orchids in the vase. <PERSON> cut some more roses and orchids from her flower garden. There are now 21 orchids and 22 roses in the vase.", "Question": "How many more roses than orchids are there in the vase now?", "Equation": "( 22.0 - 21.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-741", "Body": "He had a total of 40 saltwater animals in different aquariums. Each aquarium has 2 animals in it.", "Question": "How many aquariums did he have?", "Equation": "( 40.0 / 2.0 )", "Answer": 20.0, "Type": "Common-Division"}, {"ID": "chal-742", "Body": "Every day <PERSON> spends 6 hours on learning english and 7 hours on learning chinese. If he learns for 5 days", "Question": "How many hours does he spend on learning english and chinese in all?", "Equation": "( ( 6.0 + 7.0 ) * 5.0 )", "Answer": 65.0, "Type": "Multiplication"}, {"ID": "chal-743", "Body": "<PERSON> picked 7 apples from her tree. Thereafter 2 new apples grew on the tree. Now the tree has 6 apples still on it.", "Question": "How many apples did the tree have to begin with?", "Equation": "( ( 6.0 - 2.0 ) + 7.0 )", "Answer": 11.0, "Type": "Addition"}, {"ID": "chal-744", "Body": "There were 8 people on the bus. At the next stop 12 more people got on the bus and 3 people got off.", "Question": "How many people are there on the bus now?", "Equation": "( ( 8.0 + 12.0 ) - 3.0 )", "Answer": 17.0, "Type": "Subtraction"}, {"ID": "chal-745", "Body": "<PERSON> had 15 apps and 24 files on his phone. After deleting some apps and files he had 21 apps and 4 files left.", "Question": "How many more apps than files does he have left on the phone?", "Equation": "( 21.0 - 4.0 )", "Answer": 17.0, "Type": "Subtraction"}, {"ID": "chal-746", "Body": "<PERSON> made 173 cakes. He bought 103 new cakes and sold 86 cakes.", "Question": "How many cakes does baker still have?", "Equation": "( ( 173.0 - 86.0 ) + 103.0 )", "Answer": 190.0, "Type": "Addition"}, {"ID": "chal-747", "Body": "<PERSON> is baking a cake. The recipe calls for 11 cups of sugar 6 cups of flour and 9 cups of salt. She already put in 12 cups of flour.", "Question": "How many more cups of sugar than cups of salt does she need to add now?", "Equation": "( 11.0 - 9.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-748", "Body": "<PERSON> learns english and chinese for 7 days. Every day he spends 4 hours on learning english and 5 hours on learning chinese.", "Question": "How many hours does he spend on learning english and chinese in all?", "Equation": "( ( 4.0 + 5.0 ) * 7.0 )", "Answer": 63.0, "Type": "Multiplication"}, {"ID": "chal-749", "Body": "<PERSON> has $ 7. He bought a candy bar for $ 2 and a chocolate for $ 3.", "Question": "How much money is left?", "Equation": "( 7.0 - ( 2.0 + 3.0 ) )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-750", "Body": "During summer break 61619 kids from Lawrence county stayed home and the rest went to camp. The total number of kids in Lawrence county is 91676.", "Question": "About how many kids in Lawrence county went to camp?", "Equation": "( 91676.0 - 61619.0 )", "Answer": 30057.0, "Type": "Subtraction"}, {"ID": "chal-751", "Body": "After <PERSON> started to go jogging everyday she lost 126 kilograms. She currently weighs 66 kilograms.", "Question": "How much did she weigh before starting to jog?", "Equation": "( 66.0 + 126.0 )", "Answer": 192.0, "Type": "Addition"}, {"ID": "chal-752", "Body": "<PERSON> had 7 marbles in his collection. He lost 8 marbles and found 10 new ones.", "Question": "How many more marbles did he find than those he lost?", "Equation": "( 10.0 - 8.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-753", "Body": "<PERSON> collects cards. She had 239 baseball cards and 38 Ace cards. She gave some of her cards to <PERSON> and now has 376 Ace cards and 111 baseball cards left.", "Question": "How many more Ace cards than baseball cards does <PERSON> have?", "Equation": "( 376.0 - 111.0 )", "Answer": 265.0, "Type": "Subtraction"}, {"ID": "chal-754", "Body": "In a school there are 362 boys and 257 girls. 403 more girls joined the school.", "Question": "How many more girls than boys does the school have?", "Equation": "( ( 257.0 + 403.0 ) - 362.0 )", "Answer": 298.0, "Type": "Subtraction"}, {"ID": "chal-755", "Body": "<PERSON> got a box of some crayons for his birthday. By the end of the school year he had either lost or given away 551 of them and only had 177 left.", "Question": "How many crayons did he get for his birthday?", "Equation": "( 551.0 + 177.0 )", "Answer": 728.0, "Type": "Addition"}, {"ID": "chal-756", "Body": "<PERSON> took a look at his books as well. If he has a total of 42 books and each bookshelf contains 2 books", "Question": "How many bookshelves does he have?", "Equation": "( 42.0 / 2.0 )", "Answer": 21.0, "Type": "Common-Division"}, {"ID": "chal-757", "Body": "<PERSON> played tag with 16 kids on monday. If she played tag with 12 more kids on monday than on tuesday", "Question": "How many kids did she play with on tuesday?", "Equation": "( 16.0 - 12.0 )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-758", "Body": "He then went to see the oranges being harvested. He found out that they harvest 41 sacks of unripe oranges and 86 sacks of ripe oranges per day.", "Question": "How many more sacks of ripe oranges than unripe oranges are harvested per day?", "Equation": "( 86.0 - 41.0 )", "Answer": 45.0, "Type": "Subtraction"}, {"ID": "chal-759", "Body": "<PERSON> brought 5 balloons. If <PERSON> and <PERSON> brought total of 6 balloons to the park", "Question": "How many balloons did <PERSON> bring to the park?", "Equation": "( 6.0 - 5.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-760", "Body": "The ring toss game at the carnival made 144 dollars per day. They were at the carnival for 22 days.", "Question": "How much money did they make?", "Equation": "( 144.0 * 22.0 )", "Answer": 3168.0, "Type": "Multiplication"}, {"ID": "chal-761", "Body": "Each basket of peaches has 19 red peaches and 4 green peaches. If there are 15 such baskets", "Question": "How many peaches are in the baskets altogether?", "Equation": "( ( 19.0 + 4.0 ) * 15.0 )", "Answer": 345.0, "Type": "Multiplication"}, {"ID": "chal-762", "Body": "<PERSON><PERSON> had 37 sweet cookies and 11 salty cookies. He ate 5 sweet cookies and 2 salty cookies.", "Question": "How many more sweet cookies than salty cookies did he eat?", "Equation": "( 5.0 - 2.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-763", "Body": "<PERSON> has 11 fewer peaches than <PERSON>. If <PERSON> has 17 peaches.", "Question": "How many peaches does <PERSON> have?", "Equation": "( 11.0 + 17.0 )", "Answer": 28.0, "Type": "Addition"}, {"ID": "chal-764", "Body": "<PERSON> got a box of 267 crayons for his birthday. During the school year he lost 231 crayons while he gave away 308 crayons to his friends.", "Question": "How many more crayons did he give to his friends than those he lost?", "Equation": "( 308.0 - 231.0 )", "Answer": 77.0, "Type": "Subtraction"}, {"ID": "chal-765", "Body": "If the cave is 919 feet deep and they need to travel 1307 feet", "Question": "How far is the end of the cave?", "Equation": "( 919.0 + 1307.0 )", "Answer": 2226.0, "Type": "Addition"}, {"ID": "chal-766", "Body": "There are 203 bananas and 63 oranges in <PERSON>'s collection. If the bananas are organized into 7 groups and oranges are organized into 95 groups", "Question": "How big is each group of bananas?", "Equation": "( 203.0 / 7.0 )", "Answer": 29.0, "Type": "Common-Division"}, {"ID": "chal-767", "Body": "<PERSON> wants to split a collection of eggs into groups of 5. <PERSON> has 20 eggs and 6 marbles.", "Question": "How many more eggs does <PERSON> have than marbles?", "Equation": "( 20.0 - 6.0 )", "Answer": 14.0, "Type": "Subtraction"}, {"ID": "chal-768", "Body": "<PERSON> raised 15 goldfish in the pond but stray cats loved eating them. 5 were eaten.", "Question": "How many goldfish remained?", "Equation": "( 15.0 - 5.0 )", "Answer": 10.0, "Type": "Subtraction"}, {"ID": "chal-769", "Body": "<PERSON> did 59 push-ups and 44 crunches in gym class today. <PERSON> did 19 more push-ups but 27 less crunches than zach<PERSON>.", "Question": "How many push-ups did <PERSON> do?", "Equation": "( 59.0 + 19.0 )", "Answer": 78.0, "Type": "Addition"}, {"ID": "chal-770", "Body": "<PERSON> had 19 apps on his phone. He deleted 5 apps.", "Question": "How many apps are left on his phone?", "Equation": "( 19.0 - 5.0 )", "Answer": 14.0, "Type": "Subtraction"}, {"ID": "chal-771", "Body": "<PERSON> is baking a cake. The recipe calls for 9 cups of sugar 14 cups of flour and 40 cups of salt. She already put in 4 cups of flour.", "Question": "How many more cups of flour than cups of sugar does she need to add now?", "Equation": "( ( 14.0 - 4.0 ) - 9.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-772", "Body": "He then went to see the oranges being harvested. He found out that they harvest 82 sacks of ripe oranges and 61 sacks of unripe oranges per day.", "Question": "How many sacks of ripe oranges will they have after 25 days of harvest?", "Equation": "( 82.0 * 25.0 )", "Answer": 2050.0, "Type": "Multiplication"}, {"ID": "chal-773", "Body": "<PERSON> received 10 emails and 12 letters in the morning. He then received 3 emails and 44 letters in the afternoon.", "Question": "How many more emails did <PERSON> receive in the morning than in the afternoon?", "Equation": "( 10.0 - 3.0 )", "Answer": 7.0, "Type": "Subtraction"}, {"ID": "chal-774", "Body": "<PERSON> had 62 new games and 80 old games. Her friends had 73 new games and 41 old games.", "Question": "How many old games do they have 3", "Equation": "( 80.0 + 41.0 )", "Answer": 121.0, "Type": "Addition"}, {"ID": "chal-775", "Body": "Together <PERSON> and <PERSON> have 12 apples. He has 9 apples more than <PERSON> and <PERSON> together do. <PERSON> has 8 more apples than <PERSON>.", "Question": "How many apples does He have?", "Equation": "( 12.0 + 9.0 )", "Answer": 21.0, "Type": "Addition"}, {"ID": "chal-776", "Body": "He then went to see the oranges being harvested. He found out that they harvest 67 sacks per day and that each sack containes 12 oranges.", "Question": "How many oranges are harvested if they harvest a total of 35 sacks of oranges?", "Equation": "( 12.0 * 35.0 )", "Answer": 420.0, "Type": "Multiplication"}, {"ID": "chal-777", "Body": "There are 87 oranges and 290 bananas in <PERSON>'s collection. If the bananas are organized into 2 groups and oranges are organized into 93 groups", "Question": "How big is each group of bananas?", "Equation": "( 290.0 / 2.0 )", "Answer": 145.0, "Type": "Common-Division"}, {"ID": "chal-778", "Body": "There are 384 oranges and 192 bananas in <PERSON>'s collection. If the bananas are organized into 345 groups and oranges are organized into 16 groups", "Question": "How big is each group of oranges?", "Equation": "( 384.0 / 16.0 )", "Answer": 24.0, "Type": "Common-Division"}, {"ID": "chal-779", "Body": "<PERSON> did 27 more push-ups but 7 less crunches than <PERSON> in gym class today. If <PERSON> did 5 push-ups and 17 crunches", "Question": "How many more crunches than push-ups did <PERSON> do?", "Equation": "( 17.0 - 5.0 )", "Answer": 12.0, "Type": "Subtraction"}, {"ID": "chal-780", "Body": "He also had 74 aquariums for freshwater animals and 22 aquariums for saltwater animals. Each aquarium has 46 animals in it.", "Question": "How many saltwater animals does <PERSON> have?", "Equation": "( 22.0 * 46.0 )", "Answer": 1012.0, "Type": "Multiplication"}, {"ID": "chal-781", "Body": "There were 106 dollars in <PERSON>'s wallet. After she visited a supermarket and a showroom there were 26 dollars left. If she spent 49 dollars at the showroom", "Question": "How much did she spend at the supermarket?", "Equation": "( ( 106.0 - 26.0 ) - 49.0 )", "Answer": 31.0, "Type": "Subtraction"}, {"ID": "chal-782", "Body": "The grasshopper, the frog and the mouse had a jumping contest. The grasshopper jumped 24 inches. The frog jumped 33 inches farther than the grasshopper and the mouse jumped 5 inches lesser than the frog.", "Question": "How much farther did the mouse jump than the grasshopper?", "Equation": "( 33.0 - 5.0 )", "Answer": 28.0, "Type": "Subtraction"}, {"ID": "chal-783", "Body": "<PERSON> picked 6 ripe apples from her tree. Now the tree has 2 ripe apples and 4 unripe apples.", "Question": "How many apples did the tree have to begin with?", "Equation": "( ( 6.0 + 2.0 ) + 4.0 )", "Answer": 12.0, "Type": "Addition"}, {"ID": "chal-784", "Body": "<PERSON> was reading through his favorite book. The book had 392 pages and he read 14 pages per day.", "Question": "How many days did he take to finish the book?", "Equation": "( 392.0 / 14.0 )", "Answer": 28.0, "Type": "Common-Division"}, {"ID": "chal-785", "Body": "<PERSON> had 24 more marbles than <PERSON>. <PERSON> lost 27 of his marbles at the playground while <PERSON> found 9 more marbles.", "Question": "How many more marbles did <PERSON> have than <PERSON> then?", "Equation": "( ( 24.0 + 27.0 ) + 9.0 )", "Answer": 60.0, "Type": "Addition"}, {"ID": "chal-786", "Body": "<PERSON> was placing her pencils into rows with 5 pencils in each row. If she had 35 pencils and 7 crayons", "Question": "How many rows could she make?", "Equation": "( 35.0 / 5.0 )", "Answer": 7.0, "Type": "Common-Division"}, {"ID": "chal-787", "Body": "Some children were riding on the bus. At the bus stop 2 more children got on the bus. Then there were 41 children altogether on the bus.", "Question": "How many children were riding on the bus before the bus stop?", "Equation": "( 41.0 - 2.0 )", "Answer": 39.0, "Type": "Subtraction"}, {"ID": "chal-788", "Body": "<PERSON> has 12 bottle caps in his collection. He found 53 bottle caps at the park.", "Question": "How many bottle caps does he have now?", "Equation": "( 12.0 + 53.0 )", "Answer": 65.0, "Type": "Addition"}, {"ID": "chal-789", "Body": "<PERSON> brought 5 balloons and <PERSON> brought 3 balloons to the park.", "Question": "How many more balloons did <PERSON> have than <PERSON> in the park?", "Equation": "( 5.0 - 3.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-790", "Body": "Winter is almost here and most animals are migrating to warmer countries. There are 8 bird families living near the mountain. If 42 bird families flew away to africa and 31 bird families flew away to asia", "Question": "How many more bird families flew away to africa than those that flew away to asia?", "Equation": "( 42.0 - 31.0 )", "Answer": 11.0, "Type": "Subtraction"}, {"ID": "chal-791", "Body": "<PERSON> did 46 push-ups and 58 crunches in gym class today. <PERSON> did 38 more push-ups but 62 less crunches than zach<PERSON>.", "Question": "How many more crunches than push-ups did <PERSON> do?", "Equation": "( 58.0 - 46.0 )", "Answer": 12.0, "Type": "Subtraction"}, {"ID": "chal-792", "Body": "The Ferris wheel in paradise park has 4 seats. If 20 people can ride the wheel at the same time", "Question": "How many people can each seat hold?", "Equation": "( 20.0 / 4.0 )", "Answer": 5.0, "Type": "Common-Division"}, {"ID": "chal-793", "Body": "<PERSON> had to complete 2 pages of math homework, 3 pages of reading homework and 10 more pages of biology homework.", "Question": "How many pages did she have to complete in all?", "Equation": "( ( 2.0 + 3.0 ) + 10.0 )", "Answer": 15.0, "Type": "Addition"}, {"ID": "chal-794", "Body": "For <PERSON>'s birthday she received 3 dollars from her mom. Her dad gave her 6 more dollars. If she spent 4 dollars.", "Question": "How much more money did she receive from her dad than she did from her mom?", "Equation": "( 6.0 - 3.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-795", "Body": "<PERSON> made 110 cakes. He sold 75 of them. Then he made 76 more cakes.", "Question": "How many cakes would baker still have?", "Equation": "( ( 110.0 - 75.0 ) + 76.0 )", "Answer": 111.0, "Type": "Addition"}, {"ID": "chal-796", "Body": "<PERSON> made 61 pastries and 167 cakes. If he sold 108 cakes and 44 pastries", "Question": "How many cakes would baker still have?", "Equation": "( 167.0 - 108.0 )", "Answer": 59.0, "Type": "Subtraction"}, {"ID": "chal-797", "Body": "There are 11 different books and 17 different movies in the ' crazy silly school ' series. If you read 7 of the books and watched 21 of the movies", "Question": "How many more movies than books have you read?", "Equation": "( 21.0 - 7.0 )", "Answer": 14.0, "Type": "Subtraction"}, {"ID": "chal-798", "Body": "A mailman gives 2 junk mails to each house in a block. If the mailman has to give 14 pieces of junk mail to each block.", "Question": "How many houses are there in a block?", "Equation": "( 14.0 / 2.0 )", "Answer": 7.0, "Type": "Common-Division"}, {"ID": "chal-799", "Body": "<PERSON> had 6 more marbles than <PERSON>. <PERSON> lost 7 of his marbles at the playground. If <PERSON> had 22 marbles", "Question": "How many marbles does <PERSON> have now?", "Equation": "( ( 22.0 - 6.0 ) - 7.0 )", "Answer": 9.0, "Type": "Subtraction"}, {"ID": "chal-800", "Body": "<PERSON> has $ 2. He bought a candy bar for $ 6 and a chocolate for $ 3.", "Question": "How much money did he spend to buy candy bar than he did to buy chocolate?", "Equation": "( 6.0 - 3.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-801", "Body": "A waiter had 3 customers. After some left he still had 4 customers.", "Question": "How many more customers stayed behind than those that left?", "Equation": "( 4.0 - ( 3.0 - 4.0 ) )", "Answer": 5.0, "Type": "Subtraction"}, {"ID": "chal-802", "Body": "<PERSON> played tag with 14 kids on tuesday. If she played tag with 8 more kids on monday than on tuesday", "Question": "How many kids did she play with on monday?", "Equation": "( 14.0 + 8.0 )", "Answer": 22.0, "Type": "Addition"}, {"ID": "chal-803", "Body": "<PERSON> brought 5 balloons to the park. If <PERSON> brought 6 more balloons than the number of balloons that <PERSON> brought", "Question": "How many balloons did <PERSON> bring to the park?", "Equation": "( 5.0 + 6.0 )", "Answer": 11.0, "Type": "Addition"}, {"ID": "chal-804", "Body": "<PERSON> has 10 apples. <PERSON> has 8 apples.", "Question": "How many more apples does <PERSON> have than <PERSON>?", "Equation": "( 10.0 - 8.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-805", "Body": "<PERSON> made 19 cakes and 131 pastries. If he sold 70 cakes and 88 pastries", "Question": "How many more pastries than cakes did baker make?", "Equation": "( 131.0 - 19.0 )", "Answer": 112.0, "Type": "Subtraction"}, {"ID": "chal-806", "Body": "For <PERSON>'s birthday she received 7 dollars. She spent some money and has 5 dollars left with her.", "Question": "How much money did she spend?", "Equation": "( 7.0 - 5.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-807", "Body": "18 red peaches, 14 yellow peaches and 17 green peaches are in the basket.", "Question": "How many red and yellow peaches are in the basket?", "Equation": "( 18.0 + 14.0 )", "Answer": 32.0, "Type": "Addition"}, {"ID": "chal-808", "Body": "<PERSON> gave equal numbers of crackers and cakes to his 4 friends. If he had 32 crackers and 98 cakes initially", "Question": "How many crackers did each person eat?", "Equation": "( 32.0 / 4.0 )", "Answer": 8.0, "Type": "Common-Division"}, {"ID": "chal-809", "Body": "He then went to see the oranges being harvested. He found out that they harvested 56 sacks of oranges.", "Question": "How many sacks did they harvest per day if they harvested for a total of 14 days?", "Equation": "( 56.0 / 14.0 )", "Answer": 4.0, "Type": "Common-Division"}, {"ID": "chal-810", "Body": "<PERSON> has 4 more apples than <PERSON>. Together <PERSON> and <PERSON> have 14 apples. <PERSON> has 6 apples more than <PERSON> and <PERSON> together do.", "Question": "How many apples does <PERSON> have?", "Equation": "( 14.0 + 6.0 )", "Answer": 20.0, "Type": "Addition"}, {"ID": "chal-811", "Body": "<PERSON> is baking a cake. The recipe calls for 6 cups of flour and 13 cups of sugar. She already put in some cups of flour. If she still needs 8 more cups of flour than sugar", "Question": "How many more cups of flour does she need to add?", "Equation": "( 13.0 + 8.0 )", "Answer": 21.0, "Type": "Addition"}, {"ID": "chal-812", "Body": "Winter is almost here and most animals are migrating to warmer countries. 27 bird families flew away for the winter from near a mountain. If there were 41 bird families living near the mountain initially", "Question": "How many bird families were left near the mountain?", "Equation": "( 41.0 - 27.0 )", "Answer": 14.0, "Type": "Subtraction"}, {"ID": "chal-813", "Body": "<PERSON> has 16 fewer peaches than <PERSON>. <PERSON> has 15 more peaches than <PERSON>. If <PERSON> has 12 peaches", "Question": "How many more peaches does <PERSON> have than <PERSON>?", "Equation": "( 16.0 - 15.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-814", "Body": "A book has 2 chapters. The first chapter is 60 pages long. If there are a total of 93 pages in the book", "Question": "How many pages are in the second chapter?", "Equation": "( 93.0 - 60.0 )", "Answer": 33.0, "Type": "Subtraction"}, {"ID": "chal-815", "Body": "<PERSON> got a box of some crayons for his birthday. During the school year he gave 563 crayons to his friends while he lost 558 crayons. If he only had 332 crayons left", "Question": "How many crayons did he get for his birthday?", "Equation": "( ( 563.0 + 558.0 ) + 332.0 )", "Answer": 1453.0, "Type": "Addition"}, {"ID": "chal-816", "Body": "At the stop 13 more people got on the bus. Initially there were 4 people on the bus.", "Question": "How many people are there on the bus now?", "Equation": "( 4.0 + 13.0 )", "Answer": 17.0, "Type": "Addition"}, {"ID": "chal-817", "Body": "<PERSON> played tag with some kids on monday. She played tag with 14 kids on tuesday. If she played with a total of 16 kids", "Question": "How many kids did she play with on monday?", "Equation": "( 16.0 - 14.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-818", "Body": "<PERSON>'s friend bought 137 cakes from him. If he had made 169 cakes initially", "Question": "How many cakes would baker still have?", "Equation": "( 169.0 - 137.0 )", "Answer": 32.0, "Type": "Subtraction"}, {"ID": "chal-819", "Body": "<PERSON> had 5 action figures and 9 books on a shelf in his room. Later he added 7 more action figures to the shelf.", "Question": "How many more action figures than books were on his shelf?", "Equation": "( ( 5.0 + 7.0 ) - 9.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-820", "Body": "For 19 weeks of harvest <PERSON> earns a total of $ 133.", "Question": "How much money does he earn each week?", "Equation": "( 133.0 / 19.0 )", "Answer": 7.0, "Type": "Common-Division"}, {"ID": "chal-821", "Body": "<PERSON> had some books. After selling 137 in a garage sale he had 105 left.", "Question": "How many books did he have at the start?", "Equation": "( 137.0 + 105.0 )", "Answer": 242.0, "Type": "Addition"}, {"ID": "chal-822", "Body": "Last week <PERSON> had 33 dollars and <PERSON> had 95 dollars. Over the weekend <PERSON> delivered newspapers earning 16 dollars and washed cars earning 74 dollars.", "Question": "How much money did <PERSON> earn over the weekend?", "Equation": "( 16.0 + 74.0 )", "Answer": 90.0, "Type": "Addition"}, {"ID": "chal-823", "Body": "If you have 14 cookies in total and 7 bags having equal number of cookies", "Question": "How many cookies does each bag have?", "Equation": "( 14.0 / 7.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-824", "Body": "<PERSON> gave equal numbers of crackers to his 18 friends. If he had 36 crackers", "Question": "How many crackers did each person eat?", "Equation": "( 36.0 / 18.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-825", "Body": "<PERSON> has 80 nintendo games. She found 31 more nintendo games.", "Question": "How many does she need to give away so that she will have 6 games left?", "Equation": "( ( 80.0 + 31.0 ) - 6.0 )", "Answer": 105.0, "Type": "Subtraction"}, {"ID": "chal-826", "Body": "There are 4 different books and 17 different movies in the ' crazy silly school ' series. If you read 19 of the books and watched 7 of the movies", "Question": "How many more movies do you still have to watch?", "Equation": "( 17.0 - 7.0 )", "Answer": 10.0, "Type": "Subtraction"}, {"ID": "chal-827", "Body": "He then went to see the oranges being harvested. He found out that they harvest 5 sacks of ripe oranges and 74 sacks of unripe oranges per day.", "Question": "How many more sacks of unripe oranges than ripe oranges are harvested per day?", "Equation": "( 74.0 - 5.0 )", "Answer": 69.0, "Type": "Subtraction"}, {"ID": "chal-828", "Body": "<PERSON> earns $ 368 every week during the 1359 weeks of harvest. If he has to pay $ 388 rent every week", "Question": "How much money does he pay as rent during the harvest season?", "Equation": "( 388.0 * 1359.0 )", "Answer": 527292.0, "Type": "Multiplication"}, {"ID": "chal-829", "Body": "For the walls of the house he would use 11 nails in all to secure large planks of wood. If each plank needs 3 pieces of nails to be secured and an additional 8 nails were used.", "Question": "How many planks does <PERSON> need for the house wall?", "Equation": "( ( 11.0 - 8.0 ) / 3.0 )", "Answer": 1.0, "Type": "Common-Division"}, {"ID": "chal-830", "Body": "17 red peaches and 16 green peaches are in the basket.", "Question": "How many more red peaches than green peaches are in the basket?", "Equation": "( 17.0 - 16.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-831", "Body": "Because of the decision <PERSON> asked the students to suggest specific types of food. If 324 students suggested adding mashed potatoes 374 suggested adding bacon to the menu and 128 suggested adding tomatoes", "Question": "How many students participated in the suggestion of new food items?", "Equation": "( ( 324.0 + 374.0 ) + 128.0 )", "Answer": 826.0, "Type": "Addition"}, {"ID": "chal-832", "Body": "During summer break 202958 kids from Lawrence county go to camp and the other 777622 kids stay home.", "Question": "How many more kids stayed home compared to those who went to the camp?", "Equation": "( 777622.0 - 202958.0 )", "Answer": 574664.0, "Type": "Subtraction"}, {"ID": "chal-833", "Body": "6 birds and 3 storks were sitting on the fence. 2 more storks came to join them.", "Question": "How many more birds than storks are sitting on the fence?", "Equation": "( 6.0 - ( 3.0 + 2.0 ) )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-834", "Body": "There are 22 different books and 10 different movies in the ' crazy silly school ' series. If you read 12 of the books and watched 56 of the movies", "Question": "How many more books do you still have to read?", "Equation": "( 22.0 - 12.0 )", "Answer": 10.0, "Type": "Subtraction"}, {"ID": "chal-835", "Body": "There were 12 people on the bus. At the next stop 4 more people got on the bus. Each bus can not have more than 36 people.", "Question": "How many people are there on the bus now?", "Equation": "( 12.0 + 4.0 )", "Answer": 16.0, "Type": "Addition"}, {"ID": "chal-836", "Body": "A grocery store had 57 bottles of regular soda, 26 bottles of diet soda and 27 bottles of lite soda.", "Question": "How many bottles did they have total?", "Equation": "( ( 57.0 + 26.0 ) + 27.0 )", "Answer": 110.0, "Type": "Addition"}, {"ID": "chal-837", "Body": "Because of the decision Sofia asked 288 students to suggest specific types of food. 264 students suggested adding bacon while others suggested adding mashed potatoes to the menu.", "Question": "How many students suggested mashed potatoes?", "Equation": "( 288.0 - 264.0 )", "Answer": 24.0, "Type": "Subtraction"}, {"ID": "chal-838", "Body": "<PERSON> spent $ 9. Then he spent $ 8 more. Now he has $ 17.", "Question": "How much did <PERSON> have before he spent his money?", "Equation": "( ( 9.0 + 8.0 ) + 17.0 )", "Answer": 34.0, "Type": "Addition"}, {"ID": "chal-839", "Body": "Winter is almost here and most animals are migrating to warmer countries. There are 85 bird families living near the mountain. If 23 bird families flew away to africa and 37 bird families flew away to asia", "Question": "How many bird families were left near the mountain?", "Equation": "( 85.0 - ( 23.0 + 37.0 ) )", "Answer": 25.0, "Type": "Subtraction"}, {"ID": "chal-840", "Body": "In a school there are 902 girls and 811 boys. 44 more girls joined the school.", "Question": "How many girls are there in the school now?", "Equation": "( 902.0 + 44.0 )", "Answer": 946.0, "Type": "Addition"}, {"ID": "chal-841", "Body": "<PERSON> has $ 4. He bought a chocolate for $ 7 and a candy bar for $ 2.", "Question": "How much money did he spend to buy chocolate than he did to buy candy bar?", "Equation": "( 7.0 - 2.0 )", "Answer": 5.0, "Type": "Subtraction"}, {"ID": "chal-842", "Body": "At the arcade <PERSON> had won 18 tickets. If he used 5 to buy some toys and 11 more to buy some clothes", "Question": "How many tickets did <PERSON> have left?", "Equation": "( 18.0 - ( 5.0 + 11.0 ) )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-843", "Body": "The school is planning a field trip. The school has 24 classrooms. There are 120 students in the school with each classroom having the same number of students. There are 84 seats on each school bus.", "Question": "How many students are in each class?", "Equation": "( 120.0 / 24.0 )", "Answer": 5.0, "Type": "Common-Division"}, {"ID": "chal-844", "Body": "He then went to see the oranges being harvested. He found out that the harvest will go on for 4 days.", "Question": "How many sacks do they harvest per day if they harvested a total of 56 sacks of oranges?", "Equation": "( 56.0 / 4.0 )", "Answer": 14.0, "Type": "Common-Division"}, {"ID": "chal-845", "Body": "There were 21 roses in the vase. <PERSON> threw away 34 roses from the vase and cut some more new roses from her flower garden to put in the vase. There are now 15 roses in the vase.", "Question": "How many more roses did she throw away than those she cut from her garden?", "Equation": "( 21.0 - 15.0 )", "Answer": 6.0, "Type": "Subtraction"}, {"ID": "chal-846", "Body": "<PERSON> brought 2 balloons and <PERSON> brought 6 balloons to the park. <PERSON> then bought 3 more balloons at the park.", "Question": "How many more balloons did <PERSON> have than <PERSON> in the park?", "Equation": "( 6.0 - ( 2.0 + 3.0 ) )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-847", "Body": "He then went to see the oranges being harvested. He found out that they harvest 28 sacks of ripe oranges and 52 sacks of unripe oranges per day.", "Question": "How many sacks of oranges will they have after 26 days of harvest?", "Equation": "( ( 28.0 + 52.0 ) * 26.0 )", "Answer": 2080.0, "Type": "Multiplication"}, {"ID": "chal-848", "Body": "Together <PERSON> and <PERSON> have 6 apples. He has 4 apples more than adam and jackie together do.", "Question": "How many apples does he have?", "Equation": "( 6.0 + 4.0 )", "Answer": 10.0, "Type": "Addition"}, {"ID": "chal-849", "Body": "After a typhoon, 2 trees in <PERSON>'s backyard died. If she had grown 11 trees initially", "Question": "How many more trees survived the typhoon than those that died?", "Equation": "( ( 11.0 - 2.0 ) - 2.0 )", "Answer": 7.0, "Type": "Subtraction"}, {"ID": "chal-850", "Body": "<PERSON> brought 5 balloons and <PERSON> brought 6 balloons to the park. <PERSON> then bought 3 more balloons at the park.", "Question": "How many balloons did <PERSON> bring to the park?", "Equation": "( 6.0 + 3.0 )", "Answer": 9.0, "Type": "Addition"}, {"ID": "chal-851", "Body": "The Razorback shop makes $ 62 dollars off each t-shirt and $ 99 off each jersey. During the Arkansas and Texas tech game they sold 183 t-shirts and 31 jerseys.", "Question": "How much money did they make from selling the t-shirts?", "Equation": "( 62.0 * 183.0 )", "Answer": 11346.0, "Type": "Multiplication"}, {"ID": "chal-852", "Body": "<PERSON> did 30 more push-ups than <PERSON> in gym class today. If <PERSON> did 37 push-ups", "Question": "How many push-ups did <PERSON> do?", "Equation": "( 37.0 - 30.0 )", "Answer": 7.0, "Type": "Subtraction"}, {"ID": "chal-853", "Body": "<PERSON> and his dad went strawberry picking. <PERSON>'s strawberries weighed 30 pounds. If together their strawberries weighed 47 pounds.", "Question": "How much more did his strawberries weigh than his dad's?", "Equation": "( 30.0 - ( 47.0 - 30.0 ) )", "Answer": 13.0, "Type": "Subtraction"}, {"ID": "chal-854", "Body": "<PERSON> was placing her pencils and crayons into 16 rows with 6 crayons and 21 pencils in each row.", "Question": "How many crayons does she have?", "Equation": "( 16.0 * 6.0 )", "Answer": 96.0, "Type": "Multiplication"}, {"ID": "chal-855", "Body": "<PERSON> received 3 emails in the morning, 4 emails in the afternoon and 8 emails in the evening.", "Question": "How many emails did <PERSON> receive in the morning and evening?", "Equation": "( 3.0 + 8.0 )", "Answer": 11.0, "Type": "Addition"}, {"ID": "chal-856", "Body": "<PERSON>'s room is 7 feet long. If she needs a carpet of size 14 square feet", "Question": "What is the width of her room?", "Equation": "( 14.0 / 7.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-857", "Body": "<PERSON><PERSON> bought 264 water bottles when they were on sale. If she drank 15 bottles a day for 11 days.", "Question": "How many bottles does she have left?", "Equation": "( 264.0 - ( 15.0 * 11.0 ) )", "Answer": 99.0, "Type": "Subtraction"}, {"ID": "chal-858", "Body": "33 campers went rowing in the morning 34 campers went rowing in the afternoon and 10 campers went rowing in the evening.", "Question": "How many more campers went rowing in the afternoon than in the evening?", "Equation": "( 34.0 - 10.0 )", "Answer": 24.0, "Type": "Subtraction"}, {"ID": "chal-859", "Body": "<PERSON> took a look at his books and magazines. If he has 23 books and 61 magazines in each of his 29 bookshelves", "Question": "How many books and magazines does he have in total?", "Equation": "( ( 23.0 + 61.0 ) * 29.0 )", "Answer": 2436.0, "Type": "Multiplication"}, {"ID": "chal-860", "Body": "<PERSON> was placing her pencils and crayons into 7 rows with 36 pencils and 30 crayons in each row.", "Question": "How many crayons does she have?", "Equation": "( 7.0 * 30.0 )", "Answer": 210.0, "Type": "Multiplication"}, {"ID": "chal-861", "Body": "<PERSON> made 7 cakes and 148 pastries. If he sold 15 cakes and 103 pastries", "Question": "How many pastries would baker still have?", "Equation": "( 148.0 - 103.0 )", "Answer": 45.0, "Type": "Subtraction"}, {"ID": "chal-862", "Body": "<PERSON> cut off 13 inches of his hair. If his hair was 14 inches long initially", "Question": "How long is his hair now?", "Equation": "( 14.0 - 13.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-863", "Body": "They decided to hold the party in their backyard. They have 2 sets of tables, each having the same number of chairs. If there are a total of 12 chairs in the backyard", "Question": "How many chairs are there for each table?", "Equation": "( 12.0 / 2.0 )", "Answer": 6.0, "Type": "Common-Division"}, {"ID": "chal-864", "Body": "<PERSON><PERSON> bought 88 water bottles and 13 soda bottles when they were on sale. If she drank 4 water bottles and 87 soda bottles a day", "Question": "How many days would the water bottles last?", "Equation": "( 88.0 / 4.0 )", "Answer": 22.0, "Type": "Common-Division"}, {"ID": "chal-865", "Body": "Last week <PERSON> had 60 dollars and <PERSON> had 19 dollars. <PERSON> washed cars over the weekend and now has 33 dollars.", "Question": "How much money did <PERSON> make washing cars?", "Equation": "( 33.0 - 19.0 )", "Answer": 14.0, "Type": "Subtraction"}, {"ID": "chal-866", "Body": "<PERSON> sold some books in a garage sale leaving him with 27 books. If he had 136 books initially", "Question": "How many books did he sell?", "Equation": "( 136.0 - 27.0 )", "Answer": 109.0, "Type": "Subtraction"}, {"ID": "chal-867", "Body": "Next on his checklist is wax to stick the feathers together. If the feathers require 166 g of wax and right now he has 20 g", "Question": "How many more grams of wax does he need?", "Equation": "( 166.0 - 20.0 )", "Answer": 146.0, "Type": "Subtraction"}, {"ID": "chal-868", "Body": "The bananas in <PERSON>'s collection are organized into 196 groups. If there are a total of 392 bananas in <PERSON>'s banana collection", "Question": "How big is each group?", "Equation": "( 392.0 / 196.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-869", "Body": "The grasshopper and the frog had a jumping contest. The grasshopper jumped 9 inches and the frog jumped 12 inches.", "Question": "How much farther did the frog jump than the grasshopper?", "Equation": "( 12.0 - 9.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-870", "Body": "In <PERSON>'s class 13 boys love to play marbles and 50 boys love to play cards. If <PERSON> has 26 marbles", "Question": "How many will each of the boys receive?", "Equation": "( 26.0 / 13.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-871", "Body": "<PERSON> has 18 peaches. <PERSON> has 12 fewer peaches than <PERSON> who has 8 more peaches than <PERSON>.", "Question": "How many peaches does <PERSON> have?", "Equation": "( 18.0 - 8.0 )", "Answer": 10.0, "Type": "Subtraction"}, {"ID": "chal-872", "Body": "In <PERSON>'s class 11 are boys who love to play marbles. If <PERSON> gives 9 marbles to each boy", "Question": "How many marbles did she have?", "Equation": "( 11.0 * 9.0 )", "Answer": 99.0, "Type": "Multiplication"}, {"ID": "chal-873", "Body": "<PERSON> had 30 more marbles than <PERSON>. <PERSON> lost 21 of his marbles at the playground. If <PERSON> had 91 marbles", "Question": "How many more marbles did <PERSON> have than <PERSON> then?", "Equation": "( 30.0 - 21.0 )", "Answer": 9.0, "Type": "Subtraction"}, {"ID": "chal-874", "Body": "A farmer had 105 green tomatoes and 71 red tomatoes in his garden. If he picked 137 tomatoes", "Question": "How many tomatoes are left?", "Equation": "( ( 105.0 + 71.0 ) - 137.0 )", "Answer": 39.0, "Type": "Subtraction"}, {"ID": "chal-875", "Body": "<PERSON> earns $ 21 every week during the 216 weeks of harvest. If he has to pay $ 702 tax", "Question": "How much money will have at the end of the harvest season?", "Equation": "( ( 21.0 * 216.0 ) - 702.0 )", "Answer": 3834.0, "Type": "Subtraction"}, {"ID": "chal-876", "Body": "<PERSON> collects baseball cards. She had 566 cards while <PERSON> had 234 cards. She gave some of her cards to <PERSON> and now has 535 cards left.", "Question": "How many cards does <PERSON> have now?", "Equation": "( ( 234.0 + 566.0 ) - 535.0 )", "Answer": 265.0, "Type": "Subtraction"}, {"ID": "chal-877", "Body": "<PERSON> wants to split a collection of marbles into groups of 4. <PERSON> has 10 eggs and 20 marbles.", "Question": "How many groups will be created?", "Equation": "( 20.0 / 4.0 )", "Answer": 5.0, "Type": "Common-Division"}, {"ID": "chal-878", "Body": "<PERSON> had 31 crackers. He has 23 crackers left after he gave equal numbers of crackers to his 5 friends.", "Question": "How many crackers did <PERSON> give to his friends?", "Equation": "( 31.0 - 23.0 )", "Answer": 8.0, "Type": "Subtraction"}, {"ID": "chal-879", "Body": "An industrial machine can make 6 shirts a minute. It worked for 5 minutes yesterday and for 12 minutes today.", "Question": "How many shirts did machine make today?", "Equation": "( 6.0 * 12.0 )", "Answer": 72.0, "Type": "Multiplication"}, {"ID": "chal-880", "Body": "<PERSON> and his dad went strawberry picking. <PERSON>'s strawberries weighed 10 pounds. If together their strawberries weighed 26 pounds.", "Question": "How much more did his dad's strawberries weigh than his?", "Equation": "( ( 26.0 - 10.0 ) - 10.0 )", "Answer": 6.0, "Type": "Subtraction"}, {"ID": "chal-881", "Body": "<PERSON> picked 4 apples from her tree. If the tree had 7 apples initially.", "Question": "How many apples are still there on the tree?", "Equation": "( 7.0 - 4.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-882", "Body": "After making some pies, chef had 2 apples left. Initially he had 43 apples", "Question": "How many apples did he use?", "Equation": "( 43.0 - 2.0 )", "Answer": 41.0, "Type": "Subtraction"}, {"ID": "chal-883", "Body": "<PERSON> brought 3 balloons and 20 balls while <PERSON> brought 5 balloons and 59 balls to the park.", "Question": "How many balloons did <PERSON> and <PERSON> have in the park?", "Equation": "( 3.0 + 5.0 )", "Answer": 8.0, "Type": "Addition"}, {"ID": "chal-884", "Body": "There were 53 dollars in <PERSON>'s wallet. She collected 91 more dollars from an atm. After she visited a supermarket there were 14 dollars left.", "Question": "How much more money did she spend at the supermarket than she collected at the atm?", "Equation": "( 53.0 - 14.0 )", "Answer": 39.0, "Type": "Subtraction"}, {"ID": "chal-885", "Body": "<PERSON> was placing some pencils equally into 2 rows. If she had 6 pencils", "Question": "How many pencils did she place in each row?", "Equation": "( 6.0 / 2.0 )", "Answer": 3.0, "Type": "Common-Division"}, {"ID": "chal-886", "Body": "<PERSON> played tag with 5 kids on monday. She played tag with some more kids on tuesday. If she played with a total of 15 kids", "Question": "How many kids did she play with on tuesday?", "Equation": "( 15.0 - 5.0 )", "Answer": 10.0, "Type": "Subtraction"}, {"ID": "chal-887", "Body": "<PERSON> collects bottle caps. He threw away 6 of the old ones at the park while he found 50 bottle caps new ones. Now he has 60 bottle caps in his collection.", "Question": "How many more bottle caps did danny find at the park than those he threw away?", "Equation": "( 50.0 - 6.0 )", "Answer": 44.0, "Type": "Subtraction"}, {"ID": "chal-888", "Body": "<PERSON><PERSON> had 36 cookies. He gave 14 cookies to his friend and ate 10 cookies.", "Question": "How many cookies did <PERSON><PERSON> have left?", "Equation": "( ( 36.0 - 10.0 ) - 14.0 )", "Answer": 12.0, "Type": "Subtraction"}, {"ID": "chal-889", "Body": "They decided to hold the party in their backyard. If they have 5 sets of tables and each set has 10 chairs", "Question": "How many more chairs than tables do they have?", "Equation": "( ( 5.0 * 10.0 ) - 5.0 )", "Answer": 45.0, "Type": "Subtraction"}, {"ID": "chal-890", "Body": "After resting they decided to go for a swim. The depth of the water is 16 times <PERSON>'s height. <PERSON> is 9 feet taller than <PERSON>. If <PERSON> stands at 13 feet", "Question": "How deep was the water?", "Equation": "( 13.0 * 16.0 )", "Answer": 208.0, "Type": "Multiplication"}, {"ID": "chal-891", "Body": "A farmer had 177 tomatoes and 12 potatoes in his garden. If he picked 53 tomatoes", "Question": "How many tomatoes and potatoes does he have left?", "Equation": "( ( 177.0 + 12.0 ) - 53.0 )", "Answer": 136.0, "Type": "Subtraction"}, {"ID": "chal-892", "Body": "7 red peaches, 71 yellow peaches and 8 green peaches are in the basket.", "Question": "How many more green peaches than red peaches are in the basket?", "Equation": "( 8.0 - 7.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-893", "Body": "52 campers went rowing in the morning. 61 campers went rowing in the afternoon.", "Question": "How many more campers went rowing in the afternoon than in the morning?", "Equation": "( 61.0 - 52.0 )", "Answer": 9.0, "Type": "Subtraction"}, {"ID": "chal-894", "Body": "There were 13 roses and 84 orchids in the vase. <PERSON> cut some more roses and orchids from her flower garden. There are now 91 orchids and 14 roses in the vase.", "Question": "How many roses did she cut?", "Equation": "( 14.0 - 13.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-895", "Body": "<PERSON><PERSON> bought some water bottles when they were on sale. She drank 109 bottles a day. If the bottles lasted for 74 days", "Question": "How many bottles had she bought?", "Equation": "( 109.0 * 74.0 )", "Answer": 8066.0, "Type": "Multiplication"}, {"ID": "chal-896", "Body": "<PERSON> spent $ 6 to buy 2 books each book costing him the same amount of money. Now he has $ 12.", "Question": "How much did each book cost?", "Equation": "( 6.0 / 2.0 )", "Answer": 3.0, "Type": "Common-Division"}, {"ID": "chal-897", "Body": "<PERSON> has 28 packages of gum and 14 packages of candy. There are 6 pieces in each package.", "Question": "How many pieces does <PERSON> have?", "Equation": "( ( 28.0 + 14.0 ) / 6.0 )", "Answer": 7.0, "Type": "Common-Division"}, {"ID": "chal-898", "Body": "There are 17 different movies and 11 different books in the ' crazy silly school ' series. If you read 13 of the books and watched 63 of the movies", "Question": "How many more movies than books are there in the ' crazy silly school ' series?", "Equation": "( 17.0 - 11.0 )", "Answer": 6.0, "Type": "Subtraction"}, {"ID": "chal-899", "Body": "<PERSON> received 5 emails in the morning, 8 emails in the afternoon and 72 emails in the evening.", "Question": "How many emails did <PERSON> receive in the morning and afternoon?", "Equation": "( 5.0 + 8.0 )", "Answer": 13.0, "Type": "Addition"}, {"ID": "chal-900", "Body": "<PERSON> uses 36 blocks to build a tower. If he had 59 blocks", "Question": "How many blocks are left?", "Equation": "( 59.0 - 36.0 )", "Answer": 23.0, "Type": "Subtraction"}, {"ID": "chal-901", "Body": "<PERSON> and his dad went strawberry picking. Together their strawberries weighed 20 pounds. His dad's strawberries weighed 17 pounds.", "Question": "How much did ma<PERSON>o's strawberries weigh?", "Equation": "( 20.0 - 17.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-902", "Body": "<PERSON> grew 9 trees in her backyard. After a typhoon 4 died. Then she grew 5 more trees.", "Question": "How many trees does she have left?", "Equation": "( ( 9.0 - 4.0 ) + 5.0 )", "Answer": 10.0, "Type": "Addition"}, {"ID": "chal-903", "Body": "Stray cats loved eating goldfish in the pond leaving 6 goldfish. <PERSON> had raised 8 goldfish in the pond initially.", "Question": "How many goldfish disappeared?", "Equation": "( 8.0 - 6.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-904", "Body": "<PERSON> collects bottle caps and wrappers. He found 58 bottle caps and 25 wrappers at the park. Now he has 11 wrappers and 12 bottle caps in his collection.", "Question": "How many more bottle caps than wrappers does danny have now?", "Equation": "( 12.0 - 11.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-905", "Body": "<PERSON> is baking a cake. The recipe calls for 9 cups of flour and 6 cups of sugar. She already put in 4 cups of sugar.", "Question": "How many more cups of flour than cups of sugar does she need to add now?", "Equation": "( 9.0 - ( 6.0 - 4.0 ) )", "Answer": 7.0, "Type": "Subtraction"}, {"ID": "chal-906", "Body": "<PERSON>'s room is 2 feet long and 12 feet wide. If each tile is of size 4 square feet", "Question": "How many tiles does she need to cover the whole floor?", "Equation": "( ( 2.0 * 12.0 ) / 4.0 )", "Answer": 6.0, "Type": "Common-Division"}, {"ID": "chal-907", "Body": "An industrial machine can make 6 shirts a minute. It worked for 12 minutes yesterday and for 14 shirts today.", "Question": "How many shirts did machine make altogether?", "Equation": "( 6.0 * ( 12.0 + 14.0 ) )", "Answer": 156.0, "Type": "Multiplication"}, {"ID": "chal-908", "Body": "<PERSON><PERSON> had 9 sweet cookies and 6 salty cookies. He ate 36 sweet cookies and 3 salty cookies.", "Question": "How many salty cookies did <PERSON><PERSON> have left?", "Equation": "( 6.0 - 3.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-909", "Body": "<PERSON> cut off 20 inches of his hair. If his hair is now 10 inches long", "Question": "How long was his hair before he cut?", "Equation": "( 20.0 + 10.0 )", "Answer": 30.0, "Type": "Addition"}, {"ID": "chal-910", "Body": "The grasshopper, the frog and the mouse had a jumping contest. The grasshopper jumped 36 inches. The frog jumped 17 inches farther than the grasshopper and the mouse jumped 15 inches farther than the frog.", "Question": "How far did the frog jump?", "Equation": "( 36.0 + 17.0 )", "Answer": 53.0, "Type": "Addition"}, {"ID": "chal-911", "Body": "<PERSON> has 28 packages of gum and 13 packages of candy. There are 4 pieces in each package.", "Question": "How many pieces of gum does <PERSON> have?", "Equation": "( 28.0 * 4.0 )", "Answer": 112.0, "Type": "Multiplication"}, {"ID": "chal-912", "Body": "<PERSON> made 157 cakes and 169 pastries. If he sold 158 cakes and 147 pastries", "Question": "How many more cakes than pastries did baker sell?", "Equation": "( 158.0 - 147.0 )", "Answer": 11.0, "Type": "Subtraction"}, {"ID": "chal-913", "Body": "In a school there are 868 girls and the rest are boys. If there are 281 more girls than boys", "Question": "How many pupils are there in that school?", "Equation": "( ( 868.0 + 868.0 ) - 281.0 )", "Answer": 1455.0, "Type": "Subtraction"}, {"ID": "chal-914", "Body": "<PERSON> did 51 push-ups in gym class today. <PERSON> did 49 more push-ups than <PERSON>.", "Question": "How many push-ups did <PERSON> and <PERSON> do altogether?", "Equation": "( ( 51.0 + 51.0 ) - 49.0 )", "Answer": 53.0, "Type": "Subtraction"}, {"ID": "chal-915", "Body": "Winter is almost here and most animals are migrating to warmer countries. There were 87 bird families living near the mountain. If 7 bird families flew away for winter", "Question": "How many more bird families stayed behind than those that flew away for the winter?", "Equation": "( ( 87.0 - 7.0 ) - 7.0 )", "Answer": 73.0, "Type": "Subtraction"}, {"ID": "chal-916", "Body": "A school has 485 pupils. There are 232 girls and the rest are boys.", "Question": "How many boys are there in that school?", "Equation": "( 485.0 - 232.0 )", "Answer": 253.0, "Type": "Subtraction"}, {"ID": "chal-917", "Body": "<PERSON> made 134 pastries and 11 cakes. If he sold 140 cakes and 92 pastries", "Question": "How many more pastries than cakes did baker make?", "Equation": "( 134.0 - 11.0 )", "Answer": 123.0, "Type": "Subtraction"}, {"ID": "chal-918", "Body": "<PERSON> collects baseball cards. She had 528 cards while <PERSON> had 11 cards. She gave some of her cards to j<PERSON><PERSON> and now has 252 cards left.", "Question": "How many cards did <PERSON> give to <PERSON>?", "Equation": "( 528.0 - 252.0 )", "Answer": 276.0, "Type": "Subtraction"}, {"ID": "chal-919", "Body": "The school is planning a field trip. The school has 66 classrooms. There are 102 students in the school and 8 seats on each school bus. If 22 students do n't want to go for the trip", "Question": "How many buses are needed to take the trip?", "Equation": "( ( 102.0 - 22.0 ) / 8.0 )", "Answer": 10.0, "Type": "Common-Division"}, {"ID": "chal-920", "Body": "4 people can ride the Ferris wheel in paradise park at the same time. If each seat on the Ferris wheel can hold 2 people", "Question": "How many seats does the Ferris wheel have?", "Equation": "( 4.0 / 2.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-921", "Body": "<PERSON> had 24 apps and 9 files on his phone. After deleting some apps and files he had 5 files and 12 apps left.", "Question": "How many more apps than files does he have left on the phone?", "Equation": "( 12.0 - 5.0 )", "Answer": 7.0, "Type": "Subtraction"}, {"ID": "chal-922", "Body": "44 campers went rowing in the morning 39 campers went rowing in the afternoon and 31 campers went rowing in the evening.", "Question": "How many more campers went rowing in the morning than in the afternoon?", "Equation": "( 44.0 - 39.0 )", "Answer": 5.0, "Type": "Subtraction"}, {"ID": "chal-923", "Body": "<PERSON> was reading through his favorite book. The book had 3 chapters, each with the same number of pages. It has a total of 594 pages. It took <PERSON> 607 days to finish the book.", "Question": "How many pages are in each chapter?", "Equation": "( 594.0 / 3.0 )", "Answer": 198.0, "Type": "Common-Division"}, {"ID": "chal-924", "Body": "<PERSON> has 18 square feet of carpet. If her room is 4 feet long and 20 feet wide", "Question": "How much more carpet does she need to cover the whole floor?", "Equation": "( ( 4.0 * 20.0 ) - 18.0 )", "Answer": 62.0, "Type": "Subtraction"}, {"ID": "chal-925", "Body": "There are many different books in the ' crazy silly school ' series. If you have read 13 of the books and are yet to read 8 books", "Question": "How many books are there in the ' crazy silly school ' series?", "Equation": "( 13.0 + 8.0 )", "Answer": 21.0, "Type": "Addition"}, {"ID": "chal-926", "Body": "<PERSON> collects cards. She had 438 baseball cards and 18 Ace cards. She gave some of her cards to <PERSON> and now has 55 Ace cards and 178 baseball cards left.", "Question": "How many more baseball cards than Ace cards does <PERSON> have?", "Equation": "( 178.0 - 55.0 )", "Answer": 123.0, "Type": "Subtraction"}, {"ID": "chal-927", "Body": "<PERSON> had 24 crackers. If <PERSON> gave equal numbers of crackers to his 3 friends and still had 17 crackers left", "Question": "How many crackers did <PERSON> give to his friends?", "Equation": "( 24.0 - 17.0 )", "Answer": 7.0, "Type": "Subtraction"}, {"ID": "chal-928", "Body": "The grasshopper, the frog and the mouse had a jumping contest. The grasshopper jumped 21 inches. The grasshopper jumped 25 inches farther than the frog and the mouse jumped 16 inches lesser than the frog.", "Question": "How much farther did the grasshopper jump than the mouse?", "Equation": "( 25.0 + 16.0 )", "Answer": 41.0, "Type": "Addition"}, {"ID": "chal-929", "Body": "They decided to hold the party in their backyard. They have 10 sets of tables and each set has 6 chairs. If there are 11 people sitting on chairs", "Question": "How many chairs are left unoccupied?", "Equation": "( ( 10.0 * 6.0 ) - 11.0 )", "Answer": 49.0, "Type": "Subtraction"}, {"ID": "chal-930", "Body": "After eating a hearty meal they went to see the Buckingham palace. There, <PERSON> learned that 512 visitors came to the Buckingham palace that day. If there were 471 visitors the previous day and 808 visitors the day before that", "Question": "How many visited the Buckingham palace within the past 89 days?", "Equation": "( ( 512.0 + 471.0 ) + 808.0 )", "Answer": 1791.0, "Type": "Addition"}, {"ID": "chal-931", "Body": "He then went to see the oranges being harvested. He found out that they harvest 44 sacks of ripe oranges and 25 sacks of unripe oranges per day.", "Question": "How many more sacks of ripe oranges than unripe oranges are harvested per day?", "Equation": "( 44.0 - 25.0 )", "Answer": 19.0, "Type": "Subtraction"}, {"ID": "chal-932", "Body": "White t - shirts can be purchased in packages of 53. If mom buys 57 packages of white t - shirts and 34 trousers", "Question": "How many white t - shirts will she have?", "Equation": "( 53.0 * 57.0 )", "Answer": 3021.0, "Type": "Multiplication"}, {"ID": "chal-933", "Body": "<PERSON> had 2 books. After selling some in a garage sale he bought 150 new ones. If he has 58 books now", "Question": "How many books did he sell?", "Equation": "( ( 2.0 + 150.0 ) - 58.0 )", "Answer": 94.0, "Type": "Subtraction"}, {"ID": "chal-934", "Body": "6 storks and 2 birds were sitting on the fence. 3 more birds came to join them.", "Question": "How many more storks than birds are sitting on the fence?", "Equation": "( 6.0 - ( 2.0 + 3.0 ) )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-935", "Body": "<PERSON> took a look at his books and magazines. If he has 27 books and 80 magazines in each of his 23 bookshelves", "Question": "How many books does he have in total?", "Equation": "( 27.0 * 23.0 )", "Answer": 621.0, "Type": "Multiplication"}, {"ID": "chal-936", "Body": "<PERSON> got a box of 531 crayons and 38 erasers for his birthday. At the end of the school year he only had 391 left while not having lost a single erasers.", "Question": "How many more crayons than erasers did he have left?", "Equation": "( 391.0 - 38.0 )", "Answer": 353.0, "Type": "Subtraction"}, {"ID": "chal-937", "Body": "<PERSON> earns a total of $ 1216 during the harvest. If he earns $ 16 each week", "Question": "How many weeks did the harvest last?", "Equation": "( 1216.0 / 16.0 )", "Answer": 76.0, "Type": "Common-Division"}, {"ID": "chal-938", "Body": "<PERSON> had 10 apps on his phone. He added 11 new apps. After deleting some he had 4 left.", "Question": "How many apps did he delete?", "Equation": "( ( 10.0 + 11.0 ) - 4.0 )", "Answer": 17.0, "Type": "Subtraction"}, {"ID": "chal-939", "Body": "The ring toss game at the carnival made the same amount of money each day. In total in 5 days they earned 165 dollars.", "Question": "How much did they make per day?", "Equation": "( 165.0 / 5.0 )", "Answer": 33.0, "Type": "Common-Division"}, {"ID": "chal-940", "Body": "There were 5 roses and 3 orchids in the vase. <PERSON> cut some more roses and orchids from her flower garden. There are now 12 roses and 2 orchids in the vase.", "Question": "How many more roses than orchids are there in the vase now?", "Equation": "( 12.0 - 2.0 )", "Answer": 10.0, "Type": "Subtraction"}, {"ID": "chal-941", "Body": "The Ferris wheel in paradise park has some seats. Each seat can hold 9 people. If 18 people can ride the wheel at the same time", "Question": "How many seats does the Ferris wheel have?", "Equation": "( 18.0 / 9.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-942", "Body": "<PERSON> had some apps on his phone. After deleting 18 of them he had 5 left.", "Question": "How many apps did he have on his phone at the start?", "Equation": "( 18.0 + 5.0 )", "Answer": 23.0, "Type": "Addition"}, {"ID": "chal-943", "Body": "There are 14 different movies and 15 different books in the ' crazy silly school ' series. If you read 11 of the books and watched 40 of the movies", "Question": "How many more books than movies are there in the ' crazy silly school ' series?", "Equation": "( 15.0 - 14.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-944", "Body": "<PERSON> has 86 blocks. He uses 79 blocks to build a tower and 82 blocks to build a house.", "Question": "How many more blocks did he use to build the house than he did to build the tower?", "Equation": "( 82.0 - 79.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-945", "Body": "With 28 dollars 2 packs of dvds can be bought.", "Question": "How much does each pack cost?", "Equation": "( 28.0 / 2.0 )", "Answer": 14.0, "Type": "Common-Division"}, {"ID": "chal-946", "Body": "Last week <PERSON> had 78 dollars and <PERSON> had 31 dollars. <PERSON> delivered newspapers and now has 57 dollars. <PERSON> washed cars over the weekend and now has 90 dollars.", "Question": "How much money did <PERSON> earn by delivering newspapers?", "Equation": "( 57.0 - 31.0 )", "Answer": 26.0, "Type": "Subtraction"}, {"ID": "chal-947", "Body": "<PERSON> had 15 marbles in his collection. He found 9 marbles while he lost 23 marbles.", "Question": "How many more marbles did he lose than those he found?", "Equation": "( 23.0 - 9.0 )", "Answer": 14.0, "Type": "Subtraction"}, {"ID": "chal-948", "Body": "<PERSON> received 4 emails in the morning and some more in the afternoon. If he received a total of 5 emails in the day", "Question": "How many emails did <PERSON> receive in the afternoon?", "Equation": "( 5.0 - 4.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-949", "Body": "<PERSON> gained 146 points in each round of a game. If he played 157 rounds of a trivia game", "Question": "How many points did he score in the game?", "Equation": "( 157.0 * 146.0 )", "Answer": 22922.0, "Type": "Multiplication"}, {"ID": "chal-950", "Body": "There were some roses in the vase. <PERSON> cut 16 more roses from her flower garden and put them in the vase. There are now 23 roses in the vase.", "Question": "How many roses were there in the vase at the beginning?", "Equation": "( 23.0 - 16.0 )", "Answer": 7.0, "Type": "Subtraction"}, {"ID": "chal-951", "Body": "The grasshopper, the frog and the mouse had a jumping contest. The grasshopper jumped 19 inches. The frog jumped 39 inches farther than the grasshopper and the mouse jumped 94 inches lesser than the frog.", "Question": "How far did the frog jump?", "Equation": "( 19.0 + 39.0 )", "Answer": 58.0, "Type": "Addition"}, {"ID": "chal-952", "Body": "<PERSON> has 5 more apples than <PERSON>. <PERSON> has 89 oranges and 11 apples.", "Question": "How many apples does <PERSON> have?", "Equation": "( 5.0 + 11.0 )", "Answer": 16.0, "Type": "Addition"}, {"ID": "chal-953", "Body": "After eating a hearty meal they went to see the Buckingham palace. There, <PERSON> learned that 132 visitors came to the Buckingham palace that day. If 406 people visited the Buckingham palace within the past 327 days", "Question": "How many visitors visited the Buckingham palace on the previous day?", "Equation": "( 406.0 - 132.0 )", "Answer": 274.0, "Type": "Subtraction"}, {"ID": "chal-954", "Body": "29 campers went rowing and 66 campers went hiking in the morning. 26 campers went rowing in the afternoon.", "Question": "How many more campers went rowing in the morning than in the afternoon?", "Equation": "( 29.0 - 26.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-955", "Body": "<PERSON> had 17 apps on his phone. After adding some he had 18 left.", "Question": "How many apps did he add?", "Equation": "( 18.0 - 17.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-956", "Body": "There were 174 parents in the program and 521 pupils too.", "Question": "How many more pupils were present compared to parents in the program?", "Equation": "( 521.0 - 174.0 )", "Answer": 347.0, "Type": "Subtraction"}, {"ID": "chal-957", "Body": "<PERSON><PERSON> ate 19 cookies and had 35 cookies left.", "Question": "How many cookies did he have in the beginning?", "Equation": "( 19.0 + 35.0 )", "Answer": 54.0, "Type": "Addition"}, {"ID": "chal-958", "Body": "If each bag has 3 cookies and you had 21 cookies in total", "Question": "How many bags of cookies do you have?", "Equation": "( 21.0 / 3.0 )", "Answer": 7.0, "Type": "Common-Division"}, {"ID": "chal-959", "Body": "The grasshopper, the frog and the mouse had a jumping contest. The grasshopper jumped 39 inches. The grasshopper jumped 19 inches farther than the frog and the mouse jumped 12 inches lesser than the frog.", "Question": "How far did the mouse jump?", "Equation": "( ( 39.0 - 19.0 ) - 12.0 )", "Answer": 8.0, "Type": "Subtraction"}, {"ID": "chal-960", "Body": "Being his favorite, he saved checking on the grapevines for his last stop. He was told by 94 of the pickers that they fill 90 drums of grapes in 6 days.", "Question": "How many drums of grapes would be filled per day?", "Equation": "( 90.0 / 6.0 )", "Answer": 15.0, "Type": "Common-Division"}, {"ID": "chal-961", "Body": "<PERSON> had 84 new games and 19 old games. Her friends had 8 new games and 69 old games.", "Question": "How many new games do they have together?", "Equation": "( 84.0 + 8.0 )", "Answer": 92.0, "Type": "Addition"}, {"ID": "chal-962", "Body": "Being his favorite, he saved checking on the grapevines after 57 stops. He was told by 252 of the pickers that they fill 108 drums of grapes per day.", "Question": "How many drums of grapes would be filled in 58 days?", "Equation": "( 108.0 * 58.0 )", "Answer": 6264.0, "Type": "Multiplication"}, {"ID": "chal-963", "Body": "After eating a hearty meal they went to see the Buckingham palace. There, <PERSON> learned that 45 visitors came to the Buckingham palace on the previous day. If 829 people visited the Buckingham palace within the past 85 days", "Question": "How many visitors visited the Buckingham palace on that day?", "Equation": "( 829.0 - 45.0 )", "Answer": 784.0, "Type": "Subtraction"}, {"ID": "chal-964", "Body": "<PERSON> has 9 apples. <PERSON> has 10 apples.", "Question": "How many more apples does <PERSON> have than <PERSON>?", "Equation": "( 10.0 - 9.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-965", "Body": "There are 2 baskets of peaches. Each basket has 4 red peaches and some more green peaches. If there are a total of 12 peaches in all baskets", "Question": "How many green peaches are in each basket?", "Equation": "( ( 12.0 / 2.0 ) - 4.0 )", "Answer": 2.0, "Type": "Subtraction"}, {"ID": "chal-966", "Body": "A chef had 40 apples and 54 peaches. After making some pies, he had 39 apples left.", "Question": "How many apples did he use?", "Equation": "( 40.0 - 39.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-967", "Body": "A mailman has to give 38 pieces of junk mail to each of the 78 blocks. If there are 19 houses on a block", "Question": "How many pieces of junk mail should he give each house?", "Equation": "( 38.0 / 19.0 )", "Answer": 2.0, "Type": "Common-Divison"}, {"ID": "chal-968", "Body": "<PERSON> is baking a cake. The recipe calls for 10 cups of flour 2 cups of sugar and 80 cups of salt. She already put in 7 cups of flour.", "Question": "How many more cups of flour than cups of sugar does she need to add now?", "Equation": "( ( 10.0 - 7.0 ) - 2.0 )", "Answer": 1.0, "Type": "Subtraction"}, {"ID": "chal-969", "Body": "<PERSON> played tag with 13 kids on monday and 10 kids on tuesday. She played cards with 15 kids on wednesday.", "Question": "How many kids did she play with altogether?", "Equation": "( ( 13.0 + 10.0 ) + 15.0 )", "Answer": 38.0, "Type": "Addition"}, {"ID": "chal-970", "Body": "<PERSON> needs a carpet of size 10 square feet to cover her room. If her room is 5 feet long", "Question": "What is the width of her room?", "Equation": "( 10.0 / 5.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-971", "Body": "There are 4502 skittles in <PERSON>'s skittles collection. <PERSON> also has 4276 erasers. If the skittles and erasers are organized into 154 groups", "Question": "How big is each group?", "Equation": "( ( 4502.0 + 4276.0 ) / 154.0 )", "Answer": 57.0, "Type": "Common-Division"}, {"ID": "chal-972", "Body": "A mailman has to give 4 pieces of junk mail to each house in each of the 16 blocks. If there are 17 houses in each block", "Question": "How many pieces of junk mail should he give in total?", "Equation": "( ( 4.0 * 17.0 ) * 16.0 )", "Answer": 1088.0, "Type": "Multiplication"}, {"ID": "chal-973", "Body": "A book has 3 chapters. The first chapter is 53 pages long the second chapter is 75 pages long and the third chapter is 21 pages long.", "Question": "How many more pages does the first chapter have than the third chapter?", "Equation": "( 53.0 - 21.0 )", "Answer": 32.0, "Type": "Subtraction"}, {"ID": "chal-974", "Body": "<PERSON> had 21 apps on his phone. He added 89 new apps. After deleting some he had 24 left.", "Question": "How many more apps did he add than he deleted?", "Equation": "( 24.0 - 21.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-975", "Body": "<PERSON> ate 17 pieces of candy. Then he ate 15 more. If he initially had 36 pieces of candy", "Question": "How many pieces of candy does he still have left?", "Equation": "( 36.0 - ( 17.0 + 15.0 ) )", "Answer": 4.0, "Type": "Subtraction"}, {"ID": "chal-976", "Body": "Winter is almost here and most animals are migrating to warmer countries. There are some bird families living near the mountain. 20 bird families flew away for winter while 14 bird families stayed behind.", "Question": "How many bird families were living near the mountain at the start?", "Equation": "( 20.0 + 14.0 )", "Answer": 34.0, "Type": "Addition"}, {"ID": "chal-977", "Body": "<PERSON> was reading through some books. Each book had 66 pages and it took <PERSON> 12 days to finish each book. If he takes 492 days to finish all books", "Question": "How many books did he read?", "Equation": "( 492.0 / 12.0 )", "Answer": 41.0, "Type": "Common-Division"}, {"ID": "chal-978", "Body": "<PERSON> did 36 more push-ups but 33 less crunches than <PERSON> in gym class today. If <PERSON> did 58 push-ups and 33 crunches", "Question": "How many more push-ups than crunches did <PERSON> do?", "Equation": "( 58.0 - 33.0 )", "Answer": 25.0, "Type": "Subtraction"}, {"ID": "chal-979", "Body": "<PERSON> wants to split a collection of eggs into groups of 20. <PERSON> has 5 marbles and 12 eggs.", "Question": "How many more eggs does <PERSON> have than marbles?", "Equation": "( 12.0 - 5.0 )", "Answer": 7.0, "Type": "Subtraction"}, {"ID": "chal-980", "Body": "<PERSON> made 81 cakes. Then he made 92 more cakes. He sold 46 of them.", "Question": "How many more cakes did baker make than those he sold?", "Equation": "( ( 81.0 + 92.0 ) - 46.0 )", "Answer": 127.0, "Type": "Subtraction"}, {"ID": "chal-981", "Body": "10 red peaches and some more green peaches are in the basket. If there are a total of 15 peaches in the basket", "Question": "How many green peaches are in the basket?", "Equation": "( 15.0 - 10.0 )", "Answer": 5.0, "Type": "Subtraction"}, {"ID": "chal-982", "Body": "62 campers went rowing in the morning. 39 campers went rowing in the afternoon.", "Question": "How many more campers went rowing in the morning than in the afternoon?", "Equation": "( 62.0 - 39.0 )", "Answer": 23.0, "Type": "Subtraction"}, {"ID": "chal-983", "Body": "In a school there are 315 girls and 309 boys. There are also 772 teachers", "Question": "How many people are there in that school?", "Equation": "( ( 315.0 + 309.0 ) + 772.0 )", "Answer": 1396.0, "Type": "Addition"}, {"ID": "chal-984", "Body": "In <PERSON>'s class some boys love to play marbles. If <PERSON> has 10 marbles and she gave 5 marbles to each boy", "Question": "How many boys did she give the marbles to?", "Equation": "( 10.0 / 5.0 )", "Answer": 2.0, "Type": "Common-Division"}, {"ID": "chal-985", "Body": "<PERSON> earns $ 28 every week during the 1091 weeks of harvest. He also earns $ 939 per week for working overtime. If he works overtime every week", "Question": "How much money does he earn during harvest season?", "Equation": "( ( 28.0 + 939.0 ) * 1091.0 )", "Answer": 1054997.0, "Type": "Multiplication"}, {"ID": "chal-986", "Body": "<PERSON> had to complete 11 pages of math homework, 2 pages of reading homework and 3 more pages of biology homework.", "Question": "How many more pages of math homework than biology homework did she have?", "Equation": "( 11.0 - 3.0 )", "Answer": 8.0, "Type": "Subtraction"}, {"ID": "chal-987", "Body": "<PERSON> has 3 more apples than jackie. <PERSON> has 9 apples.", "Question": "How many apples does <PERSON> have?", "Equation": "( 9.0 - 3.0 )", "Answer": 6.0, "Type": "Subtraction"}, {"ID": "chal-988", "Body": "<PERSON> picked 3 ripe apples from her tree. Now the tree has 2 ripe apples and 27 unripe apples.", "Question": "How many ripe apples did the tree have to begin with?", "Equation": "( 3.0 + 2.0 )", "Answer": 5.0, "Type": "Addition"}, {"ID": "chal-989", "Body": "<PERSON> scored 109 points in each game. She also got 82 bonus points in each game.", "Question": "How many points did she score in 79 games?", "Equation": "( ( 109.0 + 82.0 ) * 79.0 )", "Answer": 15089.0, "Type": "Multiplication"}, {"ID": "chal-990", "Body": "<PERSON> had to complete 4 pages of reading homework, 7 pages of math homework and 19 more pages of biology homework.", "Question": "How many more pages of math homework than reading homework did she have?", "Equation": "( 7.0 - 4.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-991", "Body": "After resting they decided to go for a swim. The depth of the water is 9 times <PERSON>'s height. If <PERSON> is 15 feet tall and <PERSON> is 6 feet shorter than <PERSON>", "Question": "How deep was the water?", "Equation": "( ( 15.0 - 6.0 ) * 9.0 )", "Answer": 81.0, "Type": "Multiplication"}, {"ID": "chal-992", "Body": "In a school there are 308 girls and 318 boys. There are also 36 teachers", "Question": "How many pupils are there in that school?", "Equation": "( 308.0 + 318.0 )", "Answer": 626.0, "Type": "Addition"}, {"ID": "chal-993", "Body": "Friends of <PERSON> had 57 games and she had 63 ds games.", "Question": "How many more games does <PERSON> have than her friends?", "Equation": "( 63.0 - 57.0 )", "Answer": 6.0, "Type": "Subtraction"}, {"ID": "chal-994", "Body": "<PERSON> scored 84 points after playing 2 rounds of a trivia game. If he gained the same number of points each round", "Question": "How many points did he score per round?", "Equation": "( 84.0 / 2.0 )", "Answer": 42.0, "Type": "Common-Division"}, {"ID": "chal-995", "Body": "Next on his checklist is wax to stick the feathers together and colors to paint them. He needs a total of 57 g of colors to paint them. He needs 22 g of wax more. If the feathers require a total of 353 g of wax", "Question": "How many grams of wax does he already have?", "Equation": "( 353.0 - 22.0 )", "Answer": 331.0, "Type": "Subtraction"}, {"ID": "chal-996", "Body": "<PERSON> was helping her mom plant flowers and together they planted 36 seeds. They put 12 seeds in each flower bed and only 58 seeds grew into flowers in each flower bed.", "Question": "How many flower beds did they have?", "Equation": "( 36.0 / 12.0 )", "Answer": 3.0, "Type": "Common-Division"}, {"ID": "chal-997", "Body": "At the zoo, a cage had 3 snakes and 75 alligators. If 82 snakes and 19 alligators were hiding", "Question": "How many alligators were not hiding?", "Equation": "( 75.0 - 19.0 )", "Answer": 56.0, "Type": "Subtraction"}, {"ID": "chal-998", "Body": "<PERSON> was helping her mom plant flowers and together they planted 55 seeds. They put 15 seeds in each flower bed and only 60 seeds grew into flowers in each flower bed.", "Question": "How many flowers did they grow?", "Equation": "( 60.0 * ( 55.0 / 15.0 ) )", "Answer": 220.0, "Type": "Multiplication"}, {"ID": "chal-999", "Body": "<PERSON> is baking a cake. The recipe calls for 7 cups of sugar and 10 cups of flour. She already put in 4 cups of sugar.", "Question": "How many more cups of sugar does she need to add?", "Equation": "( 7.0 - 4.0 )", "Answer": 3.0, "Type": "Subtraction"}, {"ID": "chal-1000", "Body": "The grasshopper and the frog had a jumping contest. The grasshopper jumped 13 inches. The grasshopper jumped 2 inches farther than the grasshopper.", "Question": "How far did the frog jump?", "Equation": "( 13.0 - 2.0 )", "Answer": 11.0, "Type": "Subtraction"}]