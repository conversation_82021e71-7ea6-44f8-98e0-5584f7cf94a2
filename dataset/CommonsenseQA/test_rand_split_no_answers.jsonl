{"id": "90b30172e645ff91f7171a048582eb8b", "question": {"question_concept": "townhouse", "choices": [{"label": "A", "text": "suburban development"}, {"label": "B", "text": "apartment building"}, {"label": "C", "text": "bus stop"}, {"label": "D", "text": "michigan"}, {"label": "E", "text": "suburbs"}], "stem": "The townhouse was a hard sell for the realtor, it was right next to a high rise what?"}}
{"id": "000990552527b1353f98f1e1a7dfc643", "question": {"question_concept": "star", "choices": [{"label": "A", "text": "hollywood"}, {"label": "B", "text": "skyline"}, {"label": "C", "text": "outer space"}, {"label": "D", "text": "constellation"}, {"label": "E", "text": "solar system"}], "stem": "There is a star at the center of what group of celestial bodies?"}}
{"id": "dca0f2859f3c3dd43a9b2bfeff4936a8", "question": {"question_concept": "kids", "choices": [{"label": "A", "text": "ponder"}, {"label": "B", "text": "become adults"}, {"label": "C", "text": "wonder about"}, {"label": "D", "text": "open door"}, {"label": "E", "text": "distracting"}], "stem": "What were the kids doing as they looked up at the sky and clouds?"}}
{"id": "8795a949b39702af0e452c9e1229046d", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "own house"}, {"label": "B", "text": "own self"}, {"label": "C", "text": "wonderful memories"}, {"label": "D", "text": "know truth"}, {"label": "E", "text": "intelligent children"}], "stem": "The person taught an advanced class only for who?"}}
{"id": "1f74ea1f73b9f5d91a665b4d90218a6e", "question": {"question_concept": "ignorance", "choices": [{"label": "A", "text": "find truth"}, {"label": "B", "text": "hostility"}, {"label": "C", "text": "bliss"}, {"label": "D", "text": "accidents"}, {"label": "E", "text": "damage"}], "stem": "What is a likely consequence of ignorance of rules?"}}
{"id": "0b7734f608c188350573247e3ef2a00d", "question": {"question_concept": "dental office", "choices": [{"label": "A", "text": "neighborhood"}, {"label": "B", "text": "town"}, {"label": "C", "text": "street"}, {"label": "D", "text": "office building"}, {"label": "E", "text": "city"}], "stem": "After graduating the dentist set up his dental office back where he grew up, he wanted to always live in his home what?"}}
{"id": "d8d5e97e8e7f90712a81b14aee6f3627", "question": {"question_concept": "blade", "choices": [{"label": "A", "text": "cup"}, {"label": "B", "text": "fan"}, {"label": "C", "text": "chuck"}, {"label": "D", "text": "sword"}, {"label": "E", "text": "spatula"}], "stem": "Something that has a long and sharp blade is a?"}}
{"id": "8d916be530b91e6269b1d475601ae7ab", "question": {"question_concept": "doing housework", "choices": [{"label": "A", "text": "sneezing"}, {"label": "B", "text": "satisfaction"}, {"label": "C", "text": "tiredness"}, {"label": "D", "text": "backache"}, {"label": "E", "text": "get tired"}], "stem": "What will you experience after doing housework for a long time?"}}
{"id": "04919ff8acd9c71a0d7f1383255512b3", "question": {"question_concept": "writing program", "choices": [{"label": "A", "text": "bugs"}, {"label": "B", "text": "frustration"}, {"label": "C", "text": "need to integrate"}, {"label": "D", "text": "loop"}, {"label": "E", "text": "satisfaction"}], "stem": "What might a successful writing program cause?"}}
{"id": "a1f4dfbe9a3f49d4a84c2283e15d4c99", "question": {"question_concept": "check", "choices": [{"label": "A", "text": "wallet"}, {"label": "B", "text": "pay envelope"}, {"label": "C", "text": "bedside table"}, {"label": "D", "text": "desk drawer"}, {"label": "E", "text": "cash register"}], "stem": "The man wanted to telegram the check, so where did he place it?"}}
{"id": "5929f5704637184dc3390dd6964cacca", "question": {"question_concept": "sink", "choices": [{"label": "A", "text": "home"}, {"label": "B", "text": "neighbor's house"}, {"label": "C", "text": "laundry room"}, {"label": "D", "text": "car"}, {"label": "E", "text": "apartment"}], "stem": "John was worried when a sink hold opened in his yard.  It if was any bigger it might have swallowed his what?"}}
{"id": "b761674a4096d85a7f548604e8ca4f92", "question": {"question_concept": "bird", "choices": [{"label": "A", "text": "cage"}, {"label": "B", "text": "box"}, {"label": "C", "text": "nest"}, {"label": "D", "text": "countryside"}, {"label": "E", "text": "roof"}], "stem": "The bird wanted to fly, but couldn't, where was it?"}}
{"id": "6816062213d9298fbe40876b1be4e634", "question": {"question_concept": "underground map", "choices": [{"label": "A", "text": "bathroom"}, {"label": "B", "text": "library"}, {"label": "C", "text": "super market"}, {"label": "D", "text": "subway station"}, {"label": "E", "text": "county engineer's office"}], "stem": "Stan studied the underground map carefully.  He never took this route before and needed to know where to go.  What was he looking for on the map?"}}
{"id": "1891bccf27335dbc43201c9790e0996e", "question": {"question_concept": "cup of coffee", "choices": [{"label": "A", "text": "desk"}, {"label": "B", "text": "purse"}, {"label": "C", "text": "table"}, {"label": "D", "text": "coffee shop"}, {"label": "E", "text": "mexico"}], "stem": "Where do most people keep their cup of coffee while working?"}}
{"id": "6917399ea434e6c484459f895c72ef90", "question": {"question_concept": "well", "choices": [{"label": "A", "text": "kansas"}, {"label": "B", "text": "ground"}, {"label": "C", "text": "oil field"}, {"label": "D", "text": "countryside"}, {"label": "E", "text": "dry"}], "stem": "What kind of well is likely to create controversy?"}}
{"id": "a11bcfa54ff513de5a642a566b4c206c", "question": {"question_concept": "clock", "choices": [{"label": "A", "text": "stop working"}, {"label": "B", "text": "dead batteries"}, {"label": "C", "text": "fail to work"}, {"label": "D", "text": "time event"}, {"label": "E", "text": "working correctly"}], "stem": "If a clock is not ticking, what is its likely status?"}}
{"id": "c7c5b885f47cc8889ae4dffa5bf77b14", "question": {"question_concept": "cooling off", "choices": [{"label": "A", "text": "chills"}, {"label": "B", "text": "calm down"}, {"label": "C", "text": "better decisions"}, {"label": "D", "text": "dance"}, {"label": "E", "text": "revenge"}], "stem": "After the tussle everybody was cooling off, the person who stopped the fight told them to just what?"}}
{"id": "f15c3dd96d02bc6424e9ca888ebbb621", "question": {"question_concept": "love of music", "choices": [{"label": "A", "text": "play piano"}, {"label": "B", "text": "attend classical concert"}, {"label": "C", "text": "play violin"}, {"label": "D", "text": "listen to radio"}, {"label": "E", "text": "go to opera"}], "stem": "What would you do if you have a love of music involving strings and you do not have an electronics near you?"}}
{"id": "5f51c728ff1968db2e684335dcc72c6d", "question": {"question_concept": "oven", "choices": [{"label": "A", "text": "baking food"}, {"label": "B", "text": "cool temperature"}, {"label": "C", "text": "field"}, {"label": "D", "text": "roast"}, {"label": "E", "text": "bake"}], "stem": "He had to put the dough in the oven, what did he want to do with that dough?"}}
{"id": "fd2f6692cc27cce4c94997b8d4ef7987", "question": {"question_concept": "skate", "choices": [{"label": "A", "text": "spin"}, {"label": "B", "text": "romance"}, {"label": "C", "text": "hold hands"}, {"label": "D", "text": "fall down"}, {"label": "E", "text": "grab side railing"}], "stem": "When someone doesn't know how to skate well, they normally do what to stay up?"}}
{"id": "eb252ee1a725a738d8c956caec8791fd", "question": {"question_concept": "soccer field", "choices": [{"label": "A", "text": "playground"}, {"label": "B", "text": "backyard"}, {"label": "C", "text": "countryside"}, {"label": "D", "text": "mexico"}, {"label": "E", "text": "park"}], "stem": "If I am at a soccer field, and hear small children laughing and screaming nearby, where am I?"}}
{"id": "6441acd650f06440079c9ccba488a2d3", "question": {"question_concept": "eating dinner", "choices": [{"label": "A", "text": "heartburn"}, {"label": "B", "text": "feel better"}, {"label": "C", "text": "tummy ache"}, {"label": "D", "text": "indigestion"}, {"label": "E", "text": "illness"}], "stem": "After eating dinner, he threw it up due to being poisoned, what happened afterwards?"}}
{"id": "a2bfdb13e8f6f6945b827c71ab350c99", "question": {"question_concept": "breathing", "choices": [{"label": "A", "text": "warm air"}, {"label": "B", "text": "continue to live"}, {"label": "C", "text": "going to sleep"}, {"label": "D", "text": "hyperventilation"}, {"label": "E", "text": "stay alive"}], "stem": "When a person is breathing in a paper bag what are they trying to do?"}}
{"id": "99ee95c1d5a7a8e4409b6a17137dbf66", "question": {"question_concept": "beaver", "choices": [{"label": "A", "text": "australia"}, {"label": "B", "text": "countryside"}, {"label": "C", "text": "zoo"}, {"label": "D", "text": "tannery"}, {"label": "E", "text": "dictionary"}], "stem": "Where would you look to find out what a beaver is?"}}
{"id": "9b0027a8bdbb4aa5e33cc75bd84aa0b5", "question": {"question_concept": "grocery store", "choices": [{"label": "A", "text": "town"}, {"label": "B", "text": "strip mall"}, {"label": "C", "text": "street"}, {"label": "D", "text": "neighborhood"}, {"label": "E", "text": "strip mall"}], "stem": "What group of people's homes will usually have a grocery store?"}}
{"id": "9d2f3cfa032655fb72ade38941def326", "question": {"question_concept": "death", "choices": [{"label": "A", "text": "happen quickly"}, {"label": "B", "text": "happen to"}, {"label": "C", "text": "last forever"}, {"label": "D", "text": "bring sorrow"}, {"label": "E", "text": "sad"}], "stem": "Death came in the form of an airplane crash, how did it occur?"}}
{"id": "df4368b976f1f7d2e2ea4807d4c4c045", "question": {"question_concept": "swimming pool", "choices": [{"label": "A", "text": "california"}, {"label": "B", "text": "home"}, {"label": "C", "text": "resort hotel"}, {"label": "D", "text": "ymca"}, {"label": "E", "text": "motel"}], "stem": "I was in a swimming pool next to the Hollywood sign, where was I?"}}
{"id": "6843480c5c05eca606b83e9c925da43a", "question": {"question_concept": "churchyard", "choices": [{"label": "A", "text": "valley"}, {"label": "B", "text": "maine"}, {"label": "C", "text": "village"}, {"label": "D", "text": "england"}, {"label": "E", "text": "city"}], "stem": "Sam found himself in a small churchyard surrounded by tall buildings.  Where might he have been?"}}
{"id": "c4bf68163c4cf8cf16dd8ed00c906b28", "question": {"question_concept": "bookcase", "choices": [{"label": "A", "text": "public library"}, {"label": "B", "text": "house"}, {"label": "C", "text": "display nice clock"}, {"label": "D", "text": "den"}, {"label": "E", "text": "study"}], "stem": "What has more than four walls inside of it and might contain a bookcase?"}}
{"id": "d1e3039e1ada479354bfa866b3870822", "question": {"question_concept": "crumbs", "choices": [{"label": "A", "text": "table"}, {"label": "B", "text": "floor"}, {"label": "C", "text": "breadbox"}, {"label": "D", "text": "box of crackers"}, {"label": "E", "text": "rug"}], "stem": "Where do crumbs end up after they fall of your plate?"}}
{"id": "b870c8326fcd43bebec50b7a0c7a46eb", "question": {"question_concept": "death", "choices": [{"label": "A", "text": "ghost"}, {"label": "B", "text": "burial"}, {"label": "C", "text": "rebirth"}, {"label": "D", "text": "decomposition"}, {"label": "E", "text": "sadness"}], "stem": "What could happen to a soul after death?"}}
{"id": "23a30f6f1c659a84ab25c84d3385d905", "question": {"question_concept": "silicone", "choices": [{"label": "A", "text": "contact lens"}, {"label": "B", "text": "tube"}, {"label": "C", "text": "many man made items"}, {"label": "D", "text": "breast implants"}, {"label": "E", "text": "hardware store"}], "stem": "Where would you get silicone if you do not have any?"}}
{"id": "dbf107a805e2decef2dee35879a15537", "question": {"question_concept": "paper", "choices": [{"label": "A", "text": "notebook"}, {"label": "B", "text": "ream"}, {"label": "C", "text": "fax machine"}, {"label": "D", "text": "copy machine"}, {"label": "E", "text": "stock certificate"}], "stem": "The office used a lot of paper, they were constantly running out of it using the what?"}}
{"id": "75198a8f06a80f2008fc58eaab627bec", "question": {"question_concept": "news", "choices": [{"label": "A", "text": "relevant"}, {"label": "B", "text": "propaganda"}, {"label": "C", "text": "old information"}, {"label": "D", "text": "fiction"}, {"label": "E", "text": "park"}], "stem": "Printed news struggled in the modern era, with the instant internet the newspapers just felt like what?"}}
{"id": "2fef162dd803ab164e0b1b94821b9dd8", "question": {"question_concept": "relax", "choices": [{"label": "A", "text": "stretch out"}, {"label": "B", "text": "stop worrying"}, {"label": "C", "text": "go to bed"}, {"label": "D", "text": "have tea"}, {"label": "E", "text": "listen to music"}], "stem": "She was really wound up about the test, her roommate told her to relax and what?"}}
{"id": "3f930ccb2f4e3aa1dd5be9f2d38b8b48", "question": {"question_concept": "see story", "choices": [{"label": "A", "text": "picture it"}, {"label": "B", "text": "reading"}, {"label": "C", "text": "visualize"}, {"label": "D", "text": "open book"}, {"label": "E", "text": "go to movies"}], "stem": "The child's wild imagination made him able to see story that he read, he was able to do what with the story?"}}
{"id": "2fc16ace053d5a906a6d5f422a242d7c", "question": {"question_concept": "sign", "choices": [{"label": "A", "text": "city"}, {"label": "B", "text": "bus stop"}, {"label": "C", "text": "street corner"}, {"label": "D", "text": "school"}, {"label": "E", "text": "roadblock"}], "stem": "Where would you most commonly find a stop sign?"}}
{"id": "b8fec0eabb7090db994997dd694cac69", "question": {"question_concept": "weasel", "choices": [{"label": "A", "text": "washington dc"}, {"label": "B", "text": "prehistoric museum"}, {"label": "C", "text": "chicken coop"}, {"label": "D", "text": "cherry tree"}, {"label": "E", "text": "natural history museum"}], "stem": "Where would you find a weasel that is not alive?"}}
{"id": "96646a5c200f027b54ffbf8021552b80", "question": {"question_concept": "stamps", "choices": [{"label": "A", "text": "suitcase"}, {"label": "B", "text": "desk"}, {"label": "C", "text": "drawer"}, {"label": "D", "text": "cabinet"}, {"label": "E", "text": "case"}], "stem": "What part of some furniture could you put some stamps in?"}}
{"id": "ba2ebed8baf269abcfa0020ea11ba3eb", "question": {"question_concept": "learning", "choices": [{"label": "A", "text": "effectiveness"}, {"label": "B", "text": "distress"}, {"label": "C", "text": "education"}, {"label": "D", "text": "gaining knowledge"}, {"label": "E", "text": "increasing knowledge"}], "stem": "What is a good learning method known for?"}}
{"id": "4bcb3da3045057af215cbc04e763af16", "question": {"question_concept": "bat", "choices": [{"label": "A", "text": "bridge"}, {"label": "B", "text": "belfry"}, {"label": "C", "text": "new mexico"}, {"label": "D", "text": "dug out"}, {"label": "E", "text": "off the field"}], "stem": "The bat needed to go back to the benched players, where did it go?"}}
{"id": "b887bcc33bb61eb20e37e52ac1ce626f", "question": {"question_concept": "pizza", "choices": [{"label": "A", "text": "oven"}, {"label": "B", "text": "oven"}, {"label": "C", "text": "restaurant"}, {"label": "D", "text": "popular"}, {"label": "E", "text": "plate"}], "stem": "From where is a pizza removed before it is served?"}}
{"id": "bec4f1a2e26fe096a26216d762c78993", "question": {"question_concept": "music", "choices": [{"label": "A", "text": "silence"}, {"label": "B", "text": "silent"}, {"label": "C", "text": "television"}, {"label": "D", "text": "night club"}, {"label": "E", "text": "night club"}], "stem": "There is normally music at the beginning and end of the programming you watch on what?"}}
{"id": "f4ba945c957f3444e35f833dd0435c0a", "question": {"question_concept": "weasel", "choices": [{"label": "A", "text": "hen house"}, {"label": "B", "text": "michigan"}, {"label": "C", "text": "forrest"}, {"label": "D", "text": "great britain"}, {"label": "E", "text": "dumpster"}], "stem": "Where is a weasel likely to have an accent?"}}
{"id": "4d3e355cf5aa074edefe7173b77f63ec", "question": {"question_concept": "united states", "choices": [{"label": "A", "text": "northern hemisphere"}, {"label": "B", "text": "history book"}, {"label": "C", "text": "north america"}, {"label": "D", "text": "atlas"}, {"label": "E", "text": "indians"}], "stem": "The man wanted to learn about United States presidents, where should he look?"}}
{"id": "6fce16ebf11626ef55bf0c0e37c9456d", "question": {"question_concept": "skin", "choices": [{"label": "A", "text": "apples"}, {"label": "B", "text": "body"}, {"label": "C", "text": "people"}, {"label": "D", "text": "fruit"}, {"label": "E", "text": "finger"}], "stem": "The child tried to put a band-aid on the pet, his mom stopped him and explained they were only for skin of who?"}}
{"id": "9e102eadc895ea08bbfbfd80c8309ee0", "question": {"question_concept": "judging", "choices": [{"label": "A", "text": "guilty feelings"}, {"label": "B", "text": "conflict"}, {"label": "C", "text": "fight with each other"}, {"label": "D", "text": "argument"}, {"label": "E", "text": "controversy"}], "stem": "What can happen between two people judging a situation differently?"}}
{"id": "fcff323001011dd21ed51fabff2f969f", "question": {"question_concept": "platform", "choices": [{"label": "A", "text": "museum"}, {"label": "B", "text": "cafeteria"}, {"label": "C", "text": "building"}, {"label": "D", "text": "depot"}, {"label": "E", "text": "concert hall"}], "stem": "The conductor was pompous and wanted his platform higher, he wanted to make sure he could be seen clearly from every seat in the what?"}}
{"id": "43abbb0eb56d4e462c01b28afd632b8c", "question": {"question_concept": "weasel", "choices": [{"label": "A", "text": "viking ship"}, {"label": "B", "text": "mulberry bush"}, {"label": "C", "text": "chicken coop"}, {"label": "D", "text": "fedex"}, {"label": "E", "text": "rabbit warren"}], "stem": "How could a weasel go to another continent?"}}
{"id": "5b8c59723217c25ab48b653822e0adac", "question": {"question_concept": "space", "choices": [{"label": "A", "text": "milky way"}, {"label": "B", "text": "jar"}, {"label": "C", "text": "universe"}, {"label": "D", "text": "suitcase"}, {"label": "E", "text": "box"}], "stem": "Where can you find all of space?"}}
{"id": "ac8fcf96f07002db078949f4887246b3", "question": {"question_concept": "handle", "choices": [{"label": "A", "text": "saucepan"}, {"label": "B", "text": "brush"}, {"label": "C", "text": "carry purse"}, {"label": "D", "text": "keys"}, {"label": "E", "text": "umbrella"}], "stem": "The handle broke on a tool that James needed.  It was a long metal handle, with springs in it.  What was the tool?"}}
{"id": "0889b1d775c31b6c8162a3349da5ddb0", "question": {"question_concept": "church", "choices": [{"label": "A", "text": "christian community"}, {"label": "B", "text": "every town"}, {"label": "C", "text": "wedding"}, {"label": "D", "text": "populated area"}, {"label": "E", "text": "city"}], "stem": "Jenny and all of her friends go to a church. So do all of her neighbors and everyone she knows.  There is no one in her area that doesn't go to church.  Where might she live?"}}
{"id": "6a73597749f0c6803624ba4b3671c21c", "question": {"question_concept": "sitting quietly", "choices": [{"label": "A", "text": "relaxing"}, {"label": "B", "text": "satisfaction"}, {"label": "C", "text": "happiness"}, {"label": "D", "text": "relaxation"}, {"label": "E", "text": "anxiety"}], "stem": "What could happen to you after you have been sitting quietly and then are offered a job?"}}
{"id": "a069688802ba0a0418b9d3a1a5592859", "question": {"question_concept": "heifer", "choices": [{"label": "A", "text": "michigan"}, {"label": "B", "text": "countryside"}, {"label": "C", "text": "farm yard"}, {"label": "D", "text": "german field"}, {"label": "E", "text": "barnyard"}], "stem": "If a heifer were living in a rural area with other domesticated animals, what would that area be called?"}}
{"id": "778ef50cd09c693a2483f4a8ef9e2f33", "question": {"question_concept": "prison", "choices": [{"label": "A", "text": "penitentiary"}, {"label": "B", "text": "alcatraz"}, {"label": "C", "text": "countryside"}, {"label": "D", "text": "kansas"}, {"label": "E", "text": "america"}], "stem": "A person is an area where there is not a prison anywhere nearby, only grass where are they likely to be?"}}
{"id": "65868de4b9ef8c4e3c330446791afddd", "question": {"question_concept": "hunger", "choices": [{"label": "A", "text": "eating"}, {"label": "B", "text": "starvation"}, {"label": "C", "text": "eat a worm"}, {"label": "D", "text": "have lunch"}, {"label": "E", "text": "discomfort"}], "stem": "What happens when hunger is not sated?"}}
{"id": "302848fd33b7de1db309c7893c9468d5", "question": {"question_concept": "flowers", "choices": [{"label": "A", "text": "vase"}, {"label": "B", "text": "anthology"}, {"label": "C", "text": "rest area"}, {"label": "D", "text": "countryside"}, {"label": "E", "text": "table"}], "stem": "If one picked many flowers to display, what would they put them in?"}}
{"id": "f49cbed605a903623637c74d7e5752ec", "question": {"question_concept": "ferret", "choices": [{"label": "A", "text": "out of doors"}, {"label": "B", "text": "great britain"}, {"label": "C", "text": "house"}, {"label": "D", "text": "rocky mountains"}, {"label": "E", "text": "north america"}], "stem": "Even though legality is questionable, a lot of people have a pet ferret all over where?"}}
{"id": "d07d6a6db10059cd4815351405c741a6", "question": {"question_concept": "beam", "choices": [{"label": "A", "text": "bridge"}, {"label": "B", "text": "ceiling"}, {"label": "C", "text": "warehouse"}, {"label": "D", "text": "new construction"}, {"label": "E", "text": "school"}], "stem": "He worked in what large sorting area moving boxes with many a beam overhead?"}}
{"id": "091b90d1d4393b3e8790e605927e2126", "question": {"question_concept": "balloon", "choices": [{"label": "A", "text": "birthday party"}, {"label": "B", "text": "sky"}, {"label": "C", "text": "circus"}, {"label": "D", "text": "tree"}, {"label": "E", "text": "grocery store"}], "stem": "Where did the balloon go when the child lost control of it?"}}
{"id": "95b2c13b66129dc5e7551f4fa849d411", "question": {"question_concept": "door", "choices": [{"label": "A", "text": "keep people out"}, {"label": "B", "text": "glass"}, {"label": "C", "text": "xray vision"}, {"label": "D", "text": "enclose cupboard"}, {"label": "E", "text": "open"}], "stem": "Why would you be able to see through a door?"}}
{"id": "c1e186a3132cb4858fa701f4d400fbf5", "question": {"question_concept": "chair", "choices": [{"label": "A", "text": "church"}, {"label": "B", "text": "university"}, {"label": "C", "text": "furniture store"}, {"label": "D", "text": "brothel"}, {"label": "E", "text": "office"}], "stem": "Where would you sit in a chair while hearing about sin?"}}
{"id": "fbc9fe0c02a75bec9aa79bb7366e4794", "question": {"question_concept": "reading", "choices": [{"label": "A", "text": "concentration"}, {"label": "B", "text": "written material"}, {"label": "C", "text": "accumulating knowledge"}, {"label": "D", "text": "brain power"}, {"label": "E", "text": "concentrating"}], "stem": "Reading the instructions was difficult, it took all his what?"}}
{"id": "f2c11e9f99aa19752b57a8c4cfe27048", "question": {"question_concept": "accordion", "choices": [{"label": "A", "text": "music store"}, {"label": "B", "text": "instrument room"}, {"label": "C", "text": "variety show"}, {"label": "D", "text": "grocery store"}, {"label": "E", "text": "san francisco"}], "stem": "Where could you find millions of accordion?"}}
{"id": "afb23d492be6a9481caf099e19bd0fe6", "question": {"question_concept": "fart", "choices": [{"label": "A", "text": "impress"}, {"label": "B", "text": "smell bad"}, {"label": "C", "text": "attention"}, {"label": "D", "text": "offend"}, {"label": "E", "text": "expel gas"}], "stem": "A fart is something that is known to?"}}
{"id": "e2068d00603c52cfa67f96e6a12424d4", "question": {"question_concept": "rosebush", "choices": [{"label": "A", "text": "in the attic"}, {"label": "B", "text": "botanic garden"}, {"label": "C", "text": "backyard"}, {"label": "D", "text": "flower garden"}, {"label": "E", "text": "garden center"}], "stem": "Where is a personal rosebush likely to be found?"}}
{"id": "6efe7877b8edb04b0ae605f648915f3b", "question": {"question_concept": "dog", "choices": [{"label": "A", "text": "park"}, {"label": "B", "text": "dog pound"}, {"label": "C", "text": "table"}, {"label": "D", "text": "backyard"}, {"label": "E", "text": "leash"}], "stem": "James took his dog for a walk in a place where there were picnics.   Where might he have taken his dog?"}}
{"id": "411f6391e0671388f372eca1bf860dad", "question": {"question_concept": "chicken", "choices": [{"label": "A", "text": "stove"}, {"label": "B", "text": "plate"}, {"label": "C", "text": "freezer"}, {"label": "D", "text": "fast food restaurant"}, {"label": "E", "text": "pizza"}], "stem": "On what is your chicken usually presented to you?"}}
{"id": "220605d634a4c6ebf7025c891396ed12", "question": {"question_concept": "beautiful", "choices": [{"label": "A", "text": "homely"}, {"label": "B", "text": "bad"}, {"label": "C", "text": "overcast"}, {"label": "D", "text": "outdated"}, {"label": "E", "text": "plain"}], "stem": "She found the  vintage dress beautiful, this was despite that it looked very what compared to the other's elaborate dresses?"}}
{"id": "5da3c2f05d6f3cefbbbe4646ae64ae4c", "question": {"question_concept": "marmoset", "choices": [{"label": "A", "text": "warm climate"}, {"label": "B", "text": "great outdoors"}, {"label": "C", "text": "jungle"}, {"label": "D", "text": "rainforest"}, {"label": "E", "text": "shopping mall"}], "stem": "In what environment would you find a marmoset?"}}
{"id": "6b080789e4d841e70d88edd828f9e464", "question": {"question_concept": "baseball", "choices": [{"label": "A", "text": "hard"}, {"label": "B", "text": "break window"}, {"label": "C", "text": "round"}, {"label": "D", "text": "fly in sky"}, {"label": "E", "text": "fun to play"}], "stem": "What about a baseball makes it roll?"}}
{"id": "2eaa592ef41f55bd626d2b1d72f6aea3", "question": {"question_concept": "darkness", "choices": [{"label": "A", "text": "bed"}, {"label": "B", "text": "movies"}, {"label": "C", "text": "moon"}, {"label": "D", "text": "cellar"}, {"label": "E", "text": "moving"}], "stem": "Billy didn't like the darkness.  It was so dark that he couldn't make out any action.  He thought that these would be better quality."}}
{"id": "40041ef429ed015f329ffa3aef7b3476", "question": {"question_concept": "cup", "choices": [{"label": "A", "text": "kitchen cabinet"}, {"label": "B", "text": "drawers"}, {"label": "C", "text": "closet"}, {"label": "D", "text": "restaurant"}, {"label": "E", "text": "apartment"}], "stem": "Where do people keep cups?"}}
{"id": "1830737fdb09847324ddaf8e11bf4269", "question": {"question_concept": "landing", "choices": [{"label": "A", "text": "airport"}, {"label": "B", "text": "ladder"}, {"label": "C", "text": "stairwell"}, {"label": "D", "text": "apartment building"}, {"label": "E", "text": "ocean"}], "stem": "Where would you be landing if you were descending from a flight?"}}
{"id": "3dc165719e917dbb6a8f901d6b1e02a6", "question": {"question_concept": "mammoth", "choices": [{"label": "A", "text": "forest"}, {"label": "B", "text": "wild"}, {"label": "C", "text": "zoo"}, {"label": "D", "text": "movie"}, {"label": "E", "text": "ancient times"}], "stem": "Where was there previously a mammoth?"}}
{"id": "5e186319d14b1a8d6dee464e3680036b", "question": {"question_concept": "index card", "choices": [{"label": "A", "text": "card catalogue"}, {"label": "B", "text": "fileing cabnet"}, {"label": "C", "text": "oral report"}, {"label": "D", "text": "draw pictures"}, {"label": "E", "text": "library"}], "stem": "If someone is standing in front of the class with index cards, what are they doing?"}}
{"id": "b6717893b27f6c05c619684eb15690f2", "question": {"question_concept": "barbershop", "choices": [{"label": "A", "text": "razor"}, {"label": "B", "text": "city"}, {"label": "C", "text": "shopping mall"}, {"label": "D", "text": "barber college"}, {"label": "E", "text": "village"}], "stem": "Where could you find more than a few barbershop?"}}
{"id": "e0c1481f8e7353d7ba82f98429d3e7b0", "question": {"question_concept": "analysing", "choices": [{"label": "A", "text": "forget"}, {"label": "B", "text": "discovering truth"}, {"label": "C", "text": "enlightened"}, {"label": "D", "text": "new knowledge"}, {"label": "E", "text": "learn more about"}], "stem": "Sarah was analyzing the evidence because she didn't think she knew enough regarding it.   She wanted to do what?"}}
{"id": "b370186aefb03b810418051a7c3ea2f5", "question": {"question_concept": "koala", "choices": [{"label": "A", "text": "africa"}, {"label": "B", "text": "great outdoors"}, {"label": "C", "text": "queensland"}, {"label": "D", "text": "jungle"}, {"label": "E", "text": "wilderness"}], "stem": "A koala lives alone.  How might you describe its habitat?"}}
{"id": "1da772e014dc60f790ac921bcfdf549a", "question": {"question_concept": "hiking", "choices": [{"label": "A", "text": "enjoy nature"}, {"label": "B", "text": "get tired"}, {"label": "C", "text": "get lost"}, {"label": "D", "text": "pick flowers"}, {"label": "E", "text": "drink water"}], "stem": "What do you need to do while hiking to not get sick?"}}
{"id": "2a23800d9897030fdc7a9a286694ea6f", "question": {"question_concept": "ferret", "choices": [{"label": "A", "text": "north america"}, {"label": "B", "text": "rocky mountains"}, {"label": "C", "text": "own home"}, {"label": "D", "text": "work"}, {"label": "E", "text": "out of doors"}], "stem": "The black footed ferret is settling in large numbers in what geographic region?"}}
{"id": "54548d73316e95b20965f971e75a116b", "question": {"question_concept": "hearing testimony", "choices": [{"label": "A", "text": "ears"}, {"label": "B", "text": "boredom"}, {"label": "C", "text": "frustration"}, {"label": "D", "text": "careful listening"}, {"label": "E", "text": "ability to hear"}], "stem": "What could you have but still not be able to be hearing testimony if it is damaged?"}}
{"id": "fa9d57256b02efa856f9c585c112a141", "question": {"question_concept": "harpsichord", "choices": [{"label": "A", "text": "concert hall"}, {"label": "B", "text": "music shop"}, {"label": "C", "text": "museum"}, {"label": "D", "text": "band"}, {"label": "E", "text": "mansion"}], "stem": "Where might an antique harpischord be found?"}}
{"id": "2232f7c9fbdd2ab72fda34c5ab1eeda7", "question": {"question_concept": "fire", "choices": [{"label": "A", "text": "cleansing"}, {"label": "B", "text": "warm room"}, {"label": "C", "text": "hot chocolate"}, {"label": "D", "text": "warm hands"}, {"label": "E", "text": "help people"}], "stem": "What would a homeless person need if they already have a fire to stand next to?"}}
{"id": "6baa05d256dc723d6040986a354e229c", "question": {"question_concept": "animals", "choices": [{"label": "A", "text": "keep alive"}, {"label": "B", "text": "snack"}, {"label": "C", "text": "need to eat"}, {"label": "D", "text": "bite"}, {"label": "E", "text": "lie down"}], "stem": "If an animal has gone days without food, what is its likely desire?"}}
{"id": "1e565c6e6f0a9216ed43328ca7060379", "question": {"question_concept": "spam", "choices": [{"label": "A", "text": "ham"}, {"label": "B", "text": "blam"}, {"label": "C", "text": "splog"}, {"label": "D", "text": "sping"}, {"label": "E", "text": "spick"}], "stem": "Spam comes in a can, what other type of similar meat comes in a can?"}}
{"id": "709ec266b92bc2d2cf3f749e721e880c", "question": {"question_concept": "grape", "choices": [{"label": "A", "text": "book"}, {"label": "B", "text": "fruit stand"}, {"label": "C", "text": "painting"}, {"label": "D", "text": "field"}, {"label": "E", "text": "winery"}], "stem": "Grapes are often depicted in what sort of artistic expression involving pigments?"}}
{"id": "4cefa8256eeb65cb59e9ccd226b76989", "question": {"question_concept": "light bulb", "choices": [{"label": "A", "text": "room"}, {"label": "B", "text": "idea"}, {"label": "C", "text": "basement"}, {"label": "D", "text": "theatre"}, {"label": "E", "text": "lamp"}], "stem": "When fixing a light bulb to a larger piece of furniture, what is created?"}}
{"id": "7e2cd3e8603f7382ca599c6214c09f40", "question": {"question_concept": "go to sleep", "choices": [{"label": "A", "text": "dream"}, {"label": "B", "text": "rest up"}, {"label": "C", "text": "get up early"}, {"label": "D", "text": "pillow"}, {"label": "E", "text": "nap"}], "stem": "The character in the horror story was afraid to go to sleep, this is because the monster came upon him in a what?"}}
{"id": "96f1ec19683e5fe6aa0581da455ed913", "question": {"question_concept": "wool", "choices": [{"label": "A", "text": "craft store"}, {"label": "B", "text": "clothing store"}, {"label": "C", "text": "fabric store"}, {"label": "D", "text": "make a product"}, {"label": "E", "text": "sweater"}], "stem": "If you have some wool, what could you use it for?"}}
{"id": "ebbc0aeabef3636fcef1db2335d397cd", "question": {"question_concept": "wool", "choices": [{"label": "A", "text": "craft store"}, {"label": "B", "text": "fabric store"}, {"label": "C", "text": "seamstress"}, {"label": "D", "text": "sweater"}, {"label": "E", "text": "clothing store"}], "stem": "Where could you get something that is made out of wool and ready to use?"}}
{"id": "8055f4c058cb7f75048a7567f33344c8", "question": {"question_concept": "captain", "choices": [{"label": "A", "text": "follower"}, {"label": "B", "text": "private"}, {"label": "C", "text": "deckhand"}, {"label": "D", "text": "military"}, {"label": "E", "text": "manual"}], "stem": "The captain made an order about a knot and the who did it?"}}
{"id": "c4d6b72979a717e699e96605c4f6974c", "question": {"question_concept": "releasing energy", "choices": [{"label": "A", "text": "dancing"}, {"label": "B", "text": "exercising"}, {"label": "C", "text": "sex"}, {"label": "D", "text": "sweat"}, {"label": "E", "text": "motion"}], "stem": "He enjoyed releasing energy in a healthy way, he always made time for what?"}}
{"id": "84c802b1abc620e16a701cb3d5dcaa53", "question": {"question_concept": "clerks", "choices": [{"label": "A", "text": "mail office"}, {"label": "B", "text": "post office"}, {"label": "C", "text": "store"}, {"label": "D", "text": "shop"}, {"label": "E", "text": "stock shelves"}], "stem": "James and Jim were clerks who sorted stuff.  Where might they work?"}}
{"id": "bbf271a16f66ced481869ac9246753fd", "question": {"question_concept": "gazelle", "choices": [{"label": "A", "text": "trophy room"}, {"label": "B", "text": "ball gown"}, {"label": "C", "text": "hat"}, {"label": "D", "text": "kalahari desert"}, {"label": "E", "text": "wildlife refuge"}], "stem": "A gazelle is the mascot of their high school.  Jane stood under a picture of one while wearing what?"}}
{"id": "7b5526c0858ce164739c714d37215847", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "high wages"}, {"label": "B", "text": "husband or wife"}, {"label": "C", "text": "headache"}, {"label": "D", "text": "walking"}, {"label": "E", "text": "stay alive"}], "stem": "WHo is the most important person in someone's life?"}}
{"id": "d54709b9f05fcfcb7d89542bac81beac", "question": {"question_concept": "monkey", "choices": [{"label": "A", "text": "rain forest"}, {"label": "B", "text": "captivity"}, {"label": "C", "text": "french government"}, {"label": "D", "text": "bushes"}, {"label": "E", "text": "madagascar"}], "stem": "What is a monkey in a zoo in?"}}
{"id": "fc189a0c02c7db71e3123cd7f0b4cef8", "question": {"question_concept": "doctor", "choices": [{"label": "A", "text": "emergency room"}, {"label": "B", "text": "nursing home"}, {"label": "C", "text": "medical school"}, {"label": "D", "text": "dentist"}, {"label": "E", "text": "golf course"}], "stem": "Where does a doctor work slowly?"}}
{"id": "cd53be591ce29cca772c7b5bb5e4bcb7", "question": {"question_concept": "faucet", "choices": [{"label": "A", "text": "water fountain"}, {"label": "B", "text": "draw water"}, {"label": "C", "text": "sink"}, {"label": "D", "text": "bathroom or kitchen"}, {"label": "E", "text": "restroom"}], "stem": "The broken faucet uncontrollable poured water into something.  What might that be?"}}
{"id": "8d5edc8511298586e2cc0c0f58757491", "question": {"question_concept": "deodorant", "choices": [{"label": "A", "text": "spray"}, {"label": "B", "text": "medicine cabinet"}, {"label": "C", "text": "drum sticks"}, {"label": "D", "text": "grocery store"}, {"label": "E", "text": "own bathroom"}], "stem": "Danny didn't like roll-on deoderant.  He preferred what?"}}
{"id": "1e22d463619bde9ee5130867eeaf0509", "question": {"question_concept": "weasel", "choices": [{"label": "A", "text": "natural history museum"}, {"label": "B", "text": "the arlington cemetery"}, {"label": "C", "text": "chicken coop"}, {"label": "D", "text": "washington dc"}, {"label": "E", "text": "cherry tree"}], "stem": "The populace wasn't excited to vote, they knew no matter who they chose it was just another weasel they were sending where?"}}
{"id": "bbc744818da76fc80327d03ac23f0f27", "question": {"question_concept": "committing perjury", "choices": [{"label": "A", "text": "jail time"}, {"label": "B", "text": "go to jail"}, {"label": "C", "text": "criminal prosecution"}, {"label": "D", "text": "mistrial"}, {"label": "E", "text": "injustices"}], "stem": "Jane was afraid of committing perjury at a trial.  What might perjury lead to?"}}
{"id": "3622381c889f08b6c7146b49a8806567", "question": {"question_concept": "changing society", "choices": [{"label": "A", "text": "confusion and chaos"}, {"label": "B", "text": "draught"}, {"label": "C", "text": "no water"}, {"label": "D", "text": "acceptance"}, {"label": "E", "text": "change in leadership"}], "stem": "Global warming is causing problems in California, and our rapidly changing society still can't keep up with it.   In addition to wildfires, they also have what?"}}
{"id": "683b7d13a459f5e6e310fbb2b2b9606c", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "to climb a tree"}, {"label": "B", "text": "feel ashamed"}, {"label": "C", "text": "work at home"}, {"label": "D", "text": "gain weight"}, {"label": "E", "text": "live happily"}], "stem": "If a person is feeling sad, what do they often seek?"}}
{"id": "26ed1dc67dd9e6edf45eb7d1a9ef04b1", "question": {"question_concept": "animal", "choices": [{"label": "A", "text": "live long"}, {"label": "B", "text": "feel pain"}, {"label": "C", "text": "run away"}, {"label": "D", "text": "eating"}, {"label": "E", "text": "fight for life"}], "stem": "What does the animal need to do?"}}
{"id": "a04ed842e50830e6da7d62a0a53207c1", "question": {"question_concept": "pretend", "choices": [{"label": "A", "text": "people believe"}, {"label": "B", "text": "agree"}, {"label": "C", "text": "laughter"}, {"label": "D", "text": "religion"}, {"label": "E", "text": "can fool"}], "stem": "What do people pretend to do when someone they like tells a joke?"}}
{"id": "26cf1eca0eafba24ed7efe1c8c4f6170", "question": {"question_concept": "sloth", "choices": [{"label": "A", "text": "universe"}, {"label": "B", "text": "wilderness"}, {"label": "C", "text": "dictionary"}, {"label": "D", "text": "commercial"}, {"label": "E", "text": "math book"}], "stem": "Jane turned to a random page and saw \"sloth.\" What might she have been looking at?"}}
{"id": "19b33e90b3e2d133f7a8d7674a431eca", "question": {"question_concept": "computer", "choices": [{"label": "A", "text": "think"}, {"label": "B", "text": "get a virus"}, {"label": "C", "text": "run programs"}, {"label": "D", "text": "process information"}, {"label": "E", "text": "make decisions"}], "stem": "What can a computer do quickly?"}}
{"id": "1a21a3b77a805f7a453ff7dc69efb845", "question": {"question_concept": "hands", "choices": [{"label": "A", "text": "articulate"}, {"label": "B", "text": "sign language"}, {"label": "C", "text": "cup face"}, {"label": "D", "text": "soft"}, {"label": "E", "text": "cup water"}], "stem": "I had nothing but my hands from which to survive in the woods, what did I use them for?"}}
{"id": "03aac4e041884106a614b7a101dd1f4d", "question": {"question_concept": "men", "choices": [{"label": "A", "text": "gods"}, {"label": "B", "text": "boys"}, {"label": "C", "text": "ladies"}, {"label": "D", "text": "children"}, {"label": "E", "text": "lady"}], "stem": "Before we can become men, we are?"}}
{"id": "011e451bc75b78fe4960a9915d05213f", "question": {"question_concept": "paint", "choices": [{"label": "A", "text": "fulfilment"}, {"label": "B", "text": "draw"}, {"label": "C", "text": "park"}, {"label": "D", "text": "with brush"}, {"label": "E", "text": "wallpaper"}], "stem": "Rather than make a mess removing it all, they just decided to paint over the what?"}}
{"id": "71abe27c155a5e582883919e74ff0bc1", "question": {"question_concept": "cymbal", "choices": [{"label": "A", "text": "drumkit"}, {"label": "B", "text": "music store"}, {"label": "C", "text": "your brother"}, {"label": "D", "text": "symphony orchestra"}, {"label": "E", "text": "marching band"}], "stem": "Who is likely to have a large cymbal?"}}
{"id": "b2ffa6f4d7ea097907fb89d611963350", "question": {"question_concept": "driving to work", "choices": [{"label": "A", "text": "ongoing issue"}, {"label": "B", "text": "transportation cost"}, {"label": "C", "text": "stress"}, {"label": "D", "text": "getting there"}, {"label": "E", "text": "road rage"}], "stem": "While driving to work, what might a person be dreading?"}}
{"id": "44d474361e046b556b48b31e1bf5b611", "question": {"question_concept": "cold", "choices": [{"label": "A", "text": "baking"}, {"label": "B", "text": "prepared"}, {"label": "C", "text": "amiable"}, {"label": "D", "text": "opposite of hot"}, {"label": "E", "text": "chilly"}], "stem": "John acted cold, but in reality he was very what?"}}
{"id": "35c2b1c7637dc83575bb26c1d9553336", "question": {"question_concept": "small dog", "choices": [{"label": "A", "text": "aspca"}, {"label": "B", "text": "kenne"}, {"label": "C", "text": "basket"}, {"label": "D", "text": "next door"}, {"label": "E", "text": "person's home"}], "stem": "Where would a small dog that is someone's pet live?"}}
{"id": "50151d231fade66bc95684562d161958", "question": {"question_concept": "deliver", "choices": [{"label": "A", "text": "take away"}, {"label": "B", "text": "pick up"}, {"label": "C", "text": "ship"}, {"label": "D", "text": "keep"}, {"label": "E", "text": "receiving"}], "stem": "The package was set t o deliver the next day, but the customer had the option to go what it now?"}}
{"id": "7d0f388d8c9c8dbf7c2143c804bfab1c_1", "question": {"question_concept": "bacteria", "choices": [{"label": "A", "text": "water"}, {"label": "B", "text": "petri dish"}, {"label": "C", "text": "ground"}, {"label": "D", "text": "dirt"}, {"label": "E", "text": "finger"}], "stem": "The biohemist mom feared her dirt eating child would consume bacteria what did she avoid with him?"}}
{"id": "8e9852f85771fceacf387d727b0772e5", "question": {"question_concept": "getting", "choices": [{"label": "A", "text": "wanting more"}, {"label": "B", "text": "conquest"}, {"label": "C", "text": "ownership"}, {"label": "D", "text": "disappointment"}, {"label": "E", "text": "satisfaction"}], "stem": "He liked the car and decided to buy it, he was now getting what of his first vehicle?"}}
{"id": "1055f0701fef17174ca35a6d7be66842", "question": {"question_concept": "breathing fresh air", "choices": [{"label": "A", "text": "breathe deeply"}, {"label": "B", "text": "look around"}, {"label": "C", "text": "living"}, {"label": "D", "text": "feeling good"}, {"label": "E", "text": "fresh air"}], "stem": "The two woke up and went out on the cabin's porch, they were breathing fresh air and one remarked \"now this is what I call what\"?"}}
{"id": "a30f60507591ad14d87daa1a5a557f2f", "question": {"question_concept": "get warm", "choices": [{"label": "A", "text": "feel more comfortable"}, {"label": "B", "text": "swimming pool"}, {"label": "C", "text": "sleep"}, {"label": "D", "text": "start to sweat"}, {"label": "E", "text": "sweating"}], "stem": "When will you be unable to get warm since you will not know that you are cold?"}}
{"id": "463019f928bd81579eaec5bfc9ce298a", "question": {"question_concept": "letter", "choices": [{"label": "A", "text": "swimming pool"}, {"label": "B", "text": "post office"}, {"label": "C", "text": "envelope"}, {"label": "D", "text": "mail box"}, {"label": "E", "text": "alphabet"}], "stem": "Where can you go to have a person assist you in mailing a letter?"}}
{"id": "6884a0deac2a638822f4946dfa8e3eb2", "question": {"question_concept": "going to work", "choices": [{"label": "A", "text": "leave home"}, {"label": "B", "text": "stress"}, {"label": "C", "text": "malaise"}, {"label": "D", "text": "anger"}, {"label": "E", "text": "making money"}], "stem": "What is the ultimate goal of going to work?"}}
{"id": "65ca5ca4ca1774d67adb78eeef8cc7d5", "question": {"question_concept": "run", "choices": [{"label": "A", "text": "walk quickly"}, {"label": "B", "text": "walking"}, {"label": "C", "text": "stand still"}, {"label": "D", "text": "walk slowly"}, {"label": "E", "text": "go quickly"}], "stem": "James was scared to run through the course because he feared injury.  What might he do instead?"}}
{"id": "88007fc6a84123e90b792dc1807de427", "question": {"question_concept": "dental office", "choices": [{"label": "A", "text": "minnesota"}, {"label": "B", "text": "hospital"}, {"label": "C", "text": "michigan"}, {"label": "D", "text": "nebraska"}, {"label": "E", "text": "town"}], "stem": "If I am looking for a dental office in Ann Arbor, what state am I likely in?"}}
{"id": "178872169facd619022718ff94993b34", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "feel loved"}, {"label": "B", "text": "cross street"}, {"label": "C", "text": "laugh out loud"}, {"label": "D", "text": "cry"}, {"label": "E", "text": "hurry home"}], "stem": "What does a person do when they hear a joke?"}}
{"id": "e0fee64a8401f66e3c35c79d8519844e", "question": {"question_concept": "teacher", "choices": [{"label": "A", "text": "time test"}, {"label": "B", "text": "tell story"}, {"label": "C", "text": "lower expectations"}, {"label": "D", "text": "quit"}, {"label": "E", "text": "encourage"}], "stem": "Johnny's teacher felt that he had such potential that he should not do what?"}}
{"id": "f6e88f129e0cc58d9c04765b37f152fe", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "look beautiful"}, {"label": "B", "text": "intellectual stimulation"}, {"label": "C", "text": "compliments"}, {"label": "D", "text": "she was late"}, {"label": "E", "text": "time to rest"}], "stem": "The person spent all morning putting on make-up and elegant clothing, what was their result?"}}
{"id": "2b0e5425d86ac18c47cd1806a74fb284", "question": {"question_concept": "get warm", "choices": [{"label": "A", "text": "more comfortable"}, {"label": "B", "text": "heater on"}, {"label": "C", "text": "you're cold"}, {"label": "D", "text": "heat stroke"}, {"label": "E", "text": "feel comfortable"}], "stem": "When would you need to get warm?"}}
{"id": "f514fdb807c433be25529cc79610e3b3", "question": {"question_concept": "servant", "choices": [{"label": "A", "text": "free person"}, {"label": "B", "text": "slave"}, {"label": "C", "text": "lord"}, {"label": "D", "text": "boss"}, {"label": "E", "text": "in charge"}], "stem": "James was the servant.   Max was his what?"}}
{"id": "91e6269752463b39ec50888d0a13f46a", "question": {"question_concept": "graveyard", "choices": [{"label": "A", "text": "church property"}, {"label": "B", "text": "every town"}, {"label": "C", "text": "arlington"}, {"label": "D", "text": "cemetery"}, {"label": "E", "text": "grave yard"}], "stem": "The soldier was interred at the graveyard outside of Washington DC, where was he buried?"}}
{"id": "2273f05dbd17824a8d2111d4fc2b5c47", "question": {"question_concept": "remember", "choices": [{"label": "A", "text": "focus on"}, {"label": "B", "text": "try"}, {"label": "C", "text": "spotlight"}, {"label": "D", "text": "take pictures"}, {"label": "E", "text": "memorize"}], "stem": "When you are trying to remember something, you are putting what on it?"}}
{"id": "cddbdb53dbcc149bf1912aa7ea171570", "question": {"question_concept": "sunshine", "choices": [{"label": "A", "text": "sea"}, {"label": "B", "text": "summer"}, {"label": "C", "text": "moon"}, {"label": "D", "text": "windowsill"}, {"label": "E", "text": "desktop"}], "stem": "What could sunshine light on fire if it is magnified?"}}
{"id": "1912cc3ef40e13763d0375c531d2f1c0", "question": {"question_concept": "disability", "choices": [{"label": "A", "text": "competency"}, {"label": "B", "text": "potential"}, {"label": "C", "text": "capacity"}, {"label": "D", "text": "strength"}, {"label": "E", "text": "competence"}], "stem": "Even if you have a disability you can still have what which is the ability to do your job well?"}}
{"id": "ebae1b07ab3230d4b59b2c7ecc5a89d3", "question": {"question_concept": "cemetery", "choices": [{"label": "A", "text": "most cities"}, {"label": "B", "text": "churchyard"}, {"label": "C", "text": "field"}, {"label": "D", "text": "city"}, {"label": "E", "text": "countryside"}], "stem": "Sarah was looking for an ancestor who was buried in an old cemetery.   Where might he find that cemetery?"}}
{"id": "27e65eba6f596045187e7af84151340a", "question": {"question_concept": "analysing", "choices": [{"label": "A", "text": "more intelligent"}, {"label": "B", "text": "better understanding"}, {"label": "C", "text": "enlightened"}, {"label": "D", "text": "discovering truth"}, {"label": "E", "text": "headache"}], "stem": "Bob spent all of his time analyzing the works of masters because he wanted to become what?"}}
{"id": "83d853f94d28a5507a7db70d396d3dff", "question": {"question_concept": "judging", "choices": [{"label": "A", "text": "giving a test"}, {"label": "B", "text": "feeling guilty"}, {"label": "C", "text": "responsibility"}, {"label": "D", "text": "go to jail"}, {"label": "E", "text": "being judged"}], "stem": "What should you prepare for if you are judging someone?"}}
{"id": "ecdb6d61299c2f8fd29a56380c7f6aa3", "question": {"question_concept": "needle", "choices": [{"label": "A", "text": "desk"}, {"label": "B", "text": "hospital"}, {"label": "C", "text": "doctor's office"}, {"label": "D", "text": "haystack"}, {"label": "E", "text": "mom's sewing kit"}], "stem": "Where do you get a needle from an IV?"}}
{"id": "c6f2cbec1de5ed18901375529cffed43_1", "question": {"question_concept": "stay in bed", "choices": [{"label": "A", "text": "tired"}, {"label": "B", "text": "were sick"}, {"label": "C", "text": "you're sick"}, {"label": "D", "text": "lazy"}, {"label": "E", "text": "rest more"}], "stem": "The couple decided to stay in bed all day together, unfortunately though it was because they what?"}}
{"id": "a3244a4b31bf92bf70c773e21f4da5ec", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "thank god"}, {"label": "B", "text": "kill each other"}, {"label": "C", "text": "commit suicide"}, {"label": "D", "text": "believe in god"}, {"label": "E", "text": "experience pain"}], "stem": "What was a historical occurrence in some cases when people of a certain belief system met people with different beliefs?"}}
{"id": "a72c911c9e5c7b315b034305dc294afc", "question": {"question_concept": "stuff", "choices": [{"label": "A", "text": "cupboard"}, {"label": "B", "text": "box"}, {"label": "C", "text": "anything"}, {"label": "D", "text": "table"}, {"label": "E", "text": "cabinet"}], "stem": "The sloppy dad saw the random stuff in the kitchen, and saw space for it next to the glasses, where did he decide to place it?"}}
{"id": "54592ec6a82ff212eed8e2ee14de18a3", "question": {"question_concept": "computers", "choices": [{"label": "A", "text": "expensive"}, {"label": "B", "text": "9 gigahashes per second"}, {"label": "C", "text": "fun"}, {"label": "D", "text": "fast"}, {"label": "E", "text": "do work"}], "stem": "Computers complete mathematic operations at what speed?"}}
{"id": "9aca1e1ca1d53f0a4489cb59ca78418e", "question": {"question_concept": "going to market", "choices": [{"label": "A", "text": "spending money"}, {"label": "B", "text": "bankruptcy"}, {"label": "C", "text": "tedium"}, {"label": "D", "text": "time wasted"}, {"label": "E", "text": "meeting new people"}], "stem": "What might frequent trips of going to market cause?"}}
{"id": "8ce45dcedea9223e850ade4801985564", "question": {"question_concept": "couples", "choices": [{"label": "A", "text": "enjoy movie"}, {"label": "B", "text": "enjoy sex"}, {"label": "C", "text": "row with each other"}, {"label": "D", "text": "have sex"}, {"label": "E", "text": "company"}], "stem": "The couples were together for fifty years in the study, what did they do less?"}}
{"id": "0ca6391549e2b1f8e4ea809c0e5b256a", "question": {"question_concept": "trash", "choices": [{"label": "A", "text": "dumpster"}, {"label": "B", "text": "wastepaper basket"}, {"label": "C", "text": "soccer game"}, {"label": "D", "text": "garbage can"}, {"label": "E", "text": "wastebasket"}], "stem": "What is another name for trash can?"}}
{"id": "68250b9f3448c4b65aa9ce688c6e4cf3", "question": {"question_concept": "competing", "choices": [{"label": "A", "text": "bad blood"}, {"label": "B", "text": "puncher"}, {"label": "C", "text": "winning or losing"}, {"label": "D", "text": "accomplishment"}, {"label": "E", "text": "rivalry"}], "stem": "If you competing against someone who is a rival what may you share?"}}
{"id": "61984fa9de4fc5484dca07aa697c03b2", "question": {"question_concept": "pillow case", "choices": [{"label": "A", "text": "drawer"}, {"label": "B", "text": "england"}, {"label": "C", "text": "pillow"}, {"label": "D", "text": "bedding store"}, {"label": "E", "text": "bedroom"}], "stem": "Where do you normal use a pillow case?"}}
{"id": "36aa45a085fad5e9c9479811f19bffea", "question": {"question_concept": "requisite", "choices": [{"label": "A", "text": "dispensable"}, {"label": "B", "text": "unworthy"}, {"label": "C", "text": "unneeded"}, {"label": "D", "text": "inessential"}, {"label": "E", "text": "unnecessary"}], "stem": "The adviser had said the class was a requisite for his major, but on the first day he realized it was completely what?"}}
{"id": "5566e5b229dc312159ead031240af4fd", "question": {"question_concept": "map", "choices": [{"label": "A", "text": "atlas"}, {"label": "B", "text": "museum"}, {"label": "C", "text": "amusement park"}, {"label": "D", "text": "backpack"}, {"label": "E", "text": "classroom"}], "stem": "Where might someone find a map on the wall?"}}
{"id": "7c7576876bed0d9e07e69bc10cedbfd8", "question": {"question_concept": "barber shop", "choices": [{"label": "A", "text": "high street"}, {"label": "B", "text": "small town"}, {"label": "C", "text": "seville"}, {"label": "D", "text": "comb"}, {"label": "E", "text": "canada"}], "stem": "Where is a movie about a barber shop done?"}}
{"id": "38e5d6155751fd84b046ecd71c29f44a", "question": {"question_concept": "guitar", "choices": [{"label": "A", "text": "music room"}, {"label": "B", "text": "rock band"}, {"label": "C", "text": "toy store"}, {"label": "D", "text": "stage"}, {"label": "E", "text": "concert"}], "stem": "The student practiced his guitar often, he always spent his free period where?"}}
{"id": "ce8c5db1047aa21178422eaa11c4a33d", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "catch cold"}, {"label": "B", "text": "thank god"}, {"label": "C", "text": "cross street"}, {"label": "D", "text": "promise to do"}, {"label": "E", "text": "driving"}], "stem": "How does a person get around town?"}}
{"id": "8db48320d6def5cbf15e1ba800ddc9fa", "question": {"question_concept": "tote bag", "choices": [{"label": "A", "text": "house"}, {"label": "B", "text": "shopping cart"}, {"label": "C", "text": "store called target"}, {"label": "D", "text": "school"}, {"label": "E", "text": "city"}], "stem": "The store offered cloth bags, but she had already brought her tote bag from her what?"}}
{"id": "9c5880baa5e1746f83b40b9f7e4e118c", "question": {"question_concept": "apple tree", "choices": [{"label": "A", "text": "their house"}, {"label": "B", "text": "paradise"}, {"label": "C", "text": "dirt"}, {"label": "D", "text": "south africa"}, {"label": "E", "text": "park"}], "stem": "According to many people of faith the first people ate from an apple tree causing them to be expelled from where?"}}
{"id": "b34abcccf20b28c1539cb26dc5da6b74", "question": {"question_concept": "liken", "choices": [{"label": "A", "text": "discriminate"}, {"label": "B", "text": "differentiate"}, {"label": "C", "text": "contrast"}, {"label": "D", "text": "distinguish"}, {"label": "E", "text": "discern"}], "stem": "What the opposite of liken?"}}
{"id": "2c2dfb48b4bcfb134991c37679dbccd6", "question": {"question_concept": "commit to memory", "choices": [{"label": "A", "text": "to delete their accounts"}, {"label": "B", "text": "must remember"}, {"label": "C", "text": "pass exams"}, {"label": "D", "text": "important"}, {"label": "E", "text": "useful"}], "stem": "Why do people memorize their passwords?"}}
{"id": "189bed3cb3fbac403343294d25141925", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "talk to each other"}, {"label": "B", "text": "shake hands"}, {"label": "C", "text": "pay bills"}, {"label": "D", "text": "own property"}, {"label": "E", "text": "rub noses"}], "stem": "How do people usually begin an interaction?"}}
{"id": "6d94fb7d13aff3ed9eedda570498e8ca", "question": {"question_concept": "living things", "choices": [{"label": "A", "text": "increase in size"}, {"label": "B", "text": "procreate"}, {"label": "C", "text": "move"}, {"label": "D", "text": "increase in size"}, {"label": "E", "text": "increase population"}], "stem": "what do living things do as they get older?"}}
{"id": "7f710a9fc9b0669b1d54e089627c9d5f", "question": {"question_concept": "toilet paper", "choices": [{"label": "A", "text": "rest area"}, {"label": "B", "text": "department store"}, {"label": "C", "text": "grocery store"}, {"label": "D", "text": "cabinet"}, {"label": "E", "text": "bathroom"}], "stem": "Where can toilet paper be bought?"}}
{"id": "a97c51e1ff690058dd27219ce22c6508", "question": {"question_concept": "procreating", "choices": [{"label": "A", "text": "quintuplets"}, {"label": "B", "text": "large family"}, {"label": "C", "text": "children's laughter"}, {"label": "D", "text": "population increase"}, {"label": "E", "text": "added responsibilities"}], "stem": "What do I want that makes me want to procreate a lot?"}}
{"id": "5db6f0b5c7b4ed600010157d629a2f5f", "question": {"question_concept": "falling", "choices": [{"label": "A", "text": "getting hurt"}, {"label": "B", "text": "being laughed at"}, {"label": "C", "text": "lacerations"}, {"label": "D", "text": "gravity"}, {"label": "E", "text": "contact with"}], "stem": "Falling doesn't cause injury.   Injury is cause by the ground, specifically what involving the ground?"}}
{"id": "99c81f995c9182b92ebc658c89b1b417", "question": {"question_concept": "vegetables", "choices": [{"label": "A", "text": "supermarket"}, {"label": "B", "text": "fridge"}, {"label": "C", "text": "refrigerator"}, {"label": "D", "text": "cashier"}, {"label": "E", "text": "vegetable garden"}], "stem": "Where do you buy vegetables?"}}
{"id": "7e0023fe1f499a61d5a6f9af01effbfc", "question": {"question_concept": "money", "choices": [{"label": "A", "text": "settle everything"}, {"label": "B", "text": "multiple more money"}, {"label": "C", "text": "pay bills"}, {"label": "D", "text": "increase power"}, {"label": "E", "text": "control people"}], "stem": "The politician was taking money from lobbyists, what were they hoping to do by giving it?"}}
{"id": "b99b1c7a51a26f5178475313a9bbd287", "question": {"question_concept": "number", "choices": [{"label": "A", "text": "mathematics"}, {"label": "B", "text": "statistic"}, {"label": "C", "text": "percentage"}, {"label": "D", "text": "equation"}, {"label": "E", "text": "math problem"}], "stem": "What would I need if I want to know the number of people with cancer and I know the total number of people?"}}
{"id": "666922651ffa1c7db321f29613e2a127", "question": {"question_concept": "local", "choices": [{"label": "A", "text": "foreign"}, {"label": "B", "text": "far away"}, {"label": "C", "text": "everywhere"}, {"label": "D", "text": "national"}, {"label": "E", "text": "worldwide"}], "stem": "The company blew up over night, it seemed like one day they were local and the next what?"}}
{"id": "bb7108a24332df7512017038505e329d", "question": {"question_concept": "getting warm", "choices": [{"label": "A", "text": "feeling iritable"}, {"label": "B", "text": "pleasure"}, {"label": "C", "text": "euphoria"}, {"label": "D", "text": "starting fire"}, {"label": "E", "text": "get hot"}], "stem": "Danny was getting warm. He turned on the air conditioner so he wouldn't do what?"}}
{"id": "e4e4384278560fc9d86c670732ae1025", "question": {"question_concept": "chess pawn", "choices": [{"label": "A", "text": "toy store"}, {"label": "B", "text": "chess set"}, {"label": "C", "text": "soccer"}, {"label": "D", "text": "chess game"}, {"label": "E", "text": "small case"}], "stem": "I have a chess pawn and 15 other pieces, what could I play with this?"}}
{"id": "5abced4fbb2487ab085fe86653e05465", "question": {"question_concept": "potato", "choices": [{"label": "A", "text": "croquettes"}, {"label": "B", "text": "prince edward island"}, {"label": "C", "text": "main course"}, {"label": "D", "text": "french fries"}, {"label": "E", "text": "root cellar"}], "stem": "The family was poor and couldn't afford meat, so what purpose did the potato serve in their meal?"}}
{"id": "5d08f46e6d34d7b18523504704995fd1", "question": {"question_concept": "making bread", "choices": [{"label": "A", "text": "satisfaction"}, {"label": "B", "text": "mess"}, {"label": "C", "text": "pride"}, {"label": "D", "text": "gratifying"}, {"label": "E", "text": "better eating"}], "stem": "What positive effect can making bread cause?"}}
{"id": "622995494e6e096f778670cecb3d830b", "question": {"question_concept": "microphone boom", "choices": [{"label": "A", "text": "tv studio"}, {"label": "B", "text": "new york"}, {"label": "C", "text": "concert"}, {"label": "D", "text": "recording studio"}, {"label": "E", "text": "building"}], "stem": "Where is a microphone boom likely to be used to broadcast live music?"}}
{"id": "4ed764f071d4f558f9ec42b48ef3ca01", "question": {"question_concept": "popular", "choices": [{"label": "A", "text": "unknown"}, {"label": "B", "text": "nerdy"}, {"label": "C", "text": "disliked"}, {"label": "D", "text": "annoyed"}, {"label": "E", "text": "geek"}], "stem": "It was a weird place to be, she was the most popular but also the most what?"}}
{"id": "bedcaa84506fe21fc324db3c618cdfa8", "question": {"question_concept": "communicating", "choices": [{"label": "A", "text": "sharing of knowledge"}, {"label": "B", "text": "people to think"}, {"label": "C", "text": "distributed information"}, {"label": "D", "text": "response"}, {"label": "E", "text": "misunderstandings"}], "stem": "If I have information, why would I want to be communicating it?"}}
{"id": "ea0ee21980f85234853de55d7064d10c", "question": {"question_concept": "fox", "choices": [{"label": "A", "text": "painting"}, {"label": "B", "text": "hen house"}, {"label": "C", "text": "wooded areas"}, {"label": "D", "text": "chicken coop"}, {"label": "E", "text": "bird's nest"}], "stem": "Where would finding a fox make a farmer angry?"}}
{"id": "45fd62a1570cf4e226de657ada37bcc6", "question": {"question_concept": "cheese", "choices": [{"label": "A", "text": "the cupboard"}, {"label": "B", "text": "refrigerator"}, {"label": "C", "text": "plate"}, {"label": "D", "text": "fridge"}, {"label": "E", "text": "mouse trap"}], "stem": "Where is usually the best place to store cheese?"}}
{"id": "bfddbe2b019d7ab2bb1bb25871716f06", "question": {"question_concept": "pretending", "choices": [{"label": "A", "text": "imagining"}, {"label": "B", "text": "acting skills"}, {"label": "C", "text": "creativity"}, {"label": "D", "text": "vehicle"}, {"label": "E", "text": "play"}], "stem": "The instructor explained that pretending was all it was, and practicing that was the only way to hone their what?"}}
{"id": "f4a30172513ca4c5763ded05f2f9a756", "question": {"question_concept": "terrorists", "choices": [{"label": "A", "text": "explode bombs"}, {"label": "B", "text": "afghanistan"}, {"label": "C", "text": "prison"}, {"label": "D", "text": "downtown"}, {"label": "E", "text": "airport"}], "stem": "Where do terrorist grow heroin poppies in the mountains?"}}
{"id": "5b0b9d64c3ce0d4d3648d45bbd88ff61", "question": {"question_concept": "cavity", "choices": [{"label": "A", "text": "teeth"}, {"label": "B", "text": "mouth"}, {"label": "C", "text": "molar"}, {"label": "D", "text": "too much sugar"}, {"label": "E", "text": "dentist"}], "stem": "If you have a cavity in the back of you mouth where is it?"}}
{"id": "cfef1ee6edd1e673f85e624509cb6f05", "question": {"question_concept": "copulating", "choices": [{"label": "A", "text": "reproduction"}, {"label": "B", "text": "ejaculation"}, {"label": "C", "text": "babies"}, {"label": "D", "text": "rapport"}, {"label": "E", "text": "cumming"}], "stem": "What is the biological intent when two people are copulating?"}}
{"id": "d2542eb1408a9420c520145c46a82663", "question": {"question_concept": "buying products", "choices": [{"label": "A", "text": "owning"}, {"label": "B", "text": "pleasure"}, {"label": "C", "text": "agony"}, {"label": "D", "text": "never used"}, {"label": "E", "text": "disagreements"}], "stem": "What always happens to products after you are buying products?"}}
{"id": "42ce969a0fd4788aaadff77e8405f288", "question": {"question_concept": "apartment", "choices": [{"label": "A", "text": "avoid buying"}, {"label": "B", "text": "staying cheap"}, {"label": "C", "text": "town"}, {"label": "D", "text": "budget"}, {"label": "E", "text": "michigan"}], "stem": "Why would you try to find a bad apartment?"}}
{"id": "cea0ef7b40f17c03793a0d5785ce8091", "question": {"question_concept": "train ticket", "choices": [{"label": "A", "text": "polar express"}, {"label": "B", "text": "purse"}, {"label": "C", "text": "train depot"}, {"label": "D", "text": "conductor's hand"}, {"label": "E", "text": "ticket booth"}], "stem": "Where do you wait before using your train ticket?"}}
{"id": "3d045798bf3bcd73608d297ec8426953", "question": {"question_concept": "shaft", "choices": [{"label": "A", "text": "mine"}, {"label": "B", "text": "column"}, {"label": "C", "text": "windshaft"}, {"label": "D", "text": "building"}, {"label": "E", "text": "tunnel"}], "stem": "The construction began and they sunk a big support shaft into the ground, this would support the fancy what out front?"}}
{"id": "4f434f12da41878d149d085b1df26408", "question": {"question_concept": "storey", "choices": [{"label": "A", "text": "tall building"}, {"label": "B", "text": "mall"}, {"label": "C", "text": "apartment building"}, {"label": "D", "text": "book of stories"}, {"label": "E", "text": "roof"}], "stem": "From where could you read your child a storey before bed?"}}
{"id": "46d709541c4a58e71f9b7f47d3a2ff12", "question": {"question_concept": "buying", "choices": [{"label": "A", "text": "feel better"}, {"label": "B", "text": "possessing more"}, {"label": "C", "text": "relax"}, {"label": "D", "text": "go broke"}, {"label": "E", "text": "losing money"}], "stem": "Buying something nice after a stressful event can make you what?"}}
{"id": "7b4fd5d4f54eb97257eee2c859f7cc69", "question": {"question_concept": "bazaar", "choices": [{"label": "A", "text": "festival"}, {"label": "B", "text": "canada"}, {"label": "C", "text": "arabia"}, {"label": "D", "text": "istanbul"}, {"label": "E", "text": "india"}], "stem": "Where would you be shopping if you were at a bazaar named in a 1950's song?"}}
{"id": "fd6857afa7df49af59cb6b3378010c87", "question": {"question_concept": "water", "choices": [{"label": "A", "text": "wet clothes"}, {"label": "B", "text": "spring forth"}, {"label": "C", "text": "take several forms"}, {"label": "D", "text": "take it"}, {"label": "E", "text": "room temperature"}], "stem": "What would you do if someone poured water above you?"}}
{"id": "30f7b893533032e1b7572ad40400dc76", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "thank god"}, {"label": "B", "text": "enjoy working"}, {"label": "C", "text": "complain"}, {"label": "D", "text": "wait in line"}, {"label": "E", "text": "offer help"}], "stem": "What is a person helping for when going to customer service?"}}
{"id": "8ec10dc05137c639d1d51780a18fff2f", "question": {"question_concept": "waging war", "choices": [{"label": "A", "text": "armies"}, {"label": "B", "text": "energy"}, {"label": "C", "text": "using electric"}, {"label": "D", "text": "asserting power"}, {"label": "E", "text": "weapons"}], "stem": "The country was waging war, those back home had to ration their household use of what?"}}
{"id": "18e72c9c9da44dbf888d6ac0deadb0ef", "question": {"question_concept": "runway", "choices": [{"label": "A", "text": "military base"}, {"label": "B", "text": "bowling alley"}, {"label": "C", "text": "mall"}, {"label": "D", "text": "city"}, {"label": "E", "text": "fashion show"}], "stem": "Where would a runway used by people wearing camo in an array of colors?"}}
{"id": "ef730bdda2cb6081f1c0d1a67291415b", "question": {"question_concept": "skeleton", "choices": [{"label": "A", "text": "building"}, {"label": "B", "text": "aircraft"}, {"label": "C", "text": "case"}, {"label": "D", "text": "ship"}, {"label": "E", "text": "museum"}], "stem": "What has a keel in its skeleton?"}}
{"id": "5f9675ecdc9de4feccaa84588db94c24", "question": {"question_concept": "road", "choices": [{"label": "A", "text": "valley"}, {"label": "B", "text": "bridge"}, {"label": "C", "text": "new york"}, {"label": "D", "text": "town"}, {"label": "E", "text": "north america"}], "stem": "Where can one find a road that goes through New York City?"}}
{"id": "bdb789fc941b9e1906b3ba858703b157", "question": {"question_concept": "dog", "choices": [{"label": "A", "text": "nose"}, {"label": "B", "text": "paws"}, {"label": "C", "text": "big heart"}, {"label": "D", "text": "one mouth"}, {"label": "E", "text": "four legs"}], "stem": "Dog lovers are known to have?"}}
{"id": "8206e2a8bfb379249fb4d95bc92378ac", "question": {"question_concept": "chair", "choices": [{"label": "A", "text": "synagogue"}, {"label": "B", "text": "friend's house"}, {"label": "C", "text": "auditorium"}, {"label": "D", "text": "desk"}, {"label": "E", "text": "office"}], "stem": "Where do students sit in chairs for large meeetings?"}}
{"id": "e3f2d6d45f26aeeeb63defeb9ba18614", "question": {"question_concept": "cat", "choices": [{"label": "A", "text": "meat loaf"}, {"label": "B", "text": "back yard"}, {"label": "C", "text": "microwave"}, {"label": "D", "text": "floor"}, {"label": "E", "text": "bedroom"}], "stem": "A cat smelled something delicious and jumped into something dangerous. What might it have smelled?"}}
{"id": "7a96b8faba4541809bf8060492e6531b", "question": {"question_concept": "performing", "choices": [{"label": "A", "text": "happiness"}, {"label": "B", "text": "accolades"}, {"label": "C", "text": "tiredness"}, {"label": "D", "text": "do best"}, {"label": "E", "text": "sadness"}], "stem": "If you're successful while performing you should receive what from you critics?"}}
{"id": "23820bf771919047a8d0ab30b0a722b3", "question": {"question_concept": "monkey", "choices": [{"label": "A", "text": "banana tree"}, {"label": "B", "text": "sailor suit"}, {"label": "C", "text": "treetops"}, {"label": "D", "text": "gorilla suit"}, {"label": "E", "text": "mulberry bush"}], "stem": "A person in the Navy is wearing a monkey suit, what should they be wearing?"}}
{"id": "cdecadaf8838312c4b717f54ef0f3e83", "question": {"question_concept": "parking area", "choices": [{"label": "A", "text": "apartment complex"}, {"label": "B", "text": "people"}, {"label": "C", "text": "ugly"}, {"label": "D", "text": "city"}, {"label": "E", "text": "amusement park"}], "stem": "The parking area was full at every hour and every day with residents' cars, where was it located?"}}
{"id": "7137dd6f5b64eb313a9178f69e55b2c4", "question": {"question_concept": "talking", "choices": [{"label": "A", "text": "revelation"}, {"label": "B", "text": "sore throat"}, {"label": "C", "text": "conversation"}, {"label": "D", "text": "drink"}, {"label": "E", "text": "dry mouth"}], "stem": "What can talking for a long time cause that makes you think of waterfalls?"}}
{"id": "f62d475a3ae5922c9e93b3ddb2a243bb", "question": {"question_concept": "national highway", "choices": [{"label": "A", "text": "canada"}, {"label": "B", "text": "united states"}, {"label": "C", "text": "major cities"}, {"label": "D", "text": "the country"}, {"label": "E", "text": "atlas"}], "stem": "They began their family trip, they went from one national highway to another in their adventure across where?"}}
{"id": "d237ab9aad3cf167e09dea0507f35b02", "question": {"question_concept": "learn", "choices": [{"label": "A", "text": "remember information"}, {"label": "B", "text": "quit"}, {"label": "C", "text": "run"}, {"label": "D", "text": "teach"}, {"label": "E", "text": "forget"}], "stem": "Billy learned that he was a really bad runner, so he decided to do what?"}}
{"id": "bb0d05f15c214a2f958767e61deb3882", "question": {"question_concept": "scenery", "choices": [{"label": "A", "text": "park"}, {"label": "B", "text": "photograph"}, {"label": "C", "text": "cake"}, {"label": "D", "text": "picture"}, {"label": "E", "text": "painting"}], "stem": "What does an artist make when they see beautiful scenery?"}}
{"id": "18904c8a04b9dab4e9c5cd24e60afbba", "question": {"question_concept": "base", "choices": [{"label": "A", "text": "desirable"}, {"label": "B", "text": "likeable"}, {"label": "C", "text": "admirable"}, {"label": "D", "text": "home plate"}, {"label": "E", "text": "exhausting"}], "stem": "Defending one's base against enemy attacks at all cost is what?"}}
{"id": "5ea1c9129d3195720e3ad8ff334f6479", "question": {"question_concept": "playing guitar", "choices": [{"label": "A", "text": "listening to music"}, {"label": "B", "text": "making sound"}, {"label": "C", "text": "arthritis"}, {"label": "D", "text": "making music"}, {"label": "E", "text": "singing"}], "stem": "What happens to a persons hands after years of playing guitar?"}}
{"id": "d1f788f39ed4a98a5aa43ec44736a030", "question": {"question_concept": "solidity", "choices": [{"label": "A", "text": "space"}, {"label": "B", "text": "openness"}, {"label": "C", "text": "fluidity"}, {"label": "D", "text": "hollowness"}, {"label": "E", "text": "weakness"}], "stem": "If something is known for its solidity it is often the case that it has few what?"}}
{"id": "5d5fbce4682e27c308c492cb8a885e1c", "question": {"question_concept": "hold", "choices": [{"label": "A", "text": "let go"}, {"label": "B", "text": "lose"}, {"label": "C", "text": "give up"}, {"label": "D", "text": "hold on"}, {"label": "E", "text": "put down"}], "stem": "Sally couldn't hold on to Luke.  He kepy wiggling out of his grip.  With the match taking so long, she decided to do what?"}}
{"id": "7f1c37c50aa2826924554a969676ec63", "question": {"question_concept": "sharp", "choices": [{"label": "A", "text": "jagged"}, {"label": "B", "text": "dull"}, {"label": "C", "text": "unobservant"}, {"label": "D", "text": "inelegant"}, {"label": "E", "text": "inaccurate"}], "stem": "If your cutting tools aren't sharp they can lead to what kind of cut performance?"}}
{"id": "f0845c750514a31a063dc0c0dbf2466b", "question": {"question_concept": "examine thing", "choices": [{"label": "A", "text": "know what"}, {"label": "B", "text": "might want"}, {"label": "C", "text": "interested in"}, {"label": "D", "text": "learn more about"}, {"label": "E", "text": "use it"}], "stem": "What is the benefit of deciding to examine a thing?"}}
{"id": "bab752fb6b11993834a9a5248db3bfe7", "question": {"question_concept": "book", "choices": [{"label": "A", "text": "xbox"}, {"label": "B", "text": "bedside table"}, {"label": "C", "text": "coffee table"}, {"label": "D", "text": "backpack"}, {"label": "E", "text": "school room"}], "stem": "He had stayed up playing video games, and now his book made a good pillow where?"}}
{"id": "aae8e5595b8c9a2e68c901097802d40c", "question": {"question_concept": "getting in line", "choices": [{"label": "A", "text": "common sense"}, {"label": "B", "text": "longer lines"}, {"label": "C", "text": "fatigue"}, {"label": "D", "text": "get worried"}, {"label": "E", "text": "agitation"}], "stem": "I was getting in line and had a 5 hour wait, what did I feel over my time standing there?"}}
{"id": "ae112757c389ccab476ed55b65a73fe9", "question": {"question_concept": "route", "choices": [{"label": "A", "text": "the globe"}, {"label": "B", "text": "post office"}, {"label": "C", "text": "map"}, {"label": "D", "text": "get to specific place"}, {"label": "E", "text": "atlas"}], "stem": "Where might one leave from to pursue their route as part of their job?"}}
{"id": "cf1d1041b22f588ea187aee2e9b22bb8", "question": {"question_concept": "child", "choices": [{"label": "A", "text": "breathe"}, {"label": "B", "text": "fall down"}, {"label": "C", "text": "play tag"}, {"label": "D", "text": "ask questions"}, {"label": "E", "text": "eat"}], "stem": "A child wants to survive, what does he need to do?"}}
{"id": "bed9fb7dbd32f3228a1ae8dbf6aed2f6_1", "question": {"question_concept": "pencils", "choices": [{"label": "A", "text": "backpack"}, {"label": "B", "text": "classroom"}, {"label": "C", "text": "store"}, {"label": "D", "text": "pocket"}, {"label": "E", "text": "cabinet"}], "stem": "It was the first day and he was prepared, he had a whole pack of pencils in his what?"}}
{"id": "d77861add52c70d9c67b7f5766fcd241", "question": {"question_concept": "food", "choices": [{"label": "A", "text": "refrigerators"}, {"label": "B", "text": "kitchen"}, {"label": "C", "text": "disneyland"}, {"label": "D", "text": "record store"}, {"label": "E", "text": "shop"}], "stem": "Where do you buy food shaped like mickey mouse?"}}
{"id": "93a88ad4a7583040649e0cf3d0f3c211", "question": {"question_concept": "carpet", "choices": [{"label": "A", "text": "desk"}, {"label": "B", "text": "basement"}, {"label": "C", "text": "office"}, {"label": "D", "text": "bedroom"}, {"label": "E", "text": "building"}], "stem": "Where might carpet help your feet stay warm?"}}
{"id": "db5c06784bd7885138232850969653a7", "question": {"question_concept": "many people", "choices": [{"label": "A", "text": "listen to music"}, {"label": "B", "text": "watch t v"}, {"label": "C", "text": "talk"}, {"label": "D", "text": "play games"}, {"label": "E", "text": "eat ice cream"}], "stem": "Many people enjoy getting together with friends, they like to gather around the table to do what?"}}
{"id": "8f3e31e46c81511e39c4f4028bc0b232", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "acknowledgment"}, {"label": "B", "text": "congratulated"}, {"label": "C", "text": "critical thinking"}, {"label": "D", "text": "problem solving"}, {"label": "E", "text": "equal opportunity"}], "stem": "If a person really wants to focus on a topic and judge it honestly, what should they be using?"}}
{"id": "5dfee150d0435fefa4b3a5c2d292e378", "question": {"question_concept": "waiting for", "choices": [{"label": "A", "text": "impatience"}, {"label": "B", "text": "time consuming"}, {"label": "C", "text": "being late"}, {"label": "D", "text": "reflection"}, {"label": "E", "text": "have time to think"}], "stem": "What is the benefit to you of waiting for a bus?"}}
{"id": "209faba061890995e3b25e9f99a4b6ed", "question": {"question_concept": "lemur", "choices": [{"label": "A", "text": "zoo"}, {"label": "B", "text": "dictionary"}, {"label": "C", "text": "wilderness"}, {"label": "D", "text": "sunshine"}, {"label": "E", "text": "rain forest"}], "stem": "He had only seen a lemur on television, but during the trip he saw one out in the what?"}}
{"id": "69a596dc47a864be31b189e599de2fe1", "question": {"question_concept": "discovering truth", "choices": [{"label": "A", "text": "startled"}, {"label": "B", "text": "enlightenment"}, {"label": "C", "text": "murders"}, {"label": "D", "text": "wars"}, {"label": "E", "text": "denial"}], "stem": "The military tribunal was discovering the truth, what were they investigating?"}}
{"id": "20d97af0a60ceae1eec856f694a4dc9f", "question": {"question_concept": "exercising", "choices": [{"label": "A", "text": "sore muscles"}, {"label": "B", "text": "pain"}, {"label": "C", "text": "tiredness"}, {"label": "D", "text": "handsome face"}, {"label": "E", "text": "muscle growth"}], "stem": "The man wanted to impress women, so he began exercising, what was his expected result?"}}
{"id": "345f5fa9925de3a48035ff85bbeede35", "question": {"question_concept": "armoire", "choices": [{"label": "A", "text": "bedroom"}, {"label": "B", "text": "living room"}, {"label": "C", "text": "furniture store"}, {"label": "D", "text": "house"}, {"label": "E", "text": "closet"}], "stem": "When is an armoire found near the entrance to your dwelling?"}}
{"id": "f56f9a720f1140037d3f967b1a5ecd34", "question": {"question_concept": "written", "choices": [{"label": "A", "text": "unwritten"}, {"label": "B", "text": "oral"}, {"label": "C", "text": "aforewritten"}, {"label": "D", "text": "paper"}, {"label": "E", "text": "verbal"}], "stem": "The minimalist author looked at his written work, but what was he focused on?"}}
{"id": "79256fcf19c4ef22f187a1941152dcab", "question": {"question_concept": "examine thing", "choices": [{"label": "A", "text": "rest"}, {"label": "B", "text": "discovery"}, {"label": "C", "text": "interests"}, {"label": "D", "text": "learning"}, {"label": "E", "text": "observe"}], "stem": "The detective was on a stakeout to examine thing, he was going to sit there all day and what?"}}
{"id": "e30ae6ab5bce9793f0ed8c32c0627390", "question": {"question_concept": "printing on printer", "choices": [{"label": "A", "text": "lead"}, {"label": "B", "text": "using paper"}, {"label": "C", "text": "computer"}, {"label": "D", "text": "error"}, {"label": "E", "text": "drink coffee"}], "stem": "Printing on printer at home can get costly, between the ink and what?"}}
{"id": "850b587650fa7c11766dcb3df58d7348", "question": {"question_concept": "everyone", "choices": [{"label": "A", "text": "canidate to win election"}, {"label": "B", "text": "victory in war"}, {"label": "C", "text": "confused"}, {"label": "D", "text": "understood"}, {"label": "E", "text": "happiness"}], "stem": "In the gruesome aftermath everyone struggled, though it was over there was definitely no what?"}}
{"id": "f552db63a413af5eb7db1ee9b916afab", "question": {"question_concept": "friends", "choices": [{"label": "A", "text": "have dinner"}, {"label": "B", "text": "understand each other"}, {"label": "C", "text": "part company"}, {"label": "D", "text": "keep secrets"}, {"label": "E", "text": "group together"}], "stem": "What would friends do if they no longer like each other?"}}
{"id": "5e8d6d7b72205e07bc1112d053980355", "question": {"question_concept": "contemplating", "choices": [{"label": "A", "text": "completeness"}, {"label": "B", "text": "clear thought"}, {"label": "C", "text": "deep thoughts"}, {"label": "D", "text": "revelations"}, {"label": "E", "text": "sense of fulfillment"}], "stem": "What would a person feel contemplating their life accomplishments?"}}
{"id": "7e611b76883e645cc7db357b8ac7b3d3", "question": {"question_concept": "reproducing", "choices": [{"label": "A", "text": "fun"}, {"label": "B", "text": "birth"}, {"label": "C", "text": "propagation"}, {"label": "D", "text": "overpopulation"}, {"label": "E", "text": "plowing"}], "stem": "The farmer began reproducing the crop, he kept up this what until his fields were full?"}}
{"id": "205ab92415db0d60a257ee8fba2daacd", "question": {"question_concept": "candy", "choices": [{"label": "A", "text": "theatre"}, {"label": "B", "text": "supermarket"}, {"label": "C", "text": "jar"}, {"label": "D", "text": "amusement park"}, {"label": "E", "text": "movies"}], "stem": "Where can you buy candy and see ushers?"}}
{"id": "a11eb3798cda0c7cd471bc93c738e743", "question": {"question_concept": "cookie", "choices": [{"label": "A", "text": "jar"}, {"label": "B", "text": "store"}, {"label": "C", "text": "dessert"}, {"label": "D", "text": "evercookie"}, {"label": "E", "text": "home"}], "stem": "Sarah noticed that she was out of cookies. She was going to serve some with dinner, so she needed more.  Where might she go?"}}
{"id": "ac9437d351ecbbaee50499d4eba2df6d", "question": {"question_concept": "contraceptive", "choices": [{"label": "A", "text": "vagina"}, {"label": "B", "text": "medicine cabinet"}, {"label": "C", "text": "pharmacy"}, {"label": "D", "text": "kitchen"}, {"label": "E", "text": "drug store"}], "stem": "Where might someone store a contraceptive?"}}
{"id": "c2c0c889b9880ca21b507a2bef95ba53", "question": {"question_concept": "barbershop", "choices": [{"label": "A", "text": "shopping mall"}, {"label": "B", "text": "water"}, {"label": "C", "text": "commercial area"}, {"label": "D", "text": "dirt"}, {"label": "E", "text": "razor"}], "stem": "In a barbershop, what does the barber use to clean off someone's face after shaving them?"}}
{"id": "98e29a0593f509e4d7829fdb24119ecd", "question": {"question_concept": "playing sports", "choices": [{"label": "A", "text": "hurt"}, {"label": "B", "text": "sweating"}, {"label": "C", "text": "injuries"}, {"label": "D", "text": "pain"}, {"label": "E", "text": "rain"}], "stem": "Twisting an ankle while playing sports will cause what?"}}
{"id": "2256ca4fac74766f422f17dec031d161", "question": {"question_concept": "furniture", "choices": [{"label": "A", "text": "room"}, {"label": "B", "text": "store"}, {"label": "C", "text": "friend's house"}, {"label": "D", "text": "removal van"}, {"label": "E", "text": "building"}], "stem": "What do you usually fill with furniture?"}}
{"id": "414bd3088551ea2bb0a68eef6a9ad934", "question": {"question_concept": "eyeglasses", "choices": [{"label": "A", "text": "shirt pocket"}, {"label": "B", "text": "michigan"}, {"label": "C", "text": "on one's head"}, {"label": "D", "text": "breast pocket"}, {"label": "E", "text": "case"}], "stem": "Where might eyeglasses get fogged up in the winter?"}}
{"id": "572b7be6350be6f8361e0a4440637ce1", "question": {"question_concept": "car", "choices": [{"label": "A", "text": "driveway"}, {"label": "B", "text": "parking lot"}, {"label": "C", "text": "scrap heap"}, {"label": "D", "text": "parking garage"}, {"label": "E", "text": "garage"}], "stem": "The car was completely totaled in the accident, the only place it belonged now was atop a what?"}}
{"id": "9f2c79604bb86212104211adf85f5bfa", "question": {"question_concept": "being bored", "choices": [{"label": "A", "text": "entertain"}, {"label": "B", "text": "go somewhere"}, {"label": "C", "text": "knit"}, {"label": "D", "text": "play chess"}, {"label": "E", "text": "make patchwork quilt"}], "stem": "When you are being bored, strategy games and traveling are ways to do what?"}}
{"id": "81c3ccc031d3ebc1c9275ff65b6be807", "question": {"question_concept": "door", "choices": [{"label": "A", "text": "found on car"}, {"label": "B", "text": "closed"}, {"label": "C", "text": "apartment"}, {"label": "D", "text": "locked"}, {"label": "E", "text": "opened"}], "stem": "What would you do to a door that you want to get through?"}}
{"id": "6c6ea635a004b73a5847b6542684803b", "question": {"question_concept": "crab", "choices": [{"label": "A", "text": "tidepools"}, {"label": "B", "text": "fancy restaurant"}, {"label": "C", "text": "fish department"}, {"label": "D", "text": "most offices"}, {"label": "E", "text": "business"}], "stem": "What sort of place is likely to sell crab dishes?"}}
{"id": "3d6a28bbc1271d43b281e265630345d2", "question": {"question_concept": "stand in line", "choices": [{"label": "A", "text": "tickets"}, {"label": "B", "text": "polite"}, {"label": "C", "text": "money from bank"}, {"label": "D", "text": "wait turn"}, {"label": "E", "text": "killing time"}], "stem": "Why was the couple standing in line?"}}
{"id": "a5ba48993e46e56480d9dc7196d58542", "question": {"question_concept": "condominium", "choices": [{"label": "A", "text": "michigan"}, {"label": "B", "text": "complex"}, {"label": "C", "text": "large city building"}, {"label": "D", "text": "towels"}, {"label": "E", "text": "florida"}], "stem": "What can fit in a condominium?"}}
{"id": "770eebe6eba4dec72190965ad7884d22", "question": {"question_concept": "bag", "choices": [{"label": "A", "text": "school"}, {"label": "B", "text": "shopping mall"}, {"label": "C", "text": "grocery store"}, {"label": "D", "text": "shopping cart"}, {"label": "E", "text": "bookstore"}], "stem": "Where do you get books in a bag?"}}
{"id": "67a23a2fd1f445db4b7dc342a887d159", "question": {"question_concept": "hair salon", "choices": [{"label": "A", "text": "mail"}, {"label": "B", "text": "shopping center"}, {"label": "C", "text": "small town"}, {"label": "D", "text": "hotel"}, {"label": "E", "text": "metropolitan city"}], "stem": "Where are you likely to find a variety of hair salons?"}}
{"id": "3f9317664dc7484437a8ee518a8ee840", "question": {"question_concept": "insanity", "choices": [{"label": "A", "text": "kill people"}, {"label": "B", "text": "run in marathon"}, {"label": "C", "text": "advance into battle"}, {"label": "D", "text": "wage war"}, {"label": "E", "text": "seek help"}], "stem": "What risk of people with severe insanity is that they might do what?"}}
{"id": "f2211ec560b9ffb60dd728438e969dbb", "question": {"question_concept": "information", "choices": [{"label": "A", "text": "book"}, {"label": "B", "text": "magazine"}, {"label": "C", "text": "internet"}, {"label": "D", "text": "television"}, {"label": "E", "text": "newspaper"}], "stem": "Where might I get written information on a past event?"}}
{"id": "670aa5d324cc3f39c034dc0fc68d5861", "question": {"question_concept": "stay in bed", "choices": [{"label": "A", "text": "play dead"}, {"label": "B", "text": "you're sick"}, {"label": "C", "text": "rest more"}, {"label": "D", "text": "were sick"}, {"label": "E", "text": "left alone"}], "stem": "Sarah wanted to stay in bed all day.  She was up late with a cough last night and wanted to do what?"}}
{"id": "511666b1003b340a0a8213ec5fe38b81", "question": {"question_concept": "eating hamburger", "choices": [{"label": "A", "text": "rocket launch"}, {"label": "B", "text": "tasty"}, {"label": "C", "text": "gas"}, {"label": "D", "text": "indigestion"}, {"label": "E", "text": "health problems"}], "stem": "What will happen after eating hamburger every day for too long?"}}
{"id": "1ab67e7593bf8858bd45ed606486327c", "question": {"question_concept": "gamblers", "choices": [{"label": "A", "text": "building"}, {"label": "B", "text": "book bets"}, {"label": "C", "text": "race track"}, {"label": "D", "text": "deal seconds"}, {"label": "E", "text": "casino"}], "stem": "What type of structure would hold any type of person including gamblers?"}}
{"id": "bfd67879d0b3550a94d99210387025ed", "question": {"question_concept": "subway station", "choices": [{"label": "A", "text": "city underground"}, {"label": "B", "text": "london"}, {"label": "C", "text": "big city"}, {"label": "D", "text": "new york"}, {"label": "E", "text": "a sandwich"}], "stem": "Who has a need for a subway station?"}}
{"id": "b573fb3b40fd85a9171c1e0b459b71cb", "question": {"question_concept": "world", "choices": [{"label": "A", "text": "chaotic"}, {"label": "B", "text": "round"}, {"label": "C", "text": "complicated"}, {"label": "D", "text": "diverse"}, {"label": "E", "text": "small"}], "stem": "John had good reason for his actions, but he couldn't explain himself to his daughter.  She say things in black and white, but really, that wasn't how the world was.  What was the world?"}}
{"id": "cacdd89beb62e8c0d20d8c26f06d2e3d", "question": {"question_concept": "wing", "choices": [{"label": "A", "text": "hospital"}, {"label": "B", "text": "buffalo"}, {"label": "C", "text": "airplane"}, {"label": "D", "text": "mansion"}, {"label": "E", "text": "large building"}], "stem": "Billy lived in the east wing of his home.  The west wing was where he kept boxes full of junk.  Sometimes he wished he hadn't decided to live in what?"}}
{"id": "78cd945a7f08bb254115d70659458120", "question": {"question_concept": "ficus", "choices": [{"label": "A", "text": "green house"}, {"label": "B", "text": "large pot"}, {"label": "C", "text": "park"}, {"label": "D", "text": "nursery"}, {"label": "E", "text": "arboretum"}], "stem": "Jenny took her ficus for walks.  Everyone thought that this was silly.  You don't walk a plant, they said. It was big and heavy, too.  Still, every day she took it for a walk.  Where might she have taken it?"}}
{"id": "3047e6b519fc811a70e52cf79fe97e77", "question": {"question_concept": "learning", "choices": [{"label": "A", "text": "correct answers"}, {"label": "B", "text": "intelligence"}, {"label": "C", "text": "effectiveness"}, {"label": "D", "text": "education"}, {"label": "E", "text": "distress"}], "stem": "What is the hoped result of learning?"}}
{"id": "8d75b7124c922af798e2c82f4c20b5bb", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "incentive"}, {"label": "B", "text": "attempt suicide"}, {"label": "C", "text": "wave goodbye"}, {"label": "D", "text": "further cause"}, {"label": "E", "text": "thank god"}], "stem": "How might a person be prompted to take action?"}}
{"id": "2983b16eacbaa0583b7c5c98d56e97c1", "question": {"question_concept": "listening to music", "choices": [{"label": "A", "text": "smile"}, {"label": "B", "text": "entertainment"}, {"label": "C", "text": "deafness"}, {"label": "D", "text": "home run"}, {"label": "E", "text": "calm"}], "stem": "What could listening to music cause you to do?"}}
{"id": "dbccba5e0c7c0545f2b4d45f63db61d2", "question": {"question_concept": "shopping complex", "choices": [{"label": "A", "text": "subarbs"}, {"label": "B", "text": "michigan"}, {"label": "C", "text": "big city"}, {"label": "D", "text": "suburbia"}, {"label": "E", "text": "overseas"}], "stem": "A person is driving by an upscale shopping complex, where is it located?"}}
{"id": "219cbfc92cb8624da9f649edeabb9e5f", "question": {"question_concept": "understanding better", "choices": [{"label": "A", "text": "peace"}, {"label": "B", "text": "decreased knowledge"}, {"label": "C", "text": "ideas"}, {"label": "D", "text": "interest in"}, {"label": "E", "text": "wisdom"}], "stem": "Zoss was a philosopher.  He choose philosophy because thought that understanding things better would lead to what?"}}
{"id": "a63d96a1a9387973976dff68c4039981", "question": {"question_concept": "tower", "choices": [{"label": "A", "text": "airport"}, {"label": "B", "text": "radio station"}, {"label": "C", "text": "city"}, {"label": "D", "text": "medieval castle"}, {"label": "E", "text": "london"}], "stem": "As the earl approached on horse back and saw the tower, what did he know he was close to?"}}
{"id": "421fcd84441db9de906319d7f0c61432", "question": {"question_concept": "television", "choices": [{"label": "A", "text": "cabinet"}, {"label": "B", "text": "house"}, {"label": "C", "text": "garage"}, {"label": "D", "text": "apartment"}, {"label": "E", "text": "bedroom"}], "stem": "Where must a television be kept quiet?"}}
{"id": "4cdf83fe252f0883a93af57a421fd9c8", "question": {"question_concept": "dust", "choices": [{"label": "A", "text": "television"}, {"label": "B", "text": "everywhere"}, {"label": "C", "text": "corner"}, {"label": "D", "text": "attic"}, {"label": "E", "text": "most buildings"}], "stem": "Billy spends his time cleaning dust. Where can dust be found?"}}
{"id": "298089ddab6eddec5e2c629abf1a51c8", "question": {"question_concept": "run after ball", "choices": [{"label": "A", "text": "get tired"}, {"label": "B", "text": "have legs"}, {"label": "C", "text": "look first"}, {"label": "D", "text": "not fat"}, {"label": "E", "text": "fast"}], "stem": "Jay would run after the ball into the street, and always failed to so what?"}}
{"id": "6ead94dce0ff31bc39c62bbb42f133b5", "question": {"question_concept": "apples", "choices": [{"label": "A", "text": "knife"}, {"label": "B", "text": "stems"}, {"label": "C", "text": "seeds inside"}, {"label": "D", "text": "peel"}, {"label": "E", "text": "grocery store"}], "stem": "When you cut into an apple what is something that you can find inside of it?"}}
{"id": "fdf107a872ff151241266c2fc5de4535", "question": {"question_concept": "car", "choices": [{"label": "A", "text": "cost money"}, {"label": "B", "text": "crash"}, {"label": "C", "text": "pass"}, {"label": "D", "text": "go fast"}, {"label": "E", "text": "move quickly"}], "stem": "What does a car usually do on a freeway?"}}
{"id": "9a0f29ed1eeee98931f0d1d670b41b7a", "question": {"question_concept": "microphone", "choices": [{"label": "A", "text": "submarine"}, {"label": "B", "text": "demonstration"}, {"label": "C", "text": "stage"}, {"label": "D", "text": "conference"}, {"label": "E", "text": "record sound"}], "stem": "The person was scared to face the crowd, but they knew they had to walk out to the microphone and speak at center what?"}}
{"id": "ee506d6aa3ccc859f6d5b4054c047fc3", "question": {"question_concept": "entertaining", "choices": [{"label": "A", "text": "favors"}, {"label": "B", "text": "boredom"}, {"label": "C", "text": "gratification"}, {"label": "D", "text": "validation"}, {"label": "E", "text": "happiness"}], "stem": "What did the businessmen want when they sought entertaining experiences?"}}
{"id": "1898388f414bfb89016ed5a3ed0752d0", "question": {"question_concept": "apple tree", "choices": [{"label": "A", "text": "nevada"}, {"label": "B", "text": "washington state"}, {"label": "C", "text": "pennsylvania"}, {"label": "D", "text": "front yard"}, {"label": "E", "text": "countryside"}], "stem": "What type of local would you expect to see one apple tree after another?"}}
{"id": "3141857e63c3cce542a7a210860e234f", "question": {"question_concept": "space shuttle", "choices": [{"label": "A", "text": "orbit"}, {"label": "B", "text": "solar system"}, {"label": "C", "text": "universe"}, {"label": "D", "text": "outerspace"}, {"label": "E", "text": "there are four choices for each sentence"}], "stem": "The space shuttle flew past Neptune, where was it looking to leave?"}}
{"id": "f26e52a3b9c40e78bb485e70f7368a82", "question": {"question_concept": "shopping bag", "choices": [{"label": "A", "text": "restaurant"}, {"label": "B", "text": "good will"}, {"label": "C", "text": "supermarket"}, {"label": "D", "text": "grocery store"}, {"label": "E", "text": "mart"}], "stem": "Where would you find a shopping bag filled with clothes?"}}
{"id": "68b3569431a6a9ba85bf0ffa64a5d4a1", "question": {"question_concept": "bad", "choices": [{"label": "A", "text": "excellent"}, {"label": "B", "text": "sufficient"}, {"label": "C", "text": "happy"}, {"label": "D", "text": "competent"}, {"label": "E", "text": "beneficial"}], "stem": "She thought she had done bad on the test, but the A+ showed she had done what?"}}
{"id": "d3861b05b57e4be80eb7171842a92eb3", "question": {"question_concept": "serve customers", "choices": [{"label": "A", "text": "help"}, {"label": "B", "text": "come back"}, {"label": "C", "text": "food"}, {"label": "D", "text": "money"}, {"label": "E", "text": "pay to"}], "stem": "What would you want to get after you serve customers?"}}
{"id": "b611cac8d175cf35e44e1d21e824aba5", "question": {"question_concept": "friends", "choices": [{"label": "A", "text": "borrow money"}, {"label": "B", "text": "meet for lunch"}, {"label": "C", "text": "talking about"}, {"label": "D", "text": "smile"}, {"label": "E", "text": "keep secrets"}], "stem": "A good thing for friends to do when they want to discuss something important?"}}
{"id": "5abbb0e366a0327dcca1b237457c098f", "question": {"question_concept": "having food", "choices": [{"label": "A", "text": "weight gain"}, {"label": "B", "text": "gas"}, {"label": "C", "text": "getting fat"}, {"label": "D", "text": "digesting"}, {"label": "E", "text": "not hungry"}], "stem": "Janes loved having food, but he couldn't eat as much as he would like because he has trouble doing what?"}}
{"id": "********************************", "question": {"question_concept": "beer", "choices": [{"label": "A", "text": "bar"}, {"label": "B", "text": "keg"}, {"label": "C", "text": "neighbor's house"}, {"label": "D", "text": "casino"}, {"label": "E", "text": "friend's house"}], "stem": "Where can I get a beer from someone I don't know well?"}}
{"id": "f6d9271cabb87afb7c6b4187b195a199", "question": {"question_concept": "piano", "choices": [{"label": "A", "text": "performance"}, {"label": "B", "text": "restaurant"}, {"label": "C", "text": "symphony orchestra"}, {"label": "D", "text": "church"}, {"label": "E", "text": "concert hall"}], "stem": "The man sat down at his piano and looked into the darkness and heard coughs and whispers, where was he?"}}
{"id": "48bee74b35fd02b60303ec156a54294c", "question": {"question_concept": "bird", "choices": [{"label": "A", "text": "experience flight"}, {"label": "B", "text": "eat a worm"}, {"label": "C", "text": "learn to fly"}, {"label": "D", "text": "attempt to fly"}, {"label": "E", "text": "squawk"}], "stem": "The bird was alarmed when a raccoon walked by her next, what did she do to scare it away?"}}
{"id": "e4b103a4f1373b6a92c460437a562bd4", "question": {"question_concept": "cardboard", "choices": [{"label": "A", "text": "warehouse"}, {"label": "B", "text": "recycling bin"}, {"label": "C", "text": "packaging materials"}, {"label": "D", "text": "recycle bin"}, {"label": "E", "text": "trash"}], "stem": "Some people are environmentalists, when they see cardboard in the rubbish they move it to the what?"}}
{"id": "0dc56e6fa6050863ebdee9d225b8c208", "question": {"question_concept": "printing on printer", "choices": [{"label": "A", "text": "toner"}, {"label": "B", "text": "error"}, {"label": "C", "text": "using paper"}, {"label": "D", "text": "paper and ink"}, {"label": "E", "text": "drink coffee"}], "stem": "He was trying to do printing on printer, but there was some what popping up?"}}
{"id": "7da9d09579298472619e012c2e395bb5", "question": {"question_concept": "committee", "choices": [{"label": "A", "text": "church"}, {"label": "B", "text": "business"}, {"label": "C", "text": "government"}, {"label": "D", "text": "school"}, {"label": "E", "text": "city hall"}], "stem": "A committee is just a way to get overtime and special pay, that's why people who work in where always make them?"}}
{"id": "4f8eae6183aaad0c9abea351287c7b03", "question": {"question_concept": "children", "choices": [{"label": "A", "text": "listen to music"}, {"label": "B", "text": "reach over"}, {"label": "C", "text": "need care"}, {"label": "D", "text": "cut and paste"}, {"label": "E", "text": "play basketball"}], "stem": "What do children love to do with scissors and paper?"}}
{"id": "fae79a6551a98db1217fa61a7a243587", "question": {"question_concept": "platform", "choices": [{"label": "A", "text": "building"}, {"label": "B", "text": "museum"}, {"label": "C", "text": "park"}, {"label": "D", "text": "arena"}, {"label": "E", "text": "concert hall"}], "stem": "The main attraction was a reconstructed dinosaur skeleton, it stood tall on a platform in the center of the what?"}}
{"id": "144fe132d8a7c178a6acedddc3aff873", "question": {"question_concept": "stop", "choices": [{"label": "A", "text": "motor"}, {"label": "B", "text": "telegraph"}, {"label": "C", "text": "organ"}, {"label": "D", "text": "telegram"}, {"label": "E", "text": "email"}], "stem": "James wanted to stop the message, but it was already out for delivery.  This type of message could he very fast. What is this?"}}
{"id": "5bdd8b4357171af3732b505f6093c49b", "question": {"question_concept": "nightclub", "choices": [{"label": "A", "text": "major city"}, {"label": "B", "text": "building"}, {"label": "C", "text": "manhattan"}, {"label": "D", "text": "city"}, {"label": "E", "text": "downtown area"}], "stem": "What structure is a nightclub in?"}}
{"id": "9e8cf3376a136e9351671606bac8f1f2", "question": {"question_concept": "apple tree", "choices": [{"label": "A", "text": "washington state"}, {"label": "B", "text": "flowers"}, {"label": "C", "text": "farm yard"}, {"label": "D", "text": "new jersey"}, {"label": "E", "text": "old orchard beach"}], "stem": "Where would you find an apple tree, a tractor, or plow?"}}
{"id": "71d66349ea1c21045973528be3874301", "question": {"question_concept": "running after ball", "choices": [{"label": "A", "text": "catching up with"}, {"label": "B", "text": "physical exertion"}, {"label": "C", "text": "sprinting"}, {"label": "D", "text": "squinting"}, {"label": "E", "text": "tiredness"}], "stem": "How  will running after a ball need to be done?"}}
{"id": "7fe3d905cb5647ef31ba49e4a5477cd4", "question": {"question_concept": "value", "choices": [{"label": "A", "text": "expensive"}, {"label": "B", "text": "worthlessness"}, {"label": "C", "text": "disesteem"}, {"label": "D", "text": "invaluable"}, {"label": "E", "text": "worthless"}], "stem": "What is the opposite of something that is valuable?"}}
{"id": "1d9519689d508ced6edfc9a04bb1ada2", "question": {"question_concept": "animals", "choices": [{"label": "A", "text": "less fur"}, {"label": "B", "text": "gay"}, {"label": "C", "text": "fly"}, {"label": "D", "text": "drink"}, {"label": "E", "text": "feel pain"}], "stem": "Animals needed to travel large distances, so what traits evolved from this need?"}}
{"id": "eaae9b3976dc98dd116ea08c34d58145", "question": {"question_concept": "rosebush", "choices": [{"label": "A", "text": "outdoors"}, {"label": "B", "text": "flower bed"}, {"label": "C", "text": "fountain"}, {"label": "D", "text": "flower garden"}, {"label": "E", "text": "park"}], "stem": "Where are almost all rosebushes found?"}}
{"id": "4fd9bb4a67bb054432495fc62d41ccef", "question": {"question_concept": "sound", "choices": [{"label": "A", "text": "quiet"}, {"label": "B", "text": "music"}, {"label": "C", "text": "silence"}, {"label": "D", "text": "vision"}, {"label": "E", "text": "movie"}], "stem": "They were trying the impossible, to capture the sound of what?"}}
{"id": "bc24fc52b5019544aa9ad270a93e64c2", "question": {"question_concept": "communicate", "choices": [{"label": "A", "text": "send email"}, {"label": "B", "text": "listen"}, {"label": "C", "text": "talk with people"}, {"label": "D", "text": "think"}, {"label": "E", "text": "speak out"}], "stem": "When you communicate among a group in person what are you doing?"}}
{"id": "9c816bd4f0517ea0f16dffe7d86123ac_1", "question": {"question_concept": "projectile ball", "choices": [{"label": "A", "text": "flintlock"}, {"label": "B", "text": "motion"}, {"label": "C", "text": "arcade"}, {"label": "D", "text": "tennis court"}, {"label": "E", "text": "mzzleloader"}], "stem": "What fires a projectile ball and holds a burning rope?"}}
{"id": "a5d1dea3eaf8c90ff423bb37a6a3a62b", "question": {"question_concept": "lizard", "choices": [{"label": "A", "text": "iraq"}, {"label": "B", "text": "tropical areas"}, {"label": "C", "text": "garden"}, {"label": "D", "text": "pet shop"}, {"label": "E", "text": "desert country"}], "stem": "Where would you find a lizard not native to an area with a lot of rain?"}}
{"id": "857c967b7c5de845597620e7a158b3db", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "headache"}, {"label": "B", "text": "not feel pain"}, {"label": "C", "text": "more leisure time"}, {"label": "D", "text": "acquire wealth"}, {"label": "E", "text": "injury"}], "stem": "What does a person want when they get hurt?"}}
{"id": "2aaf458e8f6a44aa1fcfb81ff814c720", "question": {"question_concept": "salesman", "choices": [{"label": "A", "text": "toy store"}, {"label": "B", "text": "front door"}, {"label": "C", "text": "counter"}, {"label": "D", "text": "shop"}, {"label": "E", "text": "car show"}], "stem": "Of anybody you want to find where, a salesman is probably the worst surprise behind it?"}}
{"id": "7109a210dbec4d6dbd8c276b8ae644fa", "question": {"question_concept": "red", "choices": [{"label": "A", "text": "utah"}, {"label": "B", "text": "louisiana"}, {"label": "C", "text": "texas"}, {"label": "D", "text": "oklahoma"}, {"label": "E", "text": "redtwig"}], "stem": "What state is usually always red in the national elections?"}}
{"id": "87ddb84c02d2c380159338aded52f268", "question": {"question_concept": "have fun", "choices": [{"label": "A", "text": "enjoy yourself"}, {"label": "B", "text": "smile"}, {"label": "C", "text": "laughter"}, {"label": "D", "text": "have sex"}, {"label": "E", "text": "stop working"}], "stem": "What do people do to express when they have fun?"}}
{"id": "d32be3cf3fe1b701118331921d31b0f4", "question": {"question_concept": "watching television", "choices": [{"label": "A", "text": "entertainment"}, {"label": "B", "text": "falling asleep"}, {"label": "C", "text": "relaxation"}, {"label": "D", "text": "sleep"}, {"label": "E", "text": "wasted time"}], "stem": "The man was watching television in hopes of catching his favorite show, what was he seeking?"}}
{"id": "4a6f559d71502a656787a180404e041b", "question": {"question_concept": "lint", "choices": [{"label": "A", "text": "rug"}, {"label": "B", "text": "pouch"}, {"label": "C", "text": "laundromat"}, {"label": "D", "text": "pocket"}, {"label": "E", "text": "purse"}], "stem": "Where might there be a small amount of lint?"}}
{"id": "d9dd74ee33260bba54f8708e5385b96a", "question": {"question_concept": "buying products", "choices": [{"label": "A", "text": "bankruptcy"}, {"label": "B", "text": "shopping"}, {"label": "C", "text": "debt"}, {"label": "D", "text": "spending money"}, {"label": "E", "text": "agony"}], "stem": "I designed some new buying products to help you through financial difficultises. If you owe a lot of money what can this help you get out of?"}}
{"id": "0a22e846d650cfab0b6a157fd670639b", "question": {"question_concept": "christian", "choices": [{"label": "A", "text": "commit sin"}, {"label": "B", "text": "fail"}, {"label": "C", "text": "rebel"}, {"label": "D", "text": "attend church"}, {"label": "E", "text": "believe in jesus christ"}], "stem": "How would you know if someone who does not succeed often is a christian?"}}
{"id": "202ca80830063bde5e0e003a9fc8685d", "question": {"question_concept": "creating art", "choices": [{"label": "A", "text": "controversy"}, {"label": "B", "text": "communication"}, {"label": "C", "text": "pleasure"}, {"label": "D", "text": "pride"}, {"label": "E", "text": "sadness"}], "stem": "Solo feels good when he's creating art.  Seeing his work take form makes him feel immense satisfaction in his skills.  Showing it to others makes him feel what emotion?"}}
{"id": "0ecca9e9b71eeb9af17abc512a37432c", "question": {"question_concept": "sand", "choices": [{"label": "A", "text": "beach"}, {"label": "B", "text": "desert"}, {"label": "C", "text": "surface of earth"}, {"label": "D", "text": "concrete"}, {"label": "E", "text": "ground"}], "stem": "Gary was walking for a long time, but all he could see was sand.  He didn't know how far he went, or how much farther he had to go.  Where might Gary be?"}}
{"id": "4f997524423ce868d0b1951788405bcb", "question": {"question_concept": "helping", "choices": [{"label": "A", "text": "better world"}, {"label": "B", "text": "obligation"}, {"label": "C", "text": "enjoyment"}, {"label": "D", "text": "will thank"}, {"label": "E", "text": "good feelings"}], "stem": "What is the overall goal likely to be of someone who is helping others?"}}
{"id": "7cca88e55ddd03e225756491eca7140e", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "caregiver"}, {"label": "B", "text": "acknowledgment"}, {"label": "C", "text": "art"}, {"label": "D", "text": "creativity"}, {"label": "E", "text": "much money"}], "stem": "If a young person wants to express themselves, what outlets do they try to find?"}}
{"id": "af846676633ceeebf5e9f87b5098f62b", "question": {"question_concept": "ceiling", "choices": [{"label": "A", "text": "building"}, {"label": "B", "text": "bedroom"}, {"label": "C", "text": "loft"}, {"label": "D", "text": "house"}, {"label": "E", "text": "classroom"}], "stem": "If someone is looking at a ceiling in their bedroom, where are they likely located?"}}
{"id": "544d1957d61d1f97e52d77e5b28673ac", "question": {"question_concept": "child", "choices": [{"label": "A", "text": "count to ten"}, {"label": "B", "text": "state name"}, {"label": "C", "text": "write"}, {"label": "D", "text": "read book"}, {"label": "E", "text": "dress herself"}], "stem": "What does a child learn in school to do?"}}
{"id": "33573f60dac3ea26ebe0e182a9a29fb7", "question": {"question_concept": "praying", "choices": [{"label": "A", "text": "sense of peace"}, {"label": "B", "text": "wasted time"}, {"label": "C", "text": "relief"}, {"label": "D", "text": "talk to god"}, {"label": "E", "text": "feel safer"}], "stem": "They were lost in the woods and scared, but praying made them what?"}}
{"id": "ee59810181e1ef04a60b46ed28abb3e9", "question": {"question_concept": "point", "choices": [{"label": "A", "text": "needle"}, {"label": "B", "text": "middle"}, {"label": "C", "text": "alpenstock"}, {"label": "D", "text": "arrowhead"}, {"label": "E", "text": "arrow"}], "stem": "He needed stitches, the doctor aimed the point of the what and began?"}}
{"id": "3d06dfb3b44ba5606f3ef49ebe7b0b4a", "question": {"question_concept": "row", "choices": [{"label": "A", "text": "arena"}, {"label": "B", "text": "auditorium"}, {"label": "C", "text": "theatre"}, {"label": "D", "text": "farmer's field"}, {"label": "E", "text": "vegetable garden"}], "stem": "People were sitting in rows waiting for the performance, where were they?"}}
{"id": "42ab078f45b2cfa612bffbecaaf01572", "question": {"question_concept": "creating art", "choices": [{"label": "A", "text": "enlightenment"}, {"label": "B", "text": "pleasure"}, {"label": "C", "text": "love"}, {"label": "D", "text": "relax"}, {"label": "E", "text": "frustration"}], "stem": "If I am creating art and drop my paintbrush, what is my feeling?"}}
{"id": "2a1f501e267e4ad1a50f82c33c112bf3", "question": {"question_concept": "bad", "choices": [{"label": "A", "text": "paper"}, {"label": "B", "text": "competent"}, {"label": "C", "text": "sincere"}, {"label": "D", "text": "premium"}, {"label": "E", "text": "upright"}], "stem": "The person decided they wanted to return the overpriced product, it was really bad quality for the what kind of price tag?"}}
{"id": "d33cd51f8cbdf3c85510174bff994df8", "question": {"question_concept": "ship", "choices": [{"label": "A", "text": "list to port"}, {"label": "B", "text": "run aground"}, {"label": "C", "text": "heading east"}, {"label": "D", "text": "near dock"}, {"label": "E", "text": "near shore"}], "stem": "Why would a ship slow down to avoid a collision?"}}
{"id": "ca585de81536853a3720b501acba7bab", "question": {"question_concept": "run", "choices": [{"label": "A", "text": "go quickly"}, {"label": "B", "text": "learn to walk"}, {"label": "C", "text": "get out of bed"}, {"label": "D", "text": "eat a salad"}, {"label": "E", "text": "stretches"}], "stem": "What should you do to prepare for a run?"}}
{"id": "43a9cb8ede82f7010422ba3588f7ece2", "question": {"question_concept": "neck", "choices": [{"label": "A", "text": "chest"}, {"label": "B", "text": "body"}, {"label": "C", "text": "shoulder"}, {"label": "D", "text": "bottle"}, {"label": "E", "text": "guillotine"}], "stem": "You neck comes out of where?"}}
{"id": "cecb27a6186f77fed1a61949f7d9d6bd", "question": {"question_concept": "chatting with friends", "choices": [{"label": "A", "text": "agreement"}, {"label": "B", "text": "crying"}, {"label": "C", "text": "communication"}, {"label": "D", "text": "love"}, {"label": "E", "text": "laughter"}], "stem": "What happens many times while chatting with friends?"}}
{"id": "99cf0fa9b30ac255fc3385cb497e98cb", "question": {"question_concept": "coil", "choices": [{"label": "A", "text": "television"}, {"label": "B", "text": "electric motor"}, {"label": "C", "text": "car"}, {"label": "D", "text": "refrigerator"}, {"label": "E", "text": "circuit"}], "stem": "The compressor coil was dirty so what wouldn't work?"}}
{"id": "1f9b49c181f14cbcfd043e624011f5fd", "question": {"question_concept": "locker room", "choices": [{"label": "A", "text": "health club"}, {"label": "B", "text": "stadium"}, {"label": "C", "text": "gymnasium"}, {"label": "D", "text": "locker room"}, {"label": "E", "text": "gim"}], "stem": "You might store your leotard in the locker room here."}}
{"id": "e71d8231251e1ee2916c1fc3d5feaf6e", "question": {"question_concept": "air conditioning", "choices": [{"label": "A", "text": "car"}, {"label": "B", "text": "offices"}, {"label": "C", "text": "popsicle"}, {"label": "D", "text": "movie theatre"}, {"label": "E", "text": "house"}], "stem": "The service technician grossly overcharged, but the customer didn't know how to fix the air conditioning of his what?"}}
{"id": "110ad2fa55cdd30eb08e0ffa0200b61d", "question": {"question_concept": "yard", "choices": [{"label": "A", "text": "gas"}, {"label": "B", "text": "city"}, {"label": "C", "text": "michigan"}, {"label": "D", "text": "three feet"}, {"label": "E", "text": "subdivision"}], "stem": "Most of the yards are filled with grass in what glove-shaped state?"}}
{"id": "127549ae53cee4bec9469ec28a091d93", "question": {"question_concept": "cost", "choices": [{"label": "A", "text": "income"}, {"label": "B", "text": "benefit"}, {"label": "C", "text": "tax benefit"}, {"label": "D", "text": "revenue"}, {"label": "E", "text": "for free"}], "stem": "The company refused to do repairs.  They felt that the cost of fixing the bridge would exceed what."}}
{"id": "4e55946454064f1a02f1c7015a28cd2a", "question": {"question_concept": "death", "choices": [{"label": "A", "text": "surprise everyone"}, {"label": "B", "text": "last forever"}, {"label": "C", "text": "happen to"}, {"label": "D", "text": "happen quickly"}, {"label": "E", "text": "it is nature's way"}], "stem": "Death was permanent for me, so how long did it occur?"}}
{"id": "e6774b0c2e0ab44180051f4d52ccc1a0", "question": {"question_concept": "giving assistance", "choices": [{"label": "A", "text": "helpfulness"}, {"label": "B", "text": "good feeling"}, {"label": "C", "text": "patience"}, {"label": "D", "text": "happiness"}, {"label": "E", "text": "feeling good"}], "stem": "Someone who enjoys giving assistance is likely to be known for what?"}}
{"id": "c736aace51654949102677a7da7b9022", "question": {"question_concept": "drinking alcohol", "choices": [{"label": "A", "text": "wicked"}, {"label": "B", "text": "disturbing"}, {"label": "C", "text": "frequent urination"}, {"label": "D", "text": "vomiting"}, {"label": "E", "text": "accidents"}], "stem": "If someone spent the night drinking alcohol in Boston, they might wake up with what kind of headache?"}}
{"id": "16905db72520432d0fa38d8eb304e826", "question": {"question_concept": "fox", "choices": [{"label": "A", "text": "northern hemisphere"}, {"label": "B", "text": "undergrowth"}, {"label": "C", "text": "countryside"}, {"label": "D", "text": "mountains"}, {"label": "E", "text": "nantucket"}], "stem": "A fox is walking around New England, where is a likely place it could be found?"}}
{"id": "702a2d72ea4563c9c45c6e445441f4e8", "question": {"question_concept": "smoke", "choices": [{"label": "A", "text": "kill yourself"}, {"label": "B", "text": "think again"}, {"label": "C", "text": "you're stupid"}, {"label": "D", "text": "cigarette"}, {"label": "E", "text": "eat dinner"}], "stem": "Smoke isn't good for the body, it's basically a slow way to do what?"}}
{"id": "bbbb2745f89f5d6554c7a317dbbd4ee0", "question": {"question_concept": "all people", "choices": [{"label": "A", "text": "due process"}, {"label": "B", "text": "hurt"}, {"label": "C", "text": "different"}, {"label": "D", "text": "innocent until proven guilty"}, {"label": "E", "text": "human"}], "stem": "It seems everybody crucifying the man have forgotten the basics of the system, all people are supposed to be assumed what?"}}
{"id": "d32234db233c80777ed28873ada7a2b9", "question": {"question_concept": "sharp", "choices": [{"label": "A", "text": "unobservant"}, {"label": "B", "text": "inaccurate"}, {"label": "C", "text": "dull"}, {"label": "D", "text": "rough"}, {"label": "E", "text": "above board"}], "stem": "The knife was advertised as being always sharp, but obviously regular use will make anything what?"}}
{"id": "3037ec4c19bb3286d9ebd9416bb64f66", "question": {"question_concept": "wound", "choices": [{"label": "A", "text": "fight"}, {"label": "B", "text": "patient"}, {"label": "C", "text": "injured person"}, {"label": "D", "text": "body"}, {"label": "E", "text": "battle"}], "stem": "Unfortunately he suffered a mortal wound, he was one of many lost in the what?"}}
{"id": "c1f14c31f4d98445652e806cea770c99", "question": {"question_concept": "horse", "choices": [{"label": "A", "text": "pet"}, {"label": "B", "text": "go away"}, {"label": "C", "text": "run quickly"}, {"label": "D", "text": "drink water"}, {"label": "E", "text": "sleep"}], "stem": "Jamie is a cowboy.  He owns a horse.  What might the horse do when he digs his boots into the horse's side."}}
{"id": "8a1c1a76553e4e8d04b2c9578c06d766", "question": {"question_concept": "boredom", "choices": [{"label": "A", "text": "have fun"}, {"label": "B", "text": "talk more"}, {"label": "C", "text": "play games"}, {"label": "D", "text": "learn new"}, {"label": "E", "text": "go somewhere"}], "stem": "How might someone avoid boredom?"}}
{"id": "a3a634d7ce345192ac43c1789e576bd5", "question": {"question_concept": "hearing testimony", "choices": [{"label": "A", "text": "exasperation"}, {"label": "B", "text": "annoyed"}, {"label": "C", "text": "anger"}, {"label": "D", "text": "boredom"}, {"label": "E", "text": "scrutinizing"}], "stem": "What emotional response would be elicited from listening to others try to convince you about the efficacy of something which you do not care about?"}}
{"id": "bea369dd17724c1a28af2e3f0e1dc212", "question": {"question_concept": "industry", "choices": [{"label": "A", "text": "big city"}, {"label": "B", "text": "los angeles"}, {"label": "C", "text": "factory"}, {"label": "D", "text": "civilization"}, {"label": "E", "text": "michigan"}], "stem": "Where would find most of a country's major industry?"}}
{"id": "06fdd7ffe69cb4137f09f1d92b163a3d", "question": {"question_concept": "cooling off", "choices": [{"label": "A", "text": "sweating"}, {"label": "B", "text": "air conditioning"}, {"label": "C", "text": "expansion"}, {"label": "D", "text": "shivering"}, {"label": "E", "text": "relaxation"}], "stem": "When you work out and get hot you need some cooling off afterwards, in the meantime the body gets a head start by what?"}}
{"id": "d6bc0f38f5250b18b758cfb31c2bdd69", "question": {"question_concept": "tile", "choices": [{"label": "A", "text": "wall"}, {"label": "B", "text": "computer game"}, {"label": "C", "text": "roof"}, {"label": "D", "text": "floor"}, {"label": "E", "text": "ceiling"}], "stem": "Where would you step on a tile?"}}
{"id": "82222274f049146f6235caa3b175c443", "question": {"question_concept": "upright piano", "choices": [{"label": "A", "text": "living room"}, {"label": "B", "text": "band"}, {"label": "C", "text": "college"}, {"label": "D", "text": "house"}, {"label": "E", "text": "back pocket"}], "stem": "I bought an upright piano, but my mother says not to leave it outside, where should it put it?"}}
{"id": "60a5108218c3a7233bb242f1f7b82a33", "question": {"question_concept": "toy soldier", "choices": [{"label": "A", "text": "movies"}, {"label": "B", "text": "ebay auction"}, {"label": "C", "text": "toy box"}, {"label": "D", "text": "toy store"}, {"label": "E", "text": "child's hand"}], "stem": "Where would you find a toy soldier that is not being played with?"}}
{"id": "7ce905a29e6b62683ae1548696395934", "question": {"question_concept": "light source", "choices": [{"label": "A", "text": "hallway"}, {"label": "B", "text": "sky"}, {"label": "C", "text": "flashlight"}, {"label": "D", "text": "books"}, {"label": "E", "text": "candy"}], "stem": "You need a like source to enjoy what communication item?"}}
{"id": "6c9d67a10f24fd92629620a6e9344dff", "question": {"question_concept": "house", "choices": [{"label": "A", "text": "rooms"}, {"label": "B", "text": "out house"}, {"label": "C", "text": "basement"}, {"label": "D", "text": "living room"}, {"label": "E", "text": "bathroom"}], "stem": "The separate areas in your house are called what?"}}
{"id": "51a8956459f8b2e074f0b9c7ed17500c", "question": {"question_concept": "pen", "choices": [{"label": "A", "text": "classroom"}, {"label": "B", "text": "pocket"}, {"label": "C", "text": "friend"}, {"label": "D", "text": "neighbor's house"}, {"label": "E", "text": "desk drawer"}], "stem": "What is the closest place where you could borrow a pen?"}}
{"id": "95dd791dbe5e9b0c8905211c8401f350", "question": {"question_concept": "shopping complex", "choices": [{"label": "A", "text": "subarbs"}, {"label": "B", "text": "countryside"}, {"label": "C", "text": "michigan"}, {"label": "D", "text": "suburbia"}, {"label": "E", "text": "big city"}], "stem": "Jill is at a shopping complex surrounded by traffic and large cities, where is she?"}}
{"id": "25b000b2707cf0f53326e5b50380c442", "question": {"question_concept": "air conditioner", "choices": [{"label": "A", "text": "humidity"}, {"label": "B", "text": "house"}, {"label": "C", "text": "office building"}, {"label": "D", "text": "texas"}, {"label": "E", "text": "warm climates"}], "stem": "What state is likely to have an abundance of air conditioners?"}}
{"id": "19fa764527b857aa677b1eb05d493ad7", "question": {"question_concept": "beaver", "choices": [{"label": "A", "text": "books"}, {"label": "B", "text": "ontario"}, {"label": "C", "text": "woodlands"}, {"label": "D", "text": "washington"}, {"label": "E", "text": "magnet"}], "stem": "Beaver went to school near DC and went for a vacation in Canada.  During his vacation he bought many souvenirs. What is something he might have bought?"}}
{"id": "f912e19d112c410cac12024421f42688", "question": {"question_concept": "security", "choices": [{"label": "A", "text": "concert"}, {"label": "B", "text": "university"}, {"label": "C", "text": "airport"}, {"label": "D", "text": "office"}, {"label": "E", "text": "home"}], "stem": "Where would you install a security system?"}}
{"id": "d33f1f8978e37dc80c46433c37e962f2", "question": {"question_concept": "cigarette", "choices": [{"label": "A", "text": "tobacconist shop"}, {"label": "B", "text": "mouth"}, {"label": "C", "text": "ashtray"}, {"label": "D", "text": "carton"}, {"label": "E", "text": "hand"}], "stem": "What holds a cigarette being lit?"}}
{"id": "190df9ea1a3c098c5c5eba943639d455", "question": {"question_concept": "playing guitar", "choices": [{"label": "A", "text": "compose song"}, {"label": "B", "text": "pick"}, {"label": "C", "text": "practice"}, {"label": "D", "text": "have fun"}, {"label": "E", "text": "desire"}], "stem": "What does someone have for a lead person playing guitar who is attractive?"}}
{"id": "1369d9e90a997efbcf35df7d89d8e3a0", "question": {"question_concept": "happy", "choices": [{"label": "A", "text": "unfortunate"}, {"label": "B", "text": "disillusioned"}, {"label": "C", "text": "inappropriate"}, {"label": "D", "text": "disenchanted"}, {"label": "E", "text": "sadness"}], "stem": "He expected a happy life, but when he was subjected to hardship over and over again, what did he feel?"}}
{"id": "1bc6614aaf1dd7f12737e63401112ab3", "question": {"question_concept": "human", "choices": [{"label": "A", "text": "school"}, {"label": "B", "text": "mortuary"}, {"label": "C", "text": "television"}, {"label": "D", "text": "conclave"}, {"label": "E", "text": "stadium"}], "stem": "Where do humans gather for sports?"}}
{"id": "cc1c2cc873f9a8de8d74759c3153878b", "question": {"question_concept": "light source", "choices": [{"label": "A", "text": "sky"}, {"label": "B", "text": "torch"}, {"label": "C", "text": "books"}, {"label": "D", "text": "house"}, {"label": "E", "text": "lamp"}], "stem": "Where is the most powerful light source?"}}
{"id": "ecbed1b7fe87e1981d5f9fa528d31b46", "question": {"question_concept": "reproducing", "choices": [{"label": "A", "text": "bizarre"}, {"label": "B", "text": "offspring"}, {"label": "C", "text": "fun"}, {"label": "D", "text": "propagation"}, {"label": "E", "text": "birth"}], "stem": "They loved reproducing the night with their friends every year, it was the most what thing most of them do?"}}
{"id": "7cf9d225a1d655aedd9cc0eaaed2bccb", "question": {"question_concept": "climbing", "choices": [{"label": "A", "text": "getting higher"}, {"label": "B", "text": "falling down"}, {"label": "C", "text": "intoxication"}, {"label": "D", "text": "exhilaration"}, {"label": "E", "text": "exhaustion"}], "stem": "the trainee had been climbing ladders all day and passed the test, when he got home he collapsed on the couch from what?"}}
{"id": "a423b820e1675ce51d2eb829c518e59f", "question": {"question_concept": "playroom", "choices": [{"label": "A", "text": "building"}, {"label": "B", "text": "preschool"}, {"label": "C", "text": "big house"}, {"label": "D", "text": "nursery school"}, {"label": "E", "text": "toy"}], "stem": "The kids were all in the playroom when the parents came to pick their children up from where?"}}
{"id": "1fa9eedb707a37f897783adb251381b0", "question": {"question_concept": "running", "choices": [{"label": "A", "text": "breathlessness"}, {"label": "B", "text": "increased heart rate"}, {"label": "C", "text": "calluses"}, {"label": "D", "text": "tiredness"}, {"label": "E", "text": "lactic buildup"}], "stem": "Running when you're out of shape can easily lead to something that might be distressing and uncomfortable.  What can it lead to."}}
{"id": "bb375dbd3d47d3c58b60de4db9531e1b_1", "question": {"question_concept": "central passage", "choices": [{"label": "A", "text": "piramid"}, {"label": "B", "text": "arena"}, {"label": "C", "text": "large building"}, {"label": "D", "text": "public building"}, {"label": "E", "text": "tomb"}], "stem": "The central passage of what leads to an area where a person of royal birth is put to rest?"}}
{"id": "4e82ba7b8a1197b741e0c7c1ea9a0783", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "more leisure time"}, {"label": "B", "text": "own house"}, {"label": "C", "text": "acquire wealth"}, {"label": "D", "text": "not feel pain"}, {"label": "E", "text": "a vacation"}], "stem": "What does a person want to do by working?"}}
{"id": "3bb8f309e1f19bb386b96985389a515e", "question": {"question_concept": "procreate", "choices": [{"label": "A", "text": "moan"}, {"label": "B", "text": "die"}, {"label": "C", "text": "std"}, {"label": "D", "text": "moaning"}, {"label": "E", "text": "kiss"}], "stem": "How does one start the process of procreate?"}}
{"id": "4434076be3f84f19a91fcd653a9047ec", "question": {"question_concept": "performing", "choices": [{"label": "A", "text": "accolades"}, {"label": "B", "text": "injury"}, {"label": "C", "text": "tiredness"}, {"label": "D", "text": "do best"}, {"label": "E", "text": "stay home"}], "stem": "To be successful while performing what must you do?"}}
{"id": "b164f62bb040e08499661827da2c2db4", "question": {"question_concept": "rung", "choices": [{"label": "A", "text": "highchair"}, {"label": "B", "text": "rocking chair"}, {"label": "C", "text": "straight chair"}, {"label": "D", "text": "rocking chair"}, {"label": "E", "text": "folding chair"}], "stem": "What type of chair has a rung?"}}
{"id": "f544ba6dc7de86da942d18bc0396a105", "question": {"question_concept": "discovering truth", "choices": [{"label": "A", "text": "asking questions"}, {"label": "B", "text": "learning"}, {"label": "C", "text": "crying"}, {"label": "D", "text": "surprise"}, {"label": "E", "text": "feeling hurt"}], "stem": "What does someone experience when they discovering truth that is sad?"}}
{"id": "633e38c3ad3eaff6fbd006399d22b336", "question": {"question_concept": "staircase", "choices": [{"label": "A", "text": "mansion"}, {"label": "B", "text": "school"}, {"label": "C", "text": "house"}, {"label": "D", "text": "palace"}, {"label": "E", "text": "cellar"}], "stem": "Where kind of structure are you in if there is a fancy chandelier next to a staircase?"}}
{"id": "9b75b7316b6f6fd1c28430fabf918502", "question": {"question_concept": "answering questions", "choices": [{"label": "A", "text": "teaching"}, {"label": "B", "text": "panic"}, {"label": "C", "text": "discussion"}, {"label": "D", "text": "correct"}, {"label": "E", "text": "funeral"}], "stem": "Asking and answering questions is essential for a lively what?"}}
{"id": "98efaf3f3a9c78e4c56e2880a4e7d451", "question": {"question_concept": "writing program", "choices": [{"label": "A", "text": "bugs"}, {"label": "B", "text": "mud"}, {"label": "C", "text": "unexpected results"}, {"label": "D", "text": "loop"}, {"label": "E", "text": "need to integrate"}], "stem": "Sally was writing a program in basic.  It wouldn't complete or stop.  It was stuck in what?"}}
{"id": "57accd1c707c9e7b390991cee7e9fc3d", "question": {"question_concept": "safe", "choices": [{"label": "A", "text": "in danger"}, {"label": "B", "text": "unsafe"}, {"label": "C", "text": "harmful"}, {"label": "D", "text": "insecure"}, {"label": "E", "text": "obesity"}], "stem": "She bought a home alarm to try to feel safe, but she still felt vulnerable and what of herself?"}}
{"id": "2951a7f61d0f4825138930fecc2df16f", "question": {"question_concept": "backdrop", "choices": [{"label": "A", "text": "a tornado"}, {"label": "B", "text": "photography studio"}, {"label": "C", "text": "theater"}, {"label": "D", "text": "photo studio"}, {"label": "E", "text": "stage setting"}], "stem": "Joe stood in front of the backdrop and watched as the hands prepared the what?"}}
{"id": "e9651dacc35dfe3a0428cbe8c77b1047", "question": {"question_concept": "fruit", "choices": [{"label": "A", "text": "go off"}, {"label": "B", "text": "decay"}, {"label": "C", "text": "grow mold"}, {"label": "D", "text": "taste sweet"}, {"label": "E", "text": "keep fresh"}], "stem": "The trucks transporting the fruit had refrigerated trailers, this was to help do what to the produce?"}}
{"id": "eeab50c5a049aef9f1ecfe8f7a872b05", "question": {"question_concept": "current", "choices": [{"label": "A", "text": "old news"}, {"label": "B", "text": "tomorrow"}, {"label": "C", "text": "outdated"}, {"label": "D", "text": "now"}, {"label": "E", "text": "later"}], "stem": "It's important to keep your software current or the security features may be what?"}}
{"id": "fb3c859b32e6ee649504ea10def6256d", "question": {"question_concept": "babies", "choices": [{"label": "A", "text": "sleep soundly"}, {"label": "B", "text": "like spinach"}, {"label": "C", "text": "giggle"}, {"label": "D", "text": "clap hands"}, {"label": "E", "text": "cry"}], "stem": "If a baby is typical, what does it not do?"}}
{"id": "bc7e740fe5e8935a28bdfe40760cd3aa", "question": {"question_concept": "pass class", "choices": [{"label": "A", "text": "study hard"}, {"label": "B", "text": "smart"}, {"label": "C", "text": "study"}, {"label": "D", "text": "homework"}, {"label": "E", "text": "turn in assignments"}], "stem": "What do you have to be to pass class?"}}
{"id": "b2e103d4b84cfb586a17628c98e10608", "question": {"question_concept": "kissing", "choices": [{"label": "A", "text": "get cold"}, {"label": "B", "text": "runny nose"}, {"label": "C", "text": "herpes"}, {"label": "D", "text": "catch cold"}, {"label": "E", "text": "sexual stimulation"}], "stem": "What might prolonged kissing lead to?"}}
{"id": "a086e49b7864619b40e2b96e4463ca03", "question": {"question_concept": "horse", "choices": [{"label": "A", "text": "washington"}, {"label": "B", "text": "farmyard"}, {"label": "C", "text": "glue factory"}, {"label": "D", "text": "race track"}, {"label": "E", "text": "painting"}], "stem": "Where might a horse wander around?"}}
{"id": "04a281e57410baffbbff0b6f94d948f8", "question": {"question_concept": "lady", "choices": [{"label": "A", "text": "grocery store"}, {"label": "B", "text": "church"}, {"label": "C", "text": "supermarket"}, {"label": "D", "text": "retreat"}, {"label": "E", "text": "bathroom"}], "stem": "The lady sought enlightenment and belonging, where did he go?"}}
{"id": "2da8b0db736e3566400346d97c87ed3d", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "self esteem"}, {"label": "B", "text": "acquire wealth"}, {"label": "C", "text": "own house"}, {"label": "D", "text": "delicious food"}, {"label": "E", "text": "beautiful flowers"}], "stem": "A person wanting to be successful needs to work on many things, the most important of which is having high what?"}}
{"id": "3dc9043ab70ad772e965abea20eedbed", "question": {"question_concept": "have lunch", "choices": [{"label": "A", "text": "prepare food"}, {"label": "B", "text": "find food"}, {"label": "C", "text": "have time for"}, {"label": "D", "text": "buy food"}, {"label": "E", "text": "get food"}], "stem": "In order to have a hot lunch what do you need to do first?"}}
{"id": "ff504d5cebb63437d7fe4ae3f4b4fea6", "question": {"question_concept": "reading book", "choices": [{"label": "A", "text": "enlightenment"}, {"label": "B", "text": "philosophy"}, {"label": "C", "text": "learn things"}, {"label": "D", "text": "nightmares"}, {"label": "E", "text": "entertainment"}], "stem": "James did a research report on reading books. It is one of the oldest forms of what?"}}
{"id": "631da190b213e7333fca35ad459b72be", "question": {"question_concept": "trash", "choices": [{"label": "A", "text": "garbage dump"}, {"label": "B", "text": "subway"}, {"label": "C", "text": "hospital"}, {"label": "D", "text": "dumpster"}, {"label": "E", "text": "dustbin"}], "stem": "What place might have a lot of trash that is not supposed to be there?"}}
{"id": "8c29212609efe7c3a29f5edcc0bb5e1e", "question": {"question_concept": "marmot", "choices": [{"label": "A", "text": "study hall"}, {"label": "B", "text": "great plains"}, {"label": "C", "text": "north america"}, {"label": "D", "text": "countryside"}, {"label": "E", "text": "encyclopedia"}], "stem": "How can a student learn about a marmot?"}}
{"id": "ce1d9ce5e1a06446c90cfbc4d7c3157d", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "acknowledgment"}, {"label": "B", "text": "receive gifts"}, {"label": "C", "text": "headache"}, {"label": "D", "text": "third degree burns"}, {"label": "E", "text": "understand themselves"}], "stem": "What may a person get to help them feel respected?"}}
{"id": "49f17af7f55cdd6a29731589d3a45ed4", "question": {"question_concept": "most birds", "choices": [{"label": "A", "text": "wings"}, {"label": "B", "text": "ability to fly"}, {"label": "C", "text": "learn to fly"}, {"label": "D", "text": "beak"}, {"label": "E", "text": "learn to fly"}], "stem": "Danny liked to watch birds.  Most birds have an organ that they use to eat.  What is this organ called?"}}
{"id": "8010142a704066cf4482b89565c5b546", "question": {"question_concept": "most", "choices": [{"label": "A", "text": "mournful"}, {"label": "B", "text": "bitterest"}, {"label": "C", "text": "corrupt"}, {"label": "D", "text": "representative"}, {"label": "E", "text": "cleverest"}], "stem": "What does someone have the most of if they lost someone and are resentful?"}}
{"id": "1d5fc356f8a6bdeba0d39adfb3e5e8bc", "question": {"question_concept": "bill", "choices": [{"label": "A", "text": "congress"}, {"label": "B", "text": "restaurant"}, {"label": "C", "text": "table"}, {"label": "D", "text": "envelope"}, {"label": "E", "text": "wallet"}], "stem": "Where do you usually receive a bill at the end of your experience?"}}
{"id": "374364d492f4852fff1618870d508349", "question": {"question_concept": "meeting friend", "choices": [{"label": "A", "text": "panic"}, {"label": "B", "text": "cheer"}, {"label": "C", "text": "relaxation"}, {"label": "D", "text": "make her feel better"}, {"label": "E", "text": "talk"}], "stem": "John was meeting a friend who had a depressing day.  What did he want to do to her?"}}
{"id": "970761496497d7eca7678e1d1c918a00", "question": {"question_concept": "exercise", "choices": [{"label": "A", "text": "shortness of breath"}, {"label": "B", "text": "death"}, {"label": "C", "text": "injury"}, {"label": "D", "text": "fitness"}, {"label": "E", "text": "need for food"}], "stem": "If you exercise to intensely what can happen?"}}
{"id": "6a45cdc3296259f0ae27ad772de92ab0", "question": {"question_concept": "student", "choices": [{"label": "A", "text": "complete test"}, {"label": "B", "text": "solve equation"}, {"label": "C", "text": "study book"}, {"label": "D", "text": "begin teaching"}, {"label": "E", "text": "finish college"}], "stem": "What will a student do after graduating college?"}}
{"id": "e1ed7f714f9bccb1007fe027c819951c", "question": {"question_concept": "main artery", "choices": [{"label": "A", "text": "neck"}, {"label": "B", "text": "torso"}, {"label": "C", "text": "human body"}, {"label": "D", "text": "heart"}, {"label": "E", "text": "body of animal"}], "stem": "There are four main artery in the what?"}}
{"id": "6ca2a844a53521a8a54f7efe0ec1b10a", "question": {"question_concept": "going to market", "choices": [{"label": "A", "text": "that you"}, {"label": "B", "text": "apples"}, {"label": "C", "text": "stress"}, {"label": "D", "text": "walking"}, {"label": "E", "text": "bankruptcy"}], "stem": "How might someone be going to market?"}}
{"id": "fa6af9a16599a30d9897f78771dfd9e1", "question": {"question_concept": "exercise", "choices": [{"label": "A", "text": "fitness"}, {"label": "B", "text": "shortness of breath"}, {"label": "C", "text": "weight gain"}, {"label": "D", "text": "injury"}, {"label": "E", "text": "need for food"}], "stem": "What does exercise usually hope to improve?"}}
{"id": "43a150efaf18bd859831ab4761dc2436", "question": {"question_concept": "pray", "choices": [{"label": "A", "text": "you're scared"}, {"label": "B", "text": "in the morning"}, {"label": "C", "text": "talk to god"}, {"label": "D", "text": "salvation"}, {"label": "E", "text": "were religious"}], "stem": "WHen do most people pray even when note religious?"}}
{"id": "7c3c7e958629444608ce15bbfc662498", "question": {"question_concept": "temple", "choices": [{"label": "A", "text": "india"}, {"label": "B", "text": "middle east"}, {"label": "C", "text": "buddhism"}, {"label": "D", "text": "china"}, {"label": "E", "text": "jerusalem"}], "stem": "Where in South Asia can you find a temple?"}}
{"id": "b82493e188ab497ee2195d4a3ed82c6d", "question": {"question_concept": "hamburgers", "choices": [{"label": "A", "text": "cooked on grill"}, {"label": "B", "text": "mcdonalds"}, {"label": "C", "text": "hockey game"}, {"label": "D", "text": "burger king"}, {"label": "E", "text": "fast food restaurant"}], "stem": "James didn't like Whoppers, but he still wanted hamburgers.  What type of place might he go to?"}}
{"id": "fa720b796d0748ff58fe29d37b1e38d7", "question": {"question_concept": "snow", "choices": [{"label": "A", "text": "shovelling"}, {"label": "B", "text": "avalanches"}, {"label": "C", "text": "blizzard"}, {"label": "D", "text": "christmas"}, {"label": "E", "text": "blackout"}], "stem": "What is it called when snow falls fast enough to obscure vison?"}}
{"id": "a4995ea9c7441383d68651cf536e35c6", "question": {"question_concept": "attending school", "choices": [{"label": "A", "text": "play games"}, {"label": "B", "text": "taking tests"}, {"label": "C", "text": "get smart"}, {"label": "D", "text": "colds and flu"}, {"label": "E", "text": "boredom"}], "stem": "What's one of the things you do a lot while attending school?"}}
{"id": "97201bbafa7af5854a3b0c5ee7107fb9", "question": {"question_concept": "showroom", "choices": [{"label": "A", "text": "appliance store"}, {"label": "B", "text": "car dealership"}, {"label": "C", "text": "car lot"}, {"label": "D", "text": "vegas"}, {"label": "E", "text": "city"}], "stem": "James looked around the showroom but didn't see anything he liked.  The salesman talked him into trying some things, though.  He felt that he shouldn't have come to this place.  Where is he?"}}
{"id": "7f757725231983b77b37efc43bf0d6c4", "question": {"question_concept": "visiting museum", "choices": [{"label": "A", "text": "pondering"}, {"label": "B", "text": "tired feet"}, {"label": "C", "text": "being bored"}, {"label": "D", "text": "standing"}, {"label": "E", "text": "gaining knowledge"}], "stem": "John loved visiting the museum.  He spent his time considering what the paintings represented.   He didn't understand all of them, but he did a lot of what?"}}
{"id": "67590f4b9f40e72bb343b644a827c167", "question": {"question_concept": "bathroom", "choices": [{"label": "A", "text": "bus station"}, {"label": "B", "text": "school"}, {"label": "C", "text": "flat"}, {"label": "D", "text": "neighbor's house"}, {"label": "E", "text": "at hotel"}], "stem": "James had to use the bathroom, but he couldn't do it in a place he wasn't familiar with or in a public place. So he  waited until he got back to his what?"}}
{"id": "04fbbcd5fdf697b61cfd3d6932661029", "question": {"question_concept": "pie", "choices": [{"label": "A", "text": "fridge"}, {"label": "B", "text": "refrigerator"}, {"label": "C", "text": "windowsill"}, {"label": "D", "text": "freezer"}, {"label": "E", "text": "oven"}], "stem": "Where do you cook the pie?"}}
{"id": "4ccad7e34e499faf745bf6d8102e13c8", "question": {"question_concept": "hike", "choices": [{"label": "A", "text": "take photos"}, {"label": "B", "text": "seeing bear"}, {"label": "C", "text": "get wet"}, {"label": "D", "text": "pick berries"}, {"label": "E", "text": "see beautiful views"}], "stem": "What is a fun thing to do when you get too hot on a hike?"}}
{"id": "88c0e5de2977ef959a8c6b759a848dab", "question": {"question_concept": "live life", "choices": [{"label": "A", "text": "help people"}, {"label": "B", "text": "have no other choice"}, {"label": "C", "text": "luck"}, {"label": "D", "text": "all know"}, {"label": "E", "text": "was given to"}], "stem": "When you get a second chance to live life, an opportunity was what to you?"}}
{"id": "5c95114e3958b40d45d45a0af7e8bd8f", "question": {"question_concept": "bring home fish", "choices": [{"label": "A", "text": "sushi"}, {"label": "B", "text": "rice"}, {"label": "C", "text": "hungry"}, {"label": "D", "text": "have cooked"}, {"label": "E", "text": "else to eat"}], "stem": "Bring home fish, clean it, and then you you eat it. What is missing that you have done to it?"}}
{"id": "4d3db7523e35f1877b05ac93153ba6ba", "question": {"question_concept": "water", "choices": [{"label": "A", "text": "underground stream"}, {"label": "B", "text": "sump pump"}, {"label": "C", "text": "clouds"}, {"label": "D", "text": "sink"}, {"label": "E", "text": "hydroelectric dam"}], "stem": "Water flows through what when not above ground?"}}
{"id": "1fc4b3f15eb693ffe178448d9451de57", "question": {"question_concept": "roof", "choices": [{"label": "A", "text": "all buildings"}, {"label": "B", "text": "every house"}, {"label": "C", "text": "shack"}, {"label": "D", "text": "garage"}, {"label": "E", "text": "state of disrepair"}], "stem": "The roof was sagging and leaking, one could say it was in a what?"}}
{"id": "deeb58cba711012734fd660378061823", "question": {"question_concept": "eating dinner", "choices": [{"label": "A", "text": "throwing up"}, {"label": "B", "text": "heartburn"}, {"label": "C", "text": "indigestion"}, {"label": "D", "text": "sleepiness"}, {"label": "E", "text": "be sick"}], "stem": "If you were eating dinner and then you ran to a toilet, what is most likely to happen?"}}
{"id": "e1de2aaaa0cb88e3ce797a75079a2269", "question": {"question_concept": "performance", "choices": [{"label": "A", "text": "concert hall"}, {"label": "B", "text": "commercial"}, {"label": "C", "text": "show"}, {"label": "D", "text": "movies"}, {"label": "E", "text": "theatre"}], "stem": "Where can you see their performance?"}}
{"id": "35c635a87dbbdbfcc7195613f9c8faa3", "question": {"question_concept": "animal", "choices": [{"label": "A", "text": "jungle"}, {"label": "B", "text": "cafe"}, {"label": "C", "text": "pet store"}, {"label": "D", "text": "north america"}, {"label": "E", "text": "park"}], "stem": "There are many semi-wild animals at this place.  What might this place be?"}}
{"id": "a9246f1c130a4bfecdfb37f1c7c21f5b", "question": {"question_concept": "human", "choices": [{"label": "A", "text": "apartment"}, {"label": "B", "text": "new orleans"}, {"label": "C", "text": "department store"}, {"label": "D", "text": "new jersey"}, {"label": "E", "text": "workplace"}], "stem": "A human is hitch hiking in Lousiana, where is he likely heading?"}}
{"id": "0e7cc1ba6d074b01b246cb38f9b2f4e3", "question": {"question_concept": "wine cellar", "choices": [{"label": "A", "text": "good restaurant"}, {"label": "B", "text": "vineyard"}, {"label": "C", "text": "fancy restaurant"}, {"label": "D", "text": "dark cool place"}, {"label": "E", "text": "italy"}], "stem": "What type of place has very expensive wine in their wine cellar?"}}
{"id": "59208301872f02855a96b69c82d997d3", "question": {"question_concept": "company", "choices": [{"label": "A", "text": "yellow pages"}, {"label": "B", "text": "phone book"}, {"label": "C", "text": "armed forces"}, {"label": "D", "text": "market place"}, {"label": "E", "text": "flea market"}], "stem": "Where could you get something from a company?"}}
{"id": "82b9d59b3f1631240fb17764bc7b459e", "question": {"question_concept": "puppy", "choices": [{"label": "A", "text": "kennel"}, {"label": "B", "text": "fire station"}, {"label": "C", "text": "dog house"}, {"label": "D", "text": "table"}, {"label": "E", "text": "home"}], "stem": "Billy wanted a new puppy to bring back to his place.  Where might he find one?"}}
{"id": "0e934757a64f62e6d1adc60571b290e3", "question": {"question_concept": "cymbal", "choices": [{"label": "A", "text": "symphony orchestra"}, {"label": "B", "text": "field"}, {"label": "C", "text": "music store"}, {"label": "D", "text": "drumkit"}, {"label": "E", "text": "marching band"}], "stem": "Where is a cymbal likely to be used in a finale?"}}
{"id": "edb522b4d494f62d0b6a344c8f101fbb", "question": {"question_concept": "dancing", "choices": [{"label": "A", "text": "intercourse"}, {"label": "B", "text": "listen to music"}, {"label": "C", "text": "exhaustion"}, {"label": "D", "text": "expression"}, {"label": "E", "text": "become tired"}], "stem": "Men and women go out dancing hoping to find someone to do what with?"}}
{"id": "cf863025cbeb86080dfa7823bb857867", "question": {"question_concept": "friends", "choices": [{"label": "A", "text": "group together"}, {"label": "B", "text": "understand each other"}, {"label": "C", "text": "keep secrets"}, {"label": "D", "text": "convincing"}, {"label": "E", "text": "part company"}], "stem": "What could cause friends to all have the same opinion about something?"}}
{"id": "bb665a14e6849e2f9ee62d7334a1afc8", "question": {"question_concept": "expressing yourself", "choices": [{"label": "A", "text": "empowerment"}, {"label": "B", "text": "misunderstanding"}, {"label": "C", "text": "repercussions"}, {"label": "D", "text": "suffering"}, {"label": "E", "text": "pain"}], "stem": "What would happen if you are expressing yourself illegally?"}}
{"id": "1188320488a3f487ed629dc4732a423e", "question": {"question_concept": "form", "choices": [{"label": "A", "text": "large"}, {"label": "B", "text": "change shape"}, {"label": "C", "text": "function"}, {"label": "D", "text": "chaos"}, {"label": "E", "text": "shapeless"}], "stem": "The nearsighted man could tell the object took a form, but largely what did he notice about it?"}}
{"id": "f2b949d24975c6ea3b439acec84d5e4f", "question": {"question_concept": "cat", "choices": [{"label": "A", "text": "warm place"}, {"label": "B", "text": "floor"}, {"label": "C", "text": "barn"}, {"label": "D", "text": "roof"}, {"label": "E", "text": "beam of sunlight"}], "stem": "Where is a surface where cats usually touch daily?"}}
{"id": "15cc733b75a1a23a9173f172039c21bd_1", "question": {"question_concept": "barber shop", "choices": [{"label": "A", "text": "small town"}, {"label": "B", "text": "canada"}, {"label": "C", "text": "high street"}, {"label": "D", "text": "mini mall"}, {"label": "E", "text": "neighborhood"}], "stem": "The barber shop was always busy due to foot traffic, where was it located?"}}
{"id": "7b30bd9183624d5e7268055311883a8a", "question": {"question_concept": "wrestling", "choices": [{"label": "A", "text": "bruises"}, {"label": "B", "text": "blood"}, {"label": "C", "text": "erections"}, {"label": "D", "text": "wins"}, {"label": "E", "text": "medal"}], "stem": "John loved wrestling with big sweaty men.  He was very skilled at it, and was thrilled when he pinned them to the mat.  What did John receive when he pinned people to the mat?"}}
{"id": "bfbbf3d49d0918ff31eebaec57edfa7e", "question": {"question_concept": "large container", "choices": [{"label": "A", "text": "juice"}, {"label": "B", "text": "shed"}, {"label": "C", "text": "cabinet"}, {"label": "D", "text": "backyard"}, {"label": "E", "text": "warehouse"}], "stem": "Where at your yard would you store a large container?"}}
{"id": "9147d8333fd8003dadf286385d8547dc", "question": {"question_concept": "remote", "choices": [{"label": "A", "text": "attached"}, {"label": "B", "text": "companionable"}, {"label": "C", "text": "close"}, {"label": "D", "text": "enough"}, {"label": "E", "text": "likely"}], "stem": "What is the probability that a remote will turn on the TV?"}}
{"id": "3dd16f0d72e4175ec8f610baa2dddbf2", "question": {"question_concept": "changing room", "choices": [{"label": "A", "text": "schools"}, {"label": "B", "text": "nursery"}, {"label": "C", "text": "clothing store"}, {"label": "D", "text": "gym"}, {"label": "E", "text": "department store"}], "stem": "Where would you use a changing room along side many types of products for sale?"}}
{"id": "443422ec4a92076b1fe8c492e8b27624", "question": {"question_concept": "water", "choices": [{"label": "A", "text": "hockey"}, {"label": "B", "text": "typhoon"}, {"label": "C", "text": "ocean"}, {"label": "D", "text": "snowflake"}, {"label": "E", "text": "teardrops"}], "stem": "On a long, hard, winter day Joe felt warm water on his face.  It took him a few minutes to realize that the water was coming from him.  They were what?"}}
{"id": "e811d9d4c23690b0270a29caaac47ce5", "question": {"question_concept": "showroom", "choices": [{"label": "A", "text": "appliance store"}, {"label": "B", "text": "convention center"}, {"label": "C", "text": "city"}, {"label": "D", "text": "car dealership"}, {"label": "E", "text": "vegas"}], "stem": "The showroom had all sorts of booths.  He was glad he went to the convention, even if it was all the way in what location?"}}
{"id": "53c1362f7d6a6de25c1759517dbe316a", "question": {"question_concept": "condom", "choices": [{"label": "A", "text": "purse"}, {"label": "B", "text": "pharmacy"}, {"label": "C", "text": "bedroom"}, {"label": "D", "text": "drugstore"}, {"label": "E", "text": "on your hand"}], "stem": "Where are you most likely to be using a condom?"}}
{"id": "3961ded51daf0ccca7e3b2e57bb52bb2", "question": {"question_concept": "bill", "choices": [{"label": "A", "text": "restaurant"}, {"label": "B", "text": "paycheck"}, {"label": "C", "text": "coins"}, {"label": "D", "text": "straight"}, {"label": "E", "text": "medium"}], "stem": "When you're paying with cash and not using bills you'd be using what?"}}
{"id": "e63e77860c73a26808022d9f99ed851f", "question": {"question_concept": "ice", "choices": [{"label": "A", "text": "water cooler"}, {"label": "B", "text": "ground"}, {"label": "C", "text": "antarctica"}, {"label": "D", "text": "mixed drinks"}, {"label": "E", "text": "cold weather"}], "stem": "The forecast called for freezing rain and ice, needless to say it would be what?"}}
{"id": "9517fcd6781d7e2e504464415c37d34d", "question": {"question_concept": "getting in line", "choices": [{"label": "A", "text": "linearity"}, {"label": "B", "text": "long wait"}, {"label": "C", "text": "late"}, {"label": "D", "text": "irritation"}, {"label": "E", "text": "tempers flare"}], "stem": "What will always happen when getting in line with many people?"}}
{"id": "9c231d9160b4e235121a4b7a965f3c3d", "question": {"question_concept": "friends", "choices": [{"label": "A", "text": "comfort"}, {"label": "B", "text": "borrow money"}, {"label": "C", "text": "borrow money"}, {"label": "D", "text": "part ways"}, {"label": "E", "text": "leave"}], "stem": "The friends were troubles, why did they hang out?"}}
{"id": "f75deb6bb2b0a3c4960bf69319dee601", "question": {"question_concept": "making bread", "choices": [{"label": "A", "text": "gratifying"}, {"label": "B", "text": "allergic reactions"}, {"label": "C", "text": "pride"}, {"label": "D", "text": "mess"}, {"label": "E", "text": "loniliness"}], "stem": "What could someone make if he or she is inexperienced at making bread?"}}
{"id": "32ca8ef1dd7ddd83da0bc78cee966980", "question": {"question_concept": "praying", "choices": [{"label": "A", "text": "unrest"}, {"label": "B", "text": "relief"}, {"label": "C", "text": "sense of peace"}, {"label": "D", "text": "feel safer"}, {"label": "E", "text": "wasted time"}], "stem": "In times of turmoil she began praying, it brought her a serene what?"}}
{"id": "0a79eabcac09059922ccc9529fc37a10", "question": {"question_concept": "weapon", "choices": [{"label": "A", "text": "holster"}, {"label": "B", "text": "concealed place"}, {"label": "C", "text": "police station"}, {"label": "D", "text": "battlefield"}, {"label": "E", "text": "war"}], "stem": "Where is a tank type weapon likely to be found?"}}
{"id": "1a21a3b77a805f7a453ff7dc69efb845_1", "question": {"question_concept": "hands", "choices": [{"label": "A", "text": "glass of water"}, {"label": "B", "text": "articulate"}, {"label": "C", "text": "soft"}, {"label": "D", "text": "cup water"}, {"label": "E", "text": "sign language"}], "stem": "She was curled up on the couch to watch her show, she used both hands to what when she wanted a sip?"}}
{"id": "e285435dbbbde6d5627f37b4e21a21a5", "question": {"question_concept": "cannon", "choices": [{"label": "A", "text": "body armor"}, {"label": "B", "text": "fire grapeshot"}, {"label": "C", "text": "ungulate"}, {"label": "D", "text": "bomber"}, {"label": "E", "text": "missile"}], "stem": "On a ship a cannon is used, what is used on a plane?"}}
{"id": "621d6f1302960d2708aae3af062a08ee", "question": {"question_concept": "going on vacation", "choices": [{"label": "A", "text": "drinking"}, {"label": "B", "text": "debt"}, {"label": "C", "text": "relaxing"}, {"label": "D", "text": "relaxation"}, {"label": "E", "text": "peace"}], "stem": "Jenny enjoyed going on vacation, but she didn't do it often. She hated what came with it. What might be a consequence of a vacation?"}}
{"id": "b24bab0aa46be6f5c924b42924b06e7b", "question": {"question_concept": "army", "choices": [{"label": "A", "text": "fort"}, {"label": "B", "text": "battlefield"}, {"label": "C", "text": "other countries"}, {"label": "D", "text": "war"}, {"label": "E", "text": "sailboat"}], "stem": "Sally joined the army in peacetime.  There wasn't any combat, but she went many places.  Where did she go?"}}
{"id": "8dc34875c30acf9084c8dae964f0a69d", "question": {"question_concept": "running twenty six miles", "choices": [{"label": "A", "text": "feel tired"}, {"label": "B", "text": "getting tired"}, {"label": "C", "text": "slight discomfort"}, {"label": "D", "text": "excruciating pain"}, {"label": "E", "text": "tiredness"}], "stem": "It was his first marathon and he wasn't ready, running the twenty six miles caused him what?"}}
{"id": "d92be8de139dc4efe3f6abd9db0e30a7", "question": {"question_concept": "biggest", "choices": [{"label": "A", "text": "effusive"}, {"label": "B", "text": "enabled"}, {"label": "C", "text": "accidental"}, {"label": "D", "text": "detestable"}, {"label": "E", "text": "gigantic"}], "stem": "After George broke his leg, George's best friend, Tim, gave him the biggest gift box that could be found.   How might George's response be described respond?"}}
{"id": "ea36a782b046baa86f103b2743de0024", "question": {"question_concept": "animals", "choices": [{"label": "A", "text": "nesting"}, {"label": "B", "text": "procreate"}, {"label": "C", "text": "need to eat"}, {"label": "D", "text": "keep alive"}, {"label": "E", "text": "lie down"}], "stem": "If an animal is in the wild, what is its prime directive?"}}
{"id": "8bd918a62468b731fe0862d07481e856", "question": {"question_concept": "talk", "choices": [{"label": "A", "text": "express opinions"}, {"label": "B", "text": "open mouth"}, {"label": "C", "text": "will listen"}, {"label": "D", "text": "make sound"}, {"label": "E", "text": "not listen"}], "stem": "James wants to talk to people, but he can't because he lacks people.  What might people do when someone talks?"}}
{"id": "8c60c8e3e08636401ff86cc30b3b1cae", "question": {"question_concept": "goods", "choices": [{"label": "A", "text": "grocery store"}, {"label": "B", "text": "hardware store"}, {"label": "C", "text": "supermarket"}, {"label": "D", "text": "shop"}, {"label": "E", "text": "mall"}], "stem": "Where would you go if you want to get some bread and ice cream, but nothing else?"}}
{"id": "1bf4c6b5bd870b1a079106e1e97e5d09_1", "question": {"question_concept": "thoroughfare", "choices": [{"label": "A", "text": "passing through estate"}, {"label": "B", "text": "country"}, {"label": "C", "text": "town"}, {"label": "D", "text": "scenery"}, {"label": "E", "text": "city"}], "stem": "It was a long and dusty thoroughfare, but it passed peacefully through some beautiful what?"}}
{"id": "bad1c19a2ef6337dd37e036037b3e793", "question": {"question_concept": "flea", "choices": [{"label": "A", "text": "attack"}, {"label": "B", "text": "suck blood"}, {"label": "C", "text": "bite"}, {"label": "D", "text": "jump"}, {"label": "E", "text": "lay eggs"}], "stem": "What does a female flea do after it mates?"}}
{"id": "164f12b9b6207767703c4328b463afe6_1", "question": {"question_concept": "astronauts", "choices": [{"label": "A", "text": "space shuttle"}, {"label": "B", "text": "spaceship"}, {"label": "C", "text": "outerspace"}, {"label": "D", "text": "outerspace"}, {"label": "E", "text": "orbit"}], "stem": "Not all astronauts are pilots, but as a pilot is to aircraft an astronaut is to what?"}}
{"id": "ffdb366c7c56fa2e4b99a11ef764d528", "question": {"question_concept": "servant", "choices": [{"label": "A", "text": "hired help"}, {"label": "B", "text": "lord"}, {"label": "C", "text": "freedom"}, {"label": "D", "text": "rich person"}, {"label": "E", "text": "in charge"}], "stem": "Sam was a servant of Josh.  He pledged himself to Josh years ago.  For this reason, Sam lacked what?"}}
{"id": "439e5c11eed63b2a2adc859d2be7d93e", "question": {"question_concept": "sink", "choices": [{"label": "A", "text": "chemistry lab"}, {"label": "B", "text": "neighbor's house"}, {"label": "C", "text": "gym"}, {"label": "D", "text": "laundry room"}, {"label": "E", "text": "home"}], "stem": "Joe's sink was clogged so he had to ask Brenda for permission to wash his dishes.  Where is he washing dishes?"}}
{"id": "f6f49e53bfc9c8769bf1bfcdb351f041", "question": {"question_concept": "pencil", "choices": [{"label": "A", "text": "pocket"}, {"label": "B", "text": "in mid-air"}, {"label": "C", "text": "classroom"}, {"label": "D", "text": "university"}, {"label": "E", "text": "desk drawer"}], "stem": "Where in your office would you store an extra pencil?"}}
{"id": "3cc314bf2cabcce9e929361e98493510", "question": {"question_concept": "desk", "choices": [{"label": "A", "text": "schoolroom"}, {"label": "B", "text": "a fast food restaurant"}, {"label": "C", "text": "study"}, {"label": "D", "text": "office building"}, {"label": "E", "text": "library"}], "stem": "Where is likely to have a checkout desk?"}}
{"id": "27f4d2626bc7ec1cf98e235f48a2a35e", "question": {"question_concept": "cow", "choices": [{"label": "A", "text": "slaughter house"}, {"label": "B", "text": "countryside"}, {"label": "C", "text": "barnyard"}, {"label": "D", "text": "stable"}, {"label": "E", "text": "trailer"}], "stem": "Where is a cow likely to cry?"}}
{"id": "8e033d10e04eef8c69f9ed56f2216c1d", "question": {"question_concept": "water", "choices": [{"label": "A", "text": "fish tank"}, {"label": "B", "text": "the bus station"}, {"label": "C", "text": "sink"}, {"label": "D", "text": "reflecting pool"}, {"label": "E", "text": "sewage treatment plant"}], "stem": "The water ran through the toilet, what is its next stop?"}}
{"id": "944340d4a80de14c9386f9629c63c7a1", "question": {"question_concept": "record", "choices": [{"label": "A", "text": "tape"}, {"label": "B", "text": "melt"}, {"label": "C", "text": "erase"}, {"label": "D", "text": "play music"}, {"label": "E", "text": "compact disc"}], "stem": "The record spun on the turntable. What else did it do?"}}
{"id": "3da502d3d6e997ac27632292401ddc76", "question": {"question_concept": "marmoset", "choices": [{"label": "A", "text": "veterinarian"}, {"label": "B", "text": "underground"}, {"label": "C", "text": "dictionary"}, {"label": "D", "text": "rainforest"}, {"label": "E", "text": "colorado"}], "stem": "What would you need if you did not know what a marmoset is?"}}
{"id": "3d716eaf3dede711bc675c5ad3b8f6e7", "question": {"question_concept": "baseball", "choices": [{"label": "A", "text": "park"}, {"label": "B", "text": "sporting goods store"}, {"label": "C", "text": "break window"}, {"label": "D", "text": "america"}, {"label": "E", "text": "skating rink"}], "stem": "Where do people play baseball?"}}
{"id": "33791ed38d9a78fadc5da4079322a87d", "question": {"question_concept": "competing", "choices": [{"label": "A", "text": "working harder"}, {"label": "B", "text": "death"}, {"label": "C", "text": "winning or losing"}, {"label": "D", "text": "trying harder"}, {"label": "E", "text": "pressure"}], "stem": "Bobby hated competing but his parents didn't see. He failed and they just said he should be doing what?"}}
{"id": "399c34b2d835e8b9a8ed648055e829ea", "question": {"question_concept": "rows of seats", "choices": [{"label": "A", "text": "hockey game"}, {"label": "B", "text": "theater"}, {"label": "C", "text": "bus"}, {"label": "D", "text": "train"}, {"label": "E", "text": "theatre"}], "stem": "Where would you find a row of seats while watching men holding sticks and wearing pads?"}}
{"id": "b778c1f82c3b0ad5527933d1d209a416", "question": {"question_concept": "heart", "choices": [{"label": "A", "text": "chicken"}, {"label": "B", "text": "all mammals"}, {"label": "C", "text": "turkey"}, {"label": "D", "text": "artichoke"}, {"label": "E", "text": "person"}], "stem": "What has a heart that is good to eat?"}}
{"id": "f22baf2383e46c60da210971ca2d4bc6", "question": {"question_concept": "soprano", "choices": [{"label": "A", "text": "performance"}, {"label": "B", "text": "choir"}, {"label": "C", "text": "movie"}, {"label": "D", "text": "opera house"}, {"label": "E", "text": "choit"}], "stem": "With what would you see a soprano doing the exact same performance multiple times?"}}
{"id": "6ed9e23231b59fa7e6572f98f9a51cad", "question": {"question_concept": "spoon", "choices": [{"label": "A", "text": "serving dish"}, {"label": "B", "text": "dishwasher"}, {"label": "C", "text": "glass of iced tea"}, {"label": "D", "text": "dinner"}, {"label": "E", "text": "lunch"}], "stem": "What would you pick up with a spoon"}}
{"id": "64f0ea7a464183f008983c6c10482136", "question": {"question_concept": "magazine", "choices": [{"label": "A", "text": "library"}, {"label": "B", "text": "bed"}, {"label": "C", "text": "bookstore"}, {"label": "D", "text": "shop"}, {"label": "E", "text": "magazine rack"}], "stem": "From where would you take a magazine home without paying?"}}
{"id": "e6e46bddd22054d29e5418ecf08f2909", "question": {"question_concept": "letter opener", "choices": [{"label": "A", "text": "office supply store"}, {"label": "B", "text": "stationery store"}, {"label": "C", "text": "storage room"}, {"label": "D", "text": "sharp"}, {"label": "E", "text": "dek"}], "stem": "Where can you a letter opener and a document shredder?"}}
{"id": "76b27670cba4f44a8ee9b34bd1030b7c", "question": {"question_concept": "ranch house", "choices": [{"label": "A", "text": "countryside"}, {"label": "B", "text": "town"}, {"label": "C", "text": "subdivision"}, {"label": "D", "text": "montana"}, {"label": "E", "text": "desert"}], "stem": "The ranch house was build in a gated community, where was it located?"}}
{"id": "e89d4ceb93cdd638d8c1db5755cbe892", "question": {"question_concept": "candle", "choices": [{"label": "A", "text": "light house"}, {"label": "B", "text": "burn brightly"}, {"label": "C", "text": "emit light"}, {"label": "D", "text": "wax build-up"}, {"label": "E", "text": "fire hazard"}], "stem": "What is a reason that you should be careful when using a candle?"}}
{"id": "0da335602a9f5f9fea0a79d5e746085d", "question": {"question_concept": "summer", "choices": [{"label": "A", "text": "cold"}, {"label": "B", "text": "weather"}, {"label": "C", "text": "nice"}, {"label": "D", "text": "winter"}, {"label": "E", "text": "fall"}], "stem": "Why do I need air conditioning in the summer?"}}
{"id": "e2949cc8dbfa8a2f176555a2a4556ed5", "question": {"question_concept": "comets", "choices": [{"label": "A", "text": "solid nucleus"}, {"label": "B", "text": "set orbits"}, {"label": "C", "text": "ice"}, {"label": "D", "text": "universe"}, {"label": "E", "text": "space"}], "stem": "Space objects need something to travel, what is it?"}}
{"id": "b0a19f9e45e314a9aff80f9ece6c7e84", "question": {"question_concept": "coal", "choices": [{"label": "A", "text": "bed"}, {"label": "B", "text": "fire"}, {"label": "C", "text": "under the tree"}, {"label": "D", "text": "underground"}, {"label": "E", "text": "stocking"}], "stem": "Where would you put coal if you do not want to give anything else to someone?"}}
{"id": "7ad5bd16e8bbd6632688fec8a699fde5", "question": {"question_concept": "talking", "choices": [{"label": "A", "text": "ask question"}, {"label": "B", "text": "think"}, {"label": "C", "text": "walking"}, {"label": "D", "text": "write"}, {"label": "E", "text": "sneeze"}], "stem": "What should a person do before talking?"}}
{"id": "3ca421f7c946fbcd376c177770bc4559", "question": {"question_concept": "car", "choices": [{"label": "A", "text": "rush away"}, {"label": "B", "text": "gain speed"}, {"label": "C", "text": "suffer damage"}, {"label": "D", "text": "brakes will be in frequent action"}, {"label": "E", "text": "go fast"}], "stem": "What could happen if you are not good at driving a car?"}}
{"id": "1c4cd8b5346c300b3bc0713d40802bcd", "question": {"question_concept": "chair", "choices": [{"label": "A", "text": "friend's house"}, {"label": "B", "text": "synagogue"}, {"label": "C", "text": "office"}, {"label": "D", "text": "auditorium"}, {"label": "E", "text": "at the beach"}], "stem": "Where do people sit in a chair to pray?"}}
{"id": "efda7a003898940f989b0599d57dcf77", "question": {"question_concept": "walking", "choices": [{"label": "A", "text": "blisters"}, {"label": "B", "text": "getting somewhere"}, {"label": "C", "text": "locomotion"}, {"label": "D", "text": "staying fit"}, {"label": "E", "text": "lose weight"}], "stem": "The man was walking many miles a day despite not enjoying it, what was his goal?"}}
{"id": "b2a15146ca85877d4c6299253a97a285", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "welcome change"}, {"label": "B", "text": "believe in god"}, {"label": "C", "text": "make choice"}, {"label": "D", "text": "hate each other"}, {"label": "E", "text": "talk to each other"}], "stem": "The man told the people the time for deliberation was over and they must what?"}}
{"id": "390ff70035f08854ccc394303b7de309", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "source of income"}, {"label": "B", "text": "fair trial"}, {"label": "C", "text": "to get a paycheck"}, {"label": "D", "text": "white teeth"}, {"label": "E", "text": "own house"}], "stem": "A person is working, why is he doing that?"}}
{"id": "8a302864a78cbdf6302f6965d43fd76b", "question": {"question_concept": "sun", "choices": [{"label": "A", "text": "shine brightly"}, {"label": "B", "text": "brown skin"}, {"label": "C", "text": "solar eclipse"}, {"label": "D", "text": "warm room"}, {"label": "E", "text": "dry ground"}], "stem": "What does the sun do that allows everything to be seen clearly?"}}
{"id": "4ba4704f07b6b94260db144c9a550f40", "question": {"question_concept": "working", "choices": [{"label": "A", "text": "concentration"}, {"label": "B", "text": "bonus"}, {"label": "C", "text": "energy"}, {"label": "D", "text": "ambition"}, {"label": "E", "text": "job"}], "stem": "The volunteer had been working very hard, the boss noticed and decided to offer him a what?"}}
{"id": "e442c6202814bb6d886e96138d36841c", "question": {"question_concept": "soldier", "choices": [{"label": "A", "text": "fight enemy"}, {"label": "B", "text": "report for duty"}, {"label": "C", "text": "guard border"}, {"label": "D", "text": "receive orders"}, {"label": "E", "text": "fight for freedom"}], "stem": "The soldier was listening intently, what was he achieving?"}}
{"id": "6af8f65914f793cca04594f4746a5493", "question": {"question_concept": "food", "choices": [{"label": "A", "text": "provide comfort"}, {"label": "B", "text": "lack of availability"}, {"label": "C", "text": "cost money"}, {"label": "D", "text": "increase in price"}, {"label": "E", "text": "cost lot"}], "stem": "Why is fresh food harder to get in the winter?"}}
{"id": "43e79adbca4bcb2cc1d1693636961ef1", "question": {"question_concept": "cardboard box", "choices": [{"label": "A", "text": "bathroom"}, {"label": "B", "text": "back alley"}, {"label": "C", "text": "shoe store"}, {"label": "D", "text": "warehouse"}, {"label": "E", "text": "storage area"}], "stem": "Where is someone likely to keep a cardboard box?"}}
{"id": "********************************", "question": {"question_concept": "run errands", "choices": [{"label": "A", "text": "complete tasks"}, {"label": "B", "text": "finished"}, {"label": "C", "text": "helpful"}, {"label": "D", "text": "supplies"}, {"label": "E", "text": "get things done"}], "stem": "She had to run errands, she wrote a list of them so she could mark off each time she what?"}}
{"id": "a58f47fb7651c4e982ad5fd7332f3623", "question": {"question_concept": "changing society", "choices": [{"label": "A", "text": "mouth"}, {"label": "B", "text": "understanding"}, {"label": "C", "text": "change yourself"}, {"label": "D", "text": "action"}, {"label": "E", "text": "revolution"}], "stem": "The group thought the were changing society, but in all honesty they didn't even have a full what of the issue?"}}
{"id": "89d61c6df56dfbb42115c416424f94c0", "question": {"question_concept": "going to play", "choices": [{"label": "A", "text": "rush"}, {"label": "B", "text": "happy"}, {"label": "C", "text": "sit"}, {"label": "D", "text": "meet"}, {"label": "E", "text": "being entertained"}], "stem": "What would a kid who is going to play not want to do?"}}
{"id": "0735cd64e9e6633036ac20c0eea026d9", "question": {"question_concept": "human", "choices": [{"label": "A", "text": "detroit"}, {"label": "B", "text": "elevator"}, {"label": "C", "text": "downtown"}, {"label": "D", "text": "space shuttle"}, {"label": "E", "text": "moon"}], "stem": "An android wants to become human in a place where the water quality is questionable.  Where is this place?"}}
{"id": "123abb29c3eabce97512c706df944895", "question": {"question_concept": "monkey", "choices": [{"label": "A", "text": "captivity"}, {"label": "B", "text": "barrel"}, {"label": "C", "text": "thailand"}, {"label": "D", "text": "south africa"}, {"label": "E", "text": "new mexico"}], "stem": "In what state of being are you most likely to see a monkey?"}}
{"id": "bc750462fdc2741200e336e6e977b234", "question": {"question_concept": "glass", "choices": [{"label": "A", "text": "table"}, {"label": "B", "text": "dishwasher"}, {"label": "C", "text": "window"}, {"label": "D", "text": "cabinet"}, {"label": "E", "text": "water cooler"}], "stem": "Glass is used to build these so you can see outside."}}
{"id": "bdd544ab468a0fc7642f013aaf486f05", "question": {"question_concept": "buying products", "choices": [{"label": "A", "text": "owning"}, {"label": "B", "text": "agony"}, {"label": "C", "text": "pleasure"}, {"label": "D", "text": "disagreements"}, {"label": "E", "text": "spending money"}], "stem": "What negative effect can happen when you are buying products that are dangerous and then use the products by yourself?"}}
{"id": "e4203cee7598be2e33d05e17a7cd6b2c", "question": {"question_concept": "cleaning", "choices": [{"label": "A", "text": "take trash out"}, {"label": "B", "text": "injury"}, {"label": "C", "text": "neatness"}, {"label": "D", "text": "getting tired"}, {"label": "E", "text": "allergies"}], "stem": "What could happen to you after cleaning for a long time?"}}
{"id": "8f7417ffb74e058bbf98a8929fdfd5ed", "question": {"question_concept": "computer user", "choices": [{"label": "A", "text": "office"}, {"label": "B", "text": "house"}, {"label": "C", "text": "office building"}, {"label": "D", "text": "school"}, {"label": "E", "text": "hell"}], "stem": "Where would a computer user use someone else's computer?"}}
{"id": "05d9acb5acdd07070eb6c5749f40bca1", "question": {"question_concept": "toilet", "choices": [{"label": "A", "text": "market"}, {"label": "B", "text": "apartment"}, {"label": "C", "text": "rest area"}, {"label": "D", "text": "hospital"}, {"label": "E", "text": "motel room"}], "stem": "Where could you find a toilet a large number of people use, but that would require permission to use?"}}
{"id": "eb42b61dcdafe19082304a5516b8c36e", "question": {"question_concept": "court", "choices": [{"label": "A", "text": "palace"}, {"label": "B", "text": "gymnasium"}, {"label": "C", "text": "school"}, {"label": "D", "text": "ontario"}, {"label": "E", "text": "public building"}], "stem": "Where is one likely to find a tennis court?"}}
{"id": "be28cb55ee9566bb1ec25bbc8f310024", "question": {"question_concept": "snake", "choices": [{"label": "A", "text": "nightmare"}, {"label": "B", "text": "tropics"}, {"label": "C", "text": "feild"}, {"label": "D", "text": "wyoming"}, {"label": "E", "text": "a feather"}], "stem": "John felt that there was an anaconda snake in his room.  He thought it slithered over him.   It was huge.  But when he turned on the lights there was no snake.  It was only what?"}}
{"id": "305d7ec91951db58e7bd5980718f431f", "question": {"question_concept": "jeans", "choices": [{"label": "A", "text": "bedroom"}, {"label": "B", "text": "laundromat"}, {"label": "C", "text": "closet"}, {"label": "D", "text": "prom"}, {"label": "E", "text": "shopping mall"}], "stem": "Where should you take your jeans if you can't clean them at home?"}}
{"id": "4cb6a36274c46e5c975b38a9ea55782f", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "like ice cream"}, {"label": "B", "text": "lower expectations"}, {"label": "C", "text": "dance well"}, {"label": "D", "text": "believe in god"}, {"label": "E", "text": "sleep"}], "stem": "When people are hot and seeking a cold treat, what do they do?"}}
{"id": "fd28207dc1418cfb60f06d41760b1268", "question": {"question_concept": "title", "choices": [{"label": "A", "text": "describing"}, {"label": "B", "text": "magazine article"}, {"label": "C", "text": "safe deposit box"}, {"label": "D", "text": "shoe"}, {"label": "E", "text": "library"}], "stem": "If somebody just bought a new car, they might keep the title secure in a what?"}}
{"id": "63fec9936cf86ded4224881945aea93f", "question": {"question_concept": "bus station", "choices": [{"label": "A", "text": "street"}, {"label": "B", "text": "signs"}, {"label": "C", "text": "busy city"}, {"label": "D", "text": "city centre"}, {"label": "E", "text": "maps"}], "stem": "What are found at every bus station that show you how to get around?"}}
{"id": "6f00a6ae7cf38be4915b025f92f27135", "question": {"question_concept": "grid", "choices": [{"label": "A", "text": "template"}, {"label": "B", "text": "graph paper"}, {"label": "C", "text": "mathematics"}, {"label": "D", "text": "electrical system"}, {"label": "E", "text": "coordinate plane"}], "stem": "What is likely to be full of small grids?"}}
{"id": "7f1d80b9ccb9617c347ebb8e681a610e", "question": {"question_concept": "orange juice", "choices": [{"label": "A", "text": "carton"}, {"label": "B", "text": "fridge"}, {"label": "C", "text": "pulp in"}, {"label": "D", "text": "vitamin c"}, {"label": "E", "text": "refrigerator"}], "stem": "What is orange juice often kept in?"}}
{"id": "0bd0b376a6c139ca18841e949ed0efe5", "question": {"question_concept": "can of soda", "choices": [{"label": "A", "text": "vending machine"}, {"label": "B", "text": "restrooms"}, {"label": "C", "text": "picnic cooler"}, {"label": "D", "text": "store"}, {"label": "E", "text": "liquid"}], "stem": "Where can you buy a can of soda while at a highway rest stop?"}}
{"id": "77c16232cc9261bc4bbb4a3648339f49", "question": {"question_concept": "going to bed", "choices": [{"label": "A", "text": "get pregnant"}, {"label": "B", "text": "monsters"}, {"label": "C", "text": "sleepiness"}, {"label": "D", "text": "bad dreams"}, {"label": "E", "text": "insomnia"}], "stem": "Why are children scared of going to bed?"}}
{"id": "1f343bc5d0bace3534f458d2f41a67d5", "question": {"question_concept": "relaxing", "choices": [{"label": "A", "text": "listening to music"}, {"label": "B", "text": "flying"}, {"label": "C", "text": "reading"}, {"label": "D", "text": "falling asleep"}, {"label": "E", "text": "listen to music"}], "stem": "She enjoyed relaxing and being taken away into another world, she spent a lot of time doing what?"}}
{"id": "dd739adc57acd93b3cad4aeb87f3602e", "question": {"question_concept": "cashing in", "choices": [{"label": "A", "text": "increase in money"}, {"label": "B", "text": "making money"}, {"label": "C", "text": "drink hot drinks"}, {"label": "D", "text": "getting money"}, {"label": "E", "text": "get rich"}], "stem": "What happens when cashing in a lot of chips at a casino?"}}
{"id": "f30ccf756f489ff1a35c32116a94c624", "question": {"question_concept": "gazelle", "choices": [{"label": "A", "text": "asia"}, {"label": "B", "text": "television program"}, {"label": "C", "text": "eastern hemisphere"}, {"label": "D", "text": "open plain"}, {"label": "E", "text": "great outdoors"}], "stem": "You're like to find a gazelle in Africa or Asia, both of which are where?"}}
{"id": "31f9bf65926574c9a6dd30a2fdf25047", "question": {"question_concept": "junk mail", "choices": [{"label": "A", "text": "desk"}, {"label": "B", "text": "mail box"}, {"label": "C", "text": "waste bin"}, {"label": "D", "text": "trash"}, {"label": "E", "text": "post office"}], "stem": "My boss is a messy man, where does he carelessly toss all of his junk mail?"}}
{"id": "3b5d321d08bdb1d3247b7dd59b6dce17", "question": {"question_concept": "hooker", "choices": [{"label": "A", "text": "town"}, {"label": "B", "text": "at hotel"}, {"label": "C", "text": "at hotel"}, {"label": "D", "text": "corner of two streets"}, {"label": "E", "text": "street corner"}], "stem": "Where would a hooker give services?"}}
{"id": "1a866fc699a5a6a70995c406737dd908", "question": {"question_concept": "paper towels", "choices": [{"label": "A", "text": "cabinet"}, {"label": "B", "text": "grocery store"}, {"label": "C", "text": "pantry"}, {"label": "D", "text": "waste bin"}, {"label": "E", "text": "locker room"}], "stem": "The paper towels are sometimes kept in the office, where did the dad look for them?"}}
{"id": "e8d41d7f57974294aa7210873c6b53fd", "question": {"question_concept": "standing in line", "choices": [{"label": "A", "text": "anger"}, {"label": "B", "text": "parking ticket"}, {"label": "C", "text": "wait turn"}, {"label": "D", "text": "order"}, {"label": "E", "text": "fatigue"}], "stem": "If you're standing in line for a long time what will you likely experience?"}}
{"id": "f52ce7e62d99c29aac88b0b4268fd546", "question": {"question_concept": "garden hose", "choices": [{"label": "A", "text": "front yard"}, {"label": "B", "text": "bedroom"}, {"label": "C", "text": "garage"}, {"label": "D", "text": "backyard"}, {"label": "E", "text": "back yard"}], "stem": "Where is an unused garden hose likely stored?"}}
{"id": "d6144b08728acb3252d62b54ca946d8c", "question": {"question_concept": "sale", "choices": [{"label": "A", "text": "k mart"}, {"label": "B", "text": "outlet store"}, {"label": "C", "text": "department store"}, {"label": "D", "text": "clothing store"}, {"label": "E", "text": "classified adverisements"}], "stem": "Where can someone get a sweater on sale?"}}
{"id": "92eb20796bc60593751040c2eef90f90", "question": {"question_concept": "tools", "choices": [{"label": "A", "text": "neighbor's house"}, {"label": "B", "text": "grocery store"}, {"label": "C", "text": "drawer"}, {"label": "D", "text": "garage"}, {"label": "E", "text": "repair shop"}], "stem": "The mechanic has many tools at his?"}}
{"id": "a5a94b02b0fb9359159bfa70390acbd7", "question": {"question_concept": "affair", "choices": [{"label": "A", "text": "discipline"}, {"label": "B", "text": "divorce"}, {"label": "C", "text": "marriage"}, {"label": "D", "text": "relationship"}, {"label": "E", "text": "fidelity"}], "stem": "Mandy had an affair with Chris while wed  to Robert.  Mandy broke her promise of what?"}}
{"id": "e6050629eb052764030e252973e40976", "question": {"question_concept": "working", "choices": [{"label": "A", "text": "blisters"}, {"label": "B", "text": "getting paid"}, {"label": "C", "text": "creation"}, {"label": "D", "text": "make money"}, {"label": "E", "text": "making money"}], "stem": "When you're working for someone else what should you expect?"}}
{"id": "e6abf05fe7dea9997692461560a35a17", "question": {"question_concept": "discovering truth", "choices": [{"label": "A", "text": "learning"}, {"label": "B", "text": "calling"}, {"label": "C", "text": "feeling hurt"}, {"label": "D", "text": "asking questions"}, {"label": "E", "text": "crying"}], "stem": "The reporter was set on discovering truth, he tracked down leads and kept what?"}}
{"id": "e58eb0ec4197c29e961a7bdd4d67de4e_1", "question": {"question_concept": "competing", "choices": [{"label": "A", "text": "defeat"}, {"label": "B", "text": "aggression"}, {"label": "C", "text": "sweat"}, {"label": "D", "text": "race"}, {"label": "E", "text": "winning or losing"}], "stem": "He believed in competing honestly, being humble in victory and gracious in what?"}}
{"id": "7cd143a621339234947098d6b3a1f343", "question": {"question_concept": "chocolate", "choices": [{"label": "A", "text": "box"}, {"label": "B", "text": "movies"}, {"label": "C", "text": "supermarket"}, {"label": "D", "text": "mouth"}, {"label": "E", "text": "restaurant"}], "stem": "Where would I put some chocolate after buying it?"}}
{"id": "bc8327e16bba023dd651816180d178d4", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "talk to each other"}, {"label": "B", "text": "waste paper"}, {"label": "C", "text": "travel abroad"}, {"label": "D", "text": "change direction"}, {"label": "E", "text": "keep goin"}], "stem": "If people start going the wrong way what must they do?"}}
{"id": "65acf7c01dd3e7745ff7d7ad0d63f911", "question": {"question_concept": "rescue", "choices": [{"label": "A", "text": "arrest"}, {"label": "B", "text": "kidnap"}, {"label": "C", "text": "corrupt"}, {"label": "D", "text": "finish"}, {"label": "E", "text": "abandon"}], "stem": "The rescue was very difficult, but id led to what?"}}
{"id": "6991339de4302a6acbe1ba29c15860eb", "question": {"question_concept": "apple tree", "choices": [{"label": "A", "text": "spring"}, {"label": "B", "text": "valley"}, {"label": "C", "text": "new york"}, {"label": "D", "text": "summer"}, {"label": "E", "text": "fall"}], "stem": "The fruit of an apple tree becomes ripe when?"}}
{"id": "2d349f2faa1354c3f8beeb8cb14bf317", "question": {"question_concept": "praying", "choices": [{"label": "A", "text": "religiosity"}, {"label": "B", "text": "feel better"}, {"label": "C", "text": "pain free"}, {"label": "D", "text": "relief"}, {"label": "E", "text": "feeling better"}], "stem": "He thought of his recovering sick mother and began praying, he hoped that she was what?"}}
{"id": "6756267f644f22b0289b3b8610d130fb", "question": {"question_concept": "skate", "choices": [{"label": "A", "text": "falling down"}, {"label": "B", "text": "romance"}, {"label": "C", "text": "jumping around"}, {"label": "D", "text": "grab side railing"}, {"label": "E", "text": "spin"}], "stem": "What is a common emotion that takes place during a date at a skate rink?"}}
{"id": "858f75e8b59f0c85145abea802b4f27d", "question": {"question_concept": "chess board", "choices": [{"label": "A", "text": "internet"}, {"label": "B", "text": "park"}, {"label": "C", "text": "retirement community"}, {"label": "D", "text": "cabinet"}, {"label": "E", "text": "library"}], "stem": "Where could you find out how to use a chess board?"}}
{"id": "d9fd0b1631c1ab93a39d4c11a71331e5", "question": {"question_concept": "flowers", "choices": [{"label": "A", "text": "plant themselves"}, {"label": "B", "text": "continue to grow"}, {"label": "C", "text": "drawn"}, {"label": "D", "text": "many colors"}, {"label": "E", "text": "smell good"}], "stem": "What would happen to flowers if they are watered?"}}
{"id": "1b7d859e723437af25854d12f332c51e", "question": {"question_concept": "judging", "choices": [{"label": "A", "text": "lot of pain"}, {"label": "B", "text": "feeling bad"}, {"label": "C", "text": "controversy"}, {"label": "D", "text": "being blind about other people"}, {"label": "E", "text": "hurt feelings"}], "stem": "What is constantly judging others likely to lead to?"}}
{"id": "d53ac158543a16c054f8e5d8912ff0bc", "question": {"question_concept": "beauty salon", "choices": [{"label": "A", "text": "beautifying hair"}, {"label": "B", "text": "strip mall"}, {"label": "C", "text": "functions"}, {"label": "D", "text": "neighborhood"}, {"label": "E", "text": "clerk"}], "stem": "A new beauty salon had just opened up in her what, so she decided to walk over and check it out?"}}
{"id": "a08ae1f55e872ec5d9c2a14a4b2ced1c", "question": {"question_concept": "visiting museum", "choices": [{"label": "A", "text": "seeing artifacts"}, {"label": "B", "text": "education"}, {"label": "C", "text": "peace"}, {"label": "D", "text": "wonder"}, {"label": "E", "text": "being bored"}], "stem": "Visiting museum that is a memorial to war made the person appreciate what?"}}
{"id": "fb3e5c284e6c222345cd0222082cecfa", "question": {"question_concept": "happiness", "choices": [{"label": "A", "text": "live life"}, {"label": "B", "text": "play games"}, {"label": "C", "text": "jump in a puddle"}, {"label": "D", "text": "fiddle"}, {"label": "E", "text": "sing"}], "stem": "In the shower many find happiness with what activity?"}}
{"id": "bd64d02c00bcac6d11f1a4574a8957f0", "question": {"question_concept": "killing", "choices": [{"label": "A", "text": "suicide"}, {"label": "B", "text": "death of"}, {"label": "C", "text": "angry"}, {"label": "D", "text": "grief"}, {"label": "E", "text": "not living"}], "stem": "When the dad discovered the killing of his son, what did he feel?"}}
{"id": "ae74c12e30a5a4d6536fac0e82a7424a", "question": {"question_concept": "water", "choices": [{"label": "A", "text": "dribble"}, {"label": "B", "text": "wet clothes"}, {"label": "C", "text": "drink"}, {"label": "D", "text": "thin soup"}, {"label": "E", "text": "power turbine"}], "stem": "If you use too much water when preparing nourishment what may you end up with?"}}
{"id": "93c929a5e786422facac96d31b07b3b2", "question": {"question_concept": "trunk", "choices": [{"label": "A", "text": "kitchen"}, {"label": "B", "text": "automobile"}, {"label": "C", "text": "bedroom"}, {"label": "D", "text": "car"}, {"label": "E", "text": "zoo"}], "stem": "Where would you find a trunk containing a spare blanket or pillow?"}}
{"id": "f4471a8ea531bdd319ec6fd7ec78ed65", "question": {"question_concept": "adult", "choices": [{"label": "A", "text": "pay bills"}, {"label": "B", "text": "work"}, {"label": "C", "text": "dress himself"}, {"label": "D", "text": "dress herself"}, {"label": "E", "text": "drink beer"}], "stem": "What do most adults have to do?"}}
{"id": "61d0e01a9c6de4cfc3193f81e981c904", "question": {"question_concept": "playing chess", "choices": [{"label": "A", "text": "forgiveness"}, {"label": "B", "text": "headaches"}, {"label": "C", "text": "boredom"}, {"label": "D", "text": "humility"}, {"label": "E", "text": "frustration"}], "stem": "Joe enjoyed playing chess, but he was not a master.  He lost often.  This taught him something. What did it teach him?"}}
{"id": "e75db3ed7e3ea670df3da47400787d73", "question": {"question_concept": "dogs", "choices": [{"label": "A", "text": "do tricks"}, {"label": "B", "text": "bark"}, {"label": "C", "text": "jump up"}, {"label": "D", "text": "do many things"}, {"label": "E", "text": "own people"}], "stem": "Why do people think dogs are so smart?"}}
{"id": "2c150e59838e17e8889493439d84a18a", "question": {"question_concept": "cup", "choices": [{"label": "A", "text": "apartment"}, {"label": "B", "text": "restaurant"}, {"label": "C", "text": "kitchen cabinet"}, {"label": "D", "text": "driveway"}, {"label": "E", "text": "dishwasher"}], "stem": "Where do you have someone who is always refilling your cup?"}}
{"id": "346ece7dfa9695a98fade1d2401a3c09", "question": {"question_concept": "love", "choices": [{"label": "A", "text": "contagious"}, {"label": "B", "text": "last forever"}, {"label": "C", "text": "balance scales"}, {"label": "D", "text": "painful"}, {"label": "E", "text": "blind"}], "stem": "Love, like justice, can be said to be what?"}}
{"id": "a73d40fc2c189d34fbdc4dfd70db6b8a", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "meet expectations"}, {"label": "B", "text": "have enough food"}, {"label": "C", "text": "sleeping"}, {"label": "D", "text": "own house"}, {"label": "E", "text": "knowledgable"}], "stem": "Why is a person not hungry?"}}
{"id": "9634281c655779e843a56aa1ee09e79a", "question": {"question_concept": "standing in line", "choices": [{"label": "A", "text": "upset"}, {"label": "B", "text": "agitation"}, {"label": "C", "text": "fainting"}, {"label": "D", "text": "frustration"}, {"label": "E", "text": "boredom"}], "stem": "What does standing in line for a long time lead to?"}}
{"id": "3c0c20a7234a7ce62902e5e9a58bf33d", "question": {"question_concept": "dance", "choices": [{"label": "A", "text": "partner"}, {"label": "B", "text": "celebrate"}, {"label": "C", "text": "sitting still"}, {"label": "D", "text": "like dancing"}, {"label": "E", "text": "moving"}], "stem": "The instructor was teaching dance, she started the class turning on music and telling everybody to just start what?"}}
{"id": "ccdddee8fb94797d0839befd4e1ae40e", "question": {"question_concept": "village", "choices": [{"label": "A", "text": "third world countries"}, {"label": "B", "text": "big city"}, {"label": "C", "text": "ohio"}, {"label": "D", "text": "rural area"}, {"label": "E", "text": "africa"}], "stem": "Where are villages typically located?"}}
{"id": "7eef0b3ded8e872cb6b21acab5feaa7e", "question": {"question_concept": "wash clothes", "choices": [{"label": "A", "text": "bubble bath"}, {"label": "B", "text": "use water"}, {"label": "C", "text": "use soap"}, {"label": "D", "text": "get dirty"}, {"label": "E", "text": "gather up"}], "stem": "After she finished washing clothes, what did the woman do with them?"}}
{"id": "a522f36d0afb3f11dc5ee1123c7685fc", "question": {"question_concept": "table", "choices": [{"label": "A", "text": "rug"}, {"label": "B", "text": "corner"}, {"label": "C", "text": "demonstration"}, {"label": "D", "text": "library"}, {"label": "E", "text": "under the bed"}], "stem": "Where can you find more than one table?"}}
{"id": "e3c9699b46e09e3ae60dd247b9818ce7", "question": {"question_concept": "traveling", "choices": [{"label": "A", "text": "motion sickness"}, {"label": "B", "text": "fatigue"}, {"label": "C", "text": "jet lag"}, {"label": "D", "text": "school"}, {"label": "E", "text": "relocation"}], "stem": "Why do people traveling from one place to another and bring bed and other furniture with them?"}}
{"id": "c618331ce270b929cb00872a56e5845a_1", "question": {"question_concept": "toilet", "choices": [{"label": "A", "text": "stadium"}, {"label": "B", "text": "house"}, {"label": "C", "text": "school"}, {"label": "D", "text": "space shuttle"}, {"label": "E", "text": "restaurant"}], "stem": "The service was incredibly slow, he used the toilet twice during the what for his order where?"}}
{"id": "961e8f1d17cd8c68b3d05aed557a9c3a", "question": {"question_concept": "noise", "choices": [{"label": "A", "text": "carnival"}, {"label": "B", "text": "big city"}, {"label": "C", "text": "factory"}, {"label": "D", "text": "classroom"}, {"label": "E", "text": "concert"}], "stem": "There is a lot of noise in what group of people's homes and businesses?"}}
{"id": "26c1497e4026356a97416dc8a6334667", "question": {"question_concept": "harpsichord", "choices": [{"label": "A", "text": "concert hall"}, {"label": "B", "text": "make music"}, {"label": "C", "text": "music shop"}, {"label": "D", "text": "museum"}, {"label": "E", "text": "band"}], "stem": "What is a harpsichord likely to be a part of?"}}
{"id": "7ef358fa222e8a4d32dbf94b2a889baa", "question": {"question_concept": "getting up early", "choices": [{"label": "A", "text": "starting early"}, {"label": "B", "text": "bloodshot eyes"}, {"label": "C", "text": "good weather"}, {"label": "D", "text": "feeling tired"}, {"label": "E", "text": "sleepiness"}], "stem": "Getting up early is about the only way you're going to be able to get what on a project?"}}
{"id": "abf9ffb83096337e77a8327d256632f5", "question": {"question_concept": "icebox", "choices": [{"label": "A", "text": "antique store"}, {"label": "B", "text": "kitchen"}, {"label": "C", "text": "junk yard"}, {"label": "D", "text": "house"}, {"label": "E", "text": "basement"}], "stem": "Where would you find the icebox in your home?"}}
{"id": "********************************", "question": {"question_concept": "socialising", "choices": [{"label": "A", "text": "enjoy himself"}, {"label": "B", "text": "making friends"}, {"label": "C", "text": "anxiety"}, {"label": "D", "text": "having fun"}, {"label": "E", "text": "have fun"}], "stem": "Danny was great at socializing.  He found it easy to do what?"}}
{"id": "31d1daee2ca5aa7666b6c20ceee7489d", "question": {"question_concept": "water", "choices": [{"label": "A", "text": "pool"}, {"label": "B", "text": "soup"}, {"label": "C", "text": "puddle"}, {"label": "D", "text": "glass"}, {"label": "E", "text": "sink"}], "stem": "What can I put water in, for drinking?"}}
{"id": "4ffe455df39aef3d817e56afd5dc8df0", "question": {"question_concept": "killing people", "choices": [{"label": "A", "text": "feelings of guilt"}, {"label": "B", "text": "going to the slammer"}, {"label": "C", "text": "jailed"}, {"label": "D", "text": "prison sentence"}, {"label": "E", "text": "going to jail"}], "stem": "What happens to someone after being caught killing people?"}}
{"id": "198d038d5105e7810948e5bbfa2d1096", "question": {"question_concept": "bureau", "choices": [{"label": "A", "text": "home office"}, {"label": "B", "text": "office building"}, {"label": "C", "text": "french government"}, {"label": "D", "text": "bedroom"}, {"label": "E", "text": "bedroom"}], "stem": "Where is a good place for a bureau?"}}
{"id": "4fc4ffec37124cdfe7245f0f4a816c4b", "question": {"question_concept": "kids", "choices": [{"label": "A", "text": "color"}, {"label": "B", "text": "become adults"}, {"label": "C", "text": "wonder about"}, {"label": "D", "text": "open door"}, {"label": "E", "text": "distracting"}], "stem": "How do kids form ideas about unknown things?"}}
{"id": "bcb09fcd6fadd79356f80d9e0da5b139", "question": {"question_concept": "most people", "choices": [{"label": "A", "text": "ears"}, {"label": "B", "text": "two eyes"}, {"label": "C", "text": "two arms"}, {"label": "D", "text": "two hands"}, {"label": "E", "text": "fingers"}], "stem": "What do most people have that help them to read?"}}
{"id": "6969c77289aab12ab399f2783ac56a60", "question": {"question_concept": "dog", "choices": [{"label": "A", "text": "four legs"}, {"label": "B", "text": "feet"}, {"label": "C", "text": "teeth"}, {"label": "D", "text": "paws"}, {"label": "E", "text": "two ears"}], "stem": "If a dog is very active, it gets around a lot on what?"}}
{"id": "507f6fd160b9ca199c39f2d5be58aa85", "question": {"question_concept": "ficus", "choices": [{"label": "A", "text": "large pot"}, {"label": "B", "text": "park"}, {"label": "C", "text": "street"}, {"label": "D", "text": "tropical forest"}, {"label": "E", "text": "green house"}], "stem": "Vance wanted to buy a big ficus.  Where might he have looked for one?"}}
{"id": "88a1d42f52a5f64a174d52e3dc96da9b", "question": {"question_concept": "shark", "choices": [{"label": "A", "text": "court room"}, {"label": "B", "text": "pacific ocean"}, {"label": "C", "text": "shallow waters"}, {"label": "D", "text": "poker game"}, {"label": "E", "text": "sand box"}], "stem": "The shark got stuck in the sand, where was he?"}}
{"id": "02cc3cd2b98f6b3dfa63b49dc51f0125", "question": {"question_concept": "moving car", "choices": [{"label": "A", "text": "force"}, {"label": "B", "text": "energy"}, {"label": "C", "text": "strength"}, {"label": "D", "text": "obesity"}, {"label": "E", "text": "drive"}], "stem": "Gasoline is a polluting way of moving car, many hope to replace it one day with a cleaner what?"}}
{"id": "44e6167c3a2e91a671f267a7182f9d29", "question": {"question_concept": "office building", "choices": [{"label": "A", "text": "high tower"}, {"label": "B", "text": "city"}, {"label": "C", "text": "downtown area"}, {"label": "D", "text": "business park"}, {"label": "E", "text": "industrial complex"}], "stem": "WHere are most office buildings located?"}}
{"id": "e09bdf84babdcedc7c9a3bc332138add", "question": {"question_concept": "city", "choices": [{"label": "A", "text": "county"}, {"label": "B", "text": "wilderness"}, {"label": "C", "text": "united states"}, {"label": "D", "text": "germany"}, {"label": "E", "text": "meadow"}], "stem": "A city is surrounded by lots of different countries within a few hours drive, where is it located?"}}
{"id": "4241c618ad14e404005bae050d808142", "question": {"question_concept": "weasel", "choices": [{"label": "A", "text": "law office"}, {"label": "B", "text": "cartoon"}, {"label": "C", "text": "woodland"}, {"label": "D", "text": "own home"}, {"label": "E", "text": "ferret"}], "stem": "I am Weasel could talk.  Since most weasels can't talk, he was probably what sort of thing?"}}
{"id": "ddbb83cc14429e67d6a391827cecfdae", "question": {"question_concept": "dog", "choices": [{"label": "A", "text": "dog pound"}, {"label": "B", "text": "back yard"}, {"label": "C", "text": "farmyard"}, {"label": "D", "text": "park"}, {"label": "E", "text": "neighbor's house"}], "stem": "If a dog escaped from your property, where would be the closest place to look?"}}
{"id": "305eb7fbf0257e009c40a00bfeb62110", "question": {"question_concept": "start family", "choices": [{"label": "A", "text": "sleep"}, {"label": "B", "text": "stressed"}, {"label": "C", "text": "have sex"}, {"label": "D", "text": "need more money"}, {"label": "E", "text": "spend money"}], "stem": "What will someone have to do once they have began to start a family?"}}
{"id": "fbd32871913d534e3371e1672e78e164", "question": {"question_concept": "passing sentence", "choices": [{"label": "A", "text": "appeals"}, {"label": "B", "text": "death"}, {"label": "C", "text": "loss"}, {"label": "D", "text": "anger"}, {"label": "E", "text": "grief"}], "stem": "When James was passing sentence on the killer, he took into account something about the survivors.  What might James have taken into account?"}}
{"id": "a6ebd2b924e3863b5aa7ac8032daf25b", "question": {"question_concept": "peaceful", "choices": [{"label": "A", "text": "violent"}, {"label": "B", "text": "belligerent"}, {"label": "C", "text": "nonsense"}, {"label": "D", "text": "warring"}, {"label": "E", "text": "nonpeaceful"}], "stem": "The protest was peaceful, but what did it become after someone started throwing rocks?"}}
{"id": "4b06d650a437e367b9f077be054ee005", "question": {"question_concept": "pennies", "choices": [{"label": "A", "text": "water fountain"}, {"label": "B", "text": "pocket"}, {"label": "C", "text": "purse"}, {"label": "D", "text": "drawer"}, {"label": "E", "text": "jar"}], "stem": "Where would you put some pennies if you do not plan to use them soon?"}}
{"id": "6497e30a12e2b9172360e14250f0d878", "question": {"question_concept": "odd", "choices": [{"label": "A", "text": "normal"}, {"label": "B", "text": "frequent"}, {"label": "C", "text": "habitual"}, {"label": "D", "text": "common"}, {"label": "E", "text": "regular"}], "stem": "The odd things kept happening to him that day, their happenings became what?"}}
{"id": "7c2464c8b30cf987f4625be73def05ee", "question": {"question_concept": "staircase", "choices": [{"label": "A", "text": "multistory building"}, {"label": "B", "text": "mansion"}, {"label": "C", "text": "school"}, {"label": "D", "text": "cellar"}, {"label": "E", "text": "library"}], "stem": "Where are you if you go up a staircase before hearing a lecture?"}}
{"id": "727ea894ef83f61674c4bd1f8da3b031", "question": {"question_concept": "learning", "choices": [{"label": "A", "text": "gaining knowledge"}, {"label": "B", "text": "knowing more"}, {"label": "C", "text": "anger"}, {"label": "D", "text": "growth"}, {"label": "E", "text": "headaches"}], "stem": "What does learning accomplish?"}}
{"id": "0615ba4a7b8dca151eb6e92749f147d2", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "catch cold"}, {"label": "B", "text": "deceive himself"}, {"label": "C", "text": "take a nap"}, {"label": "D", "text": "experience pain"}, {"label": "E", "text": "absorb moisture"}], "stem": "The person used a sponge, what were they trying to do?"}}
{"id": "899a9a65d3abe7ac507266d5612dba01", "question": {"question_concept": "litter", "choices": [{"label": "A", "text": "roadsides"}, {"label": "B", "text": "street corner"}, {"label": "C", "text": "park"}, {"label": "D", "text": "ground"}, {"label": "E", "text": "playground"}], "stem": "Where can litter ruin your bbq or Frisbee game?"}}
{"id": "9dbb818a6a343b6fb98807f05ff3ee29", "question": {"question_concept": "rocking chair", "choices": [{"label": "A", "text": "old folks home"}, {"label": "B", "text": "the womb"}, {"label": "C", "text": "child's room"}, {"label": "D", "text": "front porch"}, {"label": "E", "text": "parlor"}], "stem": "Where can you find a younger woman in a rocking chair?"}}
{"id": "31f43125aabe5653a1eda342a61aeb82", "question": {"question_concept": "french horn", "choices": [{"label": "A", "text": "music class"}, {"label": "B", "text": "music store"}, {"label": "C", "text": "participate in orchestra"}, {"label": "D", "text": "parade"}, {"label": "E", "text": "concert hall"}], "stem": "Where would you hear a french horn play?"}}
{"id": "563de3554defac91d0699c683908398c", "question": {"question_concept": "empower", "choices": [{"label": "A", "text": "dishearten"}, {"label": "B", "text": "prohibit"}, {"label": "C", "text": "belittle"}, {"label": "D", "text": "forbid"}, {"label": "E", "text": "ban"}], "stem": "What might someone do to rights to stop someone from feeling empowered?"}}
{"id": "2fdd987a56351f5177b2117267c77399", "question": {"question_concept": "decanter", "choices": [{"label": "A", "text": "restaurant"}, {"label": "B", "text": "chemistry lab"}, {"label": "C", "text": "kitchen cupboard"}, {"label": "D", "text": "brewery"}, {"label": "E", "text": "mens club"}], "stem": "If a place specializes in drinks that requier a decanter, what is it unlikely to be?"}}
{"id": "6adbf046ba4652eecd8cd7509f504d5a", "question": {"question_concept": "room", "choices": [{"label": "A", "text": "kitchen"}, {"label": "B", "text": "outside"}, {"label": "C", "text": "wireroom"}, {"label": "D", "text": "understand themselves"}, {"label": "E", "text": "hallway"}], "stem": "He was locked in a room against his will, where did he long to be?"}}
{"id": "95f86a6f1de9b29b96ee5d7d054864a2", "question": {"question_concept": "passageway", "choices": [{"label": "A", "text": "airport"}, {"label": "B", "text": "cave"}, {"label": "C", "text": "maze"}, {"label": "D", "text": "store"}, {"label": "E", "text": "house"}], "stem": "Passengers are walking the passageway to an airplane, where are they?"}}
{"id": "a5542221fb2a633c43b9c983aa0806e4", "question": {"question_concept": "girlfriend", "choices": [{"label": "A", "text": "enemy"}, {"label": "B", "text": "foe"}, {"label": "C", "text": "buddy"}, {"label": "D", "text": "friend"}, {"label": "E", "text": "comrade"}], "stem": "Ever since he got with his girlfriend he slowly lost all his friends, she was now his only what?"}}
{"id": "3a2e8c6d8241cc272280a3c8efd8f969", "question": {"question_concept": "work", "choices": [{"label": "A", "text": "have job"}, {"label": "B", "text": "get dressed"}, {"label": "C", "text": "concentrate"}, {"label": "D", "text": "get going"}, {"label": "E", "text": "make money"}], "stem": "What do you have to do while you're at work?"}}
{"id": "5a8ddb276c52d37fbcd6004a3fe4e614", "question": {"question_concept": "doing housework", "choices": [{"label": "A", "text": "tiredness"}, {"label": "B", "text": "backache"}, {"label": "C", "text": "get tired"}, {"label": "D", "text": "headache"}, {"label": "E", "text": "sneezing"}], "stem": "If you have to bend over frequently while doing housework, what are you likely to experience?"}}
{"id": "f81c78911520f7ee32b7a09abd35d249", "question": {"question_concept": "bullet", "choices": [{"label": "A", "text": "bandolier"}, {"label": "B", "text": "bullet store"}, {"label": "C", "text": "casing"}, {"label": "D", "text": "gun shop"}, {"label": "E", "text": "magazine"}], "stem": "Where can you buy a bullet?"}}
{"id": "a749db34e710945120155ace0fecb965", "question": {"question_concept": "talking", "choices": [{"label": "A", "text": "presentation"}, {"label": "B", "text": "voice"}, {"label": "C", "text": "being alive"}, {"label": "D", "text": "tongue"}, {"label": "E", "text": "speech"}], "stem": "Talking in front of a class means you are delivering a?"}}
{"id": "776aa3de19bcd9d07dfd196dd1657817", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "experience pleasure"}, {"label": "B", "text": "value life"}, {"label": "C", "text": "wash dishes"}, {"label": "D", "text": "go into debt"}, {"label": "E", "text": "catch cold"}], "stem": "The person spent more money than he had, what is the result?"}}
{"id": "fd5ecbfe7061f2e1f55c4d70a0541684", "question": {"question_concept": "spine", "choices": [{"label": "A", "text": "skeleton"}, {"label": "B", "text": "ribcage"}, {"label": "C", "text": "back"}, {"label": "D", "text": "human body"}, {"label": "E", "text": "book"}], "stem": "What is the spine a part of in the human body?"}}
{"id": "9ab2f6ed68f47a467ca0b468491783e8", "question": {"question_concept": "reaching tentative agreement", "choices": [{"label": "A", "text": "relax"}, {"label": "B", "text": "calmness"}, {"label": "C", "text": "breathe"}, {"label": "D", "text": "uncertainty"}, {"label": "E", "text": "satisfaction"}], "stem": "What can a player do after reaching a tentative agreement?"}}
{"id": "e4c035ca674187417feda8a48499e824", "question": {"question_concept": "marmot", "choices": [{"label": "A", "text": "ontario"}, {"label": "B", "text": "north america"}, {"label": "C", "text": "sierra nevada mountains"}, {"label": "D", "text": "mountainous region"}, {"label": "E", "text": "great outdoors"}], "stem": "If you see a marmot on a flat plain, you're probably where?"}}
{"id": "44fc31666c8948dff76073fc61216614", "question": {"question_concept": "mountain", "choices": [{"label": "A", "text": "another mountain"}, {"label": "B", "text": "molehill"}, {"label": "C", "text": "flat land"}, {"label": "D", "text": "valley"}, {"label": "E", "text": "prairie"}], "stem": "What is the opposite of mountain?"}}
{"id": "6840dda956bad3a218b75c00bf31b88a", "question": {"question_concept": "having heart attack", "choices": [{"label": "A", "text": "bypass surgery"}, {"label": "B", "text": "recover"}, {"label": "C", "text": "fear of death"}, {"label": "D", "text": "consuming pain killer"}, {"label": "E", "text": "loss of life"}], "stem": "What is the best result of having a heart attack?"}}
{"id": "50a6b5deecffa5eee9129cdf75371232", "question": {"question_concept": "resting", "choices": [{"label": "A", "text": "healing"}, {"label": "B", "text": "feeling better"}, {"label": "C", "text": "relaxation"}, {"label": "D", "text": "recuperation"}, {"label": "E", "text": "doing nothing"}], "stem": "After surgery the lady was resting, it was part of her what?"}}
{"id": "687cae0a8e97186060ffdefc2d5c0adf", "question": {"question_concept": "reproducing", "choices": [{"label": "A", "text": "marry"}, {"label": "B", "text": "have sex"}, {"label": "C", "text": "kiss"}, {"label": "D", "text": "birth of new person"}, {"label": "E", "text": "genetic mutation"}], "stem": "How does a person begin to attract another person for reproducing?"}}
{"id": "89cf15542e98e890baa21260ca92c412", "question": {"question_concept": "plant", "choices": [{"label": "A", "text": "bloom"}, {"label": "B", "text": "flower petals"}, {"label": "C", "text": "with a sponge"}, {"label": "D", "text": "have roots"}, {"label": "E", "text": "bottle drinks"}], "stem": "How are plants able to take water and nutrients from the soil?"}}
{"id": "0358949aebf0f5ecd92f3f33ff3f50d3", "question": {"question_concept": "baseball field", "choices": [{"label": "A", "text": "baseball stadium"}, {"label": "B", "text": "japan or america"}, {"label": "C", "text": "detroit"}, {"label": "D", "text": "country"}, {"label": "E", "text": "loud"}], "stem": "If someone is in a southern state and watching a game at a baseball field, what genre of music is likely playing?"}}
{"id": "1e10346e34ea0f34828da9f2bfe34d64", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "headache"}, {"label": "B", "text": "high wages"}, {"label": "C", "text": "party"}, {"label": "D", "text": "stay alive"}, {"label": "E", "text": "husband or wife"}], "stem": "What's an important reason that a person looks for a job?"}}
{"id": "f62f43f23c42211d8fa5d95ec5ab3cc7", "question": {"question_concept": "modern", "choices": [{"label": "A", "text": "classical"}, {"label": "B", "text": "obsolete"}, {"label": "C", "text": "bland"}, {"label": "D", "text": "old fashioned"}, {"label": "E", "text": "historical"}], "stem": "Kids who have a modern taste in music find that their parents are a bit what, when it comes to their music tastes?"}}
{"id": "bf2c0495e7edfb81433f9dbef3ad21dc", "question": {"question_concept": "battle", "choices": [{"label": "A", "text": "war"}, {"label": "B", "text": "movie"}, {"label": "C", "text": "court room"}, {"label": "D", "text": "stadium"}, {"label": "E", "text": "video game"}], "stem": "Where could there be a battle that does not involve words and is real?"}}
{"id": "3e54c0771cc05ddef8b4101c180939df", "question": {"question_concept": "fly in airplane", "choices": [{"label": "A", "text": "go far"}, {"label": "B", "text": "like bird"}, {"label": "C", "text": "to read"}, {"label": "D", "text": "get home"}, {"label": "E", "text": "go someplace"}], "stem": "What is the basic motivation for flying in an airplane on business?"}}
{"id": "89b8b5dd885960200a646357d1e21530", "question": {"question_concept": "both", "choices": [{"label": "A", "text": "do with money"}, {"label": "B", "text": "shells"}, {"label": "C", "text": "two vowels"}, {"label": "D", "text": "same letters"}, {"label": "E", "text": "two syllables"}], "stem": "The student misspelled both words on the test, he didn't realize choose and pool had what next to each other?"}}
{"id": "023576e9f2a9d5ff777b154963116abf", "question": {"question_concept": "evening", "choices": [{"label": "A", "text": "day time"}, {"label": "B", "text": "daytime"}, {"label": "C", "text": "early morning"}, {"label": "D", "text": "night"}, {"label": "E", "text": "afternoon"}], "stem": "Billy loved the evening.  It was his favorite part of what?"}}
{"id": "1f4a7b36f38f4ae813e674077bf3b2e0", "question": {"question_concept": "breathing", "choices": [{"label": "A", "text": "relaxation"}, {"label": "B", "text": "warm air"}, {"label": "C", "text": "living"}, {"label": "D", "text": "body heat"}, {"label": "E", "text": "stay alive"}], "stem": "If someone was breathing in to their cold hands, they are using what to combat the cold?"}}
{"id": "b918cbbda399c54288f9373401e5112d", "question": {"question_concept": "monument", "choices": [{"label": "A", "text": "national park"}, {"label": "B", "text": "big city"}, {"label": "C", "text": "public gardens"}, {"label": "D", "text": "large city"}, {"label": "E", "text": "state park"}], "stem": "Somebody wants to look at monument in a natural setting. Where do they go?"}}
{"id": "d4ad368b772bf814c61a2b38783e6f7c", "question": {"question_concept": "sad", "choices": [{"label": "A", "text": "cheerful"}, {"label": "B", "text": "gleeful"}, {"label": "C", "text": "happy"}, {"label": "D", "text": "crying"}, {"label": "E", "text": "decent"}], "stem": "John walked into his surprise birthday party wearing his birthday suit.  He was sad that all of his friends saw him when he was not what?"}}
{"id": "ca77c633155bb01dad5d8e9d0923fd20", "question": {"question_concept": "fortune", "choices": [{"label": "A", "text": "imagination"}, {"label": "B", "text": "treasure chest"}, {"label": "C", "text": "waiter"}, {"label": "D", "text": "cookie"}, {"label": "E", "text": "bank"}], "stem": "From where did you receive a fortune after eating a meal?"}}
{"id": "771e99f1720f2a782d11a1a981edf95b", "question": {"question_concept": "ketchup", "choices": [{"label": "A", "text": "fast food restaurant"}, {"label": "B", "text": "parking lot"}, {"label": "C", "text": "refridgerator"}, {"label": "D", "text": "grocery store"}, {"label": "E", "text": "refrigerator"}], "stem": "Where can you find ketchup packets?"}}
{"id": "6c05171d59f3a46a7ce294809a6a4ef4", "question": {"question_concept": "ranch house", "choices": [{"label": "A", "text": "desert"}, {"label": "B", "text": "countryside"}, {"label": "C", "text": "texas"}, {"label": "D", "text": "mountain range"}, {"label": "E", "text": "montana"}], "stem": "The man owned a large property with a ranch house, but there wasn't much landscaping besides spiky stuff because it was located where?"}}
{"id": "d3dddc43ad1dbd197652eafbade03d68", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "board ships"}, {"label": "B", "text": "believe in god"}, {"label": "C", "text": "suffer hunger"}, {"label": "D", "text": "study books"}, {"label": "E", "text": "driving"}], "stem": "How did immigrants historically head to other countries?"}}
{"id": "66f946d9069927d1ebec0f8a883fc718", "question": {"question_concept": "immortality", "choices": [{"label": "A", "text": "dead"}, {"label": "B", "text": "dying"}, {"label": "C", "text": "mortal"}, {"label": "D", "text": "death"}, {"label": "E", "text": "mortal"}], "stem": "Having immortality means you will not be?"}}
{"id": "079f259c68211ca0be0d9510ce7c8ddc", "question": {"question_concept": "having bath", "choices": [{"label": "A", "text": "have fun"}, {"label": "B", "text": "use water"}, {"label": "C", "text": "swimming"}, {"label": "D", "text": "being clean"}, {"label": "E", "text": "wrinkled skin"}], "stem": "I was having a bath after a day at a construction site, what am I aiming for?"}}
{"id": "27afc3c8e62e6638421ba7bf33bec730", "question": {"question_concept": "play sports", "choices": [{"label": "A", "text": "take off uniform"}, {"label": "B", "text": "get in shape"}, {"label": "C", "text": "practice"}, {"label": "D", "text": "run"}, {"label": "E", "text": "stretch"}], "stem": "What should you do before and after play sports?"}}
{"id": "76d87daedf5019dd928890bba42a2036", "question": {"question_concept": "marmot", "choices": [{"label": "A", "text": "north america"}, {"label": "B", "text": "countryside"}, {"label": "C", "text": "south america"}, {"label": "D", "text": "new zealand"}, {"label": "E", "text": "africa"}], "stem": "Where could a marmot walk from one continent to another?"}}
{"id": "f770f65efda710aba448ffe93fa4a106", "question": {"question_concept": "friend", "choices": [{"label": "A", "text": "at school"}, {"label": "B", "text": "workplace"}, {"label": "C", "text": "social atmosphere"}, {"label": "D", "text": "playground"}, {"label": "E", "text": "neighbor's house"}], "stem": "where do students typically make friends?"}}
{"id": "73f4e17433320c171fb8d9e6c9f5b457", "question": {"question_concept": "picture", "choices": [{"label": "A", "text": "frame"}, {"label": "B", "text": "shelf"}, {"label": "C", "text": "art show"}, {"label": "D", "text": "table"}, {"label": "E", "text": "desktop"}], "stem": "I always keep a picture of my wife in my room, where might I set it?"}}
{"id": "742e9e86f0e615e6d81c36ceebd24d91", "question": {"question_concept": "soup", "choices": [{"label": "A", "text": "cupboard"}, {"label": "B", "text": "supermarket"}, {"label": "C", "text": "jar"}, {"label": "D", "text": "bowl"}, {"label": "E", "text": "container"}], "stem": "Where might someone store homemade soup?"}}
{"id": "82b573bc9b84b1afd744ae15cbe76a79", "question": {"question_concept": "children", "choices": [{"label": "A", "text": "wash dishes"}, {"label": "B", "text": "need care"}, {"label": "C", "text": "come home"}, {"label": "D", "text": "watch television"}, {"label": "E", "text": "walk the dog"}], "stem": "What do children do after all day at school?"}}
{"id": "6143fbb00ba8ed3e0df6fb31a68c42e8", "question": {"question_concept": "address label", "choices": [{"label": "A", "text": "envelope"}, {"label": "B", "text": "parcel"}, {"label": "C", "text": "trash"}, {"label": "D", "text": "cell phone"}, {"label": "E", "text": "desk drawer"}], "stem": "Where is an address label often used?"}}
{"id": "058771de8f47ed828bde7a01b783fbdd", "question": {"question_concept": "patient", "choices": [{"label": "A", "text": "appear better"}, {"label": "B", "text": "take pills"}, {"label": "C", "text": "will to survive"}, {"label": "D", "text": "visit doctor"}, {"label": "E", "text": "feel better"}], "stem": "The patient was still dying, in spite of the fact that the treatment made her what?"}}
{"id": "8adc90ed047cb821793c84567a14d2e2_1", "question": {"question_concept": "skin", "choices": [{"label": "A", "text": "finger"}, {"label": "B", "text": "own family"}, {"label": "C", "text": "good health"}, {"label": "D", "text": "body"}, {"label": "E", "text": "hand"}], "stem": "They just needed a drop, it was just a small prick of the skin on the what?"}}
{"id": "00d6944c50f8eded35ca81a0ba4a401a", "question": {"question_concept": "procreating", "choices": [{"label": "A", "text": "pregnancy"}, {"label": "B", "text": "having children"}, {"label": "C", "text": "children born"}, {"label": "D", "text": "population increase"}, {"label": "E", "text": "family"}], "stem": "What is the goal for most people engaging in procreating?"}}
{"id": "e7857fbf32e51f2af5b9e1237166698a", "question": {"question_concept": "doing housework", "choices": [{"label": "A", "text": "become tired"}, {"label": "B", "text": "flu symptoms"}, {"label": "C", "text": "low pay"}, {"label": "D", "text": "boredom"}, {"label": "E", "text": "nice home"}], "stem": "What is often wrong with the paycheck of someone doing housework?"}}
{"id": "747e081856a7afa2ebe675548eaab9eb", "question": {"question_concept": "mouse", "choices": [{"label": "A", "text": "garden"}, {"label": "B", "text": "department store"}, {"label": "C", "text": "addition"}, {"label": "D", "text": "cupboard"}, {"label": "E", "text": "small hole"}], "stem": "If you see a mouse scuttling across the floor, you are almost certainly not where?"}}
{"id": "d43aa9ef38f0f66e8a58cad271ba1175", "question": {"question_concept": "bean bag chair", "choices": [{"label": "A", "text": "living room"}, {"label": "B", "text": "floor"}, {"label": "C", "text": "family room"}, {"label": "D", "text": "carpet"}, {"label": "E", "text": "person's home"}], "stem": "The children were fighting in their shared area and crashed down on the bean bag chair, it burst spreading it's filling all over the what?"}}
{"id": "e062243582b62469d611416407bd03f9", "question": {"question_concept": "hands", "choices": [{"label": "A", "text": "articulate"}, {"label": "B", "text": "cup ball"}, {"label": "C", "text": "sign language"}, {"label": "D", "text": "writing"}, {"label": "E", "text": "cup water"}], "stem": "The child was fascinated by the way the one person moved their hands, when the child saw the other person understood he too wanted to learn what?"}}
{"id": "5664448835978e05b6618397d4027d85", "question": {"question_concept": "garage", "choices": [{"label": "A", "text": "downtown"}, {"label": "B", "text": "neighbor's house"}, {"label": "C", "text": "stubhub"}, {"label": "D", "text": "modern house"}, {"label": "E", "text": "car"}], "stem": "The garage was small and opened right up to the main thoroughfare, where was it located?"}}
{"id": "1e0f85e8e7125d9e2e5f1d7c7b58fb44", "question": {"question_concept": "insects", "choices": [{"label": "A", "text": "move"}, {"label": "B", "text": "sense vibrations"}, {"label": "C", "text": "flutter"}, {"label": "D", "text": "fly"}, {"label": "E", "text": "buzz"}], "stem": "The insect needed to escape the predator on the ground, what did it do?"}}
{"id": "ed7b62b75c5bd500f272eefd02c0082c", "question": {"question_concept": "aisle", "choices": [{"label": "A", "text": "department store"}, {"label": "B", "text": "church"}, {"label": "C", "text": "grocery store"}, {"label": "D", "text": "planning"}, {"label": "E", "text": "building"}], "stem": "The planners had just broken ground on the new locations, the aisle layout was something they had to consider while what?"}}
{"id": "15c2dfa273bccab70a82705ef92a3103", "question": {"question_concept": "fungus", "choices": [{"label": "A", "text": "salad"}, {"label": "B", "text": "closet"}, {"label": "C", "text": "basement"}, {"label": "D", "text": "locker room"}, {"label": "E", "text": "grocery store"}], "stem": "Where might a person get fungus?"}}
{"id": "82ccc66cfbaa8cd46620068b1bf4db95", "question": {"question_concept": "boredom", "choices": [{"label": "A", "text": "surf net"}, {"label": "B", "text": "play chess"}, {"label": "C", "text": "play cards"}, {"label": "D", "text": "watch film"}, {"label": "E", "text": "yawn"}], "stem": "What might someone do with 52 things out of boredom?"}}
{"id": "61c2e79926ae7de0491e074dc5449e62", "question": {"question_concept": "train", "choices": [{"label": "A", "text": "arrive late"}, {"label": "B", "text": "arrive on time"}, {"label": "C", "text": "arrive early"}, {"label": "D", "text": "transport mail"}, {"label": "E", "text": "slow down"}], "stem": "If a train is ahead of schedule it is likely to do what?"}}
{"id": "5c1930c58a1e7c23d7179bbbf467775e", "question": {"question_concept": "clock", "choices": [{"label": "A", "text": "fixing a clock"}, {"label": "B", "text": "stop working"}, {"label": "C", "text": "working correctly"}, {"label": "D", "text": "time event"}, {"label": "E", "text": "fail to work"}], "stem": "If a person is in the middle of a track during a race with a clock, what are they likely doing?"}}
{"id": "4f4f7b1a0bccde7a4f24ad46c0990ae1", "question": {"question_concept": "birds", "choices": [{"label": "A", "text": "eat cake"}, {"label": "B", "text": "roof"}, {"label": "C", "text": "mate"}, {"label": "D", "text": "peck"}, {"label": "E", "text": "hatch"}], "stem": "The birds stay in the nest, waiting for their kids to do what?"}}
{"id": "1abd2c5e8982d999e228cd7b4414dd7b", "question": {"question_concept": "sky", "choices": [{"label": "A", "text": "photo"}, {"label": "B", "text": "outside"}, {"label": "C", "text": "looking up"}, {"label": "D", "text": "outdoors"}, {"label": "E", "text": "place on earth"}], "stem": "A bird is in the sky, looking around, what is it looking for?"}}
{"id": "06430138ca8d06d34a470601a0406c67", "question": {"question_concept": "sun", "choices": [{"label": "A", "text": "shine brightly"}, {"label": "B", "text": "dry clothes"}, {"label": "C", "text": "brown skin"}, {"label": "D", "text": "dry ground"}, {"label": "E", "text": "warm ground"}], "stem": "What can the sun turn a muddy patch into?"}}
{"id": "97c5f6ab01ad3cf9e36ae4d7f81e50e9", "question": {"question_concept": "talking", "choices": [{"label": "A", "text": "sharing of ideas"}, {"label": "B", "text": "talk"}, {"label": "C", "text": "speak"}, {"label": "D", "text": "sneeze"}, {"label": "E", "text": "debate"}], "stem": "When someone is talking but its too low, you tell them to do this louder?"}}
{"id": "baa5513abb40b2a5c0a9dd23fa9a7b16", "question": {"question_concept": "restaurant", "choices": [{"label": "A", "text": "new york"}, {"label": "B", "text": "canada"}, {"label": "C", "text": "building"}, {"label": "D", "text": "town"}, {"label": "E", "text": "ice cream stand"}], "stem": "The man wanted an authentic pastrami deli restaurant, where should he go get one?"}}
{"id": "ee227b3dcca746954dd3e678aae6693c", "question": {"question_concept": "clerk", "choices": [{"label": "A", "text": "airport"}, {"label": "B", "text": "post office"}, {"label": "C", "text": "at hotel"}, {"label": "D", "text": "valet"}, {"label": "E", "text": "bookstore"}], "stem": "Where would a clerk give you some keys?"}}
{"id": "b09b779fc2a413e8e80b35bebf037b70", "question": {"question_concept": "grain of sand", "choices": [{"label": "A", "text": "beach"}, {"label": "B", "text": "create pearl"}, {"label": "C", "text": "clam"}, {"label": "D", "text": "sand pile"}, {"label": "E", "text": "bathing suit"}], "stem": "A grain of sad becomes the core of something precious inside of what?"}}
{"id": "43d7c90107b096f90648e90143a0cec0", "question": {"question_concept": "furniture", "choices": [{"label": "A", "text": "building"}, {"label": "B", "text": "apartment"}, {"label": "C", "text": "bed"}, {"label": "D", "text": "floor"}, {"label": "E", "text": "neighbor's house"}], "stem": "The bachelor didn't have much furniture, but it at least made his small what look more spacious?"}}
{"id": "fe2d561c9041b8e16ac82ea63ae007d5", "question": {"question_concept": "grape", "choices": [{"label": "A", "text": "winery"}, {"label": "B", "text": "supermarket"}, {"label": "C", "text": "painting"}, {"label": "D", "text": "bowl of fruit"}, {"label": "E", "text": "fruit stand"}], "stem": "What sort of vendor sells an assortment of fruits such as grapes in an outside location?"}}
{"id": "d162be2dd37f5e5666814b40d7770809", "question": {"question_concept": "iron", "choices": [{"label": "A", "text": "house"}, {"label": "B", "text": "golf bag"}, {"label": "C", "text": "linen closet"}, {"label": "D", "text": "bathroom"}, {"label": "E", "text": "laundry room"}], "stem": "Where would you put an iron after you use it?"}}
{"id": "eeeeb573acedcf03ff4526f037c486c7", "question": {"question_concept": "take stand", "choices": [{"label": "A", "text": "to cry"}, {"label": "B", "text": "witness"}, {"label": "C", "text": "testify"}, {"label": "D", "text": "tell truth"}, {"label": "E", "text": "runaway"}], "stem": "It was now her time to take stand in the trial, she swore to what?"}}
{"id": "6945b3c8b372fa7e328d0bb64c6827d0", "question": {"question_concept": "death", "choices": [{"label": "A", "text": "fast"}, {"label": "B", "text": "happen to"}, {"label": "C", "text": "happen quickly"}, {"label": "D", "text": "last forever"}, {"label": "E", "text": "surprise everyone"}], "stem": "Death has a relationship to everyone, what is that relationship?"}}
{"id": "19b9b63fc69d357c0e68cad2b2270d5f", "question": {"question_concept": "remembering", "choices": [{"label": "A", "text": "recall"}, {"label": "B", "text": "brain cells"}, {"label": "C", "text": "former life"}, {"label": "D", "text": "intelligence"}, {"label": "E", "text": "learning about"}], "stem": "What is it called when you remember something?"}}
{"id": "830513648a1ac1472a1c2b2aa39d2411", "question": {"question_concept": "gazelle", "choices": [{"label": "A", "text": "wildlife refuge"}, {"label": "B", "text": "ivory coast"}, {"label": "C", "text": "asia"}, {"label": "D", "text": "open plain"}, {"label": "E", "text": "america"}], "stem": "If you were on safari in this country and saw a gazelle, where would you be?"}}
{"id": "d602595c008cd703a51134de370ff992", "question": {"question_concept": "jumping up and down", "choices": [{"label": "A", "text": "exhaustion"}, {"label": "B", "text": "lot of noise"}, {"label": "C", "text": "hiccups"}, {"label": "D", "text": "fatigue"}, {"label": "E", "text": "getting warm"}], "stem": "After jumping up and down and starting to sweat, what sensation would you be feeling?"}}
{"id": "18513e42111fbaabf96f890769937005", "question": {"question_concept": "beaver", "choices": [{"label": "A", "text": "great outdoors"}, {"label": "B", "text": "american forests"}, {"label": "C", "text": "the ocean"}, {"label": "D", "text": "pair of pants"}, {"label": "E", "text": "zoo"}], "stem": "A beaver is unlikely to be found in what?"}}
{"id": "dcfbe580bf0157007804711d44f1eaaf", "question": {"question_concept": "eat vegetables", "choices": [{"label": "A", "text": "clean"}, {"label": "B", "text": "get gas"}, {"label": "C", "text": "stand"}, {"label": "D", "text": "prepare"}, {"label": "E", "text": "open mouth"}], "stem": "What do you need to do before you eat vegetables?"}}
{"id": "02f80bcd213706e073576a9caafad173", "question": {"question_concept": "faith", "choices": [{"label": "A", "text": "belief"}, {"label": "B", "text": "trust"}, {"label": "C", "text": "faith"}, {"label": "D", "text": "light way"}, {"label": "E", "text": "experience"}], "stem": "His faith was often tested, but he put what in the gospel and where it would take him?"}}
{"id": "4e3f62f98e9fcd5dea7bdb8acfaea4ec", "question": {"question_concept": "neighbor", "choices": [{"label": "A", "text": "park"}, {"label": "B", "text": "next house"}, {"label": "C", "text": "ditch"}, {"label": "D", "text": "suburbs"}, {"label": "E", "text": "house next door"}], "stem": "Where do many people live along with their neighbors, outside of the city center?"}}
{"id": "e850d800d88ad2871fb13aea72d4df88", "question": {"question_concept": "pen", "choices": [{"label": "A", "text": "classroom"}, {"label": "B", "text": "desk drawer"}, {"label": "C", "text": "pocket"}, {"label": "D", "text": "alone in room"}, {"label": "E", "text": "office supply store"}], "stem": "where do nerds keep a pen?"}}
{"id": "6b1a2a487fe7cd225216a77e88b745d9", "question": {"question_concept": "learning about world", "choices": [{"label": "A", "text": "intelligent"}, {"label": "B", "text": "smartness"}, {"label": "C", "text": "open mind"}, {"label": "D", "text": "pleasure"}, {"label": "E", "text": "enlightenment"}], "stem": "When learning about the world, if you really want to understand the current situation, you should learn history and this requires what?"}}
{"id": "e447a391c921ebbf85afe4ec106399c1", "question": {"question_concept": "field", "choices": [{"label": "A", "text": "wavefield"}, {"label": "B", "text": "countryside"}, {"label": "C", "text": "flower road"}, {"label": "D", "text": "rural area"}, {"label": "E", "text": "meadow"}], "stem": "What might a field filled with flowers be called?"}}
{"id": "2603a533f5d3cb54e331815cbb308276", "question": {"question_concept": "orthodox", "choices": [{"label": "A", "text": "recalcitrant"}, {"label": "B", "text": "conservadox"}, {"label": "C", "text": "heretical"}, {"label": "D", "text": "recalcitrant"}, {"label": "E", "text": "liberal"}], "stem": "James was not an orthodox person.  Whenever he was asked to conform, he responded in what way?"}}
{"id": "cbca29aba3613de3525839da8bdaa5b5", "question": {"question_concept": "children", "choices": [{"label": "A", "text": "rush around"}, {"label": "B", "text": "teach parents"}, {"label": "C", "text": "calm them"}, {"label": "D", "text": "watch television"}, {"label": "E", "text": "play video games"}], "stem": "How do fast moving children affect adults?"}}
{"id": "fd6bb314184c0388a62a4e62bba5e3de", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "appear beautiful"}, {"label": "B", "text": "suffering pain"}, {"label": "C", "text": "free slaves"}, {"label": "D", "text": "talk to each other"}, {"label": "E", "text": "be friendly"}], "stem": "What did people with a good conscience do in the 1800s?"}}
{"id": "d71cc8879853399a302e2cadcd0a180a", "question": {"question_concept": "mammoth", "choices": [{"label": "A", "text": "desert"}, {"label": "B", "text": "movie"}, {"label": "C", "text": "forest"}, {"label": "D", "text": "zoo"}, {"label": "E", "text": "wild"}], "stem": "Where could you see a mammoth?"}}
{"id": "f2a01f771003e67d9ea2df8321ad2128", "question": {"question_concept": "bringing suit", "choices": [{"label": "A", "text": "randomness"}, {"label": "B", "text": "aggravation"}, {"label": "C", "text": "great expense"}, {"label": "D", "text": "work ethic"}, {"label": "E", "text": "resentment"}], "stem": "the gambler was attempting bring suit against the slot machine manufacturer, he claimed the odds were fixed and the machine didn't have true what?"}}
{"id": "de2dbc5c01f08fccc62181ea4a926da6", "question": {"question_concept": "competing", "choices": [{"label": "A", "text": "scorekeeper"}, {"label": "B", "text": "opponent"}, {"label": "C", "text": "competition"}, {"label": "D", "text": "effort"}, {"label": "E", "text": "skill"}], "stem": "He was competing for the third time, he really enjoyed what?"}}
{"id": "52f292c7f9d1e4ad2d10e7477df87746", "question": {"question_concept": "screw", "choices": [{"label": "A", "text": "tool box"}, {"label": "B", "text": "motorcycle"}, {"label": "C", "text": "computer"}, {"label": "D", "text": "monitor"}, {"label": "E", "text": "wall outlet fixture"}], "stem": "When he screws in a cable, what is he fixing?"}}
{"id": "d1b1f231e3396f106ff6cff0b54111c2", "question": {"question_concept": "learning language", "choices": [{"label": "A", "text": "problems"}, {"label": "B", "text": "frustration"}, {"label": "C", "text": "better communication"}, {"label": "D", "text": "confidence"}, {"label": "E", "text": "trouble"}], "stem": "What prevents someone from learning language?"}}
{"id": "7fce5aac8a95de2dc2e770389e466128", "question": {"question_concept": "gift shop", "choices": [{"label": "A", "text": "museum"}, {"label": "B", "text": "hotel"}, {"label": "C", "text": "disneyland"}, {"label": "D", "text": "grocery store"}, {"label": "E", "text": "airport"}], "stem": "Where is the gift shop that people by city souvenirs at?"}}
{"id": "3e7fb680404ec490cb6b46bcbd0a469d", "question": {"question_concept": "yoyo", "choices": [{"label": "A", "text": "brother's room"}, {"label": "B", "text": "own home"}, {"label": "C", "text": "pocket"}, {"label": "D", "text": "toy shop"}, {"label": "E", "text": "in a relationship"}], "stem": "Where is the closest place you might find a yoyo?"}}
{"id": "3f7927d73b867d3a63d82f15f13db63c", "question": {"question_concept": "discovering truth", "choices": [{"label": "A", "text": "pain"}, {"label": "B", "text": "wars"}, {"label": "C", "text": "distraught"}, {"label": "D", "text": "denial"}, {"label": "E", "text": "startled"}], "stem": "Even after discovering the truth, what did the stalwart child exhibit?"}}
{"id": "28c89cc4ef19608bd87686909235f90f", "question": {"question_concept": "chatting with friends", "choices": [{"label": "A", "text": "social approval"}, {"label": "B", "text": "truthfulness"}, {"label": "C", "text": "laughter"}, {"label": "D", "text": "exchanging information"}, {"label": "E", "text": "will laugh"}], "stem": "He enjoyed chatting with friends honestly, they were all true friends and didn't need any what from each other?"}}
{"id": "602f06c342bf0e1e7fdccdfeabb4c104", "question": {"question_concept": "child", "choices": [{"label": "A", "text": "most homes"}, {"label": "B", "text": "mother's womb"}, {"label": "C", "text": "in a pen"}, {"label": "D", "text": "orphanage"}, {"label": "E", "text": "school"}], "stem": "What place might a child feel secure?"}}
{"id": "3cd8ebdfec1dc64ec21a70122db49134", "question": {"question_concept": "feet", "choices": [{"label": "A", "text": "shoes and socks"}, {"label": "B", "text": "table"}, {"label": "C", "text": "floor"}, {"label": "D", "text": "desk"}, {"label": "E", "text": "boots"}], "stem": "What is likely to have metal feet?"}}
{"id": "c8eea772c0d3e495fb27a421c1321741", "question": {"question_concept": "shirts", "choices": [{"label": "A", "text": "buttons"}, {"label": "B", "text": "suitcase"}, {"label": "C", "text": "closet"}, {"label": "D", "text": "sporting goods store"}, {"label": "E", "text": "suitcase"}], "stem": "Sally and James bought new shirts and packed them. Where might have they packed their shirts?"}}
{"id": "966ea002d044e753cbda6f403444de98", "question": {"question_concept": "judging", "choices": [{"label": "A", "text": "feeling bad"}, {"label": "B", "text": "lot of pain"}, {"label": "C", "text": "hurt feelings"}, {"label": "D", "text": "happy"}, {"label": "E", "text": "responsibility"}], "stem": "What is someone likely to feel after wrongly judging someone?"}}
{"id": "6f66187e200d73b6fb1a03c124764286", "question": {"question_concept": "stove", "choices": [{"label": "A", "text": "living room"}, {"label": "B", "text": "apartment"}, {"label": "C", "text": "porch"}, {"label": "D", "text": "friend's house"}, {"label": "E", "text": "tent"}], "stem": "Tim had a small gas stove.   He cooked on it a lot.  Where would Tim have a small gas stove?"}}
{"id": "014d62152b64376e0e8cdf0e0ccb4158", "question": {"question_concept": "go to work", "choices": [{"label": "A", "text": "get out of bed"}, {"label": "B", "text": "have job"}, {"label": "C", "text": "take bus"}, {"label": "D", "text": "get ready"}, {"label": "E", "text": "taxi"}], "stem": "How could someone go to work?"}}
{"id": "8193f811b54a2f64503e690a81766c6b", "question": {"question_concept": "eyeglasses", "choices": [{"label": "A", "text": "case"}, {"label": "B", "text": "breast pocket"}, {"label": "C", "text": "desk drawer"}, {"label": "D", "text": "shirt pocket"}, {"label": "E", "text": "michigan"}], "stem": "Where is the safest place for eyeglasses?"}}
{"id": "a737c2fae5656a33e9e31aad363e7d09", "question": {"question_concept": "god", "choices": [{"label": "A", "text": "less grief"}, {"label": "B", "text": "work miracles"}, {"label": "C", "text": "anything"}, {"label": "D", "text": "give peace"}, {"label": "E", "text": "judge men"}], "stem": "Be aware of our own mortality is hard for many, for some of those believing in their god can what?"}}
{"id": "a5151e21152ffeba204a51d702077002", "question": {"question_concept": "chess board", "choices": [{"label": "A", "text": "germany"}, {"label": "B", "text": "game shop"}, {"label": "C", "text": "library"}, {"label": "D", "text": "house"}, {"label": "E", "text": "room"}], "stem": "What is a place with many walls that could have a chess board?"}}
{"id": "25020186c6eb047b65c31665b8fce5d9", "question": {"question_concept": "magazine", "choices": [{"label": "A", "text": "waiting room"}, {"label": "B", "text": "flea market"}, {"label": "C", "text": "shop"}, {"label": "D", "text": "bookstore"}, {"label": "E", "text": "train station"}], "stem": "Bob read the magazine while he waited in line.  He wanted to get a novel, but he didn't want to read it here.  Where is he?"}}
{"id": "f6c519334c8b155f8837598c95d809ac", "question": {"question_concept": "signs", "choices": [{"label": "A", "text": "state park"}, {"label": "B", "text": "freeway"}, {"label": "C", "text": "private property"}, {"label": "D", "text": "fork in road"}, {"label": "E", "text": "demonstration"}], "stem": "Where do you see signs showing you where to set up a tent?"}}
{"id": "fab873bb6dc4f2c9056baad9c4f857cd", "question": {"question_concept": "learn", "choices": [{"label": "A", "text": "become knowledgeable"}, {"label": "B", "text": "improve yourself"}, {"label": "C", "text": "have tools"}, {"label": "D", "text": "have more knowledge"}, {"label": "E", "text": "intelligent"}], "stem": "Why do philosophers spend so much time learning about the world?"}}
{"id": "8472891d2e2baa3e95284fb66715b600", "question": {"question_concept": "answering questions", "choices": [{"label": "A", "text": "polite"}, {"label": "B", "text": "satisfaction"}, {"label": "C", "text": "boredom"}, {"label": "D", "text": "cheering"}, {"label": "E", "text": "irritation"}], "stem": "What could happen if you are listening to someone who is answering questions with very long sentences?"}}
{"id": "9f635b0e76ae817314d74eb9f2ecad09", "question": {"question_concept": "map", "choices": [{"label": "A", "text": "classroom"}, {"label": "B", "text": "atlas"}, {"label": "C", "text": "gas station"}, {"label": "D", "text": "amusement park"}, {"label": "E", "text": "backpack"}], "stem": "Where might just one map be kept?"}}
{"id": "43f4543887ad603c7d9a187ddcc0e827", "question": {"question_concept": "chocolate", "choices": [{"label": "A", "text": "supermarket"}, {"label": "B", "text": "movies"}, {"label": "C", "text": "candy factory"}, {"label": "D", "text": "restaurant"}, {"label": "E", "text": "candy store"}], "stem": "Where could you see some chocolate that is not real?"}}
{"id": "ceb4ff8c131bfce127531b536430d70a", "question": {"question_concept": "writing program", "choices": [{"label": "A", "text": "loop"}, {"label": "B", "text": "unexpected results"}, {"label": "C", "text": "frustration"}, {"label": "D", "text": "errors"}, {"label": "E", "text": "need to integrate"}], "stem": "James is writting a program but he doesn't understand why it's giving him the output that it is.  He knows that complex code can often produce what?"}}
{"id": "1b8259b7332bad1cf8e56a08049fea3b", "question": {"question_concept": "lizard", "choices": [{"label": "A", "text": "costa rica"}, {"label": "B", "text": "encyclopedia"}, {"label": "C", "text": "lizard school"}, {"label": "D", "text": "garden"}, {"label": "E", "text": "captivity"}], "stem": "The lizard wanted to learn about the world, where did it look?"}}
{"id": "4cd1daf1aeca6931e6aeaca9661e2896", "question": {"question_concept": "going to work", "choices": [{"label": "A", "text": "buy shoes"}, {"label": "B", "text": "malaise"}, {"label": "C", "text": "leave home"}, {"label": "D", "text": "making money"}, {"label": "E", "text": "stress"}], "stem": "What does a person have to do before going to work?"}}
{"id": "66a64a67308088a5581d185d8815b662", "question": {"question_concept": "making friends", "choices": [{"label": "A", "text": "smiling"}, {"label": "B", "text": "open mind"}, {"label": "C", "text": "smile"}, {"label": "D", "text": "common interests"}, {"label": "E", "text": "talking"}], "stem": "He was making friends from all walks of life, he liked to keep an what about everyone?"}}
{"id": "8879bd744e04d698b552484101d4d8b2", "question": {"question_concept": "human", "choices": [{"label": "A", "text": "two arms"}, {"label": "B", "text": "consciousness"}, {"label": "C", "text": "muscles"}, {"label": "D", "text": "one body"}, {"label": "E", "text": "body and mind"}], "stem": "What makes a human similar to a sea sponge?"}}
{"id": "9f0111a457b4b7dab1da310415d647a9", "question": {"question_concept": "wedding ring", "choices": [{"label": "A", "text": "finger"}, {"label": "B", "text": "church"}, {"label": "C", "text": "in a box"}, {"label": "D", "text": "box"}, {"label": "E", "text": "jewelery shop"}], "stem": "Where would you put a wedding ring after proposing?"}}
{"id": "a0fc7a4636b11c3b6b08873f3503ef38", "question": {"question_concept": "mechanic", "choices": [{"label": "A", "text": "highway"}, {"label": "B", "text": "machine parts"}, {"label": "C", "text": "repair shop"}, {"label": "D", "text": "race track"}, {"label": "E", "text": "garage"}], "stem": "Where could a car become damaged and need a mechanic?"}}
{"id": "cdb458320801b3a89777f34c8cdd5a54", "question": {"question_concept": "being cold", "choices": [{"label": "A", "text": "make patchwork quilt"}, {"label": "B", "text": "light fire"}, {"label": "C", "text": "go swimming"}, {"label": "D", "text": "get warm"}, {"label": "E", "text": "knit"}], "stem": "What might someone do as a hobby that will keep them from being cold?"}}
{"id": "761538adda5a35d30d3f41e354c640c7", "question": {"question_concept": "tree", "choices": [{"label": "A", "text": "fall down"}, {"label": "B", "text": "cast shadow"}, {"label": "C", "text": "burn"}, {"label": "D", "text": "branch out"}, {"label": "E", "text": "provide shelter"}], "stem": "WHat doesn't often happen to trees naturally?"}}
{"id": "f333000ded17e62b87355949d8b96c32", "question": {"question_concept": "everyone", "choices": [{"label": "A", "text": "unique personality"}, {"label": "B", "text": "feelings"}, {"label": "C", "text": "electrical circuit"}, {"label": "D", "text": "make living"}, {"label": "E", "text": "values"}], "stem": "Most everyone at least partially agrees on some basic human what that society has?"}}
{"id": "727b07571c58d6cb98865961aa15b1c9", "question": {"question_concept": "forest", "choices": [{"label": "A", "text": "africa"}, {"label": "B", "text": "countryside"}, {"label": "C", "text": "national park"}, {"label": "D", "text": "amazon basin"}, {"label": "E", "text": "temperate zone"}], "stem": "Where would you find a forest that has a large amount of rain?"}}
{"id": "44fd884b7a03d5ff6b8a0fa5126e1f19", "question": {"question_concept": "gentleman", "choices": [{"label": "A", "text": "paris"}, {"label": "B", "text": "club"}, {"label": "C", "text": "church"}, {"label": "D", "text": "suit"}, {"label": "E", "text": "europe"}], "stem": "A gentleman was giving a tour of the greatest palaces and museum, where was he giving them>"}}
{"id": "c7e4e5829ee9ad26b801fe0889d721a0", "question": {"question_concept": "shed", "choices": [{"label": "A", "text": "farm yard"}, {"label": "B", "text": "hold things"}, {"label": "C", "text": "backyard"}, {"label": "D", "text": "backyard"}, {"label": "E", "text": "ranch"}], "stem": "If you raise food animals where would you put a shed?"}}
{"id": "0638db6a74413dbae26b1d65725110e5", "question": {"question_concept": "using computer", "choices": [{"label": "A", "text": "program created"}, {"label": "B", "text": "stress"}, {"label": "C", "text": "keyboard"}, {"label": "D", "text": "pleasure"}, {"label": "E", "text": "increased efficiency"}], "stem": "The man made a living using computer to create software, everybody at work uses a what by him?"}}
{"id": "be6ef28148974512686aacd1a7463c22", "question": {"question_concept": "work", "choices": [{"label": "A", "text": "relaxation"}, {"label": "B", "text": "unemployment"}, {"label": "C", "text": "desk"}, {"label": "D", "text": "do nothing"}, {"label": "E", "text": "workhour"}], "stem": "Sam pretended to go to work every day, because he couldn't tell his wife about his what?"}}
{"id": "8cd59b279b5af91fb0cb8335fd5516b6", "question": {"question_concept": "harmonica", "choices": [{"label": "A", "text": "rock band"}, {"label": "B", "text": "its case"}, {"label": "C", "text": "music store"}, {"label": "D", "text": "pocket"}, {"label": "E", "text": "mouth"}], "stem": "Where does a street performer keep their harmonica?"}}
{"id": "379a3a817a2b225dd849efc173c4df4d", "question": {"question_concept": "cooks", "choices": [{"label": "A", "text": "better food"}, {"label": "B", "text": "not your own choice of foods"}, {"label": "C", "text": "bread fish"}, {"label": "D", "text": "prepare meals"}, {"label": "E", "text": "season with salt"}], "stem": "What would be the result of hiring cooks?"}}
{"id": "b8cd5bc0ed1f45290d180c6468e73583", "question": {"question_concept": "picture", "choices": [{"label": "A", "text": "wall"}, {"label": "B", "text": "table"}, {"label": "C", "text": "newspaper"}, {"label": "D", "text": "desktop"}, {"label": "E", "text": "book"}], "stem": "What periodical has articles and pictures?"}}
{"id": "15ad2d0c4ceb47701ed34a549311aa1f", "question": {"question_concept": "kosher restaurant", "choices": [{"label": "A", "text": "new york city"}, {"label": "B", "text": "jerusalem"}, {"label": "C", "text": "jewish neighborhoods"}, {"label": "D", "text": "seattle"}, {"label": "E", "text": "boston"}], "stem": "What American area is likely to have many kosher restaurants?"}}
{"id": "fd491ed1457a6b37f1e246498bf07d3e", "question": {"question_concept": "pillow", "choices": [{"label": "A", "text": "make seat softer"}, {"label": "B", "text": "home"}, {"label": "C", "text": "bedroom"}, {"label": "D", "text": "rest area"}, {"label": "E", "text": "motel"}], "stem": "What is a place that you can usually see a pillow in regardless of where you are in it?"}}
{"id": "c364577af9adffb9e7920a772b8e36e5", "question": {"question_concept": "drug", "choices": [{"label": "A", "text": "pharmacy"}, {"label": "B", "text": "cupboard"}, {"label": "C", "text": "bottle"}, {"label": "D", "text": "medicine cabinet"}, {"label": "E", "text": "grocery story"}], "stem": "Where can someone pick up a drug from a licensed professional?"}}
{"id": "2e5bf096d00e72abca2fd344b9902a5f", "question": {"question_concept": "sun", "choices": [{"label": "A", "text": "dry clothes"}, {"label": "B", "text": "dry ground"}, {"label": "C", "text": "brown skin"}, {"label": "D", "text": "shine brightly"}, {"label": "E", "text": "hot feet"}], "stem": "What becomes more prevalent on white people as they spend more time in the sun?"}}
{"id": "8494f80ab586308db6247293662555aa", "question": {"question_concept": "athlete", "choices": [{"label": "A", "text": "stadium"}, {"label": "B", "text": "locker room"}, {"label": "C", "text": "sporting event"}, {"label": "D", "text": "olympics"}, {"label": "E", "text": "bench"}], "stem": "Where do the best athletes go?"}}
{"id": "b1c3c73bc40f8c79f6731f845088d713", "question": {"question_concept": "read book", "choices": [{"label": "A", "text": "experience"}, {"label": "B", "text": "education"}, {"label": "C", "text": "open up"}, {"label": "D", "text": "knowledge"}, {"label": "E", "text": "learning"}], "stem": "What are students getting when they read book in school?"}}
{"id": "7faa58c01932e22f23783a15f00afe41", "question": {"question_concept": "politicians", "choices": [{"label": "A", "text": "washington d.c"}, {"label": "B", "text": "legislate"}, {"label": "C", "text": "government"}, {"label": "D", "text": "field questions"}, {"label": "E", "text": "parliament"}], "stem": "Politicians run what part of the country?"}}
{"id": "7f4a224827a03149f5c3ba86a561dfcd", "question": {"question_concept": "mustard", "choices": [{"label": "A", "text": "glass"}, {"label": "B", "text": "fast food restaurant"}, {"label": "C", "text": "refrigerator"}, {"label": "D", "text": "jar"}, {"label": "E", "text": "fridge"}], "stem": "What is fancy mustard stored in?"}}
{"id": "bddfdd79ebbc32ce047b967cd6ab5e09", "question": {"question_concept": "closet", "choices": [{"label": "A", "text": "store"}, {"label": "B", "text": "bedroom"}, {"label": "C", "text": "house"}, {"label": "D", "text": "school"}, {"label": "E", "text": "coats"}], "stem": "Where are you likely to find a large closet?"}}
{"id": "2309b5e442bcfbc45c3b22fda39b9f99", "question": {"question_concept": "nerve", "choices": [{"label": "A", "text": "human body"}, {"label": "B", "text": "brainstem"}, {"label": "C", "text": "nose"}, {"label": "D", "text": "person's body"}, {"label": "E", "text": "animal"}], "stem": "The surgeon traced the nerve to its source, where did he end up?"}}
{"id": "815870bc57582b783e9e5246e275b2a6", "question": {"question_concept": "alcohol", "choices": [{"label": "A", "text": "drunkenness"}, {"label": "B", "text": "present"}, {"label": "C", "text": "amnesia"}, {"label": "D", "text": "hangover"}, {"label": "E", "text": "burn"}], "stem": "What do you get the day after drinking a lot of alcohol?"}}
{"id": "86ac1713b4e09b1f8bbce8228af15885", "question": {"question_concept": "entertaining", "choices": [{"label": "A", "text": "boredom"}, {"label": "B", "text": "favors"}, {"label": "C", "text": "gratification"}, {"label": "D", "text": "laughter"}, {"label": "E", "text": "fatigue"}], "stem": "She insisted it would be entertaining, but still some felt what?"}}
{"id": "3a9be28536949a1b9a2b2914bbf6dee2", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "will succeed"}, {"label": "B", "text": "thank god"}, {"label": "C", "text": "death"}, {"label": "D", "text": "own property"}, {"label": "E", "text": "bring home"}], "stem": "If a person works hard and applies themselves, what is the end result for them?"}}
{"id": "50b48381f8367c4b4b19d7606f6a78dc", "question": {"question_concept": "police officer", "choices": [{"label": "A", "text": "underground"}, {"label": "B", "text": "case"}, {"label": "C", "text": "city"}, {"label": "D", "text": "street"}, {"label": "E", "text": "beat"}], "stem": "Where would a police officer be likely to stop a car?"}}
{"id": "2e307ed8cdc5f3a74b97f865fef2d434", "question": {"question_concept": "trash can", "choices": [{"label": "A", "text": "motel"}, {"label": "B", "text": "alley"}, {"label": "C", "text": "bus stop"}, {"label": "D", "text": "park"}, {"label": "E", "text": "corner"}], "stem": "What waiting area is typically next to a trash can?"}}
{"id": "7dc62c5e6733eedaea1db5c221b9e518", "question": {"question_concept": "spitting", "choices": [{"label": "A", "text": "snot"}, {"label": "B", "text": "saliva nd mouth"}, {"label": "C", "text": "spittle"}, {"label": "D", "text": "disease"}, {"label": "E", "text": "phlegm"}], "stem": "What do you typically need to keep spitting up when youi're sick?"}}
{"id": "fef4406ca970fbb91c9fcb52b753ca9a", "question": {"question_concept": "god", "choices": [{"label": "A", "text": "compassion"}, {"label": "B", "text": "mosque"}, {"label": "C", "text": "heaven"}, {"label": "D", "text": "synagogue"}, {"label": "E", "text": "church"}], "stem": "Where may a Jewish person go to worship?"}}
{"id": "f3ea1ea425cfe1ea2bff6589976fc29a", "question": {"question_concept": "football field", "choices": [{"label": "A", "text": "state"}, {"label": "B", "text": "players"}, {"label": "C", "text": "city"}, {"label": "D", "text": "high school"}, {"label": "E", "text": "university"}], "stem": "Over one hundred thousand fans filled the stadium to watch the football field, it was a big rivalry match up for the what?"}}
{"id": "4b6c5407673e33a691ab081ff99ea87e", "question": {"question_concept": "crab", "choices": [{"label": "A", "text": "red lobster"}, {"label": "B", "text": "stew pot"}, {"label": "C", "text": "human resources"}, {"label": "D", "text": "chesapeake bay"}, {"label": "E", "text": "boss's office"}], "stem": "Where should you take a crab delivery invoice?"}}
{"id": "1884a3fb3f50879e8c7432c649b96880", "question": {"question_concept": "bench", "choices": [{"label": "A", "text": "bus stop"}, {"label": "B", "text": "garden"}, {"label": "C", "text": "train station"}, {"label": "D", "text": "bus depot"}, {"label": "E", "text": "rest area"}], "stem": "What likely has one bench?"}}
{"id": "d01e4fd41aa0a4ab2b2cf1a6341ccee5", "question": {"question_concept": "audience", "choices": [{"label": "A", "text": "television"}, {"label": "B", "text": "arena"}, {"label": "C", "text": "theatre"}, {"label": "D", "text": "concert hall"}, {"label": "E", "text": "presentation"}], "stem": "Where would you find an audience watching football?"}}
{"id": "ea619f8fa41694248f52a71e44c99b54", "question": {"question_concept": "mammoth", "choices": [{"label": "A", "text": "antique store"}, {"label": "B", "text": "antarctica"}, {"label": "C", "text": "forest"}, {"label": "D", "text": "pleistocene"}, {"label": "E", "text": "smithsonian institution"}], "stem": "There were endless things to see during the trip, but the child's favorite exhibit was the mammoth at where?"}}
{"id": "7d56afe44746efe099c883255dafacf3", "question": {"question_concept": "ferret", "choices": [{"label": "A", "text": "out of doors"}, {"label": "B", "text": "comic book"}, {"label": "C", "text": "moon"}, {"label": "D", "text": "outdoors"}, {"label": "E", "text": "north carolina"}], "stem": "If a ferret was being affected by a hurricane in September 2018 he probably lives where?"}}
{"id": "035336786850646c673a6b3f0953347f", "question": {"question_concept": "drinking alcohol", "choices": [{"label": "A", "text": "do a jig"}, {"label": "B", "text": "have fun"}, {"label": "C", "text": "inebriation"}, {"label": "D", "text": "vomit"}, {"label": "E", "text": "start singing"}], "stem": "In an Irish pub what is likely to happen when people are drinking alcohol?"}}
{"id": "fd9c4345ae1dbae62ff4b3398b38a20e_1", "question": {"question_concept": "jeans", "choices": [{"label": "A", "text": "clothing store"}, {"label": "B", "text": "gap"}, {"label": "C", "text": "supermarket"}, {"label": "D", "text": "shopping mall"}, {"label": "E", "text": "laundromat"}], "stem": "She dug quarters out of her jeans to wash her jeans, the next couple hours would be spent where?"}}
{"id": "6339b9156ee1ff029eebfa60e2b77148", "question": {"question_concept": "cat", "choices": [{"label": "A", "text": "eat food"}, {"label": "B", "text": "see at night"}, {"label": "C", "text": "sun itself"}, {"label": "D", "text": "run away"}, {"label": "E", "text": "cast shadow"}], "stem": "What does a cat do when the sun is on it?"}}
{"id": "4d9a83c32929d05d5302f3417be8a3fb", "question": {"question_concept": "ventilation system", "choices": [{"label": "A", "text": "house"}, {"label": "B", "text": "hospital"}, {"label": "C", "text": "office building"}, {"label": "D", "text": "large building"}, {"label": "E", "text": "shed"}], "stem": "What old type of building is likely to have no ventilation system?"}}
{"id": "205e5e205ced0e8c7a16f40aaf110314", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "hurry home"}, {"label": "B", "text": "feel loved"}, {"label": "C", "text": "gain weight"}, {"label": "D", "text": "feel rested"}, {"label": "E", "text": "laugh out loud"}], "stem": "What does a person do after a long day of work?"}}
{"id": "e6493a7784066498ca71ff5f0a267414", "question": {"question_concept": "literature", "choices": [{"label": "A", "text": "work"}, {"label": "B", "text": "public library"}, {"label": "C", "text": "shelf"}, {"label": "D", "text": "books and magazines"}, {"label": "E", "text": "own home"}], "stem": "Name some mediums for literature."}}
{"id": "3084dbc71b99d87eee99fc1f45d5d40a", "question": {"question_concept": "talking", "choices": [{"label": "A", "text": "sore throat"}, {"label": "B", "text": "attempting to communicate"}, {"label": "C", "text": "making sound"}, {"label": "D", "text": "people to listen"}, {"label": "E", "text": "hanging out"}], "stem": "Danny knew they were talking, but he couldn't make out what they were saying. He wished he could read lips.   At this distance he couldn't even be sure if they were doing what?"}}
{"id": "30ec8fef21199b46599ae9c10a3476ce", "question": {"question_concept": "procreate", "choices": [{"label": "A", "text": "moaning"}, {"label": "B", "text": "die"}, {"label": "C", "text": "std"}, {"label": "D", "text": "kiss"}, {"label": "E", "text": "leave will"}], "stem": "What do people do when they are enjoying the process of procreate?"}}
{"id": "c72bd9d5562b02645694f7e9014749f4", "question": {"question_concept": "shareholder", "choices": [{"label": "A", "text": "building"}, {"label": "B", "text": "large company"}, {"label": "C", "text": "annual general meeting"}, {"label": "D", "text": "factory"}, {"label": "E", "text": "research center"}], "stem": "A shareholder in a manufacturing firm is part owner of what structure that creates products?"}}
{"id": "856c44264a264bdd45da0d74b70f79d1", "question": {"question_concept": "atlantic ocean", "choices": [{"label": "A", "text": "submarines"}, {"label": "B", "text": "earth"}, {"label": "C", "text": "planet"}, {"label": "D", "text": "basin"}, {"label": "E", "text": "atlas"}], "stem": "What is the place in the atlantic ocean where its tectonic plates meet?"}}
{"id": "f2d0bd0551aca2858d66e3a631b5a005", "question": {"question_concept": "weasel", "choices": [{"label": "A", "text": "fairytale"}, {"label": "B", "text": "law office"}, {"label": "C", "text": "great outdoors"}, {"label": "D", "text": "cartoon"}, {"label": "E", "text": "court room"}], "stem": "Where would you see a weasel that is not real?"}}
{"id": "68794a034db481b68ef68dd36251811c", "question": {"question_concept": "bedroom", "choices": [{"label": "A", "text": "apartment"}, {"label": "B", "text": "chairs"}, {"label": "C", "text": "loft"}, {"label": "D", "text": "dwelling"}, {"label": "E", "text": "at hotel"}], "stem": "It only had one bedroom but that was fine for the bachelor, he paid for the month at the what?"}}
{"id": "bf65a026a37c3a74c77980907791b2bb", "question": {"question_concept": "humans", "choices": [{"label": "A", "text": "animal"}, {"label": "B", "text": "brains"}, {"label": "C", "text": "two legs"}, {"label": "D", "text": "one mouth"}, {"label": "E", "text": "two ears"}], "stem": "What differentiates humans from each other?"}}
{"id": "2c6c6cb26c15a62a12af86c1e9c14aac", "question": {"question_concept": "mouse", "choices": [{"label": "A", "text": "desk drawer"}, {"label": "B", "text": "computer lab"}, {"label": "C", "text": "cupboard"}, {"label": "D", "text": "old barn"}, {"label": "E", "text": "research laboratory"}], "stem": "Where can you find a plastic mouse?"}}
{"id": "a64f862ebb15edf91ef94165a4a1c6f0", "question": {"question_concept": "name", "choices": [{"label": "A", "text": "unknown"}, {"label": "B", "text": "pseudonym"}, {"label": "C", "text": "anonymous"}, {"label": "D", "text": "dad"}, {"label": "E", "text": "call person"}], "stem": "Jake didn't know the name of his father, so he just put what into the search?"}}
{"id": "b72b40d6e66fcdbc623babd7235cdc7c", "question": {"question_concept": "entertaining", "choices": [{"label": "A", "text": "favors"}, {"label": "B", "text": "pain"}, {"label": "C", "text": "laughter"}, {"label": "D", "text": "boredom"}, {"label": "E", "text": "gratification"}], "stem": "Mom wanted to make her son's birthday party entertaining, so what did she provide?"}}
{"id": "517edd06965a60513e728b985b024d85", "question": {"question_concept": "seaweed", "choices": [{"label": "A", "text": "beach"}, {"label": "B", "text": "water"}, {"label": "C", "text": "sandbar"}, {"label": "D", "text": "ocean"}, {"label": "E", "text": "found in ocean"}], "stem": "where do you find dry seaweed ?"}}
{"id": "11016649709a58b835e1c942aae85f5a", "question": {"question_concept": "crab", "choices": [{"label": "A", "text": "pet shop"}, {"label": "B", "text": "beach sand"}, {"label": "C", "text": "fishmongers"}, {"label": "D", "text": "intertidal zone"}, {"label": "E", "text": "chesapeake bay"}], "stem": "A crab is scurry along the shore where it can be seen where is it?"}}
{"id": "5e5a5ea6eaeb327fa33619a08f247200", "question": {"question_concept": "attempt", "choices": [{"label": "A", "text": "fail"}, {"label": "B", "text": "trying"}, {"label": "C", "text": "fail"}, {"label": "D", "text": "leave"}, {"label": "E", "text": "give up"}], "stem": "What happens when we don't succeed at something we attempt?"}}
{"id": "d9938d5c680c2d4a9ed0ce3036995986", "question": {"question_concept": "friends", "choices": [{"label": "A", "text": "meet for lunch"}, {"label": "B", "text": "part ways"}, {"label": "C", "text": "disenfranchise"}, {"label": "D", "text": "leave"}, {"label": "E", "text": "comfort"}], "stem": "Friends come and go but true friendships never what?"}}
{"id": "65bc9dec9ead8c35f21eedcd7036546b", "question": {"question_concept": "waiting for", "choices": [{"label": "A", "text": "anxiety"}, {"label": "B", "text": "have fun"}, {"label": "C", "text": "tedious"}, {"label": "D", "text": "draining"}, {"label": "E", "text": "time consuming"}], "stem": "Waiting for the results require doing the same thing over and over, he didn't think it was necessary and found the task what?"}}
{"id": "dab4b31552d36394a1642512fa08c021", "question": {"question_concept": "fast food restaurant", "choices": [{"label": "A", "text": "center of town"}, {"label": "B", "text": "populated area"}, {"label": "C", "text": "theatre"}, {"label": "D", "text": "shopping mall"}, {"label": "E", "text": "strip mall"}], "stem": "Where would you find a fast food restaurant along side other merchants out of doors?"}}
{"id": "98205e970f763ed2b588413d02b37056", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "family and friends"}, {"label": "B", "text": "own house"}, {"label": "C", "text": "english house"}, {"label": "D", "text": "cotton candy"}, {"label": "E", "text": "meaningful work"}], "stem": "What does a person at a charity have?"}}
{"id": "13d601f5ec430a33d3e0912d3fb1d856", "question": {"question_concept": "using television", "choices": [{"label": "A", "text": "visual"}, {"label": "B", "text": "boredom"}, {"label": "C", "text": "laziness"}, {"label": "D", "text": "falling asleep"}, {"label": "E", "text": "sick"}], "stem": "There was nothing to do, so the child was using television, why would he do that?"}}
{"id": "5341e0f6463882349a68c98a9c962ee1", "question": {"question_concept": "keys", "choices": [{"label": "A", "text": "shelf"}, {"label": "B", "text": "front pocket"}, {"label": "C", "text": "back pocket"}, {"label": "D", "text": "purse"}, {"label": "E", "text": "piano"}], "stem": "The woman lost her keys, where should she look?"}}
{"id": "c7462a96e9cfc9c261c2806e4e8d95bc", "question": {"question_concept": "excavation", "choices": [{"label": "A", "text": "street"}, {"label": "B", "text": "construction site"}, {"label": "C", "text": "under ground"}, {"label": "D", "text": "city"}, {"label": "E", "text": "cemetary"}], "stem": "Where in an excavation would you need a flashlight to see?"}}
{"id": "a65e24918f04b6c5514db689017ecb12", "question": {"question_concept": "talking to", "choices": [{"label": "A", "text": "boredom"}, {"label": "B", "text": "learn"}, {"label": "C", "text": "persuaded"}, {"label": "D", "text": "nursing home"}, {"label": "E", "text": "communication"}], "stem": "What do people want to spend time talking to their elders?"}}
{"id": "bcbad4675403e58142f0f14249c08935", "question": {"question_concept": "text", "choices": [{"label": "A", "text": "sequence of words"}, {"label": "B", "text": "fade"}, {"label": "C", "text": "field"}, {"label": "D", "text": "explaining"}, {"label": "E", "text": "book"}], "stem": "Text on a page will do what if exposed to sunlight for too long?"}}
{"id": "1851f5d560962ba4ae8c35bae8189a0b", "question": {"question_concept": "poet", "choices": [{"label": "A", "text": "garden"}, {"label": "B", "text": "home"}, {"label": "C", "text": "bedroom"}, {"label": "D", "text": "university"}, {"label": "E", "text": "classroom"}], "stem": "Where does a poet go when they're done at work?"}}
{"id": "0ef7dabdb46fb90189ff5a8188def6e1", "question": {"question_concept": "rubber stamp", "choices": [{"label": "A", "text": "office"}, {"label": "B", "text": "art class"}, {"label": "C", "text": "desk"}, {"label": "D", "text": "made"}, {"label": "E", "text": "indiana"}], "stem": "Where would you use a floral rubber stamp?"}}
{"id": "e1c8cb2ace35327a283c050cac2257e9", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "nice family"}, {"label": "B", "text": "headache"}, {"label": "C", "text": "balanced diet"}, {"label": "D", "text": "get enough sleep"}, {"label": "E", "text": "strong bones"}], "stem": "How can a person makes sure they aren't overtired?"}}
{"id": "54eb46b65cca90e5a2b29afd642f2e2b", "question": {"question_concept": "kiss", "choices": [{"label": "A", "text": "show affection"}, {"label": "B", "text": "companion"}, {"label": "C", "text": "manipulate"}, {"label": "D", "text": "smile"}, {"label": "E", "text": "lips"}], "stem": "They were at a museum for the band kiss, their what was a huge fan?"}}
{"id": "c3c7df7de3d26d4410b45d3737a2c52d", "question": {"question_concept": "seeing people play game", "choices": [{"label": "A", "text": "sadness"}, {"label": "B", "text": "entertainment"}, {"label": "C", "text": "interest"}, {"label": "D", "text": "entertaining"}, {"label": "E", "text": "stress"}], "stem": "james doesn't like seeing people play games.  He doesn't have any what in it?"}}
{"id": "8955775b2a550c80fefe08c009183ca2", "question": {"question_concept": "shopping", "choices": [{"label": "A", "text": "cash"}, {"label": "B", "text": "money and time"}, {"label": "C", "text": "having money"}, {"label": "D", "text": "travel"}, {"label": "E", "text": "spending money"}], "stem": "What is required if you want to make a purchase when shopping?"}}
{"id": "d7c73d54e02c2fa9d75afa67484f12bd", "question": {"question_concept": "homeless people", "choices": [{"label": "A", "text": "bus depot"}, {"label": "B", "text": "bridge"}, {"label": "C", "text": "street"}, {"label": "D", "text": "require donations from passersby"}, {"label": "E", "text": "hotel"}], "stem": "Where do homeless people take shelter?"}}
{"id": "70d45d88e5155327914c4752046148e7", "question": {"question_concept": "ants", "choices": [{"label": "A", "text": "sleep"}, {"label": "B", "text": "circle food"}, {"label": "C", "text": "crawl"}, {"label": "D", "text": "fly"}, {"label": "E", "text": "follow one another"}], "stem": "What can't ants do that other bugs can?"}}
{"id": "52a0005c91e59133e979f7b93ddfc0da", "question": {"question_concept": "citizen", "choices": [{"label": "A", "text": "countryfolk"}, {"label": "B", "text": "subject"}, {"label": "C", "text": "vote"}, {"label": "D", "text": "alien"}, {"label": "E", "text": "fish"}], "stem": "John was an urban citizen, he didn't know how to deal with the what?"}}
{"id": "4d13ee7deb7702b64a356a2a4a47f122", "question": {"question_concept": "clothing", "choices": [{"label": "A", "text": "closet"}, {"label": "B", "text": "mall"}, {"label": "C", "text": "house"}, {"label": "D", "text": "suitcase"}, {"label": "E", "text": "drawer"}], "stem": "Bobby bought a lot of new clothing. When he got home he put the clothes on hangers and stored them where?"}}
{"id": "bcedebfc8d41b24067f05d726352d376", "question": {"question_concept": "grape", "choices": [{"label": "A", "text": "sunshine"}, {"label": "B", "text": "cart"}, {"label": "C", "text": "deserts"}, {"label": "D", "text": "bowl of fruit"}, {"label": "E", "text": "shops"}], "stem": "John  wanted some grapes and went looking for the. He found some California grapes in several what?"}}
{"id": "8cd9c2f52af984783bb78f77548c0b1d", "question": {"question_concept": "literature", "choices": [{"label": "A", "text": "public library"}, {"label": "B", "text": "meeting"}, {"label": "C", "text": "conference"}, {"label": "D", "text": "shelf"}, {"label": "E", "text": "smoking break"}], "stem": "Promotional literature is often handed out at what work gathering?"}}
{"id": "0f0a8f5d3eeeee6570a9f3c061b64f79", "question": {"question_concept": "parents", "choices": [{"label": "A", "text": "guide children"}, {"label": "B", "text": "control children"}, {"label": "C", "text": "ignore complaints"}, {"label": "D", "text": "understand children"}, {"label": "E", "text": "care for children"}], "stem": "The parents were listening when their kids complained, what was their goal?"}}
{"id": "65daaa5d6ca769cb3a4e97a682f39991", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "apartment"}, {"label": "B", "text": "conference"}, {"label": "C", "text": "opera"}, {"label": "D", "text": "supermarket"}, {"label": "E", "text": "musical"}], "stem": "What type of show are the people going to see?"}}
{"id": "deb782fe7943418e06387d8ca551350a", "question": {"question_concept": "bird", "choices": [{"label": "A", "text": "roof"}, {"label": "B", "text": "cage"}, {"label": "C", "text": "sky"}, {"label": "D", "text": "nest"}, {"label": "E", "text": "countryside"}], "stem": "A bird flew but couldn't find any building to land on, where was it?"}}
{"id": "9cc1f39777572dcc97c9f798b41b63e1", "question": {"question_concept": "rain", "choices": [{"label": "A", "text": "water garden"}, {"label": "B", "text": "kill weeds"}, {"label": "C", "text": "wet ground"}, {"label": "D", "text": "wet clothes"}, {"label": "E", "text": "make shoes wet"}], "stem": "Why would a gardener want rain?"}}
{"id": "279fd34967b1daae045cdd89686ed3ac", "question": {"question_concept": "showroom", "choices": [{"label": "A", "text": "city"}, {"label": "B", "text": "theater"}, {"label": "C", "text": "ballet"}, {"label": "D", "text": "electronics store"}, {"label": "E", "text": "appliance store"}], "stem": "Where can you see a showroom that does not exist?"}}
{"id": "ea50d11ebbbbdf785dd5284231324ace", "question": {"question_concept": "sports", "choices": [{"label": "A", "text": "fun"}, {"label": "B", "text": "games"}, {"label": "C", "text": "recreational"}, {"label": "D", "text": "competitive"}, {"label": "E", "text": "violent"}], "stem": "Sports don't have to be for a prize they can just be what?"}}
{"id": "b10c073f314ccbab72306b6f304fb9a4", "question": {"question_concept": "lizard", "choices": [{"label": "A", "text": "bermuda"}, {"label": "B", "text": "south carolina"}, {"label": "C", "text": "new mexico"}, {"label": "D", "text": "utah"}, {"label": "E", "text": "florida"}], "stem": "The lizard had stowed away aboard the RV as the family traveled to the beach for vacation, the extremely popular beach was located where?"}}
{"id": "0ae08150b20f9b7d8c81480fd9b09fe2", "question": {"question_concept": "explosive", "choices": [{"label": "A", "text": "war"}, {"label": "B", "text": "army"}, {"label": "C", "text": "landmine"}, {"label": "D", "text": "fireworks display"}, {"label": "E", "text": "military"}], "stem": "What organization is likely to work with explosives?"}}
{"id": "a3af4e3e46d67230e3b5acf801b1ae2b", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "lend money"}, {"label": "B", "text": "feel sorry"}, {"label": "C", "text": "pray"}, {"label": "D", "text": "cross street"}, {"label": "E", "text": "trust god"}], "stem": "James saw a person in the street.  He tried to give them some change, but they didn't want it.  Instead they told him that they didn't need anything because they had protection.  What might they have meant?"}}
{"id": "a526e2ae2cd79061d55e83336d802087", "question": {"question_concept": "meet people", "choices": [{"label": "A", "text": "go to the movies"}, {"label": "B", "text": "go outside"}, {"label": "C", "text": "friendly"}, {"label": "D", "text": "go to parties"}, {"label": "E", "text": "take class"}], "stem": "If you want to meet people who share one of your hobbies you can do what which also lets you learn more about your hobby?"}}
{"id": "9f73b5a6a23412bcfd7e72c411814d30", "question": {"question_concept": "walking", "choices": [{"label": "A", "text": "exercise"}, {"label": "B", "text": "lose weight"}, {"label": "C", "text": "move"}, {"label": "D", "text": "getting somewhere"}, {"label": "E", "text": "enjoyment"}], "stem": "What is a healthy reason for walking?"}}
{"id": "88222b0214335167580536de94163f8e", "question": {"question_concept": "honour", "choices": [{"label": "A", "text": "worry"}, {"label": "B", "text": "dishonor"}, {"label": "C", "text": "disgrace"}, {"label": "D", "text": "shame"}, {"label": "E", "text": "cowardice"}], "stem": "The soldier was given honour but he struggled being the only one who survived, he felt like he had shown fear and what?"}}
{"id": "7bff3e77b8c6ef7cc4d23ec7fee91d70", "question": {"question_concept": "printer", "choices": [{"label": "A", "text": "desk"}, {"label": "B", "text": "contra costa times newspaper company"}, {"label": "C", "text": "school"}, {"label": "D", "text": "newspaper office"}, {"label": "E", "text": "home office"}], "stem": "Where do you use a printer for the morning paper?"}}
{"id": "b3fdaa01a794dc528173b6144cbbed8a", "question": {"question_concept": "listen", "choices": [{"label": "A", "text": "you'll learn"}, {"label": "B", "text": "concentrate on sounds"}, {"label": "C", "text": "dance"}, {"label": "D", "text": "stop speaking"}, {"label": "E", "text": "hear things"}], "stem": "He told everybody to shut up and listen, with everybody quiet they began to what in the distance?"}}
{"id": "9c816bd4f0517ea0f16dffe7d86123ac", "question": {"question_concept": "projectile ball", "choices": [{"label": "A", "text": "arcade"}, {"label": "B", "text": "slingshot"}, {"label": "C", "text": "gun"}, {"label": "D", "text": "motion"}, {"label": "E", "text": "flintlock"}], "stem": "John loaded the projectile ball into his weapon. What type of weapon might it be?"}}
{"id": "88f4dd070b5c210727104424f3e568b9", "question": {"question_concept": "mouse", "choices": [{"label": "A", "text": "refridgerator"}, {"label": "B", "text": "kitchen"}, {"label": "C", "text": "cupboard"}, {"label": "D", "text": "sewer"}, {"label": "E", "text": "garage"}], "stem": "Where is someone likely to be unhappy to find a mouse nibbling on snacks?"}}
{"id": "45ea75fe3ea82b829e6e306134bcdb81", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "many people"}, {"label": "B", "text": "animal"}, {"label": "C", "text": "own house"}, {"label": "D", "text": "headache"}, {"label": "E", "text": "put together"}], "stem": "When going to a party, what did the agoraphobic person fear?"}}
{"id": "e712b032a3d40d425cd35e6e3a876d2d", "question": {"question_concept": "learning", "choices": [{"label": "A", "text": "try new"}, {"label": "B", "text": "headaches"}, {"label": "C", "text": "fall asleep"}, {"label": "D", "text": "intelligence"}, {"label": "E", "text": "pride"}], "stem": "What might happen with too much learning?"}}
{"id": "58a0402c423758da760ed2deb5669be0", "question": {"question_concept": "dreaming", "choices": [{"label": "A", "text": "wake up"}, {"label": "B", "text": "none"}, {"label": "C", "text": "fantasy"}, {"label": "D", "text": "inspiration"}, {"label": "E", "text": "car accident"}], "stem": "What can happen if you are dreaming when you should be watching your surroundings?"}}
{"id": "a8c3331e20d1c4d347a597a0a0f16f25", "question": {"question_concept": "curiosity", "choices": [{"label": "A", "text": "travel"}, {"label": "B", "text": "find truth"}, {"label": "C", "text": "go to market"}, {"label": "D", "text": "attend school"}, {"label": "E", "text": "examine thing"}], "stem": "John had a great deal of curiosity about all things.  So when all of his friends were getting jobs, he decided to continue doing what?"}}
{"id": "6134ef4ec50da0d5c436c866c02c6a6d", "question": {"question_concept": "spending money", "choices": [{"label": "A", "text": "bankruptcy"}, {"label": "B", "text": "clutter"}, {"label": "C", "text": "debt"}, {"label": "D", "text": "birthday party"}, {"label": "E", "text": "poverty"}], "stem": "What happens when spending money without paying someone back?"}}
{"id": "6c2cb78f9689a40b4a7ad660fbf06bf5", "question": {"question_concept": "car", "choices": [{"label": "A", "text": "head north"}, {"label": "B", "text": "slow down"}, {"label": "C", "text": "speed up"}, {"label": "D", "text": "quicken"}, {"label": "E", "text": "heading north"}], "stem": "A car was going from a full stop to pulling on to an on ramp, what must it have done?"}}
{"id": "6abae2ffdbe0732b481918e896fe10c1", "question": {"question_concept": "sitting quietly", "choices": [{"label": "A", "text": "falling asleep"}, {"label": "B", "text": "calm"}, {"label": "C", "text": "discomfort"}, {"label": "D", "text": "solitude"}, {"label": "E", "text": "relaxation"}], "stem": "James was sitting quietly in a hard metal chair with sharp edges.  He wanted to express something, but remained silent due to fear of annoying others.  What didn't he express?"}}
{"id": "c0c1a0111bf39056c48d6b5877247c77", "question": {"question_concept": "baseball stadium", "choices": [{"label": "A", "text": "san francisco"}, {"label": "B", "text": "chicago"}, {"label": "C", "text": "las vegas"}, {"label": "D", "text": "urban areas"}, {"label": "E", "text": "phoenix"}], "stem": "Which city's baseball stadium does the team who won the world series in 2016 play?"}}
{"id": "f73032836e6a82c3364131c057fe3411", "question": {"question_concept": "condominium", "choices": [{"label": "A", "text": "florida"}, {"label": "B", "text": "towels"}, {"label": "C", "text": "pirates"}, {"label": "D", "text": "michigan"}, {"label": "E", "text": "complex"}], "stem": "What could a condominium be?"}}
{"id": "76d448608a97ed0bf0779d4b4861c1a1", "question": {"question_concept": "mighty", "choices": [{"label": "A", "text": "insignificant"}, {"label": "B", "text": "powerless"}, {"label": "C", "text": "weakling"}, {"label": "D", "text": "helpless"}, {"label": "E", "text": "unimportant"}], "stem": "What is the opposite of someone who is mighty and powerful?"}}
{"id": "c0885e5dfc3133811852d7a51476b004", "question": {"question_concept": "running after ball", "choices": [{"label": "A", "text": "laughter"}, {"label": "B", "text": "sweating"}, {"label": "C", "text": "pregnancy"}, {"label": "D", "text": "breathing heavily"}, {"label": "E", "text": "tiredness"}], "stem": "A person that is out of shape might experience what physical effect while running after ball?"}}
{"id": "5a1417149279533eda065aa76c081a06", "question": {"question_concept": "food", "choices": [{"label": "A", "text": "fridge"}, {"label": "B", "text": "kitchen"}, {"label": "C", "text": "cooking show"}, {"label": "D", "text": "supermarket"}, {"label": "E", "text": "table"}], "stem": "Where do families usually enjoy food?"}}
{"id": "b9e58e5799de17a178f3bd48aa94c730", "question": {"question_concept": "rescue", "choices": [{"label": "A", "text": "corrupt"}, {"label": "B", "text": "hide and seek"}, {"label": "C", "text": "endanger"}, {"label": "D", "text": "kidnap"}, {"label": "E", "text": "arrest"}], "stem": "The police moved in to rescue the little girl, it was a good ending to the what?"}}
{"id": "522259b020466e10b1032a5ae74e6162", "question": {"question_concept": "contemplate", "choices": [{"label": "A", "text": "become distracted"}, {"label": "B", "text": "get ideas"}, {"label": "C", "text": "thinking"}, {"label": "D", "text": "hatred"}, {"label": "E", "text": "daydream"}], "stem": "The park was packed, he came to contemplate but it was clear he was going to what?"}}
{"id": "60cc899ed3644ec0a792141f530a3ac2", "question": {"question_concept": "house", "choices": [{"label": "A", "text": "residential area"}, {"label": "B", "text": "across the street"}, {"label": "C", "text": "street"}, {"label": "D", "text": "subdivision"}, {"label": "E", "text": "city"}], "stem": "The house didn't have a driveway, so where did its inhabitants have to park?"}}
{"id": "aa080b8da2748f0caeb124f55e03941a", "question": {"question_concept": "story", "choices": [{"label": "A", "text": "floor"}, {"label": "B", "text": "communicating moral"}, {"label": "C", "text": "near end"}, {"label": "D", "text": "end badly"}, {"label": "E", "text": "exciting to the end"}], "stem": "The story started out good, but it was terrible.  How might you describe the story?"}}
{"id": "7e6d1ea97171b01c39ad8d382d05b502", "question": {"question_concept": "dressing room", "choices": [{"label": "A", "text": "theatre"}, {"label": "B", "text": "church"}, {"label": "C", "text": "swimming pool"}, {"label": "D", "text": "gym"}, {"label": "E", "text": "department store"}], "stem": "Mary is changing into a period costume in her dressing room.  What sort of building is she most likely in?"}}
{"id": "66109f126c1a3975eb96110ff7d933ab", "question": {"question_concept": "living", "choices": [{"label": "A", "text": "reproducing"}, {"label": "B", "text": "food consumed"}, {"label": "C", "text": "eventually die"}, {"label": "D", "text": "grow leaves"}, {"label": "E", "text": "respiration"}], "stem": "Grim sure, but living things will what?"}}
{"id": "6fb014460f0f5ca8161ca8fabbb799c4", "question": {"question_concept": "mustard", "choices": [{"label": "A", "text": "fast food restaurant"}, {"label": "B", "text": "refrigerator"}, {"label": "C", "text": "fridge"}, {"label": "D", "text": "jar"}, {"label": "E", "text": "grocery shops"}], "stem": "Where can you find little packets of mustard?"}}
{"id": "932de3920bbfd30c898549490dc39ad4", "question": {"question_concept": "dirt", "choices": [{"label": "A", "text": "subway"}, {"label": "B", "text": "garden"}, {"label": "C", "text": "street"}, {"label": "D", "text": "fingernails"}, {"label": "E", "text": "bed"}], "stem": "There was too much dirt being tracked in from the unpaved road.  The what was covered with it?"}}
{"id": "38803891b0daa4025b532e33fa99f5fd_1", "question": {"question_concept": "night owl", "choices": [{"label": "A", "text": "early bird"}, {"label": "B", "text": "morning person"}, {"label": "C", "text": "early riser"}, {"label": "D", "text": "lark"}, {"label": "E", "text": "night owl"}], "stem": "The night owl begin coo his song as night fell, the songbirds of the day, such as the what, took a rest?"}}
{"id": "ab3c9e63a6533e9b769d613945d0eaaa", "question": {"question_concept": "bookshop", "choices": [{"label": "A", "text": "big city"}, {"label": "B", "text": "mall"}, {"label": "C", "text": "department store"}, {"label": "D", "text": "amazon"}, {"label": "E", "text": "student union"}], "stem": "Danny couldn't find the novel he wanted in his local bookshop or any other stores near him, so he decided to drive where to look for it?"}}
{"id": "444763de101ec847c2a7a20ce3b8607a", "question": {"question_concept": "president", "choices": [{"label": "A", "text": "whitehouse"}, {"label": "B", "text": "pta"}, {"label": "C", "text": "store"}, {"label": "D", "text": "corporation"}, {"label": "E", "text": "government"}], "stem": "The CEO is kind of like the president of a what?"}}
{"id": "8e1f85ef0d83b461de75db88587f8d61", "question": {"question_concept": "slow", "choices": [{"label": "A", "text": "slowplay"}, {"label": "B", "text": "hasty"}, {"label": "C", "text": "prompt"}, {"label": "D", "text": "rapid"}, {"label": "E", "text": "okay"}], "stem": "Sarah was very slow at making judgments.  She didn't like being what?"}}
{"id": "783b8fd37dfc3e8f4d10cfb055f8f5f2", "question": {"question_concept": "snake", "choices": [{"label": "A", "text": "ditch"}, {"label": "B", "text": "rude"}, {"label": "C", "text": "sun itself"}, {"label": "D", "text": "pet"}, {"label": "E", "text": "obesity"}], "stem": "The snake generally left people alone, but what kinds of hikers did it bite?"}}
{"id": "dad94fba9d1f2aac768d69cf5df60e39", "question": {"question_concept": "weasel", "choices": [{"label": "A", "text": "the yard"}, {"label": "B", "text": "children's song"}, {"label": "C", "text": "washington d.c"}, {"label": "D", "text": "congress"}, {"label": "E", "text": "chicken coop"}], "stem": "Where does the weasel go pop?"}}
{"id": "1881bbb2ed61353bf039152ce4c5c284", "question": {"question_concept": "using computer", "choices": [{"label": "A", "text": "anger"}, {"label": "B", "text": "happiness"}, {"label": "C", "text": "program created"}, {"label": "D", "text": "stress"}, {"label": "E", "text": "carpal tunnel syndrome"}], "stem": "A person specializing in computer science and using a computer will likely be doing what?"}}
{"id": "5db775543ce96716ac31d591aa64812a", "question": {"question_concept": "thanking", "choices": [{"label": "A", "text": "joy"}, {"label": "B", "text": "shake hands"}, {"label": "C", "text": "smile"}, {"label": "D", "text": "show your face"}, {"label": "E", "text": "appreciation"}], "stem": "What do you want to show by thanking someone?"}}
{"id": "05367f6c6b1714bc9d0cb41b9ca128dc", "question": {"question_concept": "remembering", "choices": [{"label": "A", "text": "sleep"}, {"label": "B", "text": "depression"}, {"label": "C", "text": "knowledge"}, {"label": "D", "text": "pleasure"}, {"label": "E", "text": "knowing"}], "stem": "Remembering your parents death can lead to what?"}}
{"id": "ea9864dc2740c60cbdc2214021fce5e8", "question": {"question_concept": "having sex", "choices": [{"label": "A", "text": "getting pregnant"}, {"label": "B", "text": "unwanted pregnancy"}, {"label": "C", "text": "aids"}, {"label": "D", "text": "making babies"}, {"label": "E", "text": "boredom"}], "stem": "What does having sex in a marriage lead to?"}}
{"id": "4c382cd0591c57700ae2784bd92878dc", "question": {"question_concept": "coast", "choices": [{"label": "A", "text": "united states"}, {"label": "B", "text": "florida"}, {"label": "C", "text": "denmark"}, {"label": "D", "text": "map"}, {"label": "E", "text": "california"}], "stem": "What country on the coast of Europe is the setting of Hamlet?"}}
{"id": "3f137996718762973ce2d50ad77d6fb0", "question": {"question_concept": "travellers", "choices": [{"label": "A", "text": "rest area"}, {"label": "B", "text": "airport"}, {"label": "C", "text": "bus depot"}, {"label": "D", "text": "train station"}, {"label": "E", "text": "subway"}], "stem": "Where can travellers go to catch a flight?"}}
{"id": "f0d8e473755a2fb68da4edd0feac06a1", "question": {"question_concept": "merchant", "choices": [{"label": "A", "text": "business"}, {"label": "B", "text": "driving"}, {"label": "C", "text": "store"}, {"label": "D", "text": "mall"}, {"label": "E", "text": "shopping center"}], "stem": "The merchant had to open up shop, what kind of license did he apply for?"}}
{"id": "16acdb35889b9051b7c2ead8d2c279ba", "question": {"question_concept": "water", "choices": [{"label": "A", "text": "sewer"}, {"label": "B", "text": "drenching"}, {"label": "C", "text": "ocean"}, {"label": "D", "text": "septic tank"}, {"label": "E", "text": "planet earth"}], "stem": "Where does toilet water go in a house?"}}
{"id": "846caa7353e40434bb2de5a0e1837bd8", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "learn from each other"}, {"label": "B", "text": "use weapons"}, {"label": "C", "text": "poppy flower"}, {"label": "D", "text": "smoke marijuana"}, {"label": "E", "text": "desire to travel"}], "stem": "What was my neighbor growing a plant behind his house for before he got arrested for it?"}}
{"id": "3c26098ec2326c220b979859b1c6add5", "question": {"question_concept": "neighbor", "choices": [{"label": "A", "text": "ditch"}, {"label": "B", "text": "being friends with"}, {"label": "C", "text": "suburbs"}, {"label": "D", "text": "house next door"}, {"label": "E", "text": "china"}], "stem": "Where does you nearest neighbor live?"}}
{"id": "d7fb6c1ce12e566e2c77f0465a0756bd", "question": {"question_concept": "having fun", "choices": [{"label": "A", "text": "loony tune"}, {"label": "B", "text": "visiting friends"}, {"label": "C", "text": "being understood"}, {"label": "D", "text": "playing around"}, {"label": "E", "text": "hug"}], "stem": "The couple was having fun with the activity, when they won they celebrated with a what?"}}
{"id": "ea392b261239d4458de93591d19bbf0f", "question": {"question_concept": "ficus", "choices": [{"label": "A", "text": "park"}, {"label": "B", "text": "lobby"}, {"label": "C", "text": "good health"}, {"label": "D", "text": "arboretum"}, {"label": "E", "text": "own home"}], "stem": "Where would you find a ficus in a hotel?"}}
{"id": "3a9bc8e3f50ef67cbe0e47973a224dc6", "question": {"question_concept": "death", "choices": [{"label": "A", "text": "war"}, {"label": "B", "text": "hospital"}, {"label": "C", "text": "funeral"}, {"label": "D", "text": "battlefield"}, {"label": "E", "text": "morgue"}], "stem": "In times of peace, in what building is it most likely that one encounters death?"}}
{"id": "17b59d353476da2d245a03b1f4a1a201", "question": {"question_concept": "killing people", "choices": [{"label": "A", "text": "going to jail"}, {"label": "B", "text": "get arrested"}, {"label": "C", "text": "going to prison"}, {"label": "D", "text": "murder"}, {"label": "E", "text": "die"}], "stem": "James thought that killing people was dangerous.  He didn't want to waste his life by doing what?"}}
{"id": "14fb57e6a178bab1e09e2a6ccc69ad27", "question": {"question_concept": "print", "choices": [{"label": "A", "text": "cursive writing"}, {"label": "B", "text": "online media"}, {"label": "C", "text": "shifting work"}, {"label": "D", "text": "handwriting"}, {"label": "E", "text": "handwritten"}], "stem": "Even if he tried to print it was still chicken scratch to others, his what was just illegible?"}}
{"id": "dc894375cc67fcf6e118db41c3800812", "question": {"question_concept": "toilet", "choices": [{"label": "A", "text": "refill"}, {"label": "B", "text": "smell bad"}, {"label": "C", "text": "flush"}, {"label": "D", "text": "flushed"}, {"label": "E", "text": "got dirty"}], "stem": "He had been a janitor for years, so long that the toilet no longer even what?"}}
{"id": "94bc39d5a2975424ebfdb84d05a803ec", "question": {"question_concept": "wound", "choices": [{"label": "A", "text": "injured person"}, {"label": "B", "text": "emergency room"}, {"label": "C", "text": "hospital"}, {"label": "D", "text": "patient"}, {"label": "E", "text": "soldier"}], "stem": "Who would have a wound on their body?"}}
{"id": "cafaad910e33d823e518671b10d0bda4", "question": {"question_concept": "bar stool", "choices": [{"label": "A", "text": "kitchen"}, {"label": "B", "text": "cafeteria"}, {"label": "C", "text": "tavern"}, {"label": "D", "text": "restaurant"}, {"label": "E", "text": "drunker"}], "stem": "Where are you likely to find only a bar stool for seating?"}}
{"id": "75d45b9ae60a5f2688e45e17155717c0", "question": {"question_concept": "music stand", "choices": [{"label": "A", "text": "orchestra"}, {"label": "B", "text": "practice room"}, {"label": "C", "text": "music room"}, {"label": "D", "text": "concert hall"}, {"label": "E", "text": "music store"}], "stem": "It was hours before his performance, he set up his music stand on stage and stared out into the empty what?"}}
{"id": "9548bd24b87d582eb62519eb752368fe", "question": {"question_concept": "magazines", "choices": [{"label": "A", "text": "table"}, {"label": "B", "text": "library"}, {"label": "C", "text": "market"}, {"label": "D", "text": "old book shop"}, {"label": "E", "text": "doctor"}], "stem": "After perusal, a magazine will need such a resting place."}}
{"id": "b2dde0637b822f66c2ab2643361c525f", "question": {"question_concept": "student", "choices": [{"label": "A", "text": "read book"}, {"label": "B", "text": "think for himself"}, {"label": "C", "text": "cross road"}, {"label": "D", "text": "wait in line"}, {"label": "E", "text": "manage their emotions"}], "stem": "What do students who are crossing guards help other students do?"}}
{"id": "f553595cece95ba38c7aaeb9c0ad9628", "question": {"question_concept": "read book", "choices": [{"label": "A", "text": "bored"}, {"label": "B", "text": "get comfortable"}, {"label": "C", "text": "open up"}, {"label": "D", "text": "learn new"}, {"label": "E", "text": "enjoyable"}], "stem": "The bookworm would read book after book, she found the pastime what?"}}
{"id": "15b56862896c020f0d0084990dbb3ae8", "question": {"question_concept": "singers", "choices": [{"label": "A", "text": "sound beautiful"}, {"label": "B", "text": "clear throats"}, {"label": "C", "text": "warm up"}, {"label": "D", "text": "use microphones"}, {"label": "E", "text": "create music"}], "stem": "Many singers might have to do this at some point?"}}
{"id": "7378836a7a0d450cf2f3c1c4a74fc103", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "town"}, {"label": "B", "text": "train station"}, {"label": "C", "text": "conference"}, {"label": "D", "text": "on vacation"}, {"label": "E", "text": "apartment"}], "stem": "Where do people often go to leave?"}}
{"id": "a2a494e28e0a2d86677d07ef57fcbf20", "question": {"question_concept": "awaking", "choices": [{"label": "A", "text": "shock"}, {"label": "B", "text": "depression"}, {"label": "C", "text": "tiredness"}, {"label": "D", "text": "get up"}, {"label": "E", "text": "headache"}], "stem": "Bob awakens in his bed.  His head is pounding from the alcohol he had last night but he has work to do and is determined to get it done.  What's the first thing he does?"}}
{"id": "fe9fb570190d1ca503092b0628880538", "question": {"question_concept": "contralto", "choices": [{"label": "A", "text": "concert"}, {"label": "B", "text": "describe singing voice"}, {"label": "C", "text": "choir"}, {"label": "D", "text": "fun"}, {"label": "E", "text": "chorus"}], "stem": "A contralto is a singer in what type of group?"}}
{"id": "cc175eb8899f36839c4ac9ebf73073f0", "question": {"question_concept": "auditorium", "choices": [{"label": "A", "text": "concert"}, {"label": "B", "text": "crowd"}, {"label": "C", "text": "high school"}, {"label": "D", "text": "theater"}, {"label": "E", "text": "lights"}], "stem": "What would I call a bunch of people I see in an auditorium?"}}
{"id": "4c54e3be4a1082aede3b92bf9ae30927", "question": {"question_concept": "river", "choices": [{"label": "A", "text": "country"}, {"label": "B", "text": "continent"}, {"label": "C", "text": "wilderness"}, {"label": "D", "text": "waterfall"}, {"label": "E", "text": "in a sink"}], "stem": "Where do you find an undiscovered river?"}}
{"id": "c1e5dc0c00c863a7fca8296b92c35df7", "question": {"question_concept": "tree", "choices": [{"label": "A", "text": "bud"}, {"label": "B", "text": "provide shelter"}, {"label": "C", "text": "university"}, {"label": "D", "text": "produce fruit"}, {"label": "E", "text": "state park"}], "stem": "Trees form what in the spring which eventually sprout leaves?"}}
{"id": "626ec50bab94824e2450f88bfa081798", "question": {"question_concept": "becoming inebriated", "choices": [{"label": "A", "text": "pass out"}, {"label": "B", "text": "unruly"}, {"label": "C", "text": "death and destruction"}, {"label": "D", "text": "fall down"}, {"label": "E", "text": "drunkenness"}], "stem": "She was becoming inebriated, so what happened after stumbling on the cobblestone?"}}
{"id": "0486de1e9336bda4283baef0676347e3", "question": {"question_concept": "hiking", "choices": [{"label": "A", "text": "drink water"}, {"label": "B", "text": "get lost"}, {"label": "C", "text": "get tired"}, {"label": "D", "text": "enjoy nature"}, {"label": "E", "text": "new backpack"}], "stem": "What could cause your hiking trip to continue indefinitely?"}}
{"id": "8c1f41b1d827900c819faf90eed4a962", "question": {"question_concept": "eat dinner", "choices": [{"label": "A", "text": "choke"}, {"label": "B", "text": "feel full"}, {"label": "C", "text": "watch tv"}, {"label": "D", "text": "chewing"}, {"label": "E", "text": "forget"}], "stem": "He began to eat dinner, suddenly he stood up in a startle because he was starting to what?"}}
{"id": "bb21c30f99152d1ada4bef420cd1718c", "question": {"question_concept": "friends", "choices": [{"label": "A", "text": "hang out"}, {"label": "B", "text": "meet for lunch"}, {"label": "C", "text": "borrow money"}, {"label": "D", "text": "ask for money"}, {"label": "E", "text": "keep secrets"}], "stem": "What would a friend do if they wanted to a new car but couldn't afford it?"}}
{"id": "cf79b99398fe71d14cb03645098c2fa8", "question": {"question_concept": "referee", "choices": [{"label": "A", "text": "gymnastics"}, {"label": "B", "text": "soccer game"}, {"label": "C", "text": "sporting event"}, {"label": "D", "text": "hockey game"}, {"label": "E", "text": "football"}], "stem": "The referee was confused. There was no ice on the field, this wasn't what he expected.  But the stands were full of rowdy fans, he just had to read the rule book really quickly and figure out what those two big forks were for.   What game might he be officiating?"}}
{"id": "ad89eacbdad2db9876a7f7547f564295", "question": {"question_concept": "committing suicide", "choices": [{"label": "A", "text": "anguish"}, {"label": "B", "text": "death"}, {"label": "C", "text": "misery"}, {"label": "D", "text": "being dead"}, {"label": "E", "text": "sorrow"}], "stem": "What does committing suicide cause?"}}
{"id": "3c55495b467d885d679efbd585006a06", "question": {"question_concept": "marmoset", "choices": [{"label": "A", "text": "underground"}, {"label": "B", "text": "dictionary"}, {"label": "C", "text": "latin america"}, {"label": "D", "text": "jungle"}, {"label": "E", "text": "wilderness"}], "stem": "Where would you find out what a marmoset is?"}}
{"id": "a0d623b4f633388137ec1d8e9bad824b", "question": {"question_concept": "going into trance", "choices": [{"label": "A", "text": "closed eyes"}, {"label": "B", "text": "loss of control"}, {"label": "C", "text": "headache"}, {"label": "D", "text": "memory loss"}, {"label": "E", "text": "confusion"}], "stem": "Before going into a trance, a hypnotist will ask you to make sure you have what?"}}
{"id": "3873fc1072467cfc3b531a673af7cc01", "question": {"question_concept": "peanut butter", "choices": [{"label": "A", "text": "supermarket"}, {"label": "B", "text": "cupboard"}, {"label": "C", "text": "jar"}, {"label": "D", "text": "peanut farm"}, {"label": "E", "text": "container"}], "stem": "Where are there usually a variety of peanut butter?"}}
{"id": "b5b9997bfac08d775789e4fd4fedc325", "question": {"question_concept": "map", "choices": [{"label": "A", "text": "amusement park"}, {"label": "B", "text": "truck stop"}, {"label": "C", "text": "classroom"}, {"label": "D", "text": "playground"}, {"label": "E", "text": "backpack"}], "stem": "Where might one fight a map of the world?"}}
{"id": "f66a61967465b701120a6db518d7dd4a", "question": {"question_concept": "channel", "choices": [{"label": "A", "text": "england"}, {"label": "B", "text": "television"}, {"label": "C", "text": "river"}, {"label": "D", "text": "swimming"}, {"label": "E", "text": "waterway"}], "stem": "Sill thought that the channel would be fun to swim acroll, but she really just wanted to change the channel because she didn't like what was on.  What was Sill doing?"}}
{"id": "eeb5d6cddca714f47655ba58b61f3720", "question": {"question_concept": "dogs", "choices": [{"label": "A", "text": "pant"}, {"label": "B", "text": "jump"}, {"label": "C", "text": "fleas"}, {"label": "D", "text": "bite"}, {"label": "E", "text": "mate"}], "stem": "Dogs get in heat when they feel it is time to what?"}}
{"id": "38d4136cd3ddf3d43534c0db6ff2f425", "question": {"question_concept": "company", "choices": [{"label": "A", "text": "branch out"}, {"label": "B", "text": "commit crime"}, {"label": "C", "text": "liquidated"}, {"label": "D", "text": "own resources"}, {"label": "E", "text": "ship goods"}], "stem": "What does a company do when they need to add new business?"}}
{"id": "892dcba0b1ab8b0ac4223952e2e4aae7", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "fell down"}, {"label": "B", "text": "celebrated"}, {"label": "C", "text": "fulfilled"}, {"label": "D", "text": "simplicity"}, {"label": "E", "text": "headache"}], "stem": "After scoring the person what?"}}
{"id": "d6cabec44619e891ff40e91c0d519777", "question": {"question_concept": "passenger", "choices": [{"label": "A", "text": "bus depot"}, {"label": "B", "text": "jumping"}, {"label": "C", "text": "bus stop"}, {"label": "D", "text": "arrive at destination"}, {"label": "E", "text": "airport"}], "stem": "How can a passenger get in the air?"}}
{"id": "7892c4226d407fde1c81072dee548560", "question": {"question_concept": "groceries", "choices": [{"label": "A", "text": "supermarket"}, {"label": "B", "text": "bathroom"}, {"label": "C", "text": "cabinet"}, {"label": "D", "text": "trunk"}, {"label": "E", "text": "shelf"}], "stem": "Where would you go if you need some groceries?"}}
{"id": "6c099e9401ecda2aaebb8e7224ed6d82", "question": {"question_concept": "books", "choices": [{"label": "A", "text": "include information"}, {"label": "B", "text": "win prizes"}, {"label": "C", "text": "explain problems"}, {"label": "D", "text": "learn"}, {"label": "E", "text": "further knowledge"}], "stem": "Some schools give an incentive to kids, if they read their books they can what?"}}
{"id": "1b9076685d81e0e4034514a3b93b38a5", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "talk to himself"}, {"label": "B", "text": "be blinded"}, {"label": "C", "text": "cross street"}, {"label": "D", "text": "cast shadow"}, {"label": "E", "text": "continue learning"}], "stem": "If there is a source of light, a person will do what opposite of it?"}}
{"id": "d747552c4295f369c56b16f2a8047a49", "question": {"question_concept": "dust", "choices": [{"label": "A", "text": "attic"}, {"label": "B", "text": "television"}, {"label": "C", "text": "radio"}, {"label": "D", "text": "most buildings"}, {"label": "E", "text": "closet"}], "stem": "What do you clean dust off of to see more clearly?"}}
{"id": "67cf5df15145ba7612c75b9a6be321b3", "question": {"question_concept": "solicitor", "choices": [{"label": "A", "text": "free work"}, {"label": "B", "text": "claim compensation"}, {"label": "C", "text": "buy house"}, {"label": "D", "text": "write letter"}, {"label": "E", "text": "court proceedings"}], "stem": "How can a solicitor help in gaining an asset?"}}
{"id": "c0c1bf18eaba41cafe2951559f5df14d", "question": {"question_concept": "stars", "choices": [{"label": "A", "text": "night sky"}, {"label": "B", "text": "sky at night"}, {"label": "C", "text": "outer space"}, {"label": "D", "text": "airport"}, {"label": "E", "text": "universe"}], "stem": "As he contemplated the stars, James felt that he wanted to visit them, to fly into what?"}}
{"id": "f1f8dc48a611b2f0a8bbb39304c7711d", "question": {"question_concept": "helping", "choices": [{"label": "A", "text": "satisfaction"}, {"label": "B", "text": "better world"}, {"label": "C", "text": "complications"}, {"label": "D", "text": "feel good about yourself"}, {"label": "E", "text": "happy"}], "stem": "Tim thought that helping people was nice.  He believed that it would make you what?"}}
{"id": "feef006f2de256e69d3f24100fcac7c1", "question": {"question_concept": "spider", "choices": [{"label": "A", "text": "basement"}, {"label": "B", "text": "cellar"}, {"label": "C", "text": "mail box"}, {"label": "D", "text": "chatroom"}, {"label": "E", "text": "web"}], "stem": "What room is a spider likely to be found in?"}}
{"id": "123ad6a93b3d2dc31418bb232386bb0f", "question": {"question_concept": "cash register", "choices": [{"label": "A", "text": "supermarket"}, {"label": "B", "text": "bookstore"}, {"label": "C", "text": "craft store"}, {"label": "D", "text": "toy store"}, {"label": "E", "text": "shop"}], "stem": "Where do you find a librarian type at the cash register?"}}
{"id": "a086e49b7864619b40e2b96e4463ca03_1", "question": {"question_concept": "horse", "choices": [{"label": "A", "text": "field"}, {"label": "B", "text": "washington"}, {"label": "C", "text": "painting"}, {"label": "D", "text": "minnesota"}, {"label": "E", "text": "farmyard"}], "stem": "The horse wanted to graze, where did it need to go?"}}
{"id": "68dd334be2b3fbee17de657692a275c6", "question": {"question_concept": "money", "choices": [{"label": "A", "text": "buy christmas presents"}, {"label": "B", "text": "eat out"}, {"label": "C", "text": "pass course"}, {"label": "D", "text": "create art"}, {"label": "E", "text": "spend"}], "stem": "She was always living paycheck to paycheck, when she would get money she would what?"}}
{"id": "a191779158f866faf7667e5ba1753756", "question": {"question_concept": "cook", "choices": [{"label": "A", "text": "add egg"}, {"label": "B", "text": "use scissors"}, {"label": "C", "text": "brown meat"}, {"label": "D", "text": "open can"}, {"label": "E", "text": "bake bread"}], "stem": "When I cook, what is the first step to take to get my item out of its cylinder container?"}}
{"id": "2dc9c60cdef4f2692b236fd4596e37e7", "question": {"question_concept": "watch film", "choices": [{"label": "A", "text": "use reasoning"}, {"label": "B", "text": "have fun"}, {"label": "C", "text": "pass time"}, {"label": "D", "text": "interesting"}, {"label": "E", "text": "see what happens"}], "stem": "When you watch a film and want to figure out a crime, you need to?"}}
{"id": "76f9327bab7464379db655233645dc71", "question": {"question_concept": "finger", "choices": [{"label": "A", "text": "hand"}, {"label": "B", "text": "hot stove"}, {"label": "C", "text": "glove"}, {"label": "D", "text": "nose"}, {"label": "E", "text": "ring"}], "stem": "Where should a finger not go?"}}
{"id": "e1fcc1199dfe403add475549c08e06dc", "question": {"question_concept": "showroom", "choices": [{"label": "A", "text": "electronics store"}, {"label": "B", "text": "appliance store"}, {"label": "C", "text": "car dealership"}, {"label": "D", "text": "vegas"}, {"label": "E", "text": "bathroom"}], "stem": "The showroom featured slot machines, where was it located?"}}
{"id": "5181ea8ed136015425f81d4abe57600e", "question": {"question_concept": "ball", "choices": [{"label": "A", "text": "toy"}, {"label": "B", "text": "earball"}, {"label": "C", "text": "bounces"}, {"label": "D", "text": "medium"}, {"label": "E", "text": "play with"}], "stem": "The ball escaped from their yard.  What did the ball do?"}}
{"id": "0b75a0ccf3b43a1e5ea837b4fddd6680", "question": {"question_concept": "cheese", "choices": [{"label": "A", "text": "plate"}, {"label": "B", "text": "mouse trap"}, {"label": "C", "text": "chair"}, {"label": "D", "text": "fridge"}, {"label": "E", "text": "refrigerator"}], "stem": "How is cheese likely to be served at a restaurant?"}}
{"id": "1e61dd7081801d655511345ca49bb8a5", "question": {"question_concept": "meet people", "choices": [{"label": "A", "text": "go to parties"}, {"label": "B", "text": "go outside"}, {"label": "C", "text": "telling jokes"}, {"label": "D", "text": "take class"}, {"label": "E", "text": "friendly"}], "stem": "It was a fun place to meet people, everybody was very what?"}}
{"id": "08dc46bc31cf7bc6130ad428b8cb4461", "question": {"question_concept": "sitting quietly", "choices": [{"label": "A", "text": "reading"}, {"label": "B", "text": "ponder"}, {"label": "C", "text": "fall asleep"}, {"label": "D", "text": "think"}, {"label": "E", "text": "enlightenment"}], "stem": "What could someone be doing if he or she is sitting quietly with his or her eyes open and nothing is in front of him or her?"}}
{"id": "358774bb18adcfeb49a8d5b2db08731b", "question": {"question_concept": "relaxing", "choices": [{"label": "A", "text": "fall asleep"}, {"label": "B", "text": "sleeping"}, {"label": "C", "text": "invigorating"}, {"label": "D", "text": "feeling better"}, {"label": "E", "text": "stress"}], "stem": "What is often the end result of relaxing?"}}
{"id": "3a4e289bf712b7beea4a2b0ae30389ee", "question": {"question_concept": "running", "choices": [{"label": "A", "text": "leg cramps"}, {"label": "B", "text": "knees"}, {"label": "C", "text": "becoming tired"}, {"label": "D", "text": "tiredness"}, {"label": "E", "text": "sore feet"}], "stem": "What is the lowest point of pain that running would cause?"}}
{"id": "5b37688a89cb0b33cab38956f8279db6", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "immune system"}, {"label": "B", "text": "butt"}, {"label": "C", "text": "one head"}, {"label": "D", "text": "fingernails"}, {"label": "E", "text": "name"}], "stem": "What do you usually call a person by?"}}
{"id": "1f064676170059b500a8f5d1d880c14f", "question": {"question_concept": "bomb", "choices": [{"label": "A", "text": "afghanistan"}, {"label": "B", "text": "arsenal"}, {"label": "C", "text": "suitcase"}, {"label": "D", "text": "aircraft"}, {"label": "E", "text": "arms depot"}], "stem": "If a terrorist wanted to blow something up, where would they keep their bomb?"}}
{"id": "cc07972c47c3b286816d04098c6e91b7", "question": {"question_concept": "setting cup on table", "choices": [{"label": "A", "text": "doesn"}, {"label": "B", "text": "tinkling sound"}, {"label": "C", "text": "kitty litter"}, {"label": "D", "text": "knocking over"}, {"label": "E", "text": "clutter"}], "stem": "John downed a glass table.  When he was setting his cup on the table he noticed something. What mgith he have noticed?"}}
{"id": "5a74721b0b6c9d6fd414d54e5b3e5efe", "question": {"question_concept": "car", "choices": [{"label": "A", "text": "race track"}, {"label": "B", "text": "city"}, {"label": "C", "text": "parking lot"}, {"label": "D", "text": "repair shop"}, {"label": "E", "text": "factory"}], "stem": "Where do many people choose not own a car?"}}
{"id": "c738af7a26aa04c9246a92d761897902", "question": {"question_concept": "in charge of project", "choices": [{"label": "A", "text": "have knowledge"}, {"label": "B", "text": "president"}, {"label": "C", "text": "take charge"}, {"label": "D", "text": "success"}, {"label": "E", "text": "boss"}], "stem": "What do you call the person in charge of a project that you are working on?"}}
{"id": "ed62186380393bedf56b41f23601fd9a", "question": {"question_concept": "food", "choices": [{"label": "A", "text": "pantry"}, {"label": "B", "text": "plate"}, {"label": "C", "text": "in a barn"}, {"label": "D", "text": "oven"}, {"label": "E", "text": "stomach"}], "stem": "Where is shelf stable food stored?"}}
{"id": "b88ce7680d1798e0798d639f81d6dc7f", "question": {"question_concept": "music stand", "choices": [{"label": "A", "text": "rehearsal"}, {"label": "B", "text": "music store"}, {"label": "C", "text": "orchestra"}, {"label": "D", "text": "concert hall"}, {"label": "E", "text": "practice room"}], "stem": "The janitors job today was a break for the normal, he got to set up many a music stand for the entire what performing that night?"}}
{"id": "b4bf290db8e532936d7f92479a159d80", "question": {"question_concept": "watch tv", "choices": [{"label": "A", "text": "sitting"}, {"label": "B", "text": "fall asleep"}, {"label": "C", "text": "eat"}, {"label": "D", "text": "have tv"}, {"label": "E", "text": "learn"}], "stem": "What position are people in typically when they watch tv?"}}
{"id": "26c20db2db6dd38f329e939c2b6313e5", "question": {"question_concept": "tickling", "choices": [{"label": "A", "text": "laughing"}, {"label": "B", "text": "giggling"}, {"label": "C", "text": "nausea"}, {"label": "D", "text": "crying"}, {"label": "E", "text": "getting kicked"}], "stem": "What effect of tickling can be misconstrued as fun even in the person wants it to stop?"}}
{"id": "57ddbce14bf86ca4c8d4cc7c925bdf47", "question": {"question_concept": "latter", "choices": [{"label": "A", "text": "aforesaid"}, {"label": "B", "text": "aforementioned"}, {"label": "C", "text": "earlier"}, {"label": "D", "text": "former"}, {"label": "E", "text": "prior"}], "stem": "When given two options, he chose the latter, what did he disregard?"}}
{"id": "bb7368d2ab3b39d496f5f3c831b3dee2", "question": {"question_concept": "country", "choices": [{"label": "A", "text": "africa"}, {"label": "B", "text": "asia"}, {"label": "C", "text": "world"}, {"label": "D", "text": "dirt"}, {"label": "E", "text": "region"}], "stem": "The country has deserts, forests, and plains; You could say it has many a what?"}}
{"id": "********************************", "question": {"question_concept": "buy", "choices": [{"label": "A", "text": "paying"}, {"label": "B", "text": "have in mind"}, {"label": "C", "text": "taking receipt"}, {"label": "D", "text": "spend money"}, {"label": "E", "text": "have enough money"}], "stem": "What is the final step of going to buy something?"}}
{"id": "afe7e803926401891cf902395991d029", "question": {"question_concept": "dictator", "choices": [{"label": "A", "text": "take power"}, {"label": "B", "text": "demand respect"}, {"label": "C", "text": "control country"}, {"label": "D", "text": "making himself in charge"}, {"label": "E", "text": "subject people"}], "stem": "A dictator wants to rule the country, but isn't popular, so how does he achieve this?"}}
{"id": "39c0f19b5721a3de456909d7c7a55fcf", "question": {"question_concept": "thinking", "choices": [{"label": "A", "text": "wonder"}, {"label": "B", "text": "mystery"}, {"label": "C", "text": "solution to problem"}, {"label": "D", "text": "knowledge"}, {"label": "E", "text": "solving problem"}], "stem": "We don' understand how thinking works or why we do it.  It's a what?"}}
{"id": "b33b85bf9dee53fd12ff3c8aa101f5bd", "question": {"question_concept": "get in shape", "choices": [{"label": "A", "text": "work out"}, {"label": "B", "text": "swim"}, {"label": "C", "text": "water"}, {"label": "D", "text": "sweating"}, {"label": "E", "text": "excercise"}], "stem": "What kind of bike could you use to get in shape?"}}
{"id": "6b69d1595d7a62adb79e74e7f3de96d0", "question": {"question_concept": "clothes", "choices": [{"label": "A", "text": "closet"}, {"label": "B", "text": "suitcase"}, {"label": "C", "text": "car"}, {"label": "D", "text": "house"}, {"label": "E", "text": "dresser"}], "stem": "Where is the best place for nice clothes?"}}
{"id": "8951012f08fc1576d232d51fe521e718", "question": {"question_concept": "having food", "choices": [{"label": "A", "text": "will not starve"}, {"label": "B", "text": "getting fat"}, {"label": "C", "text": "being full"}, {"label": "D", "text": "gas"}, {"label": "E", "text": "nausea"}], "stem": "What is the goal of having the right amount of food?"}}
{"id": "5b9f0746e7a074621ee386b7cf16aef3", "question": {"question_concept": "tiger", "choices": [{"label": "A", "text": "drink water"}, {"label": "B", "text": "jungle"}, {"label": "C", "text": "zoo"}, {"label": "D", "text": "india"}, {"label": "E", "text": "desert"}], "stem": "What country is known for it's tigers?"}}
{"id": "fc02af638f03b8e9d5b675802c044c8a", "question": {"question_concept": "remembering", "choices": [{"label": "A", "text": "knowing"}, {"label": "B", "text": "forgetting"}, {"label": "C", "text": "laugh"}, {"label": "D", "text": "problems"}, {"label": "E", "text": "nostalgia"}], "stem": "What is necessary for someone who is remembering the answer to a question?"}}
{"id": "660a37ab26ed40e2cce5f6a3ebe4cea6", "question": {"question_concept": "apples", "choices": [{"label": "A", "text": "shop"}, {"label": "B", "text": "fridge"}, {"label": "C", "text": "grocery store"}, {"label": "D", "text": "farmers market"}, {"label": "E", "text": "orchard"}], "stem": "Where do you keep apples?"}}
{"id": "4cc3d325bd8ed51b791254a68851ace3", "question": {"question_concept": "getting in line", "choices": [{"label": "A", "text": "basic organization"}, {"label": "B", "text": "banking"}, {"label": "C", "text": "friend"}, {"label": "D", "text": "patience"}, {"label": "E", "text": "intention"}], "stem": "James was getting in the line because he wanted to get tickets.  Getting tickets was his what?"}}
{"id": "aaa15bef3ba2446f35ee52dbbdfef7b0", "question": {"question_concept": "seat", "choices": [{"label": "A", "text": "show"}, {"label": "B", "text": "theatre"}, {"label": "C", "text": "bus stop"}, {"label": "D", "text": "martorell"}, {"label": "E", "text": "in cinema"}], "stem": "He took his seat on the tour bus, it would be travelling through where?"}}
{"id": "8427eb0831456ae7f6133659384ef116", "question": {"question_concept": "dreaming", "choices": [{"label": "A", "text": "wake up"}, {"label": "B", "text": "car accident"}, {"label": "C", "text": "inspiration"}, {"label": "D", "text": "confusion"}, {"label": "E", "text": "nightmares"}], "stem": "Sometimes dreaming can be seem very real, a shock during it can even cause you to what?"}}
{"id": "43f14d9b1977885dcd49ec37cb74758d", "question": {"question_concept": "lizard", "choices": [{"label": "A", "text": "new mexico"}, {"label": "B", "text": "new hampshire"}, {"label": "C", "text": "encyclopedia"}, {"label": "D", "text": "texas"}, {"label": "E", "text": "south america"}], "stem": "There still may be many undiscovered species of lizard in the jungle, especially on what continent?"}}
{"id": "dc5e1e5d196820aef741368274ac348f", "question": {"question_concept": "courthouse", "choices": [{"label": "A", "text": "michigan"}, {"label": "B", "text": "center of town"}, {"label": "C", "text": "large citties"}, {"label": "D", "text": "wisconsin"}, {"label": "E", "text": "capital city"}], "stem": "The lawyers entered the courthouse in Lansing, what state were they in?"}}
{"id": "627498015a9b84e22ad185dae7c36a9a", "question": {"question_concept": "memory", "choices": [{"label": "A", "text": "forgetting"}, {"label": "B", "text": "brainless"}, {"label": "C", "text": "forgetfulness"}, {"label": "D", "text": "forgotten"}, {"label": "E", "text": "memory loss"}], "stem": "Alzheimer's was affecting her short term memory, in day to day tasks she was suffering what?"}}
{"id": "b2f24df7e64cb7cbee5600e36111a14a", "question": {"question_concept": "section", "choices": [{"label": "A", "text": "citrus"}, {"label": "B", "text": "band"}, {"label": "C", "text": "platoon"}, {"label": "D", "text": "orchestra"}, {"label": "E", "text": "acrobats"}], "stem": "You will find string, brass, percussion, and wind sections in what large group of performers?"}}
{"id": "f632b32d5114ee2d9e38e9b9c7a63bae", "question": {"question_concept": "jeans", "choices": [{"label": "A", "text": "laundromat"}, {"label": "B", "text": "bedroom"}, {"label": "C", "text": "shopping mall"}, {"label": "D", "text": "library"}, {"label": "E", "text": "thrift store"}], "stem": "John needed to get ready to work so he  put on his freshly washed jeans.  Where was he probably at when he put on his jeans?"}}
{"id": "751136a3d00ac3606b3245bb5fd55900", "question": {"question_concept": "going to party", "choices": [{"label": "A", "text": "hook up"}, {"label": "B", "text": "meet new people"}, {"label": "C", "text": "get drunk"}, {"label": "D", "text": "enjoy yourself"}, {"label": "E", "text": "having sex"}], "stem": "why do people choose going to party?"}}
{"id": "6ee601df8d89d5b018601b99cb114c20", "question": {"question_concept": "examination table", "choices": [{"label": "A", "text": "hotel room"}, {"label": "B", "text": "vets office"}, {"label": "C", "text": "doctor's office"}, {"label": "D", "text": "hospital"}, {"label": "E", "text": "school"}], "stem": "Judy lay on the examination table.  She fell off of her bed during vigorous sex and feels like she broke something.  Where might she be?"}}
{"id": "77f502905f158f318ea4ab91933f7b14", "question": {"question_concept": "plastic", "choices": [{"label": "A", "text": "stock yard"}, {"label": "B", "text": "everything"}, {"label": "C", "text": "own home"}, {"label": "D", "text": "garbage dump"}, {"label": "E", "text": "cupboard"}], "stem": "They had finished baking the cookies for class, the mother told her daughter to get the plastic container out of the what?"}}
{"id": "7d0949a4f1046496220f59b6ecac1eed", "question": {"question_concept": "jewelry", "choices": [{"label": "A", "text": "vault"}, {"label": "B", "text": "department store"}, {"label": "C", "text": "suitcase"}, {"label": "D", "text": "handbag"}, {"label": "E", "text": "safe deposit box"}], "stem": "Where would you put jewelry if you want to bring it with you?"}}
{"id": "8fbcbe465ff2a48bc15afbd81e353283", "question": {"question_concept": "junk", "choices": [{"label": "A", "text": "television"}, {"label": "B", "text": "drawer"}, {"label": "C", "text": "attic"}, {"label": "D", "text": "bed"}, {"label": "E", "text": "counter"}], "stem": "It was getting very late and James needed to clean off the junk that covered what?"}}
{"id": "74702f28361501dfcf9d159a65266c4c", "question": {"question_concept": "making friends", "choices": [{"label": "A", "text": "talking"}, {"label": "B", "text": "open mind"}, {"label": "C", "text": "falling in love"}, {"label": "D", "text": "common interests"}, {"label": "E", "text": "smiling"}], "stem": "What facial expression shows your open to making friends?"}}
{"id": "a267964e677c708d81bacb6390772590", "question": {"question_concept": "fungus", "choices": [{"label": "A", "text": "under rocks"}, {"label": "B", "text": "grocery store"}, {"label": "C", "text": "post office"}, {"label": "D", "text": "fallen tree"}, {"label": "E", "text": "toenails"}], "stem": "Where would you get some fungus if you are hungry?"}}
{"id": "363dbed038ecbd798f6d5c253fcc9a93_1", "question": {"question_concept": "competing against", "choices": [{"label": "A", "text": "one winner"}, {"label": "B", "text": "frustration"}, {"label": "C", "text": "encouraging"}, {"label": "D", "text": "emotions"}, {"label": "E", "text": "happiness"}], "stem": "When you're making no progress while competing against someone, what might you feel?"}}
{"id": "e800f7dd1170be4d1ba1debcfa68739c", "question": {"question_concept": "umbrella", "choices": [{"label": "A", "text": "beach"}, {"label": "B", "text": "destination"}, {"label": "C", "text": "charlie chaplin film"}, {"label": "D", "text": "seattle"}, {"label": "E", "text": "suitcase"}], "stem": "He brought an umbrella for a beach visit after the business meeting, where did he keep it?"}}
{"id": "701fd06e297df41ca0bb6405b1131304", "question": {"question_concept": "interest", "choices": [{"label": "A", "text": "see exhibits"}, {"label": "B", "text": "watch film"}, {"label": "C", "text": "see particular program"}, {"label": "D", "text": "study film"}, {"label": "E", "text": "have conversation"}], "stem": "He had an interest in a work by a new director, what did he want to do?"}}
{"id": "8f0ddcc4491b25e77382b2ff7c75582d", "question": {"question_concept": "gymnasium", "choices": [{"label": "A", "text": "spa"}, {"label": "B", "text": "high school"}, {"label": "C", "text": "school or ymca"}, {"label": "D", "text": "college campus"}, {"label": "E", "text": "lunch"}], "stem": "The athletes were sore and sweaty after working out at the gymnasium, where did they want to go afterward?"}}
{"id": "e945f49906c9b14ba40e94bda6cf15a0", "question": {"question_concept": "cup", "choices": [{"label": "A", "text": "imagination"}, {"label": "B", "text": "sand box"}, {"label": "C", "text": "water fountain"}, {"label": "D", "text": "dishwasher"}, {"label": "E", "text": "kitchen cabinet"}], "stem": "You can use a cup to build a castle tower when playing in a what?"}}
{"id": "12b84809cf67528e9d27d54c9aabf4f4", "question": {"question_concept": "running", "choices": [{"label": "A", "text": "dehydration"}, {"label": "B", "text": "bone damage"}, {"label": "C", "text": "fall down"}, {"label": "D", "text": "breathing hard"}, {"label": "E", "text": "sweat"}], "stem": "What will happen after running for a long time without drinking any water?"}}
{"id": "a83ade78504b53a5e082e6b7824c1c94", "question": {"question_concept": "learning about world", "choices": [{"label": "A", "text": "foolishness"}, {"label": "B", "text": "loss of innocence"}, {"label": "C", "text": "cynicism"}, {"label": "D", "text": "open mind"}, {"label": "E", "text": "smartness"}], "stem": "What can learning about world lead to ?"}}
{"id": "9442cc92318a1ef3864fc72662323eeb", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "watch television"}, {"label": "B", "text": "listen to radio"}, {"label": "C", "text": "open letter"}, {"label": "D", "text": "thank god"}, {"label": "E", "text": "say goodbye"}], "stem": "If a person wants to publicly express themselves, what can they write?"}}
{"id": "627baeadbcf84a380ea4148efd43e38d", "question": {"question_concept": "applying for job", "choices": [{"label": "A", "text": "offer"}, {"label": "B", "text": "rejection"}, {"label": "C", "text": "income"}, {"label": "D", "text": "hope"}, {"label": "E", "text": "sandwich"}], "stem": "If you do well at an interview after applying for a job what do you want to receive?"}}
{"id": "53528254fc37a69bf6dbe8483d1ce248", "question": {"question_concept": "frying pan", "choices": [{"label": "A", "text": "department store"}, {"label": "B", "text": "washing clothes"}, {"label": "C", "text": "kitchen cabinet"}, {"label": "D", "text": "homes"}, {"label": "E", "text": "hotel kitchen"}], "stem": "Where do people take their frying pans after purchase?"}}
{"id": "43018c0d1bdbb473f981ceefeaa08fdd", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "conference"}, {"label": "B", "text": "train station"}, {"label": "C", "text": "town"}, {"label": "D", "text": "internet"}, {"label": "E", "text": "apartment"}], "stem": "Where might people gather to share information?"}}
{"id": "be98599b9e21d1fcabae14d0721fd1cb", "question": {"question_concept": "talking", "choices": [{"label": "A", "text": "communication"}, {"label": "B", "text": "waving hands"}, {"label": "C", "text": "revelation"}, {"label": "D", "text": "listening to"}, {"label": "E", "text": "distraction"}], "stem": "If a person it talking to you what should you be doing?"}}
{"id": "34ba43dba5de2ef7df1fd1e7bb003568", "question": {"question_concept": "courtroom", "choices": [{"label": "A", "text": "courthouse"}, {"label": "B", "text": "confession"}, {"label": "C", "text": "maine"}, {"label": "D", "text": "court tv"}, {"label": "E", "text": "trial"}], "stem": "What does a guilty person do in a courtroom?"}}
{"id": "9c70d7820ad3c5ca472764e6b83b1a73", "question": {"question_concept": "notes", "choices": [{"label": "A", "text": "school"}, {"label": "B", "text": "notebook"}, {"label": "C", "text": "meeting"}, {"label": "D", "text": "college"}, {"label": "E", "text": "desk"}], "stem": "He took meticulous notes, he wasn't naturally gifted but he worked very hard to do well at what?"}}
{"id": "906bcca9852bc7f0bb9e271af9b24897", "question": {"question_concept": "hot day", "choices": [{"label": "A", "text": "eat ice cream"}, {"label": "B", "text": "buy beer"}, {"label": "C", "text": "bathe"}, {"label": "D", "text": "dive"}, {"label": "E", "text": "cool off"}], "stem": "It was a hot day and they all wanted to get in the water.  Sam climbed up.  What might Sam have done?"}}
{"id": "f439e1878374900302fdc30269acb2a0", "question": {"question_concept": "respect", "choices": [{"label": "A", "text": "belittlement"}, {"label": "B", "text": "slight"}, {"label": "C", "text": "dishonor"}, {"label": "D", "text": "being rude"}, {"label": "E", "text": "irreverence"}], "stem": "The so called nerd had earned respect by standing up for himself, a person can only take so much what?"}}
{"id": "0d40b9fb6cf3b4d2f4ef584ee6a3edf6", "question": {"question_concept": "reproduce", "choices": [{"label": "A", "text": "raise children"}, {"label": "B", "text": "an addition"}, {"label": "C", "text": "what people want"}, {"label": "D", "text": "offspring"}, {"label": "E", "text": "have children"}], "stem": "If the end of the world comes and people need to reproduce what is the main goal of the couple to do?"}}
{"id": "12d50b3a82eb83154621b76e3d399a4b", "question": {"question_concept": "driving car", "choices": [{"label": "A", "text": "car crash"}, {"label": "B", "text": "gas pedal"}, {"label": "C", "text": "speeding ticket"}, {"label": "D", "text": "use gas"}, {"label": "E", "text": "doze off"}], "stem": "What does driving a car use?"}}
{"id": "aab67cf6bf81258693498826f4907cae", "question": {"question_concept": "disease", "choices": [{"label": "A", "text": "not washing hands"}, {"label": "B", "text": "lab"}, {"label": "C", "text": "hospital"}, {"label": "D", "text": "third world country"}, {"label": "E", "text": "human body"}], "stem": "Where is disease often spread rapidly?"}}
{"id": "7ac44c2766feb6b4f868815f91c1b59c", "question": {"question_concept": "join", "choices": [{"label": "A", "text": "separate"}, {"label": "B", "text": "leave"}, {"label": "C", "text": "quit"}, {"label": "D", "text": "opt out"}, {"label": "E", "text": "split apart"}], "stem": "The two boys caused trouble together, so the teacher made them join what groups?"}}
{"id": "c1f19986690f34a065448f7e685226c3", "question": {"question_concept": "waiting room", "choices": [{"label": "A", "text": "hospitals"}, {"label": "B", "text": "maternity ward"}, {"label": "C", "text": "doctor's office"}, {"label": "D", "text": "school"}, {"label": "E", "text": "airport"}], "stem": "Where is someone likely to be bored in a waiting room for something routine?"}}
{"id": "cb2657f4073a4a2950f6303c8297960a", "question": {"question_concept": "orchestra pit", "choices": [{"label": "A", "text": "theatre"}, {"label": "B", "text": "butt"}, {"label": "C", "text": "stadium"}, {"label": "D", "text": "auditorium"}, {"label": "E", "text": "opera house"}], "stem": "Sally sat too close to the orchestra pit and couldn't hear anything from the stage.  She was  really dissapointed because  the archetect didn't think about where to put the place where she was sitting. What is that place called?"}}
{"id": "9d0802f590601881a592c3e7fc1c8a48", "question": {"question_concept": "thermometer", "choices": [{"label": "A", "text": "box"}, {"label": "B", "text": "report on temperature"}, {"label": "C", "text": "cabinet"}, {"label": "D", "text": "drawer"}, {"label": "E", "text": "hospital"}], "stem": "What kind of furniture could you keep a thermometer in?"}}
{"id": "c949aae7dddff5f3421bd4f10326a057", "question": {"question_concept": "diaphragm", "choices": [{"label": "A", "text": "drugstore"}, {"label": "B", "text": "woman's body"}, {"label": "C", "text": "human"}, {"label": "D", "text": "drugstore"}, {"label": "E", "text": "person's chest"}], "stem": "What could you find a diaphragm in?"}}
{"id": "b399eb649f8b4a5405a20ed862d1f698", "question": {"question_concept": "space shuttle", "choices": [{"label": "A", "text": "traffic"}, {"label": "B", "text": "solar system"}, {"label": "C", "text": "orbit"}, {"label": "D", "text": "universe"}, {"label": "E", "text": "cape canaveral"}], "stem": "The space shuttle was stuck near the planet, what was it stuck in?"}}
{"id": "16db05e2eb539834abea0440edf95f42", "question": {"question_concept": "student", "choices": [{"label": "A", "text": "class room"}, {"label": "B", "text": "college class"}, {"label": "C", "text": "library"}, {"label": "D", "text": "every aspect of life"}, {"label": "E", "text": "university"}], "stem": "When they leave home a student is responsible for what?"}}
{"id": "c22417169bc8dfd986b6ef7919288621", "question": {"question_concept": "meat", "choices": [{"label": "A", "text": "kitchen"}, {"label": "B", "text": "freezer"}, {"label": "C", "text": "butcher shop"}, {"label": "D", "text": "frying pan"}, {"label": "E", "text": "fridge"}], "stem": "Where is meat often prepared?"}}
{"id": "0f3ee708d558f2de3fb6838b3eb8aebf", "question": {"question_concept": "taking break", "choices": [{"label": "A", "text": "not working"}, {"label": "B", "text": "sitting down"}, {"label": "C", "text": "relaxation"}, {"label": "D", "text": "vacation"}, {"label": "E", "text": "renewal"}], "stem": "If you're talking a break from employment, then what are you probably doing?"}}
{"id": "938ae69a9c1a8e8d0a8522717dcc60dc", "question": {"question_concept": "heat source", "choices": [{"label": "A", "text": "coal or wood"}, {"label": "B", "text": "solar energy"}, {"label": "C", "text": "energy"}, {"label": "D", "text": "fire"}, {"label": "E", "text": "house"}], "stem": "What heat source was commonly used in early steam engines?"}}
{"id": "28e3349754bba5df126075aa14492c53", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "hide"}, {"label": "B", "text": "read text"}, {"label": "C", "text": "play dumb"}, {"label": "D", "text": "enjoy learning"}, {"label": "E", "text": "cross street"}], "stem": "What does a person do when they don't want someone to know that they know something?"}}
{"id": "ad0d5804fc7b844ccba97aac6fde3cae", "question": {"question_concept": "talking", "choices": [{"label": "A", "text": "sneeze"}, {"label": "B", "text": "sharing of ideas"}, {"label": "C", "text": "breathe"}, {"label": "D", "text": "speak"}, {"label": "E", "text": "story telling"}], "stem": "When someone is talking, what would cause them to say bless you?"}}
{"id": "389cf73d92b20d7c47bb024ae7876210", "question": {"question_concept": "chairs", "choices": [{"label": "A", "text": "building"}, {"label": "B", "text": "theater"}, {"label": "C", "text": "board room"}, {"label": "D", "text": "office"}, {"label": "E", "text": "meeting"}], "stem": "There are two leather chairs that swivel.  Where might you expect to find chairs like this?"}}
{"id": "21cc9197f963387ec5a1f6386e5f7e46", "question": {"question_concept": "buy", "choices": [{"label": "A", "text": "eat cake"}, {"label": "B", "text": "steal"}, {"label": "C", "text": "selling"}, {"label": "D", "text": "save money"}, {"label": "E", "text": "paying"}], "stem": "The rich man told his traders not to buy anymore, his gut told him it was time to start what?"}}
{"id": "42af5c15612ed0123b10971dcc6579be", "question": {"question_concept": "shades", "choices": [{"label": "A", "text": "forest"}, {"label": "B", "text": "home"}, {"label": "C", "text": "windows"}, {"label": "D", "text": "paint store"}, {"label": "E", "text": "house"}], "stem": "What do you put shades over?"}}
{"id": "df1c7eefd41f7402dc79444ba75ab82b", "question": {"question_concept": "fallen leaves", "choices": [{"label": "A", "text": "fall season"}, {"label": "B", "text": "roof"}, {"label": "C", "text": "tree"}, {"label": "D", "text": "ground"}, {"label": "E", "text": "forest"}], "stem": "Fallen leaves covered the floor around the pot.  What might have been in the pot?"}}
{"id": "ed3c021d8ee382e939de2d491aa7e196", "question": {"question_concept": "hair salon", "choices": [{"label": "A", "text": "mail"}, {"label": "B", "text": "hotel"}, {"label": "C", "text": "metropolitan city"}, {"label": "D", "text": "shopping center"}, {"label": "E", "text": "post office"}], "stem": "Where would you find a hair salon along with many other merchants?"}}
{"id": "8b9d6ba87ab43b0054b6342b0b1ffe34", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "passion"}, {"label": "B", "text": "get laid"}, {"label": "C", "text": "accomplish goals"}, {"label": "D", "text": "talk about themselves"}, {"label": "E", "text": "own house"}], "stem": "What is person who is ambitious focused on?"}}
{"id": "440ddadefe24d2d3b589ed9706f9ba55", "question": {"question_concept": "control room", "choices": [{"label": "A", "text": "high school"}, {"label": "B", "text": "nuclear power plant"}, {"label": "C", "text": "recording studio"}, {"label": "D", "text": "prison"}, {"label": "E", "text": "building"}], "stem": "The control room opens and closes doors in what home for law breakers?"}}
{"id": "76a0c2bb61952d0a9c726f857d866135", "question": {"question_concept": "carrots", "choices": [{"label": "A", "text": "refrigerator"}, {"label": "B", "text": "the field"}, {"label": "C", "text": "farmer's market"}, {"label": "D", "text": "store"}, {"label": "E", "text": "supermarket"}], "stem": "Jacob wants to get the freshest possible carrots at the lowest price, so he's going where?"}}
{"id": "df00680da989e19f64b8688d458fa911", "question": {"question_concept": "manchester", "choices": [{"label": "A", "text": "greater manchester"}, {"label": "B", "text": "lancashire"}, {"label": "C", "text": "cheshire"}, {"label": "D", "text": "england"}, {"label": "E", "text": "england"}], "stem": "What city that begins with a C is a part of Manchester?"}}
{"id": "8fd247a41810a2bd43defcdb77701df2", "question": {"question_concept": "yard", "choices": [{"label": "A", "text": "neighborhood"}, {"label": "B", "text": "property"}, {"label": "C", "text": "michigan"}, {"label": "D", "text": "outside"}, {"label": "E", "text": "city"}], "stem": "Where are there likely to be yards side by side?"}}
{"id": "5c21aef49b441a7f5ee0c002152a4e96", "question": {"question_concept": "animals", "choices": [{"label": "A", "text": "drink"}, {"label": "B", "text": "fly"}, {"label": "C", "text": "reproduce asexually"}, {"label": "D", "text": "gay"}, {"label": "E", "text": "bite"}], "stem": "Although many humans don't like it, what are many animals naturally in the wild?"}}
{"id": "840a09342c31539bc2c3577481759797", "question": {"question_concept": "wrestling", "choices": [{"label": "A", "text": "get free"}, {"label": "B", "text": "blood"}, {"label": "C", "text": "bruises"}, {"label": "D", "text": "falling down"}, {"label": "E", "text": "competition"}], "stem": "What is a common injury while wrestling?"}}
{"id": "b3345fdcafe4ffb8890c2c1cceda4ece", "question": {"question_concept": "common", "choices": [{"label": "A", "text": "extraordinary"}, {"label": "B", "text": "beautiful"}, {"label": "C", "text": "irregular"}, {"label": "D", "text": "unusual"}, {"label": "E", "text": "special"}], "stem": "He had never seen a common snow before, the locals were used to it but for him it was fun and what?"}}
{"id": "f394d14c37e5e03556e8bfc8cd554bdc", "question": {"question_concept": "sleep at night", "choices": [{"label": "A", "text": "relax"}, {"label": "B", "text": "clear conscience"}, {"label": "C", "text": "go to sleep"}, {"label": "D", "text": "die"}, {"label": "E", "text": "get into bed"}], "stem": "He had trouble getting sleep at night, he could never cool down and what?"}}
{"id": "fea6c3521d1878495289cbc3e195bad6", "question": {"question_concept": "frogs", "choices": [{"label": "A", "text": "jump several feet"}, {"label": "B", "text": "hop"}, {"label": "C", "text": "leap"}, {"label": "D", "text": "hop several feet"}, {"label": "E", "text": "swim"}], "stem": "What can frogs do throughout their life cycle, from tadpole to adult?"}}
{"id": "4023b99af4927af5f89a50e5b1fa3b75", "question": {"question_concept": "bad", "choices": [{"label": "A", "text": "ocean"}, {"label": "B", "text": "true"}, {"label": "C", "text": "prime"}, {"label": "D", "text": "just"}, {"label": "E", "text": "excellent"}], "stem": "Jame's actions were bad, but torturing him for it was now what?"}}
{"id": "3492541e574280c78189f20fba6a4dc2", "question": {"question_concept": "getting in line", "choices": [{"label": "A", "text": "for fun"}, {"label": "B", "text": "have to wait for"}, {"label": "C", "text": "anxiety"}, {"label": "D", "text": "terrible"}, {"label": "E", "text": "wait turn"}], "stem": "Why would someone be getting in line?"}}
{"id": "905f3ac066321bce5ee913dbbc2c41d6", "question": {"question_concept": "boredom", "choices": [{"label": "A", "text": "work"}, {"label": "B", "text": "dream"}, {"label": "C", "text": "tamper"}, {"label": "D", "text": "fiddle"}, {"label": "E", "text": "play games"}], "stem": "The child was suffering boredom in the classroom, he began to do what with things?"}}
{"id": "191e3154d4039c42e8b919934828db0f", "question": {"question_concept": "humans", "choices": [{"label": "A", "text": "live forever"}, {"label": "B", "text": "male or female"}, {"label": "C", "text": "accomadable"}, {"label": "D", "text": "very adaptable"}, {"label": "E", "text": "emotional"}], "stem": "Humans live in all sorts of regions and climates, they seem to be what?"}}
{"id": "a8aed2095404a1880f0a3130d4ab5f44", "question": {"question_concept": "rosebush", "choices": [{"label": "A", "text": "nature"}, {"label": "B", "text": "field"}, {"label": "C", "text": "vegetable garden"}, {"label": "D", "text": "neighbor's yard"}, {"label": "E", "text": "botanic garden"}], "stem": "What open space is a fine place to plant a rosebush?"}}
{"id": "535e6cf97fda774c74f18b840e766709", "question": {"question_concept": "taking final exams", "choices": [{"label": "A", "text": "anxiety"}, {"label": "B", "text": "success"}, {"label": "C", "text": "headaches"}, {"label": "D", "text": "graduating"}, {"label": "E", "text": "failure"}], "stem": "What happens shortly after taking final exams?"}}
{"id": "9f090f5d97084f7fe8394386e182443d", "question": {"question_concept": "preserve", "choices": [{"label": "A", "text": "ruin"}, {"label": "B", "text": "michigan"}, {"label": "C", "text": "use"}, {"label": "D", "text": "let spoil"}, {"label": "E", "text": "rot"}], "stem": "James tried to preserve the floor as best he could, but the water seeped in causing it to do what?"}}
{"id": "a22a289f2f46a060639b5726a42d8da3", "question": {"question_concept": "playing ball", "choices": [{"label": "A", "text": "pleasure"}, {"label": "B", "text": "having fun"}, {"label": "C", "text": "losing"}, {"label": "D", "text": "injury"}, {"label": "E", "text": "lose"}], "stem": "What is someone trying to have while playing ball?"}}
{"id": "0bb575bc155f90b689fdec418b8c3f71", "question": {"question_concept": "isle", "choices": [{"label": "A", "text": "sea"}, {"label": "B", "text": "auditorium"}, {"label": "C", "text": "lake"}, {"label": "D", "text": "stadium"}, {"label": "E", "text": "ocean"}], "stem": "Where can you watch a show about an isle?"}}
{"id": "6fc4827b5c15dffcf6a15d167fe93b8f", "question": {"question_concept": "mound", "choices": [{"label": "A", "text": "lakehouse"}, {"label": "B", "text": "hell"}, {"label": "C", "text": "baseball stadium"}, {"label": "D", "text": "africa"}, {"label": "E", "text": "baseball diamond"}], "stem": "The spectators were watching the pitcher on the mound, where were the spectators?"}}
{"id": "7a9acba71edab7d66fddce4f5bb8cb9c", "question": {"question_concept": "watching television", "choices": [{"label": "A", "text": "looking stars through window"}, {"label": "B", "text": "entertainment"}, {"label": "C", "text": "wasted time"}, {"label": "D", "text": "relaxation"}, {"label": "E", "text": "falling asleep"}], "stem": "The man was watching TV late at night, what happened as a result?"}}
{"id": "a431fd9da3ab026b7df5aeb3b35c5ca1", "question": {"question_concept": "exercising", "choices": [{"label": "A", "text": "sweat"}, {"label": "B", "text": "weight loss"}, {"label": "C", "text": "get in shape"}, {"label": "D", "text": "scream"}, {"label": "E", "text": "relaxation"}], "stem": "When exercises for weight loss or to get in shape what might your body do?"}}
{"id": "21a4a50050518573cf8d589e38b895ca", "question": {"question_concept": "living life", "choices": [{"label": "A", "text": "early death"}, {"label": "B", "text": "happiness"}, {"label": "C", "text": "acquiring knowledge"}, {"label": "D", "text": "book burnings"}, {"label": "E", "text": "existing"}], "stem": "What is the best about living life with lots of smart people around you?"}}
{"id": "d7d58412eea308a64169aa74eeb9aba3", "question": {"question_concept": "help", "choices": [{"label": "A", "text": "do nothing"}, {"label": "B", "text": "hurting"}, {"label": "C", "text": "observant"}, {"label": "D", "text": "hinder"}, {"label": "E", "text": "leave"}], "stem": "The man rushed in to help, he couldn't believe so many people would stand around and what?"}}
{"id": "3ac7cee0cb7d5aa9f0fbf0b36d281fb0", "question": {"question_concept": "alcohol", "choices": [{"label": "A", "text": "distillery"}, {"label": "B", "text": "fraternity house"}, {"label": "C", "text": "plane"}, {"label": "D", "text": "hot air balloon"}, {"label": "E", "text": "wine"}], "stem": "Where can you drink alcohol in the air?"}}
{"id": "e393cee2d51bf52a60e0aae945ed2b8e", "question": {"question_concept": "glass of milk", "choices": [{"label": "A", "text": "accompaniment to meal"}, {"label": "B", "text": "home"}, {"label": "C", "text": "menu"}, {"label": "D", "text": "the kitchen"}, {"label": "E", "text": "cafeteria"}], "stem": "Where would you pour a glass of milk by yourself?"}}
{"id": "653d0ef659899c4dc028a811e2b2b32c", "question": {"question_concept": "birds", "choices": [{"label": "A", "text": "countryside"}, {"label": "B", "text": "tree"}, {"label": "C", "text": "forest"}, {"label": "D", "text": "roof"}, {"label": "E", "text": "sky"}], "stem": "John looked up while mowing his lawn and saw birds nesting.  Where might they be nesting?"}}
{"id": "8da69153b21a133a269d46308d560727", "question": {"question_concept": "human", "choices": [{"label": "A", "text": "need money"}, {"label": "B", "text": "drink tea"}, {"label": "C", "text": "have to sleep"}, {"label": "D", "text": "say hello"}, {"label": "E", "text": "eat sushi"}], "stem": "A human in Japan has a habit of doing what?"}}
{"id": "b73b323205a05d1abb3e90c2d3e6f027", "question": {"question_concept": "going for run", "choices": [{"label": "A", "text": "satisfaction"}, {"label": "B", "text": "better health"}, {"label": "C", "text": "eat right"}, {"label": "D", "text": "losing weight"}, {"label": "E", "text": "breathlessness"}], "stem": "Why would you be going for run if you are not fat?"}}
{"id": "6326fa333a82e76214d94c813befe727", "question": {"question_concept": "fatigue", "choices": [{"label": "A", "text": "sit down"}, {"label": "B", "text": "run like 'run lola run'"}, {"label": "C", "text": "get physical activity"}, {"label": "D", "text": "sleep"}, {"label": "E", "text": "have rest"}], "stem": "If your legs are experiencing fatigue what can you do?"}}
{"id": "a8c961aa8a02d1743b363a1ab7252f65", "question": {"question_concept": "performing", "choices": [{"label": "A", "text": "butterflies"}, {"label": "B", "text": "energetic"}, {"label": "C", "text": "anxiety"}, {"label": "D", "text": "happiness"}, {"label": "E", "text": "pride"}], "stem": "If you're successful when performing a difficult task, how might you feel?"}}
{"id": "96711aefd640833f1fc4e4f669323786", "question": {"question_concept": "buying christmas presents", "choices": [{"label": "A", "text": "debt"}, {"label": "B", "text": "cardiac arrest"}, {"label": "C", "text": "stress"}, {"label": "D", "text": "happiness"}, {"label": "E", "text": "pleasure"}], "stem": "Spending too much when buying christmas presents will cause you to go in to what?"}}
{"id": "ec98242bc2ee172d21fc247134e7e43c", "question": {"question_concept": "door with lock", "choices": [{"label": "A", "text": "locker"}, {"label": "B", "text": "bus terminal"}, {"label": "C", "text": "automobile"}, {"label": "D", "text": "garage"}, {"label": "E", "text": "file cabinet"}], "stem": "A car thief is thwarted by a door with lock, what was he trying to take?"}}
{"id": "089a564c57b4ce06d914614a17281ba5", "question": {"question_concept": "church", "choices": [{"label": "A", "text": "city"}, {"label": "B", "text": "children"}, {"label": "C", "text": "populated area"}, {"label": "D", "text": "every town"}, {"label": "E", "text": "christian community"}], "stem": "Who are church's made for?"}}
{"id": "31bd28eb38bfc181a9e36c9210286ae9", "question": {"question_concept": "tabby cat", "choices": [{"label": "A", "text": "home"}, {"label": "B", "text": "barn"}, {"label": "C", "text": "alley"}, {"label": "D", "text": "outside"}, {"label": "E", "text": "lap"}], "stem": "Where might you find a stray tabby cat?"}}
{"id": "db4bacb29cf87a3104626ba6de7be368", "question": {"question_concept": "words", "choices": [{"label": "A", "text": "books"}, {"label": "B", "text": "mouth"}, {"label": "C", "text": "newspaper"}, {"label": "D", "text": "sentence"}, {"label": "E", "text": "page of book"}], "stem": "Where will you find black and white words?"}}
{"id": "b176f1e1326618ad159275c3c29ed273", "question": {"question_concept": "drop of blood", "choices": [{"label": "A", "text": "clinic"}, {"label": "B", "text": "vein"}, {"label": "C", "text": "battlefield"}, {"label": "D", "text": "street"}, {"label": "E", "text": "needle"}], "stem": "Where can you a drop of blood from if nothing is around you?"}}
{"id": "a0f78af473fe64aeb2e4b9ee8eb235d3", "question": {"question_concept": "loose", "choices": [{"label": "A", "text": "close fitting"}, {"label": "B", "text": "bind"}, {"label": "C", "text": "engage"}, {"label": "D", "text": "fast"}, {"label": "E", "text": "tighten"}], "stem": "He worked the belt loose, it was beginning to what against his hip?"}}
{"id": "08a88b77afcc2e64bffb52fd793a8f3e", "question": {"question_concept": "system", "choices": [{"label": "A", "text": "place"}, {"label": "B", "text": "computer store"}, {"label": "C", "text": "human body"}, {"label": "D", "text": "nature"}, {"label": "E", "text": "computer science"}], "stem": "What is known to be a very complex system?"}}
{"id": "164cea08110f18b8f8ff9e1861f87fe6", "question": {"question_concept": "cats", "choices": [{"label": "A", "text": "purr"}, {"label": "B", "text": "claw"}, {"label": "C", "text": "think"}, {"label": "D", "text": "eat meat"}, {"label": "E", "text": "drink water"}], "stem": "The lady got her cats a new scratching post so they had something to what?"}}
{"id": "d365ed78590354f1bda41068fa5cae27_1", "question": {"question_concept": "contemplating", "choices": [{"label": "A", "text": "meaning of life"}, {"label": "B", "text": "thinking"}, {"label": "C", "text": "revelations"}, {"label": "D", "text": "discovery"}, {"label": "E", "text": "reflection"}], "stem": "The scientist was contemplating something he never saw before, what was he contemplating?"}}
{"id": "7683d753ffd06f40801d4969d5d9d91f", "question": {"question_concept": "interest", "choices": [{"label": "A", "text": "research"}, {"label": "B", "text": "watch film"}, {"label": "C", "text": "have conversation"}, {"label": "D", "text": "go to performance"}, {"label": "E", "text": "see particular program"}], "stem": "She had an interest in a documentary show, what was she browsing for?"}}
{"id": "48d17f25da85ed640dab53de309b7277", "question": {"question_concept": "dollar", "choices": [{"label": "A", "text": "table"}, {"label": "B", "text": "piggy bank"}, {"label": "C", "text": "purse"}, {"label": "D", "text": "pocket"}, {"label": "E", "text": "cash drawer"}], "stem": "Where would you put a dollar if you want your money to be organized?"}}
{"id": "a173a77f7aeb99842068e4ad9238a390", "question": {"question_concept": "glue", "choices": [{"label": "A", "text": "art room"}, {"label": "B", "text": "school"}, {"label": "C", "text": "desk drawer"}, {"label": "D", "text": "classroom"}, {"label": "E", "text": "library"}], "stem": "What is a place inside of a building where you can find a teacher using glue?"}}
{"id": "c8333ef436e07e89129aad87808320f5", "question": {"question_concept": "policeman", "choices": [{"label": "A", "text": "freeway"}, {"label": "B", "text": "street"}, {"label": "C", "text": "donut shop"}, {"label": "D", "text": "sidewalk"}, {"label": "E", "text": "police station"}], "stem": "Where will a policeman stop you if you're going too fast?"}}
{"id": "a788bce17d49a733593ec4fc7f03c61c", "question": {"question_concept": "parking area", "choices": [{"label": "A", "text": "amusement park"}, {"label": "B", "text": "city"}, {"label": "C", "text": "people"}, {"label": "D", "text": "vehicles"}, {"label": "E", "text": "apartment complex"}], "stem": "The parking area was the site of a huge party, who was there?"}}
{"id": "5fb53ef6493c635e3091e00f2ca97eca", "question": {"question_concept": "judging", "choices": [{"label": "A", "text": "trophy or ribbon"}, {"label": "B", "text": "evaluating"}, {"label": "C", "text": "responsibility"}, {"label": "D", "text": "verdict"}, {"label": "E", "text": "prejudice"}], "stem": "At the end of official judging what is handed down?"}}
{"id": "ab32b8150e7f87f6e5964889d9fdf562", "question": {"question_concept": "gambling", "choices": [{"label": "A", "text": "casino"}, {"label": "B", "text": "losing money"}, {"label": "C", "text": "lose money"}, {"label": "D", "text": "penury"}, {"label": "E", "text": "bankruptcy"}], "stem": "Gambling debts that exceed your assets will cause you to declare what?"}}
{"id": "c54faa4d84678032beb4132da313daf5", "question": {"question_concept": "child", "choices": [{"label": "A", "text": "ask many questions"}, {"label": "B", "text": "clean room"}, {"label": "C", "text": "wave goodbye"}, {"label": "D", "text": "play video games"}, {"label": "E", "text": "give a hug"}], "stem": "What does a child do when walking out ?"}}
{"id": "7ffa0c121885c2aad8ca1f1f465ce564", "question": {"question_concept": "cool off", "choices": [{"label": "A", "text": "relief"}, {"label": "B", "text": "think"}, {"label": "C", "text": "calm down"}, {"label": "D", "text": "relax"}, {"label": "E", "text": "go swimming"}], "stem": "If someone is angry and they're told to \"cool off\" what does that person want them to do?"}}
{"id": "ecc430c711c2f9a10d812ee8fe556e7c", "question": {"question_concept": "felt", "choices": [{"label": "A", "text": "hat shop"}, {"label": "B", "text": "man's hat"}, {"label": "C", "text": "mall"}, {"label": "D", "text": "craft store"}, {"label": "E", "text": "clothes"}], "stem": "Where do you typically buy accessories made out of felt?"}}
{"id": "46f0af4683b2d1803470c13c734c05b3", "question": {"question_concept": "tennis court", "choices": [{"label": "A", "text": "michigan"}, {"label": "B", "text": "ghana"}, {"label": "C", "text": "town"}, {"label": "D", "text": "wimbledon"}, {"label": "E", "text": "park"}], "stem": "James lived in a rural area and wanted to go to a tennis court.  What state might he live in?"}}
{"id": "56f58e4ac1635bb7d407bedd677319fe", "question": {"question_concept": "soft", "choices": [{"label": "A", "text": "harsh"}, {"label": "B", "text": "abrasive"}, {"label": "C", "text": "hard"}, {"label": "D", "text": "sensible"}, {"label": "E", "text": "loud"}], "stem": "He began using the soft setting to polish out a finish, this was after having started with a more what setting?"}}
{"id": "ffb650d0a8d3bb8507648c36777a16bd", "question": {"question_concept": "drill", "choices": [{"label": "A", "text": "work shop"}, {"label": "B", "text": "tool store"}, {"label": "C", "text": "tool shed"}, {"label": "D", "text": "dentist office"}, {"label": "E", "text": "repair shop"}], "stem": "Where does someone use a drill on wood work?"}}
{"id": "a7003deda241e47c794142fb7a5c3b90", "question": {"question_concept": "company", "choices": [{"label": "A", "text": "promote internally"}, {"label": "B", "text": "own resources"}, {"label": "C", "text": "commit crime"}, {"label": "D", "text": "own factory"}, {"label": "E", "text": "branch out"}], "stem": "My company is strictly mom and pop.  How do I keep it that way?"}}
{"id": "dcaf5377ed327720b278c6cd26d10974", "question": {"question_concept": "crab", "choices": [{"label": "A", "text": "cooking pot"}, {"label": "B", "text": "pot"}, {"label": "C", "text": "fish market"}, {"label": "D", "text": "tide pool"}, {"label": "E", "text": "beach sand"}], "stem": "The man wanted to serve crab, where should he put it first?"}}
{"id": "4e94dffb2f364b99d6ab323f35396992", "question": {"question_concept": "tiger", "choices": [{"label": "A", "text": "zoo"}, {"label": "B", "text": "drink water"}, {"label": "C", "text": "jungle"}, {"label": "D", "text": "india"}, {"label": "E", "text": "canada"}], "stem": "The tiger is one of many dangers in the what?"}}
{"id": "ba7cffc644dacb035c5ffbaeebdef207", "question": {"question_concept": "seeing people play game", "choices": [{"label": "A", "text": "excitement"}, {"label": "B", "text": "stress"}, {"label": "C", "text": "envy"}, {"label": "D", "text": "longing"}, {"label": "E", "text": "looking at wrist watch"}], "stem": "While seeing people play a game what may you experience if you team is doing well?"}}
{"id": "41c92f0ef20e8f4c85616016cafa3190", "question": {"question_concept": "study", "choices": [{"label": "A", "text": "concentrate"}, {"label": "B", "text": "read books"}, {"label": "C", "text": "assignment"}, {"label": "D", "text": "have book"}, {"label": "E", "text": "concentration"}], "stem": "What does needing to study usually require a great deal of?"}}
{"id": "aa36e26ef82a2bdfea163d2d06cb01d2", "question": {"question_concept": "bat", "choices": [{"label": "A", "text": "bridge"}, {"label": "B", "text": "new mexico"}, {"label": "C", "text": "place"}, {"label": "D", "text": "belfry"}, {"label": "E", "text": "dug out"}], "stem": "The bat needed a stereotypical place to live, what did it choose?"}}
{"id": "51f9903c8635c9b9eb4c62a6119133ed", "question": {"question_concept": "snowflake", "choices": [{"label": "A", "text": "december"}, {"label": "B", "text": "cloud"}, {"label": "C", "text": "air"}, {"label": "D", "text": "snow storm"}, {"label": "E", "text": "winter"}], "stem": "A snowflake with millions of friends and wind can cause all sorts of trouble, what is this called?"}}
{"id": "6ca12f315d3ecc822ff5bf570e63402d", "question": {"question_concept": "socialising", "choices": [{"label": "A", "text": "making friends"}, {"label": "B", "text": "have fun"}, {"label": "C", "text": "having fun"}, {"label": "D", "text": "anxiety"}, {"label": "E", "text": "billys mom"}], "stem": "Billy was terrible at socializing and didn't know many people at all.  What might make it difficult for Billy to socialize?"}}
{"id": "c47b4cf797be3c52c4f464c98937c972", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "water garden"}, {"label": "B", "text": "eat fruit"}, {"label": "C", "text": "catch cold"}, {"label": "D", "text": "jump for joy"}, {"label": "E", "text": "feel stupid"}], "stem": "Getting something wrong that's obvious can really make a person what?"}}
{"id": "3dcb4aaff4e3b30e98b63b14d74ac678", "question": {"question_concept": "policeman", "choices": [{"label": "A", "text": "freeway"}, {"label": "B", "text": "front door"}, {"label": "C", "text": "donut shop"}, {"label": "D", "text": "tire shop"}, {"label": "E", "text": "street"}], "stem": "Where does a policeman frequent for snacks?"}}
{"id": "ca7cd3b440fefdea6c1eadf7ebb804af", "question": {"question_concept": "accommodations", "choices": [{"label": "A", "text": "motel"}, {"label": "B", "text": "theater"}, {"label": "C", "text": "having someplace to stay"}, {"label": "D", "text": "comfort"}, {"label": "E", "text": "camp ground"}], "stem": "If a guy wants accommodations to view a movie, where is he looking to go?"}}
{"id": "597f8ea2efad999ba564f0341acb4dab", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "save money"}, {"label": "B", "text": "cry"}, {"label": "C", "text": "own house"}, {"label": "D", "text": "better job"}, {"label": "E", "text": "feel safe"}], "stem": "What will a person who plans for the future will do this more often than someone who lives in the moment?"}}
{"id": "37b9a196725f183f8a77151b732ddcb5", "question": {"question_concept": "playing ball", "choices": [{"label": "A", "text": "board game"}, {"label": "B", "text": "lose"}, {"label": "C", "text": "competition"}, {"label": "D", "text": "throwing"}, {"label": "E", "text": "having fun"}], "stem": "My friend and I were playing ball. We each wanted to win. What sort of game were we playing?"}}
{"id": "ed135a39d9796af258c5c52245cd4a06", "question": {"question_concept": "bed", "choices": [{"label": "A", "text": "snore"}, {"label": "B", "text": "awake"}, {"label": "C", "text": "sleep"}, {"label": "D", "text": "chair"}, {"label": "E", "text": "floor"}], "stem": "What does one do in bed?"}}
{"id": "bd99f9502fe6d39b5f65b17c1fb499b5", "question": {"question_concept": "tables", "choices": [{"label": "A", "text": "bathroom"}, {"label": "B", "text": "fast food restaurant"}, {"label": "C", "text": "house"}, {"label": "D", "text": "conference"}, {"label": "E", "text": "library"}], "stem": "Where does a family eat at a table after cooking dinner?"}}
{"id": "4025ea36e968e84def51e9c592314531", "question": {"question_concept": "air conditioning", "choices": [{"label": "A", "text": "hot room"}, {"label": "B", "text": "waiting room"}, {"label": "C", "text": "car"}, {"label": "D", "text": "house"}, {"label": "E", "text": "offices"}], "stem": "Where do you put personal air conditioning?"}}
{"id": "d8e9bc3054beedc0a24f8539edd9aae3", "question": {"question_concept": "work", "choices": [{"label": "A", "text": "play"}, {"label": "B", "text": "unemployment"}, {"label": "C", "text": "laziness"}, {"label": "D", "text": "unemployed"}, {"label": "E", "text": "fly"}], "stem": "If you work hard, one can also be said to do what hard?"}}
{"id": "568016ad69dd9b0413a3bec91e563fc0", "question": {"question_concept": "bacteria", "choices": [{"label": "A", "text": "petri dish"}, {"label": "B", "text": "finger"}, {"label": "C", "text": "ground"}, {"label": "D", "text": "septic tank"}, {"label": "E", "text": "water"}], "stem": "Where is bacteria most likely to grow?"}}
{"id": "c0a8030342bf84fd0ced5023b9d66029", "question": {"question_concept": "skate", "choices": [{"label": "A", "text": "falling down"}, {"label": "B", "text": "prohibit"}, {"label": "C", "text": "fall down"}, {"label": "D", "text": "get away with"}, {"label": "E", "text": "maintain balance"}], "stem": "If someone is going to skate in a restricted area what do they hope to do?"}}
{"id": "631e3e91e685d80a1e1464ce5d098bf9", "question": {"question_concept": "pray", "choices": [{"label": "A", "text": "look up"}, {"label": "B", "text": "go to church"}, {"label": "C", "text": "speak to god"}, {"label": "D", "text": "kneel"}, {"label": "E", "text": "dip fingers in holy water"}], "stem": "What would you do before pray in your house?"}}
{"id": "0aa87bbecdbad374acc1cc40d80a1641", "question": {"question_concept": "answering questions", "choices": [{"label": "A", "text": "getting tired"}, {"label": "B", "text": "boredom"}, {"label": "C", "text": "sharing information"}, {"label": "D", "text": "spitting nails"}, {"label": "E", "text": "confusion"}], "stem": "Sally was answering John's questions honestly.  She enjoyed doing what?"}}
{"id": "ab0e7bf706f1f94d7774ac06bef7fb20", "question": {"question_concept": "water", "choices": [{"label": "A", "text": "glass"}, {"label": "B", "text": "street"}, {"label": "C", "text": "puddle"}, {"label": "D", "text": "soup"}, {"label": "E", "text": "ocean"}], "stem": "What has water and mud in it?"}}
{"id": "90c30007312d7ca23fb09d50529ce17e", "question": {"question_concept": "committing murder", "choices": [{"label": "A", "text": "go to jail"}, {"label": "B", "text": "die"}, {"label": "C", "text": "dead"}, {"label": "D", "text": "kill"}, {"label": "E", "text": "done"}], "stem": "Committing murder is difficult, but if you do it right someone will end up in what state?"}}
{"id": "8803071f599c042e6d0d5a5b364b296b", "question": {"question_concept": "sloth", "choices": [{"label": "A", "text": "wilderness"}, {"label": "B", "text": "tropical jungle"}, {"label": "C", "text": "woods"}, {"label": "D", "text": "basement"}, {"label": "E", "text": "amazonia"}], "stem": "The sloth's native habitat is in the area known as what?"}}
{"id": "61e6a5bef1507ef9be89c50ab46a3e0a", "question": {"question_concept": "having food", "choices": [{"label": "A", "text": "eating food"}, {"label": "B", "text": "weight gain"}, {"label": "C", "text": "getting fat"}, {"label": "D", "text": "being over weight"}, {"label": "E", "text": "being full"}], "stem": "For what purpose does a person usually plan on having food?"}}
{"id": "3c46d1d50b0900e7c9d3e6121cecaae2", "question": {"question_concept": "banker", "choices": [{"label": "A", "text": "pond"}, {"label": "B", "text": "country club"}, {"label": "C", "text": "monopoly game"}, {"label": "D", "text": "wall street"}, {"label": "E", "text": "michigan"}], "stem": "where do most bankers work?"}}
{"id": "585d0f04348fa72bcb4e91d9c7a4dfc9", "question": {"question_concept": "native", "choices": [{"label": "A", "text": "alien"}, {"label": "B", "text": "guide foreigners"}, {"label": "C", "text": "foreigner"}, {"label": "D", "text": "immigrant"}, {"label": "E", "text": "introduced"}], "stem": "He told the people he was a native, he figured after thirty years he was hardly an what?"}}
{"id": "7337c9fb471c0ac7c5baa972ddc08c82", "question": {"question_concept": "roadway", "choices": [{"label": "A", "text": "neighborhood"}, {"label": "B", "text": "city"}, {"label": "C", "text": "city"}, {"label": "D", "text": "subdivision"}, {"label": "E", "text": "countryside"}], "stem": "A roadway doesn't have many building around it, where is it likely traversing?"}}
{"id": "60c35f7f62c4c0747ee5dfda27be72e8", "question": {"question_concept": "resting", "choices": [{"label": "A", "text": "running"}, {"label": "B", "text": "feeling better"}, {"label": "C", "text": "doing nothing"}, {"label": "D", "text": "recuperation"}, {"label": "E", "text": "relaxation"}], "stem": "If I spent my weekend resting, what might I tell a coworker I spent time doing?"}}
{"id": "6ce35cf429b2953db151ff919d4df01c", "question": {"question_concept": "lip", "choices": [{"label": "A", "text": "mouth"}, {"label": "B", "text": "kentucky fried chicken"}, {"label": "C", "text": "human"}, {"label": "D", "text": "mouth"}, {"label": "E", "text": "jars and all faces"}], "stem": "What does not have a lip?"}}
{"id": "90154aad85582a0221299791f7eaaa59", "question": {"question_concept": "meeting friend", "choices": [{"label": "A", "text": "eat dinner"}, {"label": "B", "text": "say hi"}, {"label": "C", "text": "greet"}, {"label": "D", "text": "shake hands"}, {"label": "E", "text": "have coffee"}], "stem": "If you're meeting a friend and you've already had lunch what can you do together?"}}
{"id": "c22d15ed3823aecff8092299bb5e50e1", "question": {"question_concept": "examine thing", "choices": [{"label": "A", "text": "wet"}, {"label": "B", "text": "interests"}, {"label": "C", "text": "interesting"}, {"label": "D", "text": "complex"}, {"label": "E", "text": "learn more about"}], "stem": "James was interested in examing the thing that they pulled out of the water.  He liked stuff from old shipwrecks. He probably thought that they were what?"}}
{"id": "203fec2260e4fa484977524a8f7c8d92", "question": {"question_concept": "travel", "choices": [{"label": "A", "text": "energy"}, {"label": "B", "text": "pollution"}, {"label": "C", "text": "go somewhere"}, {"label": "D", "text": "have money"}, {"label": "E", "text": "decide where to"}], "stem": "Whenever we travel we burn fuel that creates what?"}}
{"id": "d8430ac495bcf82323d70d5fc31b7b3a", "question": {"question_concept": "water", "choices": [{"label": "A", "text": "rain cloud"}, {"label": "B", "text": "bath tub"}, {"label": "C", "text": "ocean"}, {"label": "D", "text": "wishing well"}, {"label": "E", "text": "lake or river"}], "stem": "If a person paddles in the water, where are they likely to be?"}}
{"id": "c4d2a6826ccfcbbbeaa538a656eb8980", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "broken heart"}, {"label": "B", "text": "hospitalized"}, {"label": "C", "text": "annoyance"}, {"label": "D", "text": "ridiculous"}, {"label": "E", "text": "railway station"}], "stem": "A person injured themselves, where should they go?"}}
{"id": "65d3f9cdcbe7ee81473715348513316c", "question": {"question_concept": "gymnasium", "choices": [{"label": "A", "text": "high school"}, {"label": "B", "text": "spa"}, {"label": "C", "text": "pool"}, {"label": "D", "text": "college campus"}, {"label": "E", "text": "being healthy"}], "stem": "The football coach conditioned the teenagers at the gymnasium, where was this located?"}}
{"id": "f66db7b764e55002b375d46150224711", "question": {"question_concept": "horse", "choices": [{"label": "A", "text": "central park"}, {"label": "B", "text": "state fair"}, {"label": "C", "text": "american southwest"}, {"label": "D", "text": "nyc"}, {"label": "E", "text": "canada"}], "stem": "Where can you take a horse and carriage ride in a major U.S. city?"}}
{"id": "d39f9785e678bbf45a5727d5f447138b", "question": {"question_concept": "getting in line", "choices": [{"label": "A", "text": "anxiety"}, {"label": "B", "text": "have to wait for"}, {"label": "C", "text": "longer lines"}, {"label": "D", "text": "fatigue"}, {"label": "E", "text": "late"}], "stem": "Why might you be getting in at the end of the line?"}}
{"id": "590bf9fa97a3d5791c85767e25302c04", "question": {"question_concept": "scale", "choices": [{"label": "A", "text": "post office"}, {"label": "B", "text": "assay office"}, {"label": "C", "text": "butcher shop"}, {"label": "D", "text": "music store"}, {"label": "E", "text": "university"}], "stem": "Bill put gold on the scale, where does BIll likely work?"}}
{"id": "5c6ae1d05b6344bf1be0057dc3be65f2", "question": {"question_concept": "painting", "choices": [{"label": "A", "text": "great relief"}, {"label": "B", "text": "slashes"}, {"label": "C", "text": "beauty"}, {"label": "D", "text": "new look"}, {"label": "E", "text": "new color"}], "stem": "A painting has damage, what might be found on the canvas?"}}
{"id": "e5febe99cbd3635a14688e0a0c89fae4", "question": {"question_concept": "excavation", "choices": [{"label": "A", "text": "cemetary"}, {"label": "B", "text": "city"}, {"label": "C", "text": "archeological site"}, {"label": "D", "text": "canada"}, {"label": "E", "text": "construction site"}], "stem": "Where is someone likely to participate in an excavation of an Egyptian mummy?"}}
{"id": "83a89f069b856fb093e1e011b282c3b5", "question": {"question_concept": "cancer", "choices": [{"label": "A", "text": "spread"}, {"label": "B", "text": "result in death"}, {"label": "C", "text": "kill"}, {"label": "D", "text": "bad"}, {"label": "E", "text": "illness occur"}], "stem": "What will happen when cancer does not go away?"}}
{"id": "a4bc3a9d50055ea3004a86ff29816837", "question": {"question_concept": "shopping mall", "choices": [{"label": "A", "text": "pennslyvania"}, {"label": "B", "text": "indiana"}, {"label": "C", "text": "suburbs"}, {"label": "D", "text": "forest"}, {"label": "E", "text": "downtown"}], "stem": "Where is a strip shopping mall likely to be?"}}
{"id": "07e3b3766539a6b7e0f4d28ab1a19cac", "question": {"question_concept": "curling iron", "choices": [{"label": "A", "text": "drawer"}, {"label": "B", "text": "bathroom"}, {"label": "C", "text": "hair salon"}, {"label": "D", "text": "use"}, {"label": "E", "text": "turn it off"}], "stem": "What does someone do to a curling iron before leaving the house?"}}
{"id": "f7476896a6e64c7b3f3904b4038948ef", "question": {"question_concept": "table", "choices": [{"label": "A", "text": "corner"}, {"label": "B", "text": "rug"}, {"label": "C", "text": "library"}, {"label": "D", "text": "kitchen"}, {"label": "E", "text": "table"}], "stem": "What could you put a table on top of?"}}
{"id": "13c080abc144ed90d5e78748e347037f", "question": {"question_concept": "alcoholic", "choices": [{"label": "A", "text": "park"}, {"label": "B", "text": "grocery store"}, {"label": "C", "text": "prison"}, {"label": "D", "text": "homeless shelter"}, {"label": "E", "text": "bar"}], "stem": "Alcoholic refreshment is likely banned in what space for people without permanent dwellings?"}}
{"id": "0870ee9e3a47c384a484ae7cdc0a9d48", "question": {"question_concept": "apple tree", "choices": [{"label": "A", "text": "park"}, {"label": "B", "text": "washington state"}, {"label": "C", "text": "fields"}, {"label": "D", "text": "new york"}, {"label": "E", "text": "rain forest"}], "stem": "Where would you find a famous apple tree?"}}
{"id": "e165beeb49b36551a4b8abc471ec2c8d", "question": {"question_concept": "milk", "choices": [{"label": "A", "text": "lactose"}, {"label": "B", "text": "water"}, {"label": "C", "text": "water"}, {"label": "D", "text": "calcium"}, {"label": "E", "text": "refrigerator"}], "stem": "Some people do not care for milk because of what in it?"}}
{"id": "e918ba21a1018dd61e5a6fccd8f0527d", "question": {"question_concept": "talking to", "choices": [{"label": "A", "text": "learn"}, {"label": "B", "text": "friendship"}, {"label": "C", "text": "persuaded"}, {"label": "D", "text": "listen"}, {"label": "E", "text": "communication"}], "stem": "What is talking to an expert likely to result in?"}}
{"id": "b87d8520559d06d2a54e06d27ea60eb2", "question": {"question_concept": "getting in line", "choices": [{"label": "A", "text": "anxiety"}, {"label": "B", "text": "fatigue"}, {"label": "C", "text": "wait turn"}, {"label": "D", "text": "satisfied"}, {"label": "E", "text": "terrible"}], "stem": "How did the infirm man feel after getting in line at the pharmacy?"}}
{"id": "04e5f0a58c749ecc4f452a21fd94dc47", "question": {"question_concept": "hole", "choices": [{"label": "A", "text": "golf course"}, {"label": "B", "text": "swiss cheese"}, {"label": "C", "text": "play ground"}, {"label": "D", "text": "notebook paper"}, {"label": "E", "text": "doughnut"}], "stem": "Dave was playing at the 18th hold near the Krispy Kerme and he lost his ball.  He looked in his backpack for a replacement and come up with a terrible idea.  There was a crinkling sound that alerted the other players that something was amiss, and his makeshift ball did not go far.  What did he use as a ball?"}}
{"id": "016324203f91f22d936eaa74773b4460", "question": {"question_concept": "clothing", "choices": [{"label": "A", "text": "drawer"}, {"label": "B", "text": "suitcase"}, {"label": "C", "text": "closet"}, {"label": "D", "text": "floor"}, {"label": "E", "text": "house"}], "stem": "James got clothing for Christmas.  He folded it carefully and put it somewhere. Where might he has put it?"}}
{"id": "7a1fdff17d645dd9b83ff239e944290d", "question": {"question_concept": "going to market", "choices": [{"label": "A", "text": "stress"}, {"label": "B", "text": "arriving at desination"}, {"label": "C", "text": "impulse buying"}, {"label": "D", "text": "an empty wallet"}, {"label": "E", "text": "carrying bags"}], "stem": "If your'e going to market what should you bring?"}}
{"id": "898e890d6b44d1d29028177262813bd8", "question": {"question_concept": "eat hamburger", "choices": [{"label": "A", "text": "satisfy hunger"}, {"label": "B", "text": "cook one"}, {"label": "C", "text": "hungry"}, {"label": "D", "text": "good to eat"}, {"label": "E", "text": "protein"}], "stem": "The TV host would eat hamburger and explain how what it was to the camera?"}}
{"id": "8205599dadfc8a9624cddb0a89d3e06a", "question": {"question_concept": "passageway", "choices": [{"label": "A", "text": "jungle"}, {"label": "B", "text": "video game"}, {"label": "C", "text": "airport"}, {"label": "D", "text": "maze"}, {"label": "E", "text": "hotel"}], "stem": "Where would a passageway covered in vines be found?"}}
{"id": "b693fc52a837fb99c79480ceaa2401f8", "question": {"question_concept": "bald eagle", "choices": [{"label": "A", "text": "minnesota"}, {"label": "B", "text": "thermal"}, {"label": "C", "text": "feathers"}, {"label": "D", "text": "photograph"}, {"label": "E", "text": "colorado"}], "stem": "A bald eagle lifts into the air without flapping its wings, what did it take advantage of?"}}
{"id": "c8012ae18ae6dec2dce5780eec1bec27", "question": {"question_concept": "paper clips", "choices": [{"label": "A", "text": "work"}, {"label": "B", "text": "fashioned into simple tools"}, {"label": "C", "text": "drawer"}, {"label": "D", "text": "desktop"}, {"label": "E", "text": "university"}], "stem": "Where is a convenient place to store paper clips when not in use?"}}
{"id": "c5dbe48097fb33ddd77d6aea77d983e7", "question": {"question_concept": "telephone booth", "choices": [{"label": "A", "text": "london"}, {"label": "B", "text": "urban areas"}, {"label": "C", "text": "restuarant"}, {"label": "D", "text": "gas stations"}, {"label": "E", "text": "city"}], "stem": "Where can you occasionally still find a telephone booth?"}}
{"id": "9809509ba87af09ee945401653532406", "question": {"question_concept": "horses", "choices": [{"label": "A", "text": "drink water"}, {"label": "B", "text": "buck"}, {"label": "C", "text": "eat oats"}, {"label": "D", "text": "canter"}, {"label": "E", "text": "trot"}], "stem": "When a horse picks up speed it begins to what?"}}
{"id": "35d68bb6b9ec00475237433394156bc8", "question": {"question_concept": "statue", "choices": [{"label": "A", "text": "central park"}, {"label": "B", "text": "museum"}, {"label": "C", "text": "church"}, {"label": "D", "text": "water fountain"}, {"label": "E", "text": "new york city"}], "stem": "Where do you go to see the statue of liberty?"}}
{"id": "dac773277f2cd5d7c520c953aaaae3db", "question": {"question_concept": "birds", "choices": [{"label": "A", "text": "roof"}, {"label": "B", "text": "air"}, {"label": "C", "text": "park"}, {"label": "D", "text": "swimming"}, {"label": "E", "text": "countryside"}], "stem": "Where are birds likely most happy?"}}
{"id": "1310d3f4595280be53d11501c4d488c1", "question": {"question_concept": "break", "choices": [{"label": "A", "text": "electrical circuit"}, {"label": "B", "text": "putting together"}, {"label": "C", "text": "breakdance"}, {"label": "D", "text": "working"}, {"label": "E", "text": "accelerate"}], "stem": "Sam didn't care what I did to him.  All he wanted was to know that I was trying to help fix what I caused to break.  But it wasn't easy doing what to it?"}}
{"id": "9cf23f629c3bbbb504a0c3d4283d37cf", "question": {"question_concept": "creature", "choices": [{"label": "A", "text": "world"}, {"label": "B", "text": "dark place"}, {"label": "C", "text": "forest"}, {"label": "D", "text": "woods"}, {"label": "E", "text": "zoo"}], "stem": "What is a place where not all creatures live, but where it is possible to see a creature?"}}
{"id": "3b41f0bf3db2ebba1908d4f70df615ff", "question": {"question_concept": "animals", "choices": [{"label": "A", "text": "roast"}, {"label": "B", "text": "keep alive"}, {"label": "C", "text": "lie down"}, {"label": "D", "text": "need to eat"}, {"label": "E", "text": "bite"}], "stem": "What would you do to animals if you do not want to eat them?"}}
{"id": "13e5dc66569923cc2afcccfe81d68619", "question": {"question_concept": "animals", "choices": [{"label": "A", "text": "state park"}, {"label": "B", "text": "trigonometry"}, {"label": "C", "text": "fairgrounds"}, {"label": "D", "text": "zoos"}, {"label": "E", "text": "laboratory"}], "stem": "Where are animals tested on?"}}
{"id": "3de34881e026507dbb9d12ed07157e3c", "question": {"question_concept": "attending lecture", "choices": [{"label": "A", "text": "falling asleep"}, {"label": "B", "text": "applaud"}, {"label": "C", "text": "hear about neat"}, {"label": "D", "text": "taking notes"}, {"label": "E", "text": "learning"}], "stem": "While attending a lecture what can you do so that you can study important points later?"}}
{"id": "f559fe00d347816ee89722dba7e4d35d", "question": {"question_concept": "positive", "choices": [{"label": "A", "text": "falsely"}, {"label": "B", "text": "negative"}, {"label": "C", "text": "nonpositive"}, {"label": "D", "text": "bad"}, {"label": "E", "text": "uncertain"}], "stem": "John was positive that he knew the truth.  Billy, on the other hand, felt what?"}}
{"id": "0fa12481bb43195ded7ab1b5ccf03216", "question": {"question_concept": "box", "choices": [{"label": "A", "text": "store objects"}, {"label": "B", "text": "can catch ball"}, {"label": "C", "text": "store clothes"}, {"label": "D", "text": "hold things"}, {"label": "E", "text": "store food"}], "stem": "What could a box be used for if it is not refrigerated and has moths in it?"}}
{"id": "0add12d886f716f79808b73d611a22ab", "question": {"question_concept": "reply", "choices": [{"label": "A", "text": "answer"}, {"label": "B", "text": "initiate"}, {"label": "C", "text": "ignore"}, {"label": "D", "text": "response"}, {"label": "E", "text": "question"}], "stem": "He awaited the reply to his text, but all he got was an emoji for a what?"}}
{"id": "379fb9160d4348692d1a5aa10556dab6", "question": {"question_concept": "percussion instrument", "choices": [{"label": "A", "text": "drum store"}, {"label": "B", "text": "own home"}, {"label": "C", "text": "sun dress"}, {"label": "D", "text": "orchestra"}, {"label": "E", "text": "music store"}], "stem": "He really enjoyed renting time to play drums but he finally bought his own percussion instrument so he could play in his what?"}}
{"id": "0fbafeab8d19ccfca5857c1c01c4956f", "question": {"question_concept": "comfort friend", "choices": [{"label": "A", "text": "monetary"}, {"label": "B", "text": "money"}, {"label": "C", "text": "feeling bad"}, {"label": "D", "text": "sympathetic"}, {"label": "E", "text": "care"}], "stem": "She was always there to comfort friend, if anybody was what, she was there?"}}
{"id": "d43f3daa9631ba920192e5f174365ebb", "question": {"question_concept": "paper", "choices": [{"label": "A", "text": "reused"}, {"label": "B", "text": "burnt"}, {"label": "C", "text": "written on"}, {"label": "D", "text": "printed upon"}, {"label": "E", "text": "recycled"}], "stem": "He kept all his paper and plastic in a separate bin, this was so they could be what?"}}
{"id": "7b73302fe5480ff969b7faeeb3f8b3b4", "question": {"question_concept": "changing society", "choices": [{"label": "A", "text": "social disruption"}, {"label": "B", "text": "accept society"}, {"label": "C", "text": "social activism"}, {"label": "D", "text": "change yourself"}, {"label": "E", "text": "argue"}], "stem": "What do people participate in to go about changing society?"}}
{"id": "e1fa3519e0ff370988b5158dd64f4b74", "question": {"question_concept": "lock", "choices": [{"label": "A", "text": "drawer"}, {"label": "B", "text": "ignition switch"}, {"label": "C", "text": "controlling whether door opened"}, {"label": "D", "text": "firearm"}, {"label": "E", "text": "door"}], "stem": "You may want to lock what if you keep important documents inside?"}}
{"id": "5c92dc87f44998700d352ea224b19e68", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "classroom"}, {"label": "B", "text": "hotel"}, {"label": "C", "text": "populated areas"}, {"label": "D", "text": "supermarket"}, {"label": "E", "text": "race track"}], "stem": "A place where a lot of people live is called what?"}}
{"id": "2c248ffb5458cedf6881671bb96e8882", "question": {"question_concept": "blade", "choices": [{"label": "A", "text": "knife"}, {"label": "B", "text": "helicopter"}, {"label": "C", "text": "scissors"}, {"label": "D", "text": "tape dispenser"}, {"label": "E", "text": "lawn mower"}], "stem": "What type of common desk object can be used as a blade?"}}
{"id": "5f8101c2b7ce5a57bd4d267b841e8444", "question": {"question_concept": "front garden", "choices": [{"label": "A", "text": "front yard"}, {"label": "B", "text": "michigan"}, {"label": "C", "text": "forest"}, {"label": "D", "text": "outside of house"}, {"label": "E", "text": "urban area"}], "stem": "She wanted to show of her front garden to everyone, so where did she put it?"}}
{"id": "b450315e204252f3c1df6ae6e9719745", "question": {"question_concept": "opponent", "choices": [{"label": "A", "text": "ally"}, {"label": "B", "text": "proponent"}, {"label": "C", "text": "advocate"}, {"label": "D", "text": "supporter"}, {"label": "E", "text": "companion"}], "stem": "Jude was an opponent of Mr. Wiskers.  For this reason, Mr. June, who hated Mr. Miskers, was something for Jude.  What was he?"}}
{"id": "356336b2c101ea7a34eb98548c40c94e", "question": {"question_concept": "radio", "choices": [{"label": "A", "text": "school"}, {"label": "B", "text": "class"}, {"label": "C", "text": "shop"}, {"label": "D", "text": "space shuttle"}, {"label": "E", "text": "trunk"}], "stem": "He learned all about the radio for his extracurricular, it was his favorite memories of going to where?"}}
{"id": "145c49ad28c153880693a99b5281d711", "question": {"question_concept": "examine thing", "choices": [{"label": "A", "text": "learn about"}, {"label": "B", "text": "learned"}, {"label": "C", "text": "complex"}, {"label": "D", "text": "buy"}, {"label": "E", "text": "interesting"}], "stem": "The teacher said to examine thing number one, it seemed simple but under the microscope it was quite what?"}}
{"id": "4931a13dd0dc1e2d2fb87e9aa78f0dad", "question": {"question_concept": "heat source", "choices": [{"label": "A", "text": "solar energy"}, {"label": "B", "text": "bedroom"}, {"label": "C", "text": "body heat"}, {"label": "D", "text": "car"}, {"label": "E", "text": "coal or wood"}], "stem": "Where is there a heat source in the house when people are sleeping together?"}}
{"id": "78f8383b9ee9fba1779f4d42ea1edea6", "question": {"question_concept": "baby", "choices": [{"label": "A", "text": "sleep"}, {"label": "B", "text": "talk nonsense"}, {"label": "C", "text": "boy or girl"}, {"label": "D", "text": "arrive early"}, {"label": "E", "text": "learn to walk"}], "stem": "You baby must crawl before they what?"}}
{"id": "e307acb49938f9d0364120f10cb3edff", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "stand alone"}, {"label": "B", "text": "cross street"}, {"label": "C", "text": "fall asleep"}, {"label": "D", "text": "try again"}, {"label": "E", "text": "give up"}], "stem": "What would a person do if they do not succeed at something?"}}
{"id": "b0748338b350225195774fcc4dab2b73", "question": {"question_concept": "wardrobe", "choices": [{"label": "A", "text": "house"}, {"label": "B", "text": "bedroom"}, {"label": "C", "text": "mansion"}, {"label": "D", "text": "dressing room"}, {"label": "E", "text": "clothes cupboard"}], "stem": "Where is the likely place in a house for a wardrobe?"}}
{"id": "f13b10308db5c63f9453b5936bbb2c14", "question": {"question_concept": "date", "choices": [{"label": "A", "text": "pillage norway"}, {"label": "B", "text": "dress nice"}, {"label": "C", "text": "bathe"}, {"label": "D", "text": "go for haircut"}, {"label": "E", "text": "wait for"}], "stem": "What do you do for a date?"}}
{"id": "8a6a6783581db74ae107119281b6474b", "question": {"question_concept": "accelerator", "choices": [{"label": "A", "text": "force"}, {"label": "B", "text": "car"}, {"label": "C", "text": "accelerate"}, {"label": "D", "text": "fuel system"}, {"label": "E", "text": "airplane"}], "stem": "What makes an accelerator go?"}}
{"id": "e0e0df0a4f682c5717e2ac81370eb4cb", "question": {"question_concept": "calm", "choices": [{"label": "A", "text": "blustery"}, {"label": "B", "text": "stressed"}, {"label": "C", "text": "wild"}, {"label": "D", "text": "disturbed"}, {"label": "E", "text": "windy"}], "stem": "James told Sam to stay calm.  It was a dark night in Chicago, but that wasn't important.  Harry was in his basement and had said that he didn't want to be what?"}}
{"id": "60ad81e7f780c105d7337834308c8cd9", "question": {"question_concept": "cereal", "choices": [{"label": "A", "text": "at a store"}, {"label": "B", "text": "box"}, {"label": "C", "text": "shelf"}, {"label": "D", "text": "cabinet"}, {"label": "E", "text": "cupboard"}], "stem": "When a visitor asks me where to find the cereal, what do I likely tell them?"}}
{"id": "e174c3538697c2286b7b4dbaedfa9ee8", "question": {"question_concept": "fountain pen", "choices": [{"label": "A", "text": "desk drawer"}, {"label": "B", "text": "lady's purse"}, {"label": "C", "text": "blotter"}, {"label": "D", "text": "behind ear"}, {"label": "E", "text": "shirt pocket"}], "stem": "Always wear protection if putting a fountain pen where?"}}
{"id": "2ac0c61b6c04de3f8c3cf5dc23501ab5", "question": {"question_concept": "lying", "choices": [{"label": "A", "text": "avoid eye contact"}, {"label": "B", "text": "blush"}, {"label": "C", "text": "turn inside out"}, {"label": "D", "text": "feel guilty"}, {"label": "E", "text": "fall asleep"}], "stem": "Sarah wasn't good at lying.  She had an obvious tell.  Whenever she lied, her face would do what?"}}
{"id": "d391d07450e85d6d841a3e13ce93b9de", "question": {"question_concept": "jogging", "choices": [{"label": "A", "text": "fitness"}, {"label": "B", "text": "lose weight"}, {"label": "C", "text": "fatigue"}, {"label": "D", "text": "socks"}, {"label": "E", "text": "getting in shape"}], "stem": "She was jogging for hours a day, what did her legs feel?"}}
{"id": "b87b028cbe42e40e74b8f84f76c41f93", "question": {"question_concept": "working", "choices": [{"label": "A", "text": "prosperity"}, {"label": "B", "text": "earning money"}, {"label": "C", "text": "chiseling"}, {"label": "D", "text": "creation"}, {"label": "E", "text": "getting paid"}], "stem": "He was working hard on his sculpture, what was he practicing?"}}
{"id": "279a90fda74c4251b2b67ea1f4723833", "question": {"question_concept": "gentleman", "choices": [{"label": "A", "text": "tuxedo"}, {"label": "B", "text": "party"}, {"label": "C", "text": "big house"}, {"label": "D", "text": "men's room"}, {"label": "E", "text": "church"}], "stem": "When you think of a gentleman you think of him wearing a formal what?"}}
{"id": "eda022d48cfb59230b21c8d632feb17f", "question": {"question_concept": "sitting quietly", "choices": [{"label": "A", "text": "falling asleep"}, {"label": "B", "text": "solitude"}, {"label": "C", "text": "uniqueness"}, {"label": "D", "text": "relaxation"}, {"label": "E", "text": "discomfort"}], "stem": "John was sitting quietly after class. He was the only person in detention.  He enjoyed something about this.  What might he had enjoyed about his situation?"}}
{"id": "dade817bae98f69b39d59a63fbd7f49d", "question": {"question_concept": "telephone book", "choices": [{"label": "A", "text": "house"}, {"label": "B", "text": "library"}, {"label": "C", "text": "at hotel"}, {"label": "D", "text": "salon"}, {"label": "E", "text": "telephone booth"}], "stem": "What is a place that usually has an elevator and that often has a telephone book?"}}
{"id": "970d5702610cae61d1980a3833003657", "question": {"question_concept": "doctor", "choices": [{"label": "A", "text": "personal home"}, {"label": "B", "text": "golf course"}, {"label": "C", "text": "emergency room"}, {"label": "D", "text": "nursing home"}, {"label": "E", "text": "medical school"}], "stem": "Where would you find a doctor caring for the elderly who are living together?"}}
{"id": "9fbf966e07642db98799bcd509dda1ca", "question": {"question_concept": "booze", "choices": [{"label": "A", "text": "pass out"}, {"label": "B", "text": "stop bicycle"}, {"label": "C", "text": "examine thing"}, {"label": "D", "text": "stay in bed"}, {"label": "E", "text": "reach tentative agreement"}], "stem": "If you drink too much booze what are you likely to do?"}}
{"id": "2993d9aa7f3a164ec890187970fc3a97", "question": {"question_concept": "ferret", "choices": [{"label": "A", "text": "coffee table"}, {"label": "B", "text": "hutch"}, {"label": "C", "text": "north carolina"}, {"label": "D", "text": "own home"}, {"label": "E", "text": "great britain"}], "stem": "What pig piece of furniture with cabinets can ferret damage?"}}
{"id": "d2ee3008cc48217e9755666214e5cad8", "question": {"question_concept": "going to party", "choices": [{"label": "A", "text": "dancing with kids"}, {"label": "B", "text": "babies"}, {"label": "C", "text": "getting drunk"}, {"label": "D", "text": "laughter"}, {"label": "E", "text": "happiness"}], "stem": "What are expectant mothers likely going to a party to celebrate?"}}
{"id": "aa2ff90ed49a7ffb5fe572f9d53fe1d2", "question": {"question_concept": "passageway", "choices": [{"label": "A", "text": "hospital"}, {"label": "B", "text": "building"}, {"label": "C", "text": "cave"}, {"label": "D", "text": "house"}, {"label": "E", "text": "airport"}], "stem": "A mom walks with laundry through the passageway, where is she?"}}
{"id": "8d8d31491e849cc3e7396e6ea7ffb8e2", "question": {"question_concept": "answering questions", "choices": [{"label": "A", "text": "teaching"}, {"label": "B", "text": "embarassment"}, {"label": "C", "text": "job interview"}, {"label": "D", "text": "people will understand"}, {"label": "E", "text": "children will learn"}], "stem": "The politician is answering questions, why is he doing that?"}}
{"id": "0aa07d7901c31086aae32dc4568a1740", "question": {"question_concept": "value", "choices": [{"label": "A", "text": "expensive"}, {"label": "B", "text": "disrespect"}, {"label": "C", "text": "valuable"}, {"label": "D", "text": "worthlessness"}, {"label": "E", "text": "invaluable"}], "stem": "Will something with a lot of value be cheap?"}}
{"id": "886563af24c9626fd34fef097bd121ea", "question": {"question_concept": "human", "choices": [{"label": "A", "text": "write"}, {"label": "B", "text": "have fever"}, {"label": "C", "text": "die"}, {"label": "D", "text": "have to sleep"}, {"label": "E", "text": "smile"}], "stem": "A human that tricks an artificial intelligence might what afterwards?"}}
{"id": "0183fb599bd83746869b7e3a08772e97", "question": {"question_concept": "chair", "choices": [{"label": "A", "text": "cubicle"}, {"label": "B", "text": "porch"}, {"label": "C", "text": "living room"}, {"label": "D", "text": "church"}, {"label": "E", "text": "kitchen"}], "stem": "Where are people likely to put a chair at a counter?"}}
{"id": "8b7d08b479a2d32476dcb31470241043", "question": {"question_concept": "remembering", "choices": [{"label": "A", "text": "nostalgia"}, {"label": "B", "text": "sneeze"}, {"label": "C", "text": "being prepared"}, {"label": "D", "text": "writing down"}, {"label": "E", "text": "phoning"}], "stem": "If you're not good at remembering things you should do what?"}}
{"id": "753bd9bcdae10c2e02d1fd1d20e0da0f", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "sail boat"}, {"label": "B", "text": "swim"}, {"label": "C", "text": "laugh at himself"}, {"label": "D", "text": "cross street"}, {"label": "E", "text": "hurry up"}], "stem": "It can sometimes be difficult for a person who just did something embarrassing to?"}}
{"id": "f56437a35e05a6c80918c8550d066c71", "question": {"question_concept": "well", "choices": [{"label": "A", "text": "ground"}, {"label": "B", "text": "idaho"}, {"label": "C", "text": "countryside"}, {"label": "D", "text": "oil field"}, {"label": "E", "text": "kansas"}], "stem": "John is moving to a new place and has to dig a well so that he'll have water.  Where might he be living?"}}
{"id": "c40d1dbc70fad69fe1fc0ca6aed2adab", "question": {"question_concept": "shopping", "choices": [{"label": "A", "text": "standing in line"}, {"label": "B", "text": "debt"}, {"label": "C", "text": "spending money"}, {"label": "D", "text": "tiredness"}, {"label": "E", "text": "being broke"}], "stem": "What can too much shopping lead to?"}}
{"id": "3a6e4e767bf96b077a9f814abde877b2", "question": {"question_concept": "flirting", "choices": [{"label": "A", "text": "sexual attraction"}, {"label": "B", "text": "smile"}, {"label": "C", "text": "cry"}, {"label": "D", "text": "personality"}, {"label": "E", "text": "getting excited"}], "stem": "What do you do when flirting with someone?"}}
{"id": "2341e9d91c8b5d394a45b2fbcebad0b3", "question": {"question_concept": "commit suicide", "choices": [{"label": "A", "text": "dying"}, {"label": "B", "text": "end life"}, {"label": "C", "text": "happy"}, {"label": "D", "text": "drive into oncoming truck"}, {"label": "E", "text": "pain"}], "stem": "Why does someone want to commit suicide?"}}
{"id": "e3b5993657e75d9c914e132514ff5951", "question": {"question_concept": "sleeping", "choices": [{"label": "A", "text": "talking"}, {"label": "B", "text": "get cold"}, {"label": "C", "text": "snoring"}, {"label": "D", "text": "might dream"}, {"label": "E", "text": "running"}], "stem": "He was the only one in the room sleeping, this was because his what kept others awake?"}}
{"id": "41f6c1367149a77a400d685582059b7b", "question": {"question_concept": "feather", "choices": [{"label": "A", "text": "cap"}, {"label": "B", "text": "pillow"}, {"label": "C", "text": "style look"}, {"label": "D", "text": "bird cage"}, {"label": "E", "text": "birds nest"}], "stem": "What in you cap is an expression that means an accomplishment that makes you look good?"}}
{"id": "f433755e8e1519f69efb55ce81b10793", "question": {"question_concept": "pencil", "choices": [{"label": "A", "text": "library"}, {"label": "B", "text": "desktop"}, {"label": "C", "text": "classroom"}, {"label": "D", "text": "desk drawer"}, {"label": "E", "text": "backpack"}], "stem": "What place would a student use a pencil at?"}}
{"id": "9f33a00d0afc321d2ca1f4bd350d5b7c", "question": {"question_concept": "having sex", "choices": [{"label": "A", "text": "stds"}, {"label": "B", "text": "unwanted pregnancy"}, {"label": "C", "text": "orgasm"}, {"label": "D", "text": "bliss"}, {"label": "E", "text": "getting pregnant"}], "stem": "What is the result of not being careful when having sex?"}}
{"id": "fe9d17c53bed2f836af3704b2f2f0b95", "question": {"question_concept": "studying", "choices": [{"label": "A", "text": "gathering information"}, {"label": "B", "text": "impress teacher"}, {"label": "C", "text": "headaches"}, {"label": "D", "text": "inspiration"}, {"label": "E", "text": "knowing more"}], "stem": "James has finished his classes, but he's studying intensely anyway.  Why might he want to study when his classes are finished?"}}
{"id": "7861e40cb6305c37cecddd49dc71e49d", "question": {"question_concept": "cat", "choices": [{"label": "A", "text": "fly"}, {"label": "B", "text": "floor"}, {"label": "C", "text": "back yard"}, {"label": "D", "text": "warm place"}, {"label": "E", "text": "beam of sunlight"}], "stem": "What does the cat try to catch when it is shining from a windom?"}}
{"id": "1c430edbdfb11363836142f044f02254", "question": {"question_concept": "buy house", "choices": [{"label": "A", "text": "negotiate"}, {"label": "B", "text": "settle down"}, {"label": "C", "text": "bargain"}, {"label": "D", "text": "lose money"}, {"label": "E", "text": "pay for"}], "stem": "The couple wanted to buy house, but they didn't have enough to what it?"}}
{"id": "0f57857b75c023422a6bc27540c8fa21", "question": {"question_concept": "cooling off", "choices": [{"label": "A", "text": "chills"}, {"label": "B", "text": "better decisions"}, {"label": "C", "text": "swim"}, {"label": "D", "text": "calm down"}, {"label": "E", "text": "expansion"}], "stem": "How will cooling off help you when thinking about what to do?"}}
{"id": "74af5a95ab64f32be515c5c73e29300c", "question": {"question_concept": "pistol", "choices": [{"label": "A", "text": "war"}, {"label": "B", "text": "security"}, {"label": "C", "text": "pants"}, {"label": "D", "text": "police station"}, {"label": "E", "text": "police officer's belt"}], "stem": "Where would you find a pistol near people in blue uniforms?"}}
{"id": "77959fd44af8ca200d3690e0e1f3256d", "question": {"question_concept": "students", "choices": [{"label": "A", "text": "ask questions"}, {"label": "B", "text": "estimate value"}, {"label": "C", "text": "better comprehension"}, {"label": "D", "text": "study books"}, {"label": "E", "text": "read books"}], "stem": "What do students rarely have time to do for pleasure only?"}}
{"id": "8ddf2a704ddc4df5538dcecf14fde957", "question": {"question_concept": "reaching advantage", "choices": [{"label": "A", "text": "superior"}, {"label": "B", "text": "insight"}, {"label": "C", "text": "determination"}, {"label": "D", "text": "skill"}, {"label": "E", "text": "upper hand"}], "stem": "When reaching advantage in things, you are said to have superior?"}}
{"id": "9677d15134e8bdef90c16e91f9a67696", "question": {"question_concept": "onions", "choices": [{"label": "A", "text": "refrigerator"}, {"label": "B", "text": "pizza"}, {"label": "C", "text": "table"}, {"label": "D", "text": "kitchen"}, {"label": "E", "text": "pantry"}], "stem": "James wanted onions but Sarah didn't want any. Because of this,  they split the what in half?"}}
{"id": "27896354b9e2d90b96b46a917c92de14", "question": {"question_concept": "killing", "choices": [{"label": "A", "text": "commit crime"}, {"label": "B", "text": "die"}, {"label": "C", "text": "being caught"}, {"label": "D", "text": "feel mad"}, {"label": "E", "text": "feel anger"}], "stem": "What would happen to someone if a killing is happening?"}}
{"id": "61b6e331f3277fd70a2df08e762b3474", "question": {"question_concept": "cat", "choices": [{"label": "A", "text": "floor"}, {"label": "B", "text": "houseplant"}, {"label": "C", "text": "urban neighborhood"}, {"label": "D", "text": "container"}, {"label": "E", "text": "dumpsters"}], "stem": "Behind a restaurant, where can a cat find food?"}}
{"id": "a121ce1fb19f8e89e36c38bbbbd4ba49", "question": {"question_concept": "vase", "choices": [{"label": "A", "text": "cabinet"}, {"label": "B", "text": "shelf"}, {"label": "C", "text": "fall off shelf"}, {"label": "D", "text": "windowsill"}, {"label": "E", "text": "table"}], "stem": "Where would you put a vase to make sure the flowers inside it get enough sun?"}}
{"id": "22d68f07d577eb599c44f1935461bdc9", "question": {"question_concept": "glue stick", "choices": [{"label": "A", "text": "apply glue to surfaces"}, {"label": "B", "text": "office"}, {"label": "C", "text": "bedroom"}, {"label": "D", "text": "desk drawer"}, {"label": "E", "text": "classroom"}], "stem": "where do children use glue sticks?"}}
{"id": "33fc33503d33164b7f788d163ad7a8db", "question": {"question_concept": "lying", "choices": [{"label": "A", "text": "get caught"}, {"label": "B", "text": "mistrust"}, {"label": "C", "text": "being fired"}, {"label": "D", "text": "problems"}, {"label": "E", "text": "broken heart"}], "stem": "What emotion does habitual lying tends to cause?"}}
{"id": "a81a1c6aa70db5609a9fadb95e361755", "question": {"question_concept": "water", "choices": [{"label": "A", "text": "flooding"}, {"label": "B", "text": "blizzard"}, {"label": "C", "text": "drowning"}, {"label": "D", "text": "surface of earth"}, {"label": "E", "text": "cactus"}], "stem": "What does being under water for a long time lead to?"}}
{"id": "e68302bbf0173ab999ee2ab74d991ef2", "question": {"question_concept": "cogitating", "choices": [{"label": "A", "text": "crying"}, {"label": "B", "text": "headaches"}, {"label": "C", "text": "decision"}, {"label": "D", "text": "enlightenment"}, {"label": "E", "text": "new thoughts"}], "stem": "What can happen if you are cogitating successfully?"}}
{"id": "9755fad2c2f39af584d2da77c011f5d8", "question": {"question_concept": "large container", "choices": [{"label": "A", "text": "garden"}, {"label": "B", "text": "cabinet"}, {"label": "C", "text": "factory"}, {"label": "D", "text": "bedroom"}, {"label": "E", "text": "juice"}], "stem": "Where can you find the most large container?"}}
{"id": "3dc643db185338cfd8ec5a58f1cb6598", "question": {"question_concept": "parking lot", "choices": [{"label": "A", "text": "town"}, {"label": "B", "text": "car"}, {"label": "C", "text": "pedestrian path"}, {"label": "D", "text": "business district"}, {"label": "E", "text": "city"}], "stem": "You can find paid parking lots in what part of a city?"}}
{"id": "8a1ef69ebc3a93b6c2e08202eeaea749", "question": {"question_concept": "having bath", "choices": [{"label": "A", "text": "get wet"}, {"label": "B", "text": "wet hair"}, {"label": "C", "text": "wet skin"}, {"label": "D", "text": "drowning"}, {"label": "E", "text": "rash"}], "stem": "Babies must be careful when having a bath, what could happen with a distracted or absent parent?"}}
{"id": "b2e9ee51cf341b072b77e2e1d57b86e3", "question": {"question_concept": "music", "choices": [{"label": "A", "text": "opera"}, {"label": "B", "text": "bathroom"}, {"label": "C", "text": "carnival"}, {"label": "D", "text": "night club"}, {"label": "E", "text": "elevator"}], "stem": "The music was loud do you know where it came from"}}
{"id": "ed2950219edc87d0bfacc41b8bf22217", "question": {"question_concept": "bare", "choices": [{"label": "A", "text": "ample"}, {"label": "B", "text": "bareword"}, {"label": "C", "text": "covered"}, {"label": "D", "text": "full"}, {"label": "E", "text": "dressed"}], "stem": "Muslims believe that women should not have bare head but that they should be what?"}}
{"id": "9abefd2db791cdfd84f5faa0d7cbbf67", "question": {"question_concept": "contemplating", "choices": [{"label": "A", "text": "sense of fulfillment"}, {"label": "B", "text": "clear thought"}, {"label": "C", "text": "thinking"}, {"label": "D", "text": "deep thoughts"}, {"label": "E", "text": "contemplating"}], "stem": "What is someone doing when contemplating the nature of the universe?"}}
{"id": "7755512b863ce76553ba4c8ed7f9d4cf", "question": {"question_concept": "cold storage", "choices": [{"label": "A", "text": "warehouse"}, {"label": "B", "text": "laboratory"}, {"label": "C", "text": "meat van"}, {"label": "D", "text": "refrigerator"}, {"label": "E", "text": "grocery store"}], "stem": "Where could there be cold storage with millions of pieces of food in it?"}}
{"id": "0e4e1db19c9080c10e266d102296d9c1", "question": {"question_concept": "student", "choices": [{"label": "A", "text": "stand"}, {"label": "B", "text": "complete test"}, {"label": "C", "text": "do mathematics"}, {"label": "D", "text": "begin to study"}, {"label": "E", "text": "learn language"}], "stem": "What does a student at a desk do first?"}}
{"id": "e34aa4ac0dcd962eca8bedb461971043", "question": {"question_concept": "helping", "choices": [{"label": "A", "text": "satisfaction"}, {"label": "B", "text": "kindness"}, {"label": "C", "text": "pay"}, {"label": "D", "text": "heart"}, {"label": "E", "text": "happiness"}], "stem": "Some people begin helping others because of guilt, others just do it out of the goodness of their what?"}}
{"id": "35265d63f143f358a8b513555909504e", "question": {"question_concept": "menu", "choices": [{"label": "A", "text": "cafe"}, {"label": "B", "text": "advertisement"}, {"label": "C", "text": "bar"}, {"label": "D", "text": "computer program"}, {"label": "E", "text": "internet sites"}], "stem": "James clicked what he thought was a menu, but it took him to a different place.  It turned out to be what?"}}
{"id": "14363ea03a865beee43d3f72d1138996", "question": {"question_concept": "postpone", "choices": [{"label": "A", "text": "hasten"}, {"label": "B", "text": "work"}, {"label": "C", "text": "stop"}, {"label": "D", "text": "act"}, {"label": "E", "text": "decide"}], "stem": "The weather was threatening to postpone the what being done to the outside of the building?"}}
{"id": "94faa86c352cd71b613bef387ee22dcd", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "opera"}, {"label": "B", "text": "hockey game"}, {"label": "C", "text": "hospital"}, {"label": "D", "text": "water cooler"}, {"label": "E", "text": "office"}], "stem": "Where would you find people wearing uniforms and chasing a rubber disc?"}}
{"id": "ba0c611a9592548f6d90deb12b8db1ff", "question": {"question_concept": "going somewhere", "choices": [{"label": "A", "text": "elapsed time"}, {"label": "B", "text": "happiness"}, {"label": "C", "text": "movement"}, {"label": "D", "text": "relaxation"}, {"label": "E", "text": "uneasiness"}], "stem": "What sort of emotion would be experienced when out and about in an unfamiliar area?"}}
{"id": "5ab5deb85596600323a0807c6ed4a2af", "question": {"question_concept": "chess set", "choices": [{"label": "A", "text": "drawer"}, {"label": "B", "text": "cupboard"}, {"label": "C", "text": "dorm"}, {"label": "D", "text": "refrigerator"}, {"label": "E", "text": "sitting room"}], "stem": "When you're finished with a chess set you'd put it in what?"}}
{"id": "b5406e7a0fbaa7b8ee42fd256c7636a5", "question": {"question_concept": "county highway", "choices": [{"label": "A", "text": "michigan"}, {"label": "B", "text": "country"}, {"label": "C", "text": "counties"}, {"label": "D", "text": "rural areas"}, {"label": "E", "text": "map"}], "stem": "Where is a county highway likely to cut through?"}}
{"id": "9c06a9149414c95e2237cfb88173c410", "question": {"question_concept": "bean bag chair", "choices": [{"label": "A", "text": "palace"}, {"label": "B", "text": "floor"}, {"label": "C", "text": "den"}, {"label": "D", "text": "house"}, {"label": "E", "text": "family room"}], "stem": "In what structure are you likely to find a bean bag chair?"}}
{"id": "bcde206a472d28a043f7a46f7d7a772a", "question": {"question_concept": "shopping center", "choices": [{"label": "A", "text": "people"}, {"label": "B", "text": "stores"}, {"label": "C", "text": "suburbs"}, {"label": "D", "text": "populated area"}, {"label": "E", "text": "urban area"}], "stem": "Residents of the small town feared a new shopping center, what didn't they want their town to become?"}}
{"id": "24db593eb9126849f7e07e88057260d2", "question": {"question_concept": "toy balloon", "choices": [{"label": "A", "text": "supermarket"}, {"label": "B", "text": "circus"}, {"label": "C", "text": "birthday party"}, {"label": "D", "text": "toy store"}, {"label": "E", "text": "amusement park"}], "stem": "Where would I be responsible for blowing up the toy balloon?"}}
{"id": "93eb8f1347d48b7a5b39b78d7cdac7af", "question": {"question_concept": "corner shop", "choices": [{"label": "A", "text": "iowa"}, {"label": "B", "text": "kentucky"}, {"label": "C", "text": "miami"}, {"label": "D", "text": "town"}, {"label": "E", "text": "england"}], "stem": "In what group of people's homes will you find a corner shop?"}}
{"id": "c0e1007d2b344552f19db83350fae592", "question": {"question_concept": "lust", "choices": [{"label": "A", "text": "kiss"}, {"label": "B", "text": "copulate"}, {"label": "C", "text": "go to party"}, {"label": "D", "text": "procreate"}, {"label": "E", "text": "have sex"}], "stem": "The lust of one night can lead to a life altering situation if the two parties inadvertently what?"}}
{"id": "903d079abdb46a531cedf482ee70d407", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "hospitalized"}, {"label": "B", "text": "broken heart"}, {"label": "C", "text": "schizophrenia"}, {"label": "D", "text": "cold"}, {"label": "E", "text": "annoyance"}], "stem": "The person was trying to ignore other passenger on the plane talking incessantly, what did that person feel?"}}
{"id": "0f472940407eb31a1cbeb9ad33e4280f", "question": {"question_concept": "see particular program", "choices": [{"label": "A", "text": "tape"}, {"label": "B", "text": "buy tickets"}, {"label": "C", "text": "find out when"}, {"label": "D", "text": "hatred"}, {"label": "E", "text": "stop channelsurfing"}], "stem": "The old couple wanted to see particular program at the theater but were unsure when it started, they went to the theater to what?"}}
{"id": "6b4e541bc7f12e38f4a8a15962c694ce", "question": {"question_concept": "product", "choices": [{"label": "A", "text": "shelf"}, {"label": "B", "text": "demonstration"}, {"label": "C", "text": "store"}, {"label": "D", "text": "ground"}, {"label": "E", "text": "market"}], "stem": "The doors closed to customers and he got to do his favorite part of the job, he began stocking product on the what?"}}
{"id": "9ab8bd4cfff2d2b964b5fb9a4a57b074", "question": {"question_concept": "dogs", "choices": [{"label": "A", "text": "own people"}, {"label": "B", "text": "go outside"}, {"label": "C", "text": "fed us"}, {"label": "D", "text": "reproduce"}, {"label": "E", "text": "need food"}], "stem": "Out relationship with dogs is very one sided.  We can do something to dogs, but they can't do the same thing to us.  What can't dogs do to us?"}}
{"id": "485feba10623280ddfbc7f99a6c8bc1a", "question": {"question_concept": "traffic artery", "choices": [{"label": "A", "text": "town"}, {"label": "B", "text": "freeway"}, {"label": "C", "text": "major city"}, {"label": "D", "text": "high traffic area"}, {"label": "E", "text": "highway"}], "stem": "Where is likely to have a small traffic artery?"}}
{"id": "3bf15ba95e7233d8e81cd998308ccde8", "question": {"question_concept": "chips", "choices": [{"label": "A", "text": "pantry"}, {"label": "B", "text": "bar"}, {"label": "C", "text": "motherboard"}, {"label": "D", "text": "supermarket"}, {"label": "E", "text": "work"}], "stem": "The man's greatest weakness was salty snacks, he grabbed bag after bag of chips every trip to the where?"}}
{"id": "08ca0024cb29cbb8d6673175ec543d28", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "sadness"}, {"label": "B", "text": "arm himself"}, {"label": "C", "text": "receive mail"}, {"label": "D", "text": "feel sleepy"}, {"label": "E", "text": "catch cold"}], "stem": "The person hadn't slept in 3 days, what did his brain experience?"}}
{"id": "815a8367d08a14f150a6c777ad7f789a", "question": {"question_concept": "computer", "choices": [{"label": "A", "text": "box"}, {"label": "B", "text": "office building"}, {"label": "C", "text": "backpack"}, {"label": "D", "text": "school"}, {"label": "E", "text": "table"}], "stem": "Where would you put a laptop computer if you want to use it outside?"}}
{"id": "9082b65f2bc5328ea991f734f930ddb5", "question": {"question_concept": "children", "choices": [{"label": "A", "text": "watch television"}, {"label": "B", "text": "play basketball"}, {"label": "C", "text": "cut and paste"}, {"label": "D", "text": "swimming"}, {"label": "E", "text": "reach over"}], "stem": "If children were in a gym, would they be doing?"}}
{"id": "3abf430c8338c3a4cdaa3e26b96bcae2", "question": {"question_concept": "dishes", "choices": [{"label": "A", "text": "shelf"}, {"label": "B", "text": "apartment"}, {"label": "C", "text": "cabinet"}, {"label": "D", "text": "dining room"}, {"label": "E", "text": "pantry"}], "stem": "What is a place where people live that has dishes?"}}
{"id": "fb46652b6016be675e301fafe03222f3", "question": {"question_concept": "anger", "choices": [{"label": "A", "text": "punch"}, {"label": "B", "text": "calm"}, {"label": "C", "text": "field"}, {"label": "D", "text": "sweetness"}, {"label": "E", "text": "happiness"}], "stem": "The situation was causing anger, but his wife subdued it with her heartwarming what?"}}
{"id": "27a3f39930a7383a9723897eb0e88f20", "question": {"question_concept": "ferret", "choices": [{"label": "A", "text": "pair of trousers"}, {"label": "B", "text": "on holiday"}, {"label": "C", "text": "classroom"}, {"label": "D", "text": "great britain"}, {"label": "E", "text": "bad mood"}], "stem": "John the ferret was a pet ferret of a Kindergarten.  It liked to get into things that it shouldn't get into.  One day the ferret went missing and the whole school looked for it.  Mr. Johnson felt something scurry up his leg and settle around his private area. Mr. Johnson did not like this one bit.  Where was the ferret?"}}
{"id": "c9a82c294ae81ca5f2b4dd7f4c031310", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "welcome change"}, {"label": "B", "text": "catch cold"}, {"label": "C", "text": "become depressed"}, {"label": "D", "text": "pay bills"}, {"label": "E", "text": "say hi"}], "stem": "What does a person do at the end of every month?"}}
{"id": "c96f53d1a064f277d805dd00f3d9402d", "question": {"question_concept": "excellent", "choices": [{"label": "A", "text": "bad"}, {"label": "B", "text": "horrible"}, {"label": "C", "text": "poor performance"}, {"label": "D", "text": "awful"}, {"label": "E", "text": "poor"}], "stem": "If someone is typically in an excellent mood but things just are not going well for them that day, what kind of day are they having?"}}
