{"answerKey": "A", "id": "1afa02df02c908a558b4036e80242fac", "question": {"question_concept": "revolving door", "choices": [{"label": "A", "text": "bank"}, {"label": "B", "text": "library"}, {"label": "C", "text": "department store"}, {"label": "D", "text": "mall"}, {"label": "E", "text": "new york"}], "stem": "A revolving door is convenient for two direction travel, but it also serves as a security measure at a what?"}}
{"answerKey": "A", "id": "a7ab086045575bb497933726e4e6ad28", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "complete job"}, {"label": "B", "text": "learn from each other"}, {"label": "C", "text": "kill animals"}, {"label": "D", "text": "wear hats"}, {"label": "E", "text": "talk to each other"}], "stem": "What do people aim to do at work?"}}
{"answerKey": "B", "id": "b8c0a4703079cf661d7261a60a1bcbff", "question": {"question_concept": "magazines", "choices": [{"label": "A", "text": "doctor"}, {"label": "B", "text": "bookstore"}, {"label": "C", "text": "market"}, {"label": "D", "text": "train station"}, {"label": "E", "text": "mortuary"}], "stem": "Where would you find magazines along side many other printed works?"}}
{"answerKey": "A", "id": "e68fb2448fd74e402aae9982aa76e527", "question": {"question_concept": "hamburger", "choices": [{"label": "A", "text": "fast food restaurant"}, {"label": "B", "text": "pizza"}, {"label": "C", "text": "ground up dead cows"}, {"label": "D", "text": "mouth"}, {"label": "E", "text": "cow carcus"}], "stem": "Where are  you likely to find a hamburger?"}}
{"answerKey": "A", "id": "2435de612dd69f2012b9e40d6af4ce38", "question": {"question_concept": "farmland", "choices": [{"label": "A", "text": "midwest"}, {"label": "B", "text": "countryside"}, {"label": "C", "text": "estate"}, {"label": "D", "text": "farming areas"}, {"label": "E", "text": "illinois"}], "stem": "James was looking for a good place to buy farmland.  Where might he look?"}}
{"answerKey": "C", "id": "a4892551cb4beb279653ae52d0de4c89", "question": {"question_concept": "ferret", "choices": [{"label": "A", "text": "own home"}, {"label": "B", "text": "north carolina"}, {"label": "C", "text": "great britain"}, {"label": "D", "text": "hutch"}, {"label": "E", "text": "outdoors"}], "stem": "What island country is ferret popular?"}}
{"answerKey": "B", "id": "118a9093a30695622363455e4d911866", "question": {"question_concept": "cup of coffee", "choices": [{"label": "A", "text": "mildred's coffee shop"}, {"label": "B", "text": "mexico"}, {"label": "C", "text": "diner"}, {"label": "D", "text": "kitchen"}, {"label": "E", "text": "canteen"}], "stem": "In what Spanish speaking North American country can you get a great cup of coffee?"}}
{"answerKey": "D", "id": "05ea49b82e8ec519e82d6633936ab8bf", "question": {"question_concept": "animals", "choices": [{"label": "A", "text": "feel pleasure"}, {"label": "B", "text": "procreate"}, {"label": "C", "text": "pass water"}, {"label": "D", "text": "listen to each other"}, {"label": "E", "text": "sing"}], "stem": "What do animals do when an enemy is approaching?"}}
{"answerKey": "A", "id": "c0c07ce781653b2a2c01871ba2bcba93", "question": {"question_concept": "reading newspaper", "choices": [{"label": "A", "text": "literacy"}, {"label": "B", "text": "knowing how to read"}, {"label": "C", "text": "money"}, {"label": "D", "text": "buying"}, {"label": "E", "text": "money bank"}], "stem": "Reading newspaper one of many ways to practice your what?"}}
{"answerKey": "C", "id": "1d24f406b6828492040b405d3f35119c", "question": {"question_concept": "playing guitar", "choices": [{"label": "A", "text": "cry"}, {"label": "B", "text": "hear sounds"}, {"label": "C", "text": "singing"}, {"label": "D", "text": "arthritis"}, {"label": "E", "text": "making music"}], "stem": "What do people typically do while playing guitar?"}}
{"answerKey": "E", "id": "57f92025d860e32c4e780c0d51c1c20c", "question": {"question_concept": "vinyl", "choices": [{"label": "A", "text": "pants"}, {"label": "B", "text": "record albums"}, {"label": "C", "text": "record store"}, {"label": "D", "text": "cheese"}, {"label": "E", "text": "wallpaper"}], "stem": "What would vinyl be an odd thing to replace?"}}
{"answerKey": "D", "id": "81eb4b2ee66edd8bc91ee944697c4e9f", "question": {"question_concept": "something you", "choices": [{"label": "A", "text": "take time"}, {"label": "B", "text": "make noise"}, {"label": "C", "text": "make war"}, {"label": "D", "text": "make peace"}, {"label": "E", "text": "make haste"}], "stem": "If you want harmony, what is something you should try to do with the world?"}}
{"answerKey": "A", "id": "d807e7ae60976324920c8d29eb42dad6", "question": {"question_concept": "heifer", "choices": [{"label": "A", "text": "farm house"}, {"label": "B", "text": "barnyard"}, {"label": "C", "text": "stockyard"}, {"label": "D", "text": "slaughter house"}, {"label": "E", "text": "eat cake"}], "stem": "Where does a heifer's master live?"}}
{"answerKey": "D", "id": "7ea9f721ffc662918bb0c0937a487f04", "question": {"question_concept": "dog", "choices": [{"label": "A", "text": "bone"}, {"label": "B", "text": "charm"}, {"label": "C", "text": "petted"}, {"label": "D", "text": "lots of attention"}, {"label": "E", "text": "walked"}], "stem": "Aside from water and nourishment what does your dog need?"}}
{"answerKey": "C", "id": "fc1d33a2301a30214523c12573f81aba", "question": {"question_concept": "watching film", "choices": [{"label": "A", "text": "erection"}, {"label": "B", "text": "laughter"}, {"label": "C", "text": "being entertained"}, {"label": "D", "text": "fear"}, {"label": "E", "text": "bordem"}], "stem": "Janet was watching the film because she liked what?"}}
{"answerKey": "D", "id": "3b8e1d236f5169b6c833a994d6d9c39a", "question": {"question_concept": "reception area", "choices": [{"label": "A", "text": "motel"}, {"label": "B", "text": "chair"}, {"label": "C", "text": "hospital"}, {"label": "D", "text": "people"}, {"label": "E", "text": "hotels"}], "stem": "What are you waiting alongside with when you're in a reception area?"}}
{"answerKey": "D", "id": "c5c4166f2ed3c2b3517b79e6848e9ae2", "question": {"question_concept": "booze", "choices": [{"label": "A", "text": "reach tentative agreement"}, {"label": "B", "text": "stay in bed"}, {"label": "C", "text": "stop bicycle"}, {"label": "D", "text": "examine thing"}, {"label": "E", "text": "suicide"}], "stem": "When drinking booze what can you do to stay busy?"}}
{"answerKey": "E", "id": "6dc5b2884737e66543ce65f8dc40c992", "question": {"question_concept": "fencing", "choices": [{"label": "A", "text": "injury"}, {"label": "B", "text": "small cuts"}, {"label": "C", "text": "fever"}, {"label": "D", "text": "competition"}, {"label": "E", "text": "puncture wound"}], "stem": "A fencing thrust with a sharp sword towards a person would result in what?"}}
{"answerKey": "E", "id": "8af63d58cc35061dec38e5448c325988", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "tongues"}, {"label": "B", "text": "names"}, {"label": "C", "text": "brains"}, {"label": "D", "text": "feelings"}, {"label": "E", "text": "two eyes"}], "stem": "Unlike a spider and his many sight seers, people only have what?"}}
{"answerKey": "D", "id": "768fb09deab56046e1565b6a2556ad5c", "question": {"question_concept": "glue stick", "choices": [{"label": "A", "text": "classroom"}, {"label": "B", "text": "desk drawer"}, {"label": "C", "text": "at school"}, {"label": "D", "text": "office"}, {"label": "E", "text": "kitchen drawer"}], "stem": "Where do adults use glue sticks?"}}
{"answerKey": "D", "id": "cd639cf3ff82f825ace7dd2b087562bd", "question": {"question_concept": "wood", "choices": [{"label": "A", "text": "lumberyard"}, {"label": "B", "text": "synagogue"}, {"label": "C", "text": "floor"}, {"label": "D", "text": "carpet"}, {"label": "E", "text": "hardware store"}], "stem": "What could go on top of wood?"}}
{"answerKey": "C", "id": "8d79cc5e4eea11f50fab18fdea20fd4f", "question": {"question_concept": "sitting quietly", "choices": [{"label": "A", "text": "sadness"}, {"label": "B", "text": "anxiety"}, {"label": "C", "text": "inspiration"}, {"label": "D", "text": "discomfort"}, {"label": "E", "text": "insights"}], "stem": "The artist was sitting quietly pondering, then suddenly he began to paint when what struck him?"}}
{"answerKey": "D", "id": "e5ad2184e37ae88b2bf46bf6bc0ed2f4", "question": {"question_concept": "fragile", "choices": [{"label": "A", "text": "indestructible"}, {"label": "B", "text": "durable"}, {"label": "C", "text": "undestroyable"}, {"label": "D", "text": "indestructible"}, {"label": "E", "text": "unbreakable"}], "stem": "Though the thin film seemed fragile, for it's intended purpose it was actually nearly what?"}}
{"answerKey": "D", "id": "b8b287b6277fccd4b7c9c72577177328", "question": {"question_concept": "toilet", "choices": [{"label": "A", "text": "rest area"}, {"label": "B", "text": "school"}, {"label": "C", "text": "stadium"}, {"label": "D", "text": "apartment"}, {"label": "E", "text": "hospital"}], "stem": "Where could you find a toilet that only friends can use?"}}
{"answerKey": "E", "id": "f646f3e064f06423fc25b98500796cf0", "question": {"question_concept": "clever", "choices": [{"label": "A", "text": "clumsy"}, {"label": "B", "text": "ineffectual"}, {"label": "C", "text": "dull"}, {"label": "D", "text": "clumsy"}, {"label": "E", "text": "stupid"}], "stem": "What is someone who isn't clever, bright, or competent called?"}}
{"answerKey": "D", "id": "b0f7d7978ac41c465108a92660d70e84", "question": {"question_concept": "reproduce", "choices": [{"label": "A", "text": "raise children"}, {"label": "B", "text": "have children"}, {"label": "C", "text": "photo copy"}, {"label": "D", "text": "offspring"}, {"label": "E", "text": "accidently got pregnant somehow"}], "stem": "When wildlife reproduce we often refer to what comes out as what?"}}
{"answerKey": "B", "id": "54075de8b8b89ecef2e4eb4eaee2713d", "question": {"question_concept": "weasel", "choices": [{"label": "A", "text": "forrest"}, {"label": "B", "text": "barn"}, {"label": "C", "text": "public office"}, {"label": "D", "text": "out of doors"}, {"label": "E", "text": "freezer"}], "stem": "The weasel was becoming a problem, it kept getting into the chicken eggs kept in the what?"}}
{"answerKey": "A", "id": "65435b996ce9d1685bebb74b49c1ba7f", "question": {"question_concept": "reading", "choices": [{"label": "A", "text": "new perspective"}, {"label": "B", "text": "entertained"}, {"label": "C", "text": "understanding"}, {"label": "D", "text": "hunger"}, {"label": "E", "text": "tired eyes"}], "stem": "Blue read material outside of his comfort zone because he wanted to gain what?"}}
{"answerKey": "B", "id": "9889e5389917d812c09d6e5d382d333d", "question": {"question_concept": "success", "choices": [{"label": "A", "text": "vocation"}, {"label": "B", "text": "new job"}, {"label": "C", "text": "michigan"}, {"label": "D", "text": "working hard"}, {"label": "E", "text": "manual"}], "stem": "After he got hired he hoped for success at his what?"}}
{"answerKey": "B", "id": "a651ffa44ac5febf0aede6748899b981", "question": {"question_concept": "committing perjury", "choices": [{"label": "A", "text": "indictment"}, {"label": "B", "text": "crime"}, {"label": "C", "text": "violence"}, {"label": "D", "text": "lie"}, {"label": "E", "text": "go to jail"}], "stem": "Committing perjury is a serious what?"}}
{"answerKey": "B", "id": "bdcfbe2132295d437e4c5701085f19c0", "question": {"question_concept": "postpone", "choices": [{"label": "A", "text": "eat"}, {"label": "B", "text": "hasten"}, {"label": "C", "text": "antedate"}, {"label": "D", "text": "bring forward"}, {"label": "E", "text": "advance"}], "stem": "If you are prone to postpone work what will you have to do in order to finish on time?"}}
{"answerKey": "A", "id": "8d3dc21a53523850ec80771daaa5ff20", "question": {"question_concept": "underground map", "choices": [{"label": "A", "text": "library"}, {"label": "B", "text": "subway station"}, {"label": "C", "text": "county engineer's office"}, {"label": "D", "text": "super market"}, {"label": "E", "text": "home"}], "stem": "James wanted to find an old underground map from the 50s.  Where might he look for one?"}}
{"answerKey": "E", "id": "a80ee7775e934c423012fe98e20ba28b", "question": {"question_concept": "rush", "choices": [{"label": "A", "text": "take time"}, {"label": "B", "text": "dawdle"}, {"label": "C", "text": "go slowly"}, {"label": "D", "text": "ocean"}, {"label": "E", "text": "slow down"}], "stem": "Sean was in a rush to get home, but the light turned yellow and he was forced to do what?"}}
{"answerKey": "D", "id": "48a315cfa3ce11f7a9d615bc854331d5", "question": {"question_concept": "wait turn", "choices": [{"label": "A", "text": "have patience"}, {"label": "B", "text": "get in line"}, {"label": "C", "text": "sing"}, {"label": "D", "text": "stand in line"}, {"label": "E", "text": "turn left"}], "stem": "Where would a person be doing when having to wait their turn?"}}
{"answerKey": "E", "id": "4acd496cc78d96c2431279a5fd87de7c", "question": {"question_concept": "helping", "choices": [{"label": "A", "text": "satisfaction"}, {"label": "B", "text": "heart"}, {"label": "C", "text": "feel better"}, {"label": "D", "text": "pay"}, {"label": "E", "text": "happiness"}], "stem": "She was always helping at the senior center, it brought her what?"}}
{"answerKey": "C", "id": "91e0f4ab62c9d2fd440d73a3f5308d96", "question": {"question_concept": "lock", "choices": [{"label": "A", "text": "keep cloesd"}, {"label": "B", "text": "train"}, {"label": "C", "text": "ignition switch"}, {"label": "D", "text": "drawer"}, {"label": "E", "text": "firearm"}], "stem": "The lock kept the steering wheel from moving, but the thief still took his chances and began to work on the what?"}}
{"answerKey": "C", "id": "b61e849e44db16a581f0b65e28ab95dc", "question": {"question_concept": "police officer", "choices": [{"label": "A", "text": "beat"}, {"label": "B", "text": "direct traffic"}, {"label": "C", "text": "city"}, {"label": "D", "text": "street"}, {"label": "E", "text": "president"}], "stem": "Who is a police officer likely to work for?"}}
{"answerKey": "B", "id": "ba6bd1bdef02d0ebfe5370f92365ae18", "question": {"question_concept": "cake", "choices": [{"label": "A", "text": "quandry"}, {"label": "B", "text": "refrigerator"}, {"label": "C", "text": "oven"}, {"label": "D", "text": "night stand"}, {"label": "E", "text": "bakery"}], "stem": "If you have leftover cake, where would you put it?"}}
{"answerKey": "A", "id": "dc55d473c22b04877b11d584f9548194", "question": {"question_concept": "water", "choices": [{"label": "A", "text": "whirlpool bath"}, {"label": "B", "text": "coffee cup"}, {"label": "C", "text": "cup"}, {"label": "D", "text": "soft drink"}, {"label": "E", "text": "puddle"}], "stem": "A human wants to submerge himself in water, what should he use?"}}
{"answerKey": "B", "id": "113aaea2b1a27a976547f54e531d99bb", "question": {"question_concept": "doormat", "choices": [{"label": "A", "text": "facade"}, {"label": "B", "text": "front door"}, {"label": "C", "text": "doorway"}, {"label": "D", "text": "entrance porch"}, {"label": "E", "text": "hallway"}], "stem": "Where is a doormat likely to be in front of?"}}
{"answerKey": "B", "id": "ba640b9634ad6b4ad98b17b4f152e562", "question": {"question_concept": "lizard", "choices": [{"label": "A", "text": "rock"}, {"label": "B", "text": "tropical rainforest"}, {"label": "C", "text": "jazz club"}, {"label": "D", "text": "new mexico"}, {"label": "E", "text": "rocky places"}], "stem": "Bob the lizard lives in a warm place with lots of water.  Where does he probably live?"}}
{"answerKey": "B", "id": "750ebdf36a0b3b407be0fe2163e3700b", "question": {"question_concept": "money", "choices": [{"label": "A", "text": "control people"}, {"label": "B", "text": "pay bills"}, {"label": "C", "text": "hurt people"}, {"label": "D", "text": "buy food"}, {"label": "E", "text": "get things"}], "stem": "August needed  money because he was afraid that he'd be kicked out of his house.  What did he need money to do?"}}
{"answerKey": "E", "id": "8f01273422a370a8dbda6bf473a395a0", "question": {"question_concept": "information", "choices": [{"label": "A", "text": "chickens"}, {"label": "B", "text": "google"}, {"label": "C", "text": "newspaper"}, {"label": "D", "text": "online"}, {"label": "E", "text": "manual"}], "stem": "He needed more information to fix it, so he consulted the what?"}}
{"answerKey": "E", "id": "e6586bba9fe96d38792e6e6d4f2703dc", "question": {"question_concept": "picture", "choices": [{"label": "A", "text": "art show"}, {"label": "B", "text": "wall"}, {"label": "C", "text": "newspaper"}, {"label": "D", "text": "car"}, {"label": "E", "text": "table"}], "stem": "Where can you put a picture frame when it's not hung vertically?"}}
{"answerKey": "D", "id": "6e433471d0e2590b8c73ceef275022b1", "question": {"question_concept": "buying beer", "choices": [{"label": "A", "text": "lose money"}, {"label": "B", "text": "fun"}, {"label": "C", "text": "have no money"}, {"label": "D", "text": "broken law"}, {"label": "E", "text": "relaxation"}], "stem": "James knew that he shouldn't have been buying beer for minors.  He didn't even get paid for it.  Why was this bad?"}}
{"answerKey": "E", "id": "1bc986f8aea88d6927d8a45367855a94", "question": {"question_concept": "applying for job", "choices": [{"label": "A", "text": "anxiety and fear"}, {"label": "B", "text": "increased workload"}, {"label": "C", "text": "praise"}, {"label": "D", "text": "less sleep"}, {"label": "E", "text": "being employed"}], "stem": "What is the result of applying for  job?"}}
{"answerKey": "A", "id": "8d1563697d751a364d688d6701ebdb39", "question": {"question_concept": "shop", "choices": [{"label": "A", "text": "get money"}, {"label": "B", "text": "have money"}, {"label": "C", "text": "bring cash"}, {"label": "D", "text": "go to market"}, {"label": "E", "text": "bring cash"}], "stem": "What must someone do before they shop?"}}
{"answerKey": "E", "id": "91f512273a2da7ae796919069b20d6cf", "question": {"question_concept": "first violin", "choices": [{"label": "A", "text": "music store"}, {"label": "B", "text": "obesity"}, {"label": "C", "text": "symphony orchestra"}, {"label": "D", "text": "ochestra"}, {"label": "E", "text": "violin case"}], "stem": "Because John was first violin, he had to bring something important to work ever day. What did he need to bring to work?"}}
{"answerKey": "E", "id": "49cda7eedbf63b3f38e59ba72f1ee1f9", "question": {"question_concept": "telephone book", "choices": [{"label": "A", "text": "at hotel"}, {"label": "B", "text": "kitchen"}, {"label": "C", "text": "library"}, {"label": "D", "text": "telephone booth"}, {"label": "E", "text": "house"}], "stem": "What is a place that usually does not have an elevator and that sometimes has a telephone book?"}}
{"answerKey": "C", "id": "a588407ecaecf0f30c2241c30b470fe2", "question": {"question_concept": "crab", "choices": [{"label": "A", "text": "fish market"}, {"label": "B", "text": "pet shop"}, {"label": "C", "text": "fishmongers"}, {"label": "D", "text": "intertidal zone"}, {"label": "E", "text": "obesity"}], "stem": "Who is likely to be excited about a crab?"}}
{"answerKey": "C", "id": "011096bcfff30fd38046cf9db3a411c5", "question": {"question_concept": "human", "choices": [{"label": "A", "text": "pants shop"}, {"label": "B", "text": "on planet earth"}, {"label": "C", "text": "dress shop"}, {"label": "D", "text": "school"}, {"label": "E", "text": "train wreck"}], "stem": "Where can a human find clothes that aren't pants?"}}
{"answerKey": "B", "id": "435a728f45d32faa4b3c4553c966fd6b", "question": {"question_concept": "getting drunk", "choices": [{"label": "A", "text": "a seizure"}, {"label": "B", "text": "slurred speech"}, {"label": "C", "text": "death"}, {"label": "D", "text": "forgetfulness"}, {"label": "E", "text": "pass out"}], "stem": "If I was getting drunk, and people couldn't understand me, what might I be having?"}}
{"answerKey": "D", "id": "e953dee48c70159ad879143a319ec607", "question": {"question_concept": "beginning work", "choices": [{"label": "A", "text": "time"}, {"label": "B", "text": "accomplishing"}, {"label": "C", "text": "working"}, {"label": "D", "text": "momentum"}, {"label": "E", "text": "tiredness"}], "stem": "When a person is beginning work, what are they building?"}}
{"answerKey": "C", "id": "9c784727afd7176b54764055df7a7927", "question": {"question_concept": "child", "choices": [{"label": "A", "text": "fall down"}, {"label": "B", "text": "breathe"}, {"label": "C", "text": "play tag"}, {"label": "D", "text": "be dismembered by a chainsaw"}, {"label": "E", "text": "become adult"}], "stem": "A child wants to play, what would they likely want?"}}
{"answerKey": "D", "id": "b47d912136e3304cb5e5890b6b879551", "question": {"question_concept": "talking to", "choices": [{"label": "A", "text": "social life"}, {"label": "B", "text": "friendship"}, {"label": "C", "text": "eye contact"}, {"label": "D", "text": "get tired of"}, {"label": "E", "text": "learn lessons from"}], "stem": "Talking to the same person about the same thing over and over again is something someone can what?"}}
{"answerKey": "E", "id": "49b4c9e1bd7946a819e173ce8fa4c7c9", "question": {"question_concept": "noise", "choices": [{"label": "A", "text": "movie theatre"}, {"label": "B", "text": "bowling alley"}, {"label": "C", "text": "factory"}, {"label": "D", "text": "store"}, {"label": "E", "text": "classroom"}], "stem": "The teacher doesn't tolerate noise during a test in their what?"}}
{"answerKey": "B", "id": "950af0b765c298960ce3dada66df8db1", "question": {"question_concept": "freeway", "choices": [{"label": "A", "text": "california"}, {"label": "B", "text": "countryside"}, {"label": "C", "text": "big town"}, {"label": "D", "text": "florida"}, {"label": "E", "text": "america"}], "stem": "The freeway had no traffic and few buildings, where is it?"}}
{"answerKey": "D", "id": "63cf1adb5fe302b9867ead8bc8103d0b", "question": {"question_concept": "fun", "choices": [{"label": "A", "text": "watching television"}, {"label": "B", "text": "good"}, {"label": "C", "text": "cinema"}, {"label": "D", "text": "friend's house"}, {"label": "E", "text": "fairgrounds"}], "stem": "Where would you go if you wanted to have fun with a few people?"}}
{"answerKey": "B", "id": "ede4d302fc2ffe07703158f83c1493f2", "question": {"question_concept": "hot", "choices": [{"label": "A", "text": "bland"}, {"label": "B", "text": "lifeless"}, {"label": "C", "text": "sandy"}, {"label": "D", "text": "neutral"}, {"label": "E", "text": "freezing"}], "stem": "If there is a place that is hot and arid, what could it be?"}}
{"answerKey": "A", "id": "74ad13a03634e79c85382f1b90969b74", "question": {"question_concept": "curiosity", "choices": [{"label": "A", "text": "hear news"}, {"label": "B", "text": "read book"}, {"label": "C", "text": "see favorite show"}, {"label": "D", "text": "comedy show"}, {"label": "E", "text": "go somewhere"}], "stem": "What is likely to satisfy someone's curiosity?"}}
{"answerKey": "B", "id": "49e466b1782aa4837dae53ff891fcdee", "question": {"question_concept": "bar", "choices": [{"label": "A", "text": "in my pocket"}, {"label": "B", "text": "michigan"}, {"label": "C", "text": "new york city"}, {"label": "D", "text": "restaurant"}, {"label": "E", "text": "public house"}], "stem": "If you are in a bar in a glove shaped state where are you?"}}
{"answerKey": "E", "id": "a8a8ae7792901c7179ff5538c701af1f", "question": {"question_concept": "computer user", "choices": [{"label": "A", "text": "hell"}, {"label": "B", "text": "school"}, {"label": "C", "text": "indoors"}, {"label": "D", "text": "internet cafe"}, {"label": "E", "text": "house"}], "stem": "Where would a computer user be using their own computer?"}}
{"answerKey": "C", "id": "2ffa3808ce26181926990b454e429c85", "question": {"question_concept": "crab", "choices": [{"label": "A", "text": "maritime"}, {"label": "B", "text": "bodies of water"}, {"label": "C", "text": "saltwater"}, {"label": "D", "text": "galapagos"}, {"label": "E", "text": "fish market"}], "stem": "Crabs live in what sort of environment?"}}
{"answerKey": "D", "id": "4319eaa36d256a92b72445c0392f9c94", "question": {"question_concept": "snake", "choices": [{"label": "A", "text": "tree"}, {"label": "B", "text": "in a jar"}, {"label": "C", "text": "pet shops"}, {"label": "D", "text": "feild"}, {"label": "E", "text": "tropical forest"}], "stem": "Where can you find a snake in tall grass?"}}
{"answerKey": "A", "id": "ec79ef747bb89281923edb89ba26786d", "question": {"question_concept": "bench", "choices": [{"label": "A", "text": "state park"}, {"label": "B", "text": "bus stop"}, {"label": "C", "text": "bus depot"}, {"label": "D", "text": "statue"}, {"label": "E", "text": "train station"}], "stem": "What is a place that has a bench nestled in trees?"}}
{"answerKey": "A", "id": "2d33cde5e3987adc8fa2bca0af4dd3dd", "question": {"question_concept": "being hungry", "choices": [{"label": "A", "text": "eat in restaurant"}, {"label": "B", "text": "make bread"}, {"label": "C", "text": "have lunch"}, {"label": "D", "text": "cook dinner"}, {"label": "E", "text": "friends house"}], "stem": "Where is a human likely to go as a result of being hungry?"}}
{"answerKey": "D", "id": "cc46d936bf69d69a3863b0cb85d75c17", "question": {"question_concept": "regret", "choices": [{"label": "A", "text": "fun"}, {"label": "B", "text": "joy"}, {"label": "C", "text": "satisfaction"}, {"label": "D", "text": "confident"}, {"label": "E", "text": "pride"}], "stem": "He was beginning to regret taking the fight when he saw how what his opponent was?"}}
{"answerKey": "A", "id": "46bc1a50eeead10509a43a048e01194e", "question": {"question_concept": "shower curtain", "choices": [{"label": "A", "text": "bathtub"}, {"label": "B", "text": "washing area"}, {"label": "C", "text": "hotel"}, {"label": "D", "text": "shower stall"}, {"label": "E", "text": "department store"}], "stem": "Where would you find a single shower curtain being used?"}}
{"answerKey": "C", "id": "4336a8c55b7cb17275d1c60206cd2f18", "question": {"question_concept": "fire extinguisher", "choices": [{"label": "A", "text": "school bus"}, {"label": "B", "text": "boat"}, {"label": "C", "text": "house"}, {"label": "D", "text": "hospital"}, {"label": "E", "text": "school"}], "stem": "Where is a good idea but not required to have a fire extinguisher?"}}
{"answerKey": "D", "id": "a287575d3ba4b9f958536fc14a1f5b5a", "question": {"question_concept": "castle", "choices": [{"label": "A", "text": "fairy tale"}, {"label": "B", "text": "edinburgh"}, {"label": "C", "text": "germany"}, {"label": "D", "text": "europe"}, {"label": "E", "text": "antarctica"}], "stem": "What continent has the most castles?"}}
{"answerKey": "E", "id": "f481dc35b0a97a20dc5cdfe1a59746e2", "question": {"question_concept": "read book", "choices": [{"label": "A", "text": "have time"}, {"label": "B", "text": "boring"}, {"label": "C", "text": "learn new"}, {"label": "D", "text": "enjoyable"}, {"label": "E", "text": "bored"}], "stem": "If you have to read a book that is very dry and long you may become what?"}}
{"answerKey": "E", "id": "c1c7a9efa379b8a7024a71cf364a144c", "question": {"question_concept": "clipboard", "choices": [{"label": "A", "text": "desk"}, {"label": "B", "text": "windows 95"}, {"label": "C", "text": "office supply store"}, {"label": "D", "text": "see work"}, {"label": "E", "text": "school"}], "stem": "Sally used a clipboard to hold her papers while she read off names at the beginning of the day.  Where might she work?"}}
{"answerKey": "D", "id": "821b32d39f57396979069b948030afe9", "question": {"question_concept": "kids", "choices": [{"label": "A", "text": "learn things"}, {"label": "B", "text": "play games"}, {"label": "C", "text": "disneyland"}, {"label": "D", "text": "play with toys"}, {"label": "E", "text": "talking"}], "stem": "The kids didn't clean up after they had done what?"}}
{"answerKey": "A", "id": "c68b4082a6872cf8198502651d0f3352", "question": {"question_concept": "pawn", "choices": [{"label": "A", "text": "chess game"}, {"label": "B", "text": "scheme"}, {"label": "C", "text": "chess set"}, {"label": "D", "text": "checkers"}, {"label": "E", "text": "north carolina"}], "stem": "Despite the name a pawn can be quite versatile, all the parts are important in a what?"}}
{"answerKey": "C", "id": "dd11fea36d89aa09f9a6069545ba4c9c", "question": {"question_concept": "basketball", "choices": [{"label": "A", "text": "punctured"}, {"label": "B", "text": "popular in america"}, {"label": "C", "text": "full of air"}, {"label": "D", "text": "gone"}, {"label": "E", "text": "round"}], "stem": "What would not be true about a basketball if it had a hole in it but it did not lose its general shape?"}}
{"answerKey": "B", "id": "7792b2c6518ecf9775efba6d41253312", "question": {"question_concept": "awaking", "choices": [{"label": "A", "text": "irritability"}, {"label": "B", "text": "depression"}, {"label": "C", "text": "getting out of bed"}, {"label": "D", "text": "happiness"}, {"label": "E", "text": "discomfort"}], "stem": "If you are awaking multiple times throughout the night because a lot is on your mind, what is a likely cause?"}}
{"answerKey": "C", "id": "1feb4c2a0e8ed638259f5d27b16eae9a", "question": {"question_concept": "bird", "choices": [{"label": "A", "text": "cage"}, {"label": "B", "text": "sky"}, {"label": "C", "text": "countryside"}, {"label": "D", "text": "desert"}, {"label": "E", "text": "windowsill"}], "stem": "Where does a wild bird usually live?"}}
{"answerKey": "E", "id": "2de08c7a518b7c226e19bdc8fc10ef1d", "question": {"question_concept": "mice", "choices": [{"label": "A", "text": "bell cat"}, {"label": "B", "text": "bush"}, {"label": "C", "text": "attic"}, {"label": "D", "text": "countryside"}, {"label": "E", "text": "laboratory"}], "stem": "Where would you expect to find white mice?"}}
{"answerKey": "B", "id": "ea8664e77205224154f8519f922220e1", "question": {"question_concept": "fate", "choices": [{"label": "A", "text": "free will"}, {"label": "B", "text": "choice"}, {"label": "C", "text": "will"}, {"label": "D", "text": "alcohol"}, {"label": "E", "text": "freedom"}], "stem": "John felt that his actions were fate.   Harry said that he could have always made a different what?"}}
{"answerKey": "C", "id": "a64d45cecde84fdcf5f0a79805a0c6fe", "question": {"question_concept": "committing murder", "choices": [{"label": "A", "text": "go to jail"}, {"label": "B", "text": "cry"}, {"label": "C", "text": "find god"}, {"label": "D", "text": "guilty conscience"}, {"label": "E", "text": "problems"}], "stem": "What could committing murder prevent someone from doing?"}}
{"answerKey": "B", "id": "60e92cd2f35c345872d1a898e1718d55", "question": {"question_concept": "feet", "choices": [{"label": "A", "text": "michigan"}, {"label": "B", "text": "walk"}, {"label": "C", "text": "stay still"}, {"label": "D", "text": "stink"}, {"label": "E", "text": "hands"}], "stem": "George didn't have a car, but he still had his two feet.   His socks were smelly and his soles were blistered, but that didn't matter.  He could still do what?"}}
{"answerKey": "D", "id": "08f3c187908646997b9080c7e9ea7da4", "question": {"question_concept": "steel cable", "choices": [{"label": "A", "text": "abaft"}, {"label": "B", "text": "ship"}, {"label": "C", "text": "winch"}, {"label": "D", "text": "construction site"}, {"label": "E", "text": "building"}], "stem": "A crane uses many a steel cable when working a what?"}}
{"answerKey": "E", "id": "9aff72f0c480c2b4edde45bd2e7e4870", "question": {"question_concept": "farmers", "choices": [{"label": "A", "text": "raise cattle"}, {"label": "B", "text": "grow corn"}, {"label": "C", "text": "farm land"}, {"label": "D", "text": "drive tractors"}, {"label": "E", "text": "supply food"}], "stem": "What is the main purpose of farmers?"}}
{"answerKey": "A", "id": "fd243c96edec5b1b8520d5bfeddc6622", "question": {"question_concept": "penny", "choices": [{"label": "A", "text": "piggy bank"}, {"label": "B", "text": "wallet"}, {"label": "C", "text": "toy"}, {"label": "D", "text": "ground"}, {"label": "E", "text": "pocket"}], "stem": "Where can I put this penny to save for later?"}}
{"answerKey": "E", "id": "f5ec4fdfd0e37e733bfc1606b986f1e2", "question": {"question_concept": "crab", "choices": [{"label": "A", "text": "wharf"}, {"label": "B", "text": "red lobster"}, {"label": "C", "text": "tidepools"}, {"label": "D", "text": "boss's office"}, {"label": "E", "text": "stew pot"}], "stem": "Where would you put uncooked crab meat?"}}
{"answerKey": "A", "id": "e3c6d147f8a727d314046e70e9579ba0", "question": {"question_concept": "illness", "choices": [{"label": "A", "text": "sick person"}, {"label": "B", "text": "hospital"}, {"label": "C", "text": "elderly person"}, {"label": "D", "text": "graveyard"}, {"label": "E", "text": "doctor's office"}], "stem": "The man had a fear of illness, so he never visited friends who were a what?"}}
{"answerKey": "D", "id": "8ce13c6e08bf38d4cd4af756b661e47c", "question": {"question_concept": "pans", "choices": [{"label": "A", "text": "cooking"}, {"label": "B", "text": "cook food"}, {"label": "C", "text": "kitchen"}, {"label": "D", "text": "backpack"}, {"label": "E", "text": "drawer"}], "stem": "Where would you put pans if you want to bring them with you?"}}
{"answerKey": "B", "id": "0f4159e80f8dbf682819215bbf0f5b5a_1", "question": {"question_concept": "remembering", "choices": [{"label": "A", "text": "knowledge"}, {"label": "B", "text": "knowing"}, {"label": "C", "text": "forgetful"}, {"label": "D", "text": "pleasure"}, {"label": "E", "text": "depression"}], "stem": "If you're remembering something, it's because of your what of it to begin with?"}}
{"answerKey": "E", "id": "1a8b3c2a46efabcbd506f9cf70886ed0", "question": {"question_concept": "monkey", "choices": [{"label": "A", "text": "amazon basin"}, {"label": "B", "text": "friend's house"}, {"label": "C", "text": "lift number 3"}, {"label": "D", "text": "research laboratory"}, {"label": "E", "text": "african continent"}], "stem": "Which large land mass is home to the most monkeys?"}}
{"answerKey": "C", "id": "db0cfd52ca6b2bbfcf26d1a898fd929b", "question": {"question_concept": "going to bed", "choices": [{"label": "A", "text": "rest"}, {"label": "B", "text": "insomnia"}, {"label": "C", "text": "making love"}, {"label": "D", "text": "sleeping in"}, {"label": "E", "text": "texting"}], "stem": "Friday was James's 5th Anniversary.  They planned on going to bed early so that they could spend a long time doing what?"}}
{"answerKey": "C", "id": "400fb2e196e71abb70e5b3f9aab4b9ee", "question": {"question_concept": "get drunk", "choices": [{"label": "A", "text": "health"}, {"label": "B", "text": "fall down"}, {"label": "C", "text": "stagger"}, {"label": "D", "text": "get arrested"}, {"label": "E", "text": "vomit"}], "stem": "The teens were trying to hide that they get drink, but when they walked in the door their what gave it away?"}}
{"answerKey": "C", "id": "3fb36127a61903029a363911a1d2b1e9_1", "question": {"question_concept": "landing", "choices": [{"label": "A", "text": "ocean"}, {"label": "B", "text": "apartment building"}, {"label": "C", "text": "stairwell"}, {"label": "D", "text": "airport"}, {"label": "E", "text": "room"}], "stem": "You'll find a landing at the top of what?"}}
{"answerKey": "D", "id": "8494b0b95533dcedbd76ae2916c481d4", "question": {"question_concept": "anybody", "choices": [{"label": "A", "text": "forget"}, {"label": "B", "text": "oil squeaky hinge"}, {"label": "C", "text": "question authority"}, {"label": "D", "text": "wash dishes"}, {"label": "E", "text": "oik squeaky hinge"}], "stem": "Anybody could be hired in the kitchen, what was needed of them?"}}
{"answerKey": "E", "id": "1531f1523f5fd24bbdb42c311dbf90e8", "question": {"question_concept": "wind instrument", "choices": [{"label": "A", "text": "music store"}, {"label": "B", "text": "create music"}, {"label": "C", "text": "zoo"}, {"label": "D", "text": "music room"}, {"label": "E", "text": "symphony"}], "stem": "Where can you find a number of wind instruments together in public?"}}
{"answerKey": "E", "id": "716ce4404a84b42dd64e561390c4b53b", "question": {"question_concept": "subway stop", "choices": [{"label": "A", "text": "urban area"}, {"label": "B", "text": "metropolis"}, {"label": "C", "text": "chicago"}, {"label": "D", "text": "new york city"}, {"label": "E", "text": "toronto"}], "stem": "A mountie got off at a subway stop.  What city might he be in?"}}
{"answerKey": "E", "id": "5169f7ae0781b15161551de3a189ebef", "question": {"question_concept": "illustrate point", "choices": [{"label": "A", "text": "did not understand"}, {"label": "B", "text": "accepting"}, {"label": "C", "text": "make clear"}, {"label": "D", "text": "understood"}, {"label": "E", "text": "understanding"}], "stem": "What do you want someone to do when you illustrate point?"}}
{"answerKey": "B", "id": "ef22ef7aeec70aaa688720f805c1cf38", "question": {"question_concept": "having fun", "choices": [{"label": "A", "text": "happiness"}, {"label": "B", "text": "stress relief"}, {"label": "C", "text": "pleasure"}, {"label": "D", "text": "ocean"}, {"label": "E", "text": "may laugh"}], "stem": "Billy set aside a block of time for having fun after work. Why might he do this?"}}
{"answerKey": "D", "id": "514310637fb43a252bfadc8cbf79b277", "question": {"question_concept": "lazy", "choices": [{"label": "A", "text": "restless"}, {"label": "B", "text": "active"}, {"label": "C", "text": "lazybutt"}, {"label": "D", "text": "productive"}, {"label": "E", "text": "hard work"}], "stem": "The man in the white suit was very lazy.  He did nothing useful.  Meanwhile, the ban in the blue had put in effort and was very what?"}}
{"answerKey": "B", "id": "9370b2b0897b796dec4a40f107854c8d", "question": {"question_concept": "greed", "choices": [{"label": "A", "text": "keep things"}, {"label": "B", "text": "make friends"}, {"label": "C", "text": "play poker"}, {"label": "D", "text": "conquer opponent"}, {"label": "E", "text": "lie"}], "stem": "What would you be unable to do if you have too much greed?"}}
{"answerKey": "E", "id": "49902e768c45aa41a0f9f95be81114e5", "question": {"question_concept": "hotel", "choices": [{"label": "A", "text": "bed away from home"}, {"label": "B", "text": "wwii bunker"}, {"label": "C", "text": "resort"}, {"label": "D", "text": "las vegas"}, {"label": "E", "text": "city"}], "stem": "It was a long trip from the farm, so he stayed in a hotel when he arrived at the what?"}}
{"answerKey": "B", "id": "e1f90cd664a6b150291e6d8444d85c54", "question": {"question_concept": "servant", "choices": [{"label": "A", "text": "freedom"}, {"label": "B", "text": "rich person"}, {"label": "C", "text": "hired help"}, {"label": "D", "text": "in charge"}, {"label": "E", "text": "busy"}], "stem": "I did not need a servant.  I was not a what?"}}
{"answerKey": "D", "id": "320ec9b68fdefe13d59cc8b628083790", "question": {"question_concept": "canal", "choices": [{"label": "A", "text": "michigan"}, {"label": "B", "text": "amsterdam"}, {"label": "C", "text": "venice"}, {"label": "D", "text": "bridge"}, {"label": "E", "text": "barges to travel on"}], "stem": "How would you get from one side of a canal to another?"}}
{"answerKey": "D", "id": "964185aed0e381853332bca1a4d91f46", "question": {"question_concept": "learning about world", "choices": [{"label": "A", "text": "newness"}, {"label": "B", "text": "loss of innocence"}, {"label": "C", "text": "enlightenment"}, {"label": "D", "text": "open mind"}, {"label": "E", "text": "smartness"}], "stem": "When learning about the world and different cultures, what is important if you are committed to eliminating preconceived notions"}}
{"answerKey": "E", "id": "db8e010754c532d78635e5b7cf81a147", "question": {"question_concept": "computers", "choices": [{"label": "A", "text": "share files"}, {"label": "B", "text": "do arithmetic"}, {"label": "C", "text": "turn on"}, {"label": "D", "text": "cost money"}, {"label": "E", "text": "multitask"}], "stem": "An underrated thing about computers is how they manage workflow, at one time it was a big deal when they could first do what?"}}
{"answerKey": "D", "id": "998381f854f51da2a6ccde45909e5168", "question": {"question_concept": "obstructing justice", "choices": [{"label": "A", "text": "committing perjury"}, {"label": "B", "text": "prosecution"}, {"label": "C", "text": "attack"}, {"label": "D", "text": "getting hurt"}, {"label": "E", "text": "riot"}], "stem": "Obstructing justice is sometimes an excuse used for police brutality which causes what in people?"}}
{"answerKey": "D", "id": "bc38ad28e99cff7a65771233f734a007", "question": {"question_concept": "washing clothes", "choices": [{"label": "A", "text": "damaged"}, {"label": "B", "text": "wet clothes"}, {"label": "C", "text": "wear out"}, {"label": "D", "text": "torn"}, {"label": "E", "text": "have fun"}], "stem": "While washing clothes they became what when caught on the sharp object?"}}
{"answerKey": "E", "id": "e3949997bf9d02048cfa5d8dd0f287aa", "question": {"question_concept": "seafood restaurant", "choices": [{"label": "A", "text": "maine"}, {"label": "B", "text": "shoe shop"}, {"label": "C", "text": "city"}, {"label": "D", "text": "boston"}, {"label": "E", "text": "coastal cities"}], "stem": "Seafood restaurants are used to draw tourists where?"}}
{"answerKey": "A", "id": "a7d51b753c2113d8b2dbd0ebb5375855", "question": {"question_concept": "niece", "choices": [{"label": "A", "text": "family tree"}, {"label": "B", "text": "family reunion"}, {"label": "C", "text": "babysitting"}, {"label": "D", "text": "brother's house"}, {"label": "E", "text": "heirlooms"}], "stem": "James's nice asked him about her grandfather. She was interested in learning about what?"}}
{"answerKey": "A", "id": "********************************", "question": {"question_concept": "stars", "choices": [{"label": "A", "text": "universe"}, {"label": "B", "text": "orbit"}, {"label": "C", "text": "night sky"}, {"label": "D", "text": "outer space"}, {"label": "E", "text": "his wallet"}], "stem": "James looked up and saw the start twinkling in the black yonder.  He marveled the sheer number of them and the size of what?"}}
{"answerKey": "E", "id": "5ac83e9e6fa9851ad3cccb0d57c1d88f", "question": {"question_concept": "playing tennis", "choices": [{"label": "A", "text": "becoming tired"}, {"label": "B", "text": "tennis elbow"}, {"label": "C", "text": "exercise"}, {"label": "D", "text": "hunger"}, {"label": "E", "text": "victory"}], "stem": "What would encourage someone to continue playing tennis?"}}
{"answerKey": "C", "id": "2c0030cc14a27be2401dcfdaa501f0fc", "question": {"question_concept": "relaxing", "choices": [{"label": "A", "text": "deep breathing"}, {"label": "B", "text": "worried"}, {"label": "C", "text": "fall asleep"}, {"label": "D", "text": "invigorating"}, {"label": "E", "text": "feeling good"}], "stem": "James found the sound relaxing.   It was so relaxing he almost did what despite his efforts?"}}
{"answerKey": "C", "id": "feb83263e6be392351db0794004efc3f", "question": {"question_concept": "dime store", "choices": [{"label": "A", "text": "commercial building"}, {"label": "B", "text": "old movie"}, {"label": "C", "text": "small neighborhood"}, {"label": "D", "text": "past"}, {"label": "E", "text": "mall"}], "stem": "What regions of a town would you have found a dime store?"}}
{"answerKey": "E", "id": "80697d599280d994d8a584c95824ef1f", "question": {"question_concept": "chess set", "choices": [{"label": "A", "text": "toy store"}, {"label": "B", "text": "michigan"}, {"label": "C", "text": "living room"}, {"label": "D", "text": "attic"}, {"label": "E", "text": "cupboard"}], "stem": "Where might an unused chess set be stored?"}}
{"answerKey": "D", "id": "3c1800e7dd96d37fdd3c51b9fe502342", "question": {"question_concept": "settle", "choices": [{"label": "A", "text": "wander"}, {"label": "B", "text": "migrate"}, {"label": "C", "text": "scare"}, {"label": "D", "text": "disturb"}, {"label": "E", "text": "agitate"}], "stem": "james told his son to settle down and be careful.  There were many frogs mating in the area, and James didn't want his son to do what to them?"}}
{"answerKey": "B", "id": "4da33e6f4b789776acb1bc10195baa83", "question": {"question_concept": "air conditioning", "choices": [{"label": "A", "text": "car"}, {"label": "B", "text": "house"}, {"label": "C", "text": "offices"}, {"label": "D", "text": "park"}, {"label": "E", "text": "movie theatre"}], "stem": "A man wants air conditioning while we watches the game on Saturday, where will it likely be installed?"}}
{"answerKey": "B", "id": "ae038e9af9d5a511ada7456b5e73b15e", "question": {"question_concept": "balalaika", "choices": [{"label": "A", "text": "movie dr"}, {"label": "B", "text": "orchestra"}, {"label": "C", "text": "music store"}, {"label": "D", "text": "cat"}, {"label": "E", "text": "symphony"}], "stem": "What could be playing a balailaika?"}}
{"answerKey": "E", "id": "a400b9fd1e319f901471c4b42d401c52", "question": {"question_concept": "sailor", "choices": [{"label": "A", "text": "coming home"}, {"label": "B", "text": "row boat"}, {"label": "C", "text": "board ship"}, {"label": "D", "text": "inflatable raft"}, {"label": "E", "text": "sail boat"}], "stem": "Sailors drive many different types of boats, what type of boat involves their namesake."}}
{"answerKey": "C", "id": "9dffd2021771e0ecddb19031acf3701b", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "bus stop"}, {"label": "B", "text": "tunnel"}, {"label": "C", "text": "synagogue"}, {"label": "D", "text": "fairy tale"}, {"label": "E", "text": "street corner"}], "stem": "Where could a person avoid the rain?"}}
{"answerKey": "E", "id": "3730c646fdf54472ab873aac9ff7852e", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "have choice"}, {"label": "B", "text": "mentally challenged"}, {"label": "C", "text": "own house"}, {"label": "D", "text": "obesity"}, {"label": "E", "text": "lots of space"}], "stem": "Why would a person like to have a large house?"}}
{"answerKey": "C", "id": "175e7dcdded13d5adafaebf2264c3abd", "question": {"question_concept": "book", "choices": [{"label": "A", "text": "bookstore"}, {"label": "B", "text": "classroom"}, {"label": "C", "text": "discount store"}, {"label": "D", "text": "school room"}, {"label": "E", "text": "bedside table"}], "stem": "Where will a cheap book be found?"}}
{"answerKey": "B", "id": "11d7db1d8e1cff2f40d4184f15cf7ae7", "question": {"question_concept": "idiots", "choices": [{"label": "A", "text": "internet cafe"}, {"label": "B", "text": "sporting event"}, {"label": "C", "text": "pressing wrong buttons"}, {"label": "D", "text": "obesity"}, {"label": "E", "text": "hockey game"}], "stem": "John and James are idiots. They bought two tickets to the Falcons vs the Jets even though neither wanted to see the what?"}}
{"answerKey": "C", "id": "08db69edf0ec5848c1a53dca8fc1601a", "question": {"question_concept": "bigger", "choices": [{"label": "A", "text": "accidental"}, {"label": "B", "text": "detestable"}, {"label": "C", "text": "effusive"}, {"label": "D", "text": "enabled"}, {"label": "E", "text": "apathetic"}], "stem": "James noticed that his penis was bigger. .  How might he act toward his plastic surgeon?"}}
{"answerKey": "D", "id": "855ab6ba47f6311104c4d29e24ef0234", "question": {"question_concept": "professors", "choices": [{"label": "A", "text": "methods of facts"}, {"label": "B", "text": "teach courses"}, {"label": "C", "text": "wear wrinkled tweed jackets"}, {"label": "D", "text": "school students"}, {"label": "E", "text": "state facts"}], "stem": "Who do professors work with?"}}
{"answerKey": "D", "id": "7ec11eeca4221795c117943ca2639e86", "question": {"question_concept": "anemone", "choices": [{"label": "A", "text": "intertidal zone"}, {"label": "B", "text": "coral sea"}, {"label": "C", "text": "under water"}, {"label": "D", "text": "flower bed"}, {"label": "E", "text": "florida keys"}], "stem": "Colorful anemone look somewhat like what object you find on window sills?"}}
{"answerKey": "A", "id": "e9389b08fdd17f14b148d498d6ff4dfe", "question": {"question_concept": "aliens", "choices": [{"label": "A", "text": "outer space"}, {"label": "B", "text": "weekly world news"}, {"label": "C", "text": "roswell"}, {"label": "D", "text": "universe"}, {"label": "E", "text": "mars"}], "stem": "From where do aliens arrive?"}}
{"answerKey": "B", "id": "afa2899cc21e204fa64e63e7839e8c1e", "question": {"question_concept": "drink", "choices": [{"label": "A", "text": "had a party"}, {"label": "B", "text": "were thirsty"}, {"label": "C", "text": "refreshment"}, {"label": "D", "text": "getting drunk"}, {"label": "E", "text": "celebrating"}], "stem": "The hikers stopped to have a drink, simply put they what?"}}
{"answerKey": "D", "id": "f898eb5b789d2dc6804edba269f051f0", "question": {"question_concept": "begin work", "choices": [{"label": "A", "text": "apply for job"}, {"label": "B", "text": "sleep"}, {"label": "C", "text": "concentrate"}, {"label": "D", "text": "shower"}, {"label": "E", "text": "just do"}], "stem": "When you get up in the morning before you begin work you should do what?"}}
{"answerKey": "B", "id": "7ed7379fc51fd35a47be022f6c56ce51", "question": {"question_concept": "kitten", "choices": [{"label": "A", "text": "living room"}, {"label": "B", "text": "floor"}, {"label": "C", "text": "warm place"}, {"label": "D", "text": "carpet"}, {"label": "E", "text": "farmhouse"}], "stem": "The kitten had nothing to dig it's claws into, so when it tried to stop it slid across what?"}}
{"answerKey": "E", "id": "15798a23ee6952fedd6d202064069126", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "complete collection"}, {"label": "B", "text": "own house"}, {"label": "C", "text": "procrastinate"}, {"label": "D", "text": "explode"}, {"label": "E", "text": "have to hold"}], "stem": "If a person is trying to keep something in their hand what should they do?"}}
{"answerKey": "D", "id": "273d0134e8ce53d4ebcf41ca7fde02af", "question": {"question_concept": "home", "choices": [{"label": "A", "text": "field"}, {"label": "B", "text": "neighborhood"}, {"label": "C", "text": "star can"}, {"label": "D", "text": "city or town"}, {"label": "E", "text": "apartment building"}], "stem": "Where could you find hundreds of thousands of home?"}}
{"answerKey": "D", "id": "2f0931adc3d0d422d9ab6264395e89d8", "question": {"question_concept": "playing baseball", "choices": [{"label": "A", "text": "sore muscles"}, {"label": "B", "text": "errors"}, {"label": "C", "text": "happiness"}, {"label": "D", "text": "injury"}, {"label": "E", "text": "fun"}], "stem": "Playing baseball is a lot like any other sport, there is always a risk of what?"}}
{"answerKey": "C", "id": "d00d3ba777cb3889a45799d72fca0a50", "question": {"question_concept": "movie", "choices": [{"label": "A", "text": "drive in movie"}, {"label": "B", "text": "drive in movie"}, {"label": "C", "text": "television"}, {"label": "D", "text": "video store"}, {"label": "E", "text": "show"}], "stem": "If I want to watch a movie without leaving my home what might I use?"}}
{"answerKey": "A", "id": "b1f36d1c8ab7e5a28783cb38e8709c27", "question": {"question_concept": "take stand", "choices": [{"label": "A", "text": "testify"}, {"label": "B", "text": "runaway"}, {"label": "C", "text": "witness"}, {"label": "D", "text": "tell truth"}, {"label": "E", "text": "go home"}], "stem": "The victim was to take stand today, they were going to do what?"}}
{"answerKey": "D", "id": "a5e76dd088aab4f89e2fe93f6de6e46d", "question": {"question_concept": "grooming", "choices": [{"label": "A", "text": "cleanliness"}, {"label": "B", "text": "mistakes"}, {"label": "C", "text": "growth"}, {"label": "D", "text": "satisfaction"}, {"label": "E", "text": "late"}], "stem": "What does a successful dog grooming session likely to make a owner feel?"}}
{"answerKey": "D", "id": "ac6f0e24dd6203cda43e1089dcf081d6", "question": {"question_concept": "runner", "choices": [{"label": "A", "text": "near finish line"}, {"label": "B", "text": "finish"}, {"label": "C", "text": "get tired"}, {"label": "D", "text": "gain ground"}, {"label": "E", "text": "trip over"}], "stem": "The runner was in third place, but he pushed harder and thought he might be able to reach second.  What was beginning to do?"}}
{"answerKey": "B", "id": "1ab746bcd100ccf513055fe93c61010b", "question": {"question_concept": "cave", "choices": [{"label": "A", "text": "west virginia"}, {"label": "B", "text": "kentucky"}, {"label": "C", "text": "rocky hills"}, {"label": "D", "text": "scotland"}, {"label": "E", "text": "canyon"}], "stem": "The tourist entered Mammoth cave, what state were they in?"}}
{"answerKey": "B", "id": "af836abc58e0daf36df1d8d6830b70c5", "question": {"question_concept": "applying for job", "choices": [{"label": "A", "text": "horror"}, {"label": "B", "text": "anxiety and fear"}, {"label": "C", "text": "rejection"}, {"label": "D", "text": "increased workload"}, {"label": "E", "text": "being employed"}], "stem": "What does someone typically feel when applying for a job?"}}
{"answerKey": "E", "id": "2ed66cfd206723a006b37599b516ad6e", "question": {"question_concept": "obstructing justice", "choices": [{"label": "A", "text": "prosecution"}, {"label": "B", "text": "getting hurt"}, {"label": "C", "text": "sweat"}, {"label": "D", "text": "steam"}, {"label": "E", "text": "committing perjury"}], "stem": "He was on trial for obstructing justice, during which he made a questionable comment and was also found guilty of what?"}}
{"answerKey": "E", "id": "e89a2762d578cb7bc2cc0a5b2a16d933", "question": {"question_concept": "buy presents for others", "choices": [{"label": "A", "text": "tears"}, {"label": "B", "text": "please"}, {"label": "C", "text": "like"}, {"label": "D", "text": "thank"}, {"label": "E", "text": "make happy"}], "stem": "What kind of feelings does buying presents for others create?"}}
{"answerKey": "A", "id": "43cec0fff43a976fade9112d02b66021", "question": {"question_concept": "marmot", "choices": [{"label": "A", "text": "countryside"}, {"label": "B", "text": "great plains"}, {"label": "C", "text": "encyclopedia"}, {"label": "D", "text": "jungle"}, {"label": "E", "text": "north america"}], "stem": "What green area is a marmot likely to be found in?"}}
{"answerKey": "D", "id": "30e66db11e0257a14a17108b90cd69fb", "question": {"question_concept": "current", "choices": [{"label": "A", "text": "later"}, {"label": "B", "text": "updated"}, {"label": "C", "text": "still"}, {"label": "D", "text": "resistance"}, {"label": "E", "text": "now"}], "stem": "Jan tested the current, and noticed that it was high.  He thought that the wires might have too much what?"}}
{"answerKey": "E", "id": "f21ef67b31bd36a3174b6b4c7b4bbc7b", "question": {"question_concept": "teacher", "choices": [{"label": "A", "text": "lower expectations"}, {"label": "B", "text": "encourage"}, {"label": "C", "text": "fear"}, {"label": "D", "text": "time test"}, {"label": "E", "text": "tell story"}], "stem": "What does a kindergarten teacher do before nap time?"}}
{"answerKey": "B", "id": "e476e2c8c278eaecfe1a8b884b6aeb8e", "question": {"question_concept": "stranger", "choices": [{"label": "A", "text": "friend"}, {"label": "B", "text": "family"}, {"label": "C", "text": "known person"}, {"label": "D", "text": "park"}, {"label": "E", "text": "outsider"}], "stem": "Sam was a stranger.  Even so, Mark treated him like what?"}}
{"answerKey": "A", "id": "191e3c676f05a11d6b2565d8c27d2001", "question": {"question_concept": "light source", "choices": [{"label": "A", "text": "closed room"}, {"label": "B", "text": "sky"}, {"label": "C", "text": "dard"}, {"label": "D", "text": "his grave"}, {"label": "E", "text": "house"}], "stem": "Bob's only light source was a small bulb.  There were four walls, if there was a door he couldn't see it.  What was Bob in?"}}
{"answerKey": "D", "id": "99098375c7b651d524eebac72e358238", "question": {"question_concept": "computer", "choices": [{"label": "A", "text": "manual"}, {"label": "B", "text": "process information"}, {"label": "C", "text": "power down"}, {"label": "D", "text": "control model"}, {"label": "E", "text": "reason exists"}], "stem": "James thought of criminal justice like a computer program.  It need to work right.   What ideas might James not like?"}}
{"answerKey": "E", "id": "290fac9f881a83d8bfb34355f8e71044", "question": {"question_concept": "card slot", "choices": [{"label": "A", "text": "slot machine"}, {"label": "B", "text": "ticket machine"}, {"label": "C", "text": "bank machine"}, {"label": "D", "text": "telephone"}, {"label": "E", "text": "automated teller"}], "stem": "With the card slot lit up he knew how to get started finding his balance with what?"}}
{"answerKey": "C", "id": "6c36226b23377a0dd0188bf56840e22a", "question": {"question_concept": "play sports", "choices": [{"label": "A", "text": "wash your clothes"}, {"label": "B", "text": "get in shape"}, {"label": "C", "text": "practice"}, {"label": "D", "text": "take off uniform"}, {"label": "E", "text": "stretch"}], "stem": "To play sports professionally you must do what very often?"}}
{"answerKey": "C", "id": "aa5aa36557a5fbb93391506182f1025c", "question": {"question_concept": "releasing energy", "choices": [{"label": "A", "text": "motion"}, {"label": "B", "text": "stretch"}, {"label": "C", "text": "exercise"}, {"label": "D", "text": "movement"}, {"label": "E", "text": "muscles"}], "stem": "Some people prefer releasing energy through work while others prefer to release it through what?"}}
{"answerKey": "C", "id": "a38df3e750b1edd30f905e17af803c61", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "grope"}, {"label": "B", "text": "acknowledgment"}, {"label": "C", "text": "comfortable clothes"}, {"label": "D", "text": "ipod"}, {"label": "E", "text": "passionate kisses"}], "stem": "What will a person going for a jog likely be wearing?"}}
{"answerKey": "E", "id": "dba51270f789c75a2e38a5201b124d99", "question": {"question_concept": "reading newspaper", "choices": [{"label": "A", "text": "patience"}, {"label": "B", "text": "falling down"}, {"label": "C", "text": "literacy"}, {"label": "D", "text": "buying"}, {"label": "E", "text": "knowing how to read"}], "stem": "The child pretended he was reading newspaper, he couldn't actually do it without what?"}}
{"answerKey": "A", "id": "1be8ec824eb0c7218b6bc160fd191428", "question": {"question_concept": "helping", "choices": [{"label": "A", "text": "satisfaction"}, {"label": "B", "text": "complications"}, {"label": "C", "text": "train"}, {"label": "D", "text": "feel good about yourself"}, {"label": "E", "text": "enjoyment"}], "stem": "Jenny enjoyed helping people.  It brought her a great deal of what?"}}
{"answerKey": "B", "id": "0e80f2afe5c4f652e8720b52d7c06c87", "question": {"question_concept": "cleaning clothes", "choices": [{"label": "A", "text": "feminism"}, {"label": "B", "text": "sanitation"}, {"label": "C", "text": "ruined"}, {"label": "D", "text": "wrinkles"}, {"label": "E", "text": "buttons to fall off"}], "stem": "What might someone believe in if they are cleaning clothes?"}}
{"answerKey": "D", "id": "b67971747e95ba425a5b81e0ba8d0b28", "question": {"question_concept": "basement", "choices": [{"label": "A", "text": "eat cake"}, {"label": "B", "text": "closet"}, {"label": "C", "text": "church"}, {"label": "D", "text": "office building"}, {"label": "E", "text": "own house"}], "stem": "Where would you find a basement that can be accessed with an elevator?"}}
{"answerKey": "D", "id": "fcd39cfa321728fea069a6ae4285b06f", "question": {"question_concept": "program", "choices": [{"label": "A", "text": "learn how to"}, {"label": "B", "text": "have a friend"}, {"label": "C", "text": "knowledge"}, {"label": "D", "text": "take class"}, {"label": "E", "text": "have computer"}], "stem": "In order to learn to program from another person you can do what?"}}
{"answerKey": "E", "id": "cb6766fb25daee911fc8e9816b98938c", "question": {"question_concept": "muscle", "choices": [{"label": "A", "text": "body of animal"}, {"label": "B", "text": "arm"}, {"label": "C", "text": "bodybuilder"}, {"label": "D", "text": "body of dog"}, {"label": "E", "text": "human body"}], "stem": "He was at the gym trying to build muscle, what is it called that he is trying to build muscle on?"}}
{"answerKey": "D", "id": "54231f875bb7fe4d3e4afb6eae64387c", "question": {"question_concept": "plants", "choices": [{"label": "A", "text": "dirt"}, {"label": "B", "text": "no neurons in"}, {"label": "C", "text": "millions of cells"}, {"label": "D", "text": "flowers on"}, {"label": "E", "text": "roots"}], "stem": "What part of plants is pretty?"}}
{"answerKey": "B", "id": "7d7f7d7a8ae3b20ca9fc0da6efe467b4", "question": {"question_concept": "going fishing", "choices": [{"label": "A", "text": "food"}, {"label": "B", "text": "relaxation"}, {"label": "C", "text": "killing"}, {"label": "D", "text": "missing morning cartoons"}, {"label": "E", "text": "boredom"}], "stem": "The man was going fishing instead of work, what is he seeking?"}}
{"answerKey": "E", "id": "31b72d4e4ae7c672c20e27e42499ec79", "question": {"question_concept": "pit", "choices": [{"label": "A", "text": "backyard"}, {"label": "B", "text": "rock"}, {"label": "C", "text": "mine"}, {"label": "D", "text": "cherry"}, {"label": "E", "text": "peach"}], "stem": "What could you get an unsmooth pit from?"}}
{"answerKey": "A", "id": "26ce83b8e9a263079aa8cdbd5258d667", "question": {"question_concept": "reply", "choices": [{"label": "A", "text": "initiate"}, {"label": "B", "text": "ignore"}, {"label": "C", "text": "question"}, {"label": "D", "text": "answer"}, {"label": "E", "text": "ask"}], "stem": "The man tried to reply to the woman, but he had difficulty keeping track of conversations that he didn't do what to?"}}
{"answerKey": "E", "id": "30138608d4934a75cf0911a06b021374", "question": {"question_concept": "anybody", "choices": [{"label": "A", "text": "question authority"}, {"label": "B", "text": "act fool"}, {"label": "C", "text": "wash dishes"}, {"label": "D", "text": "act innocent"}, {"label": "E", "text": "forget"}], "stem": "I couldn't find anybody who recalled the event, what were they adroit at doing?"}}
{"answerKey": "A", "id": "01abce8c4964371d85a5be2019f75827", "question": {"question_concept": "dining room", "choices": [{"label": "A", "text": "mansion"}, {"label": "B", "text": "every house"}, {"label": "C", "text": "own home"}, {"label": "D", "text": "table"}, {"label": "E", "text": "restaurant"}], "stem": "Where would you find a large dining room containing a fancy chandelier?"}}
{"answerKey": "D", "id": "3e2222c99e11fca2ad4af2d470eb8ea2_1", "question": {"question_concept": "runway", "choices": [{"label": "A", "text": "back yard"}, {"label": "B", "text": "bowling alley"}, {"label": "C", "text": "city"}, {"label": "D", "text": "military base"}, {"label": "E", "text": "fashion show"}], "stem": "The extremely large cargo plane could only land at a specialized runway, these were only located at a what?"}}
{"answerKey": "C", "id": "847dbf5b73c3e8d49bb9a36491d95e79", "question": {"question_concept": "carpet", "choices": [{"label": "A", "text": "bedroom"}, {"label": "B", "text": "movie theater"}, {"label": "C", "text": "bowling alley"}, {"label": "D", "text": "church"}, {"label": "E", "text": "office"}], "stem": "The carpet was smelly and discouraged the league from playing there, where was this smelly carpet installed?"}}
{"answerKey": "B", "id": "fa031cff8e11e75c68d6a99ef0e5ca3a", "question": {"question_concept": "brownstone", "choices": [{"label": "A", "text": "brooklyn"}, {"label": "B", "text": "ring"}, {"label": "C", "text": "subdivision"}, {"label": "D", "text": "bricks"}, {"label": "E", "text": "new york city"}], "stem": "How can someone be let into a brownstone?"}}
{"answerKey": "C", "id": "c592258c88295756833e9796e881057b", "question": {"question_concept": "upright piano", "choices": [{"label": "A", "text": "music class"}, {"label": "B", "text": "college"}, {"label": "C", "text": "music store"}, {"label": "D", "text": "music room"}, {"label": "E", "text": "music band"}], "stem": "Where would someone purchase an upright piano?"}}
{"answerKey": "A", "id": "e1403a7c581bc263aea2ed8d179826d1", "question": {"question_concept": "ottoman", "choices": [{"label": "A", "text": "living room"}, {"label": "B", "text": "parlor"}, {"label": "C", "text": "furniture store"}, {"label": "D", "text": "basement"}, {"label": "E", "text": "kitchen"}], "stem": "Where would you keep an ottoman near your front door?"}}
{"answerKey": "E", "id": "15c38f66e811d6ed68cde931bc31d93c", "question": {"question_concept": "diving", "choices": [{"label": "A", "text": "going somewhere"}, {"label": "B", "text": "splats"}, {"label": "C", "text": "cancer"}, {"label": "D", "text": "getting wet"}, {"label": "E", "text": "spinal injuries"}], "stem": "Diving into backyard pools can be very dangerous and can lead to serious head and what?"}}
{"answerKey": "D", "id": "1ac54dbf6b67f27daa3d456416047584", "question": {"question_concept": "snake", "choices": [{"label": "A", "text": "tropical forest"}, {"label": "B", "text": "oregon"}, {"label": "C", "text": "woods"}, {"label": "D", "text": "pet store"}, {"label": "E", "text": "louisiana"}], "stem": "Where would one find a snake in a cage?"}}
{"answerKey": "A", "id": "21763a65765b5405c9a54484c2e54a72", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "end of line"}, {"label": "B", "text": "buildings"}, {"label": "C", "text": "apartment"}, {"label": "D", "text": "neighbor's house"}, {"label": "E", "text": "address"}], "stem": "Where are people likely to become impatient?"}}
{"answerKey": "C", "id": "c492b8b9754a181c924c1df19998cbc7", "question": {"question_concept": "fail", "choices": [{"label": "A", "text": "winning"}, {"label": "B", "text": "passing"}, {"label": "C", "text": "completing"}, {"label": "D", "text": "do well"}, {"label": "E", "text": "succeeding"}], "stem": "When you fail to finish something, you failed at doing what to it"}}
{"answerKey": "C", "id": "fff554fffa1a0adc64b8d1e21d55534b", "question": {"question_concept": "form", "choices": [{"label": "A", "text": "shapeless"}, {"label": "B", "text": "quality"}, {"label": "C", "text": "function"}, {"label": "D", "text": "change shape"}, {"label": "E", "text": "chaos"}], "stem": "John didn't care about style.  He felt that form was less important than what?"}}
{"answerKey": "D", "id": "8ea5720718c0e122efa6277edb511569", "question": {"question_concept": "watch film", "choices": [{"label": "A", "text": "see what happens"}, {"label": "B", "text": "enjoy stories"}, {"label": "C", "text": "pass time"}, {"label": "D", "text": "have fun"}, {"label": "E", "text": "interesting"}], "stem": "When you get together with friends to watch film, you might do plenty of this?"}}
{"answerKey": "A", "id": "23e4257a49972efd8a97672f060be1c1", "question": {"question_concept": "supermarket", "choices": [{"label": "A", "text": "strip mall"}, {"label": "B", "text": "city or town"}, {"label": "C", "text": "shoppingcentre"}, {"label": "D", "text": "boutique"}, {"label": "E", "text": "vermont"}], "stem": "A supermarket is uncommon in what type of collection of shops?"}}
{"answerKey": "D", "id": "a018d65a74b9e77d81014fd8f6d78f77", "question": {"question_concept": "scale", "choices": [{"label": "A", "text": "music store"}, {"label": "B", "text": "assay office"}, {"label": "C", "text": "tidal wave"}, {"label": "D", "text": "butcher shop"}, {"label": "E", "text": "bathroom"}], "stem": "Bill puts meat on the scale, where does Bill work?"}}
{"answerKey": "E", "id": "24ceaf5c10863e73919b5f1b0f2db38e", "question": {"question_concept": "food", "choices": [{"label": "A", "text": "zoo"}, {"label": "B", "text": "pan"}, {"label": "C", "text": "bowl"}, {"label": "D", "text": "kitchen"}, {"label": "E", "text": "spoon"}], "stem": "I'm having some food at my party, what will I need to serve it?"}}
{"answerKey": "E", "id": "900492bd731f8f615ed7c08155737d44", "question": {"question_concept": "run", "choices": [{"label": "A", "text": "learn to walk"}, {"label": "B", "text": "walking"}, {"label": "C", "text": "walk slowly"}, {"label": "D", "text": "breathe"}, {"label": "E", "text": "stand still"}], "stem": "Before racers start to run they must do what at the starting line?"}}
{"answerKey": "B", "id": "4e3f85dc92eaad4ae6bc6529d62e382c", "question": {"question_concept": "actor", "choices": [{"label": "A", "text": "mask"}, {"label": "B", "text": "branch out"}, {"label": "C", "text": "wear costume"}, {"label": "D", "text": "pretend"}, {"label": "E", "text": "sing songs"}], "stem": "What does an actor do when they are bored of their roles?"}}
{"answerKey": "E", "id": "fa1f17ca535c7e875f4f58510dc2f430", "question": {"question_concept": "immortality", "choices": [{"label": "A", "text": "mortal"}, {"label": "B", "text": "dying"}, {"label": "C", "text": "death"}, {"label": "D", "text": "dead"}, {"label": "E", "text": "mortal"}], "stem": "What is a person called who doesn't have immortality?"}}
{"answerKey": "C", "id": "76b6f0765a3b2fba71021f902142edc0", "question": {"question_concept": "watching tv", "choices": [{"label": "A", "text": "headache"}, {"label": "B", "text": "laughter"}, {"label": "C", "text": "laziness"}, {"label": "D", "text": "erections"}, {"label": "E", "text": "wasting time"}], "stem": "Why would you be watching tv instead of doing something else?"}}
{"answerKey": "B", "id": "f1368ab1d4ee05d72d555474fcd737d7", "question": {"question_concept": "chewing food", "choices": [{"label": "A", "text": "broken jaw"}, {"label": "B", "text": "sore mouth"}, {"label": "C", "text": "eating"}, {"label": "D", "text": "good digestion"}, {"label": "E", "text": "avoiding choking"}], "stem": "If chewing food is difficult for you, what is a possible reason?"}}
{"answerKey": "D", "id": "3dee8fc7f0a3fbf4de111b6686fca157", "question": {"question_concept": "keyboard instrument", "choices": [{"label": "A", "text": "music store"}, {"label": "B", "text": "band"}, {"label": "C", "text": "medium"}, {"label": "D", "text": "orchestra"}, {"label": "E", "text": "piano store"}], "stem": "He had to wear a tuxedo while playing the keyboard instrument, so did the other hundred members of the what?"}}
{"answerKey": "C", "id": "ea0e7771afd86a59fd9f7764b77e3fa4", "question": {"question_concept": "leaf", "choices": [{"label": "A", "text": "floral arrangement"}, {"label": "B", "text": "ground"}, {"label": "C", "text": "forrest"}, {"label": "D", "text": "field"}, {"label": "E", "text": "compost pile"}], "stem": "Where do you find the most amount of leafs?"}}
{"answerKey": "E", "id": "2c845646032bbf27fb3904330d59d324", "question": {"question_concept": "animals", "choices": [{"label": "A", "text": "meadow"}, {"label": "B", "text": "play room"}, {"label": "C", "text": "surface of earth"}, {"label": "D", "text": "zoos"}, {"label": "E", "text": "fairgrounds"}], "stem": "Where can children play with animals?"}}
{"answerKey": "E", "id": "bc08c354e5bead6863ea4a29cb8fa359", "question": {"question_concept": "weasel", "choices": [{"label": "A", "text": "mulberry bush"}, {"label": "B", "text": "animated film"}, {"label": "C", "text": "chicken coop"}, {"label": "D", "text": "history book"}, {"label": "E", "text": "children's story"}], "stem": "What kind of tale might feature a talking weasel?"}}
{"answerKey": "C", "id": "fb35c7aa5694bab2cde4b7257bfae003", "question": {"question_concept": "bald eagle", "choices": [{"label": "A", "text": "outside"}, {"label": "B", "text": "world"}, {"label": "C", "text": "protection"}, {"label": "D", "text": "colorado"}, {"label": "E", "text": "america"}], "stem": "What kind of status is the bald eagle given?"}}
{"answerKey": "C", "id": "e2a9f0041d17a9944377a91bef5e0d0d", "question": {"question_concept": "rest", "choices": [{"label": "A", "text": "need to"}, {"label": "B", "text": "hungry"}, {"label": "C", "text": "feel more energetic"}, {"label": "D", "text": "weak"}, {"label": "E", "text": "regenerate"}], "stem": "Why do most people take a quick rest during the day?"}}
{"answerKey": "B", "id": "ae56eff01d05422ddbcb26be7181356a", "question": {"question_concept": "running", "choices": [{"label": "A", "text": "mushroom"}, {"label": "B", "text": "falling down"}, {"label": "C", "text": "sweating"}, {"label": "D", "text": "exhaustion"}, {"label": "E", "text": "getting tired"}], "stem": "What could suddenly stop someone when he or she is running?"}}
{"answerKey": "E", "id": "895aa97bb84d874d71b2aed572cebfdd", "question": {"question_concept": "monkey", "choices": [{"label": "A", "text": "zoo"}, {"label": "B", "text": "barrel"}, {"label": "C", "text": "research laboratory"}, {"label": "D", "text": "captivity"}, {"label": "E", "text": "thailand"}], "stem": "Where would you find a monkey in the wild?"}}
{"answerKey": "A", "id": "9d625e948e9c3777e7cc54ed8ffea135", "question": {"question_concept": "sloth", "choices": [{"label": "A", "text": "tropical jungle"}, {"label": "B", "text": "manual"}, {"label": "C", "text": "work"}, {"label": "D", "text": "transit"}, {"label": "E", "text": "countryside"}], "stem": "Where could a sloth live?"}}
{"answerKey": "A", "id": "d107d67d525a686fbd8282314d2ea33c", "question": {"question_concept": "gentleman", "choices": [{"label": "A", "text": "club"}, {"label": "B", "text": "assembly hall"}, {"label": "C", "text": "meditation center"}, {"label": "D", "text": "meeting"}, {"label": "E", "text": "church"}], "stem": "A gentleman is carrying equipment for golf, what is he likely to have?"}}
{"answerKey": "A", "id": "fee5ff19811750ad019665af7b36b3c4", "question": {"question_concept": "courtyard", "choices": [{"label": "A", "text": "lawn"}, {"label": "B", "text": "kids"}, {"label": "C", "text": "asshole"}, {"label": "D", "text": "spain"}, {"label": "E", "text": "office complex"}], "stem": "If you have a home with a courtyard, what's one thing you probably don't have to care for any longer?"}}
{"answerKey": "E", "id": "e69da59cbcf2a302e4523571eba8186b", "question": {"question_concept": "computer", "choices": [{"label": "A", "text": "classroom"}, {"label": "B", "text": "facebook"}, {"label": "C", "text": "school"}, {"label": "D", "text": "apartment"}, {"label": "E", "text": "demonstration"}], "stem": "The computer was difficult for he to understand at the store, so what did she sign up for to learn more?"}}
{"answerKey": "E", "id": "2dd138a63b5895cf737ced793cc668e7", "question": {"question_concept": "car", "choices": [{"label": "A", "text": "go fast"}, {"label": "B", "text": "start running"}, {"label": "C", "text": "going too fast"}, {"label": "D", "text": "look good"}, {"label": "E", "text": "last several years"}], "stem": "If you take the risk buying a used car, you still hope it can what?"}}
{"answerKey": "E", "id": "b33047f46db680a9b630c13e8ca115cc", "question": {"question_concept": "sitting quietly", "choices": [{"label": "A", "text": "eat"}, {"label": "B", "text": "think"}, {"label": "C", "text": "reading"}, {"label": "D", "text": "meditate"}, {"label": "E", "text": "fall asleep"}], "stem": "Dan was ditting quietly on the couch with a book in his hand.  Laurie thought that he was just focused on what he was doing, but he actually did what?"}}
{"answerKey": "C", "id": "f20d40bc4af588223e880e0bb58b27b8", "question": {"question_concept": "cars", "choices": [{"label": "A", "text": "cost money"}, {"label": "B", "text": "slow down"}, {"label": "C", "text": "move people"}, {"label": "D", "text": "turn right"}, {"label": "E", "text": "get girls"}], "stem": "What is the primary purpose of cars?"}}
{"answerKey": "A", "id": "b6b66d4519a84b8331ea55f84767e9df", "question": {"question_concept": "alabama", "choices": [{"label": "A", "text": "united states"}, {"label": "B", "text": "deep south"}, {"label": "C", "text": "floribama"}, {"label": "D", "text": "gulf states"}, {"label": "E", "text": "florabama"}], "stem": "Alabama is full of different people, but they are all citizens of what?"}}
{"answerKey": "E", "id": "952cf4b2f7a434b2eeae9f4c7ed89c0a", "question": {"question_concept": "rise", "choices": [{"label": "A", "text": "set"}, {"label": "B", "text": "fall"}, {"label": "C", "text": "park"}, {"label": "D", "text": "descend"}, {"label": "E", "text": "reduce"}], "stem": "They were hoping their campaign would create a rise in awareness of the problem and hopefully do what to its effect?"}}
{"answerKey": "A", "id": "b63e5cd88bfe75d29ff9fdc6dd97fed6", "question": {"question_concept": "airplanes", "choices": [{"label": "A", "text": "slow down"}, {"label": "B", "text": "crash"}, {"label": "C", "text": "speed up"}, {"label": "D", "text": "land"}, {"label": "E", "text": "carry people"}], "stem": "What do airplanes do as they are arriving at the gate?"}}
{"answerKey": "C", "id": "ec5a336080e37fbe95d72ad5f9c65ba7", "question": {"question_concept": "mental illness", "choices": [{"label": "A", "text": "managed"}, {"label": "B", "text": "dancing"}, {"label": "C", "text": "recur"}, {"label": "D", "text": "effectively treated"}, {"label": "E", "text": "cause suffering"}], "stem": "If a person with mental illness stops treatment what will likely happen?"}}
{"answerKey": "E", "id": "6386bcf080633bc3eeb3317a5435b7b7", "question": {"question_concept": "animals", "choices": [{"label": "A", "text": "sick"}, {"label": "B", "text": "mammals"}, {"label": "C", "text": "males"}, {"label": "D", "text": "bite"}, {"label": "E", "text": "attack"}], "stem": "The gimmicky low brow TV show was about animals when they what?"}}
{"answerKey": "E", "id": "43ab0ff711e60d51f943bbd2cdd6515a", "question": {"question_concept": "machine", "choices": [{"label": "A", "text": "museum"}, {"label": "B", "text": "house"}, {"label": "C", "text": "laboratory"}, {"label": "D", "text": "library"}, {"label": "E", "text": "industrial area"}], "stem": "A loud machine is irritating, but many are expected where?"}}
{"answerKey": "A", "id": "11c4c78d61e8212f0984fd07eb22b669", "question": {"question_concept": "ruler", "choices": [{"label": "A", "text": "drawer"}, {"label": "B", "text": "desk"}, {"label": "C", "text": "the backside"}, {"label": "D", "text": "office"}, {"label": "E", "text": "measure distance"}], "stem": "What part of a table would you put a ruler in?"}}
{"answerKey": "C", "id": "e61891746aa94ab57aaa754614034aef", "question": {"question_concept": "kissing", "choices": [{"label": "A", "text": "strong feelings"}, {"label": "B", "text": "herpes"}, {"label": "C", "text": "shortness of breath"}, {"label": "D", "text": "excitement"}, {"label": "E", "text": "arousal"}], "stem": "What happens if someone kisses too long?"}}
{"answerKey": "C", "id": "97da9aa4ea4b22744ec51cba49f35bfc", "question": {"question_concept": "light source", "choices": [{"label": "A", "text": "sky"}, {"label": "B", "text": "house"}, {"label": "C", "text": "lamp"}, {"label": "D", "text": "match"}, {"label": "E", "text": "candle"}], "stem": "If I have a modern light source in my living room, what is it likely to be?"}}
{"answerKey": "E", "id": "46241bc83e8d81196ae5783b2b9854a4", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "smell smoke"}, {"label": "B", "text": "cross street"}, {"label": "C", "text": "cry"}, {"label": "D", "text": "bank savings"}, {"label": "E", "text": "look angry"}], "stem": "The person saw the mess his children made, what was his following reaction?"}}
{"answerKey": "A", "id": "18844d3aa4e52b331b5382c8244cf4db", "question": {"question_concept": "dark glasses", "choices": [{"label": "A", "text": "blind person"}, {"label": "B", "text": "glove box"}, {"label": "C", "text": "movie studio"}, {"label": "D", "text": "ray charles"}, {"label": "E", "text": "glove compartment"}], "stem": "Who might wear dark glasses indoors?"}}
{"answerKey": "B", "id": "056b33c7050c167b0d4348d40d169358", "question": {"question_concept": "stones", "choices": [{"label": "A", "text": "quarries"}, {"label": "B", "text": "field"}, {"label": "C", "text": "park"}, {"label": "D", "text": "bridge"}, {"label": "E", "text": "made from rocks"}], "stem": "Where would stones not be arranged in a path?"}}
{"answerKey": "D", "id": "31d7dd1d00aabe411568df3e72d5b5e0", "question": {"question_concept": "bald eagle", "choices": [{"label": "A", "text": "rural area"}, {"label": "B", "text": "book"}, {"label": "C", "text": "canada"}, {"label": "D", "text": "painting"}, {"label": "E", "text": "aviary"}], "stem": "A bald eagle is likely to be found on what kind of work?"}}
{"answerKey": "C", "id": "cbf3dd48b4d591fc872a53cd4b9dd3af", "question": {"question_concept": "hostess", "choices": [{"label": "A", "text": "group people"}, {"label": "B", "text": "ready parlor for guests"}, {"label": "C", "text": "welcome guests"}, {"label": "D", "text": "work room"}, {"label": "E", "text": "park"}], "stem": "The hostess was good at her job, she always had a smile when she would what?"}}
{"answerKey": "C", "id": "60e8f1a86d4063895f340cd1e3c55f50", "question": {"question_concept": "learning", "choices": [{"label": "A", "text": "overconfidence"}, {"label": "B", "text": "effectiveness"}, {"label": "C", "text": "knowing more"}, {"label": "D", "text": "head grows larger"}, {"label": "E", "text": "growth"}], "stem": "What is likely to happen to someone who is learning?"}}
{"answerKey": "A", "id": "eee8cb7a0d806a62d2de24831f82e3e1", "question": {"question_concept": "agreeing with", "choices": [{"label": "A", "text": "compliance"}, {"label": "B", "text": "eligible"}, {"label": "C", "text": "contract"}, {"label": "D", "text": "harmony"}, {"label": "E", "text": "friendship"}], "stem": "The inspector was agreeing with the factory protocols, what was the status of the factory?"}}
{"answerKey": "C", "id": "9a23a7f04e63bf9f4c7dfe50c58abfd2", "question": {"question_concept": "standing up", "choices": [{"label": "A", "text": "train"}, {"label": "B", "text": "effort"}, {"label": "C", "text": "balance"}, {"label": "D", "text": "feet"}, {"label": "E", "text": "muscles"}], "stem": "After standing up I had to sit right back down, why would I feel like this?"}}
{"answerKey": "C", "id": "e3426e4f60c142aa3d813479f79d6305", "question": {"question_concept": "bar", "choices": [{"label": "A", "text": "new york city"}, {"label": "B", "text": "las vegas"}, {"label": "C", "text": "restaurant"}, {"label": "D", "text": "nightclub"}, {"label": "E", "text": "park"}], "stem": "Where do you go on a night out before going to the bar?"}}
{"answerKey": "D", "id": "3526550b02d9594abd4fc43553010fc6", "question": {"question_concept": "gun", "choices": [{"label": "A", "text": "police station"}, {"label": "B", "text": "crime scene"}, {"label": "C", "text": "restroom"}, {"label": "D", "text": "drawer"}, {"label": "E", "text": "holster"}], "stem": "The dad wanted to protect his house, where did he put his gun?"}}
{"answerKey": "E", "id": "e567c94d88829fb07a30e3d46c02e664", "question": {"question_concept": "happiness", "choices": [{"label": "A", "text": "jump up and down"}, {"label": "B", "text": "jump up and down"}, {"label": "C", "text": "sing"}, {"label": "D", "text": "play games"}, {"label": "E", "text": "fiddle"}], "stem": "What instrument can be played with an air of happiness?"}}
{"answerKey": "E", "id": "cf5a710c931779fb3dde198e0ace3b6a", "question": {"question_concept": "boredom", "choices": [{"label": "A", "text": "watch film"}, {"label": "B", "text": "fire game"}, {"label": "C", "text": "hang out at bar"}, {"label": "D", "text": "go skiing"}, {"label": "E", "text": "skateboard"}], "stem": "What to kids do for boredom on a ramp?"}}
{"answerKey": "E", "id": "0f2377604e628c55ba588366139396b9", "question": {"question_concept": "quill", "choices": [{"label": "A", "text": "feather"}, {"label": "B", "text": "chicken"}, {"label": "C", "text": "calligraphy"}, {"label": "D", "text": "porcupine"}, {"label": "E", "text": "hedgehog"}], "stem": "What animal has quills all over it?"}}
{"answerKey": "A", "id": "ada088b7c97de80336ad043757c2db16", "question": {"question_concept": "office", "choices": [{"label": "A", "text": "work"}, {"label": "B", "text": "school building"}, {"label": "C", "text": "paper"}, {"label": "D", "text": "city"}, {"label": "E", "text": "habit"}], "stem": "Why would you go to an office?"}}
{"answerKey": "B", "id": "beef0aa2058297904bb4acc1dc340c85", "question": {"question_concept": "having food", "choices": [{"label": "A", "text": "digesting"}, {"label": "B", "text": "not hungry"}, {"label": "C", "text": "gas"}, {"label": "D", "text": "weight gain"}, {"label": "E", "text": "feeling of fullness"}], "stem": "When is the worst time for having food?"}}
{"answerKey": "E", "id": "ba9a05bd2086c0d37733e26479d6630f", "question": {"question_concept": "buying", "choices": [{"label": "A", "text": "using money"}, {"label": "B", "text": "feel better"}, {"label": "C", "text": "ocean"}, {"label": "D", "text": "losing money"}, {"label": "E", "text": "go broke"}], "stem": "If you spend all your time buying and not saving what is is likely to happen?"}}
{"answerKey": "C", "id": "6b0bf501aa68b06ddc5ad72ac5ff68fc", "question": {"question_concept": "mouse", "choices": [{"label": "A", "text": "tin"}, {"label": "B", "text": "department store"}, {"label": "C", "text": "garden"}, {"label": "D", "text": "small hole"}, {"label": "E", "text": "cupboard"}], "stem": "Though a mouse might prefer your house, you might also see him where?"}}
{"answerKey": "B", "id": "926298bbdd03ce96acfeb4408b888b61", "question": {"question_concept": "performing", "choices": [{"label": "A", "text": "singing"}, {"label": "B", "text": "act"}, {"label": "C", "text": "feat"}, {"label": "D", "text": "smile"}, {"label": "E", "text": "acting"}], "stem": "What is performing a type of?"}}
{"answerKey": "A", "id": "faa0aa438b94c19be8ff52ee80d9e298", "question": {"question_concept": "car", "choices": [{"label": "A", "text": "head north"}, {"label": "B", "text": "speed up"}, {"label": "C", "text": "heading north"}, {"label": "D", "text": "go fast"}, {"label": "E", "text": "headed south"}], "stem": "The car was going from Alabama to New York, what was its goal?"}}
{"answerKey": "E", "id": "9310c39a0752f28640c3a05cba1d5ca7", "question": {"question_concept": "trash", "choices": [{"label": "A", "text": "dirt"}, {"label": "B", "text": "subway"}, {"label": "C", "text": "state park"}, {"label": "D", "text": "container"}, {"label": "E", "text": "dustbin"}], "stem": "What do they call the trash in Australia?"}}
{"answerKey": "A", "id": "fee5f4e9d8e37f0183e36eb9b8dbcbb9", "question": {"question_concept": "potato", "choices": [{"label": "A", "text": "boiling water"}, {"label": "B", "text": "paper bag"}, {"label": "C", "text": "restaurants"}, {"label": "D", "text": "underground"}, {"label": "E", "text": "cupboard"}], "stem": "Joan wants to cook a potato, where should she place it?"}}
{"answerKey": "D", "id": "5392af3f1c4665e95ff3354e5115de42", "question": {"question_concept": "fortune", "choices": [{"label": "A", "text": "cookie"}, {"label": "B", "text": "bank"}, {"label": "C", "text": "real estate"}, {"label": "D", "text": "imagination"}, {"label": "E", "text": "bank roll"}], "stem": "Writers with a great what can amass a large fortune?"}}
{"answerKey": "C", "id": "4c5c74b3287492d6ddb2da4c8c0fd51a", "question": {"question_concept": "animals", "choices": [{"label": "A", "text": "the moon"}, {"label": "B", "text": "fairgrounds"}, {"label": "C", "text": "surface of earth"}, {"label": "D", "text": "meadow"}, {"label": "E", "text": "zoos"}], "stem": "Where do all animals live?"}}
{"answerKey": "A", "id": "52f3eb6c9a6b9671050fc769d465ed03", "question": {"question_concept": "living", "choices": [{"label": "A", "text": "sometimes bad"}, {"label": "B", "text": "happy"}, {"label": "C", "text": "respiration"}, {"label": "D", "text": "growing older"}, {"label": "E", "text": "death"}], "stem": "How are the conditions for someone who is living in a homeless shelter?"}}
{"answerKey": "A", "id": "03ee30b5801b61aee791a551a9d9a49f", "question": {"question_concept": "knitting", "choices": [{"label": "A", "text": "relaxation"}, {"label": "B", "text": "arthritis"}, {"label": "C", "text": "adrenaline"}, {"label": "D", "text": "your"}, {"label": "E", "text": "sweater may produced"}], "stem": "You can do knitting to get the feeling of what?"}}
{"answerKey": "B", "id": "6d1d483745bc0aae0f4dd04e851ceffb", "question": {"question_concept": "table", "choices": [{"label": "A", "text": "dining room"}, {"label": "B", "text": "conference"}, {"label": "C", "text": "kitchen"}, {"label": "D", "text": "in a lake"}, {"label": "E", "text": "demonstration"}], "stem": "What might a very large table be?"}}
{"answerKey": "C", "id": "bf10bfda7328c8671e15adf8546b64d7", "question": {"question_concept": "tax", "choices": [{"label": "A", "text": "candy"}, {"label": "B", "text": "death and"}, {"label": "C", "text": "free money"}, {"label": "D", "text": "discount"}, {"label": "E", "text": "credit"}], "stem": "John got his tax refund back.  He treated it like it was what?"}}
{"answerKey": "E", "id": "0b3a3ee40dd25be9735ac5e3342ca4dd", "question": {"question_concept": "awake", "choices": [{"label": "A", "text": "have fun"}, {"label": "B", "text": "enjoy with friends"}, {"label": "C", "text": "stretch"}, {"label": "D", "text": "yawn"}, {"label": "E", "text": "sneezing"}], "stem": "A person with an allergy might be doing what if they awake suddenly?"}}
{"answerKey": "A", "id": "77e2a0b469b56bea81921a4a945ffcb5", "question": {"question_concept": "ferret", "choices": [{"label": "A", "text": "classroom"}, {"label": "B", "text": "outdoors"}, {"label": "C", "text": "aquarium"}, {"label": "D", "text": "north carolina"}, {"label": "E", "text": "great britain"}], "stem": "Where is a ferret unlikely to be?"}}
{"answerKey": "B", "id": "dc964e4f6df6b70815e81e466d0ff717", "question": {"question_concept": "oceans", "choices": [{"label": "A", "text": "tanned"}, {"label": "B", "text": "wet"}, {"label": "C", "text": "wide"}, {"label": "D", "text": "very deep"}, {"label": "E", "text": "fish"}], "stem": "If you jump in any of the oceans you will get?"}}
{"answerKey": "B", "id": "6b9221c1af583ffb43580857d6fde38a", "question": {"question_concept": "bladder", "choices": [{"label": "A", "text": "collapsed"}, {"label": "B", "text": "empty"}, {"label": "C", "text": "full"}, {"label": "D", "text": "filled"}, {"label": "E", "text": "stretchable"}], "stem": "Immediately after peeing, a person's bladder is what?"}}
{"answerKey": "D", "id": "4dc2c4596b08e9bfd893174e67bff40a", "question": {"question_concept": "eat", "choices": [{"label": "A", "text": "wash dishes"}, {"label": "B", "text": "throwing up"}, {"label": "C", "text": "drinking"}, {"label": "D", "text": "throw up"}, {"label": "E", "text": "turn inside out"}], "stem": "The lady would eat and eat, and because of mental issues would then make herself what?"}}
{"answerKey": "A", "id": "8ae24d3ff199077a59e0d970feb665b7", "question": {"question_concept": "car", "choices": [{"label": "A", "text": "go downtown"}, {"label": "B", "text": "appear suddenly"}, {"label": "C", "text": "go fast"}, {"label": "D", "text": "bottom out"}, {"label": "E", "text": "east"}], "stem": "A car was hailed to chauffeur someone to the opera house, where was it heading?"}}
{"answerKey": "B", "id": "d64a676e9d22e7edd12e7f4ce267a9f0", "question": {"question_concept": "entertainment", "choices": [{"label": "A", "text": "movie"}, {"label": "B", "text": "show"}, {"label": "C", "text": "concert venue"}, {"label": "D", "text": "casino"}, {"label": "E", "text": "theatre"}], "stem": "What do you go to see for live entertainment?"}}
{"answerKey": "C", "id": "54ecb521df1d0f5b130a393c42b4126d", "question": {"question_concept": "ferret", "choices": [{"label": "A", "text": "bad mood"}, {"label": "B", "text": "hutch"}, {"label": "C", "text": "classroom"}, {"label": "D", "text": "pair of trousers"}, {"label": "E", "text": "year"}], "stem": "The teacher thought that a ferret can be very mischievous and probably wouldn't make a great pet for the entire what?"}}
{"answerKey": "B", "id": "b7276bb9139ec25c98c7e3822404eb6c", "question": {"question_concept": "creek", "choices": [{"label": "A", "text": "forest"}, {"label": "B", "text": "valley"}, {"label": "C", "text": "outdoors"}, {"label": "D", "text": "countryside"}, {"label": "E", "text": "woods"}], "stem": "A creek is a body of water found in what low land?"}}
{"answerKey": "E", "id": "ecb8758b0d088f9aedc182a516dd1190", "question": {"question_concept": "bird", "choices": [{"label": "A", "text": "forest"}, {"label": "B", "text": "bathroom"}, {"label": "C", "text": "windowsill"}, {"label": "D", "text": "countryside"}, {"label": "E", "text": "cage"}], "stem": "If I have a pet bird, what does it likely live in?"}}
{"answerKey": "B", "id": "f2645d0ee8662b6553954cee7e77979e", "question": {"question_concept": "playing basketball", "choices": [{"label": "A", "text": "study"}, {"label": "B", "text": "have fun"}, {"label": "C", "text": "pain"}, {"label": "D", "text": "cheers"}, {"label": "E", "text": "knee injury"}], "stem": "Joe and Mac were playing basketball. They did it every day in their back yard.  Why were they playing basketball?"}}
{"answerKey": "C", "id": "ea6d1a739ea841be282e13789270651e", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "unpleasant things"}, {"label": "B", "text": "hangnail"}, {"label": "C", "text": "have no home"}, {"label": "D", "text": "have no car"}, {"label": "E", "text": "schizophrenia"}], "stem": "What makes someone a nomad?"}}
{"answerKey": "D", "id": "c82ed0c2a2e115452b4d596c5faafbcf", "question": {"question_concept": "dog", "choices": [{"label": "A", "text": "salad"}, {"label": "B", "text": "petted"}, {"label": "C", "text": "affection"}, {"label": "D", "text": "bone"}, {"label": "E", "text": "lots of attention"}], "stem": "What is a treat that you dog will enjoy?"}}
{"answerKey": "B", "id": "163d83851ecd4a4144b31b8738e4c335", "question": {"question_concept": "dress", "choices": [{"label": "A", "text": "man suit"}, {"label": "B", "text": "pants"}, {"label": "C", "text": "naked"}, {"label": "D", "text": "action"}, {"label": "E", "text": "long skirt"}], "stem": "Women used to be expected to wear a dress but it's now acceptable for them to wear what?"}}
{"answerKey": "A", "id": "095767956c500ca1af7cf7671556de5b", "question": {"question_concept": "memorize", "choices": [{"label": "A", "text": "awake"}, {"label": "B", "text": "repeat"}, {"label": "C", "text": "sleeping"}, {"label": "D", "text": "concentrate"}, {"label": "E", "text": "read aloud"}], "stem": "The fact that Joe was able to memorize the list in spite of his apparent  state proved that part of his brain was what?"}}
{"answerKey": "C", "id": "d31ee38f67d1173275e120b8ad36039c", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "gain weight"}, {"label": "B", "text": "thank god"}, {"label": "C", "text": "catch cold"}, {"label": "D", "text": "suicide"}, {"label": "E", "text": "cross street"}], "stem": "What is a wet person likely to do?"}}
{"answerKey": "A", "id": "c410a4626dfce4b4cfd3e5937602cd77", "question": {"question_concept": "disease", "choices": [{"label": "A", "text": "healthy"}, {"label": "B", "text": "passing around"}, {"label": "C", "text": "cure"}, {"label": "D", "text": "wellness"}, {"label": "E", "text": "healthy"}], "stem": "After recovering from the disease, what did the doctor call the patient?"}}
{"answerKey": "E", "id": "14d760e43728e9e4643c414627f2b596", "question": {"question_concept": "edge", "choices": [{"label": "A", "text": "triangle"}, {"label": "B", "text": "middle"}, {"label": "C", "text": "corner"}, {"label": "D", "text": "center"}, {"label": "E", "text": "interior"}], "stem": "The painter started to edge the room with tape, he always took extra care to make the lines clean and crisp when working with an what?"}}
{"answerKey": "D", "id": "abcf1b550b4d44f46d4f68b8e1d98ec8", "question": {"question_concept": "anemone", "choices": [{"label": "A", "text": "nursery"}, {"label": "B", "text": "museum"}, {"label": "C", "text": "gulf of mexico"}, {"label": "D", "text": "tide pool"}, {"label": "E", "text": "intertidal zone"}], "stem": "After high tide, where on the coast can you look to find a sea anemone?"}}
{"answerKey": "B", "id": "5b8af6f26335dbd501b0104c71e26d9e", "question": {"question_concept": "driving car", "choices": [{"label": "A", "text": "say hello"}, {"label": "B", "text": "wreak"}, {"label": "C", "text": "pollution"}, {"label": "D", "text": "smoke"}, {"label": "E", "text": "relaxation"}], "stem": "What could a driving car do to a pedestrian?"}}
{"answerKey": "D", "id": "4364b4b342fb7b44434bd6694bf8fd51", "question": {"question_concept": "boredom", "choices": [{"label": "A", "text": "play cards"}, {"label": "B", "text": "skateboard"}, {"label": "C", "text": "meet interesting people"}, {"label": "D", "text": "listen to music"}, {"label": "E", "text": "go to a concert"}], "stem": "People do many things to alleviate boredom.  If you can't get out of the house you might decide to do what?"}}
{"answerKey": "D", "id": "3ffe67fb009529d9b0c49ccd7141ee4a", "question": {"question_concept": "potato", "choices": [{"label": "A", "text": "boiling water"}, {"label": "B", "text": "root cellar"}, {"label": "C", "text": "rocket ship"}, {"label": "D", "text": "paper bag"}, {"label": "E", "text": "underground"}], "stem": "At a grocery store they sell individual potatoes, where does the grocery clerk likely put the potato?"}}
{"answerKey": "E", "id": "f372587fa4c99d5bebf0d0eb987c44e2", "question": {"question_concept": "mat", "choices": [{"label": "A", "text": "doorway"}, {"label": "B", "text": "living room"}, {"label": "C", "text": "sand"}, {"label": "D", "text": "floors"}, {"label": "E", "text": "bathroom"}], "stem": "What room is a rubber bath mat usually kept?"}}
{"answerKey": "E", "id": "d35a8a3bd560fdd651ecf314878ed30f", "question": {"question_concept": "meat", "choices": [{"label": "A", "text": "oil"}, {"label": "B", "text": "freezer"}, {"label": "C", "text": "ham sandwich"}, {"label": "D", "text": "oven"}, {"label": "E", "text": "frying pan"}], "stem": "What would you put meat on top of to cook it?"}}
{"answerKey": "A", "id": "0542414710025f56b0c26e1bae5c4d06", "question": {"question_concept": "mineral", "choices": [{"label": "A", "text": "multivitamin"}, {"label": "B", "text": "farm"}, {"label": "C", "text": "michigan"}, {"label": "D", "text": "earth"}, {"label": "E", "text": "ore"}], "stem": "Minerals can be obtained in what way for a person who avoids leafy greens?"}}
{"answerKey": "A", "id": "1875f70cf736c68c7a9df3ef870224a1", "question": {"question_concept": "cashing in", "choices": [{"label": "A", "text": "happy"}, {"label": "B", "text": "receiving money"}, {"label": "C", "text": "getting paid"}, {"label": "D", "text": "spending money"}, {"label": "E", "text": "selling out"}], "stem": "What could you be a few hours after you finish cashing in due to your cash?"}}
{"answerKey": "C", "id": "83250ae2dfeb2e3886ead4cde8e1290f", "question": {"question_concept": "having bath", "choices": [{"label": "A", "text": "hydration"}, {"label": "B", "text": "being clear"}, {"label": "C", "text": "personal cleanliness"}, {"label": "D", "text": "will drown"}, {"label": "E", "text": "use of water"}], "stem": "The smelly man was having a bath, but what is he pursuing?"}}
{"answerKey": "E", "id": "70c39372c0d50566554fd72c768b75f6", "question": {"question_concept": "stopping being married to", "choices": [{"label": "A", "text": "pleasure"}, {"label": "B", "text": "detachment"}, {"label": "C", "text": "exercise"}, {"label": "D", "text": "bankruptcy"}, {"label": "E", "text": "fights"}], "stem": "What might a couple have a lot of when they are deciding on stopping being married to each other?"}}
{"answerKey": "A", "id": "c21ec5b367f409a0288d616f626555ae", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "much money"}, {"label": "B", "text": "own house"}, {"label": "C", "text": "creativity"}, {"label": "D", "text": "new car"}, {"label": "E", "text": "caregiver"}], "stem": "If a person is working a lot, what are they likely trying to earn?"}}
{"answerKey": "C", "id": "a2cd03ed068f6d613e85f3a60f4db0a1", "question": {"question_concept": "high prices", "choices": [{"label": "A", "text": "car"}, {"label": "B", "text": "theatre"}, {"label": "C", "text": "airport"}, {"label": "D", "text": "hotel"}, {"label": "E", "text": "disneyland"}], "stem": "The traveling business man was glad his credit card had perks, it offset the high prices for travel from a what?"}}
{"answerKey": "D", "id": "d2871dc28c82471e5d7f71f79e49c257", "question": {"question_concept": "toilet", "choices": [{"label": "A", "text": "bathroom"}, {"label": "B", "text": "motel room"}, {"label": "C", "text": "nearest public restroom"}, {"label": "D", "text": "house"}, {"label": "E", "text": "apartment"}], "stem": "Billy hated using other people's toilets. He was only comfortable on his own.  So whenever he needed to poop, he would go back to his what?"}}
{"answerKey": "D", "id": "94770e75c4e2000e717b4218ddff19e8", "question": {"question_concept": "forest", "choices": [{"label": "A", "text": "earth"}, {"label": "B", "text": "south america"}, {"label": "C", "text": "amazon basin"}, {"label": "D", "text": "temperate zone"}, {"label": "E", "text": "national park"}], "stem": "The forest experienced a cold winter, where is it located?"}}
{"answerKey": "D", "id": "08ad17d3ca1838b8724d21cf5921ec52", "question": {"question_concept": "anger", "choices": [{"label": "A", "text": "release energy"}, {"label": "B", "text": "destroy enemy"}, {"label": "C", "text": "punch"}, {"label": "D", "text": "write letter"}, {"label": "E", "text": "lose your temper"}], "stem": "How can you let someone know about your anger without hurting him or her?"}}
{"answerKey": "A", "id": "21fb76bd8349628b441c76f47c33e77b", "question": {"question_concept": "brownstone", "choices": [{"label": "A", "text": "new york city"}, {"label": "B", "text": "subdivision"}, {"label": "C", "text": "ring"}, {"label": "D", "text": "hazleton"}, {"label": "E", "text": "live in"}], "stem": "Where is one likely to find a brownstone?"}}
{"answerKey": "C", "id": "e151b44e0a7bf08a1dd3c861eef09161", "question": {"question_concept": "telephone", "choices": [{"label": "A", "text": "bar"}, {"label": "B", "text": "friend's house"}, {"label": "C", "text": "desktop"}, {"label": "D", "text": "party"}, {"label": "E", "text": "office"}], "stem": "What may I place the telephone on?"}}
{"answerKey": "B", "id": "46351b3a6beb694c5f623583a3b1473d", "question": {"question_concept": "light source", "choices": [{"label": "A", "text": "books"}, {"label": "B", "text": "dard"}, {"label": "C", "text": "sky"}, {"label": "D", "text": "closed room"}, {"label": "E", "text": "television"}], "stem": "What language type is someone from Iran likely to use?"}}
{"answerKey": "E", "id": "db75e16788cf56d5dfb9773eaf91fe7e", "question": {"question_concept": "party", "choices": [{"label": "A", "text": "meeting"}, {"label": "B", "text": "blowing off steam"}, {"label": "C", "text": "stay home"}, {"label": "D", "text": "partying hard"}, {"label": "E", "text": "studying"}], "stem": "John went to a party that lasted all night.  Because of this, he didn't have time for what?"}}
{"answerKey": "A", "id": "ffd89796a9b09bef56c5803f188764c6", "question": {"question_concept": "child", "choices": [{"label": "A", "text": "set table"}, {"label": "B", "text": "form opinions"}, {"label": "C", "text": "make honey"}, {"label": "D", "text": "become adult"}, {"label": "E", "text": "gather flowers"}], "stem": "The child wasn't allowed in the kitchen but still wanted to help, what could it do to help in the dining room?"}}
{"answerKey": "C", "id": "5622e49306bb82ec1cec817ad0506c60", "question": {"question_concept": "expressing yourself", "choices": [{"label": "A", "text": "slow"}, {"label": "B", "text": "understood"}, {"label": "C", "text": "suffering"}, {"label": "D", "text": "embarrassment"}, {"label": "E", "text": "fun"}], "stem": "He was having a hard time expressing himself in a healthy way, the psychologist said he was mentally what?"}}
{"answerKey": "E", "id": "6efaeb796307036719635242fa5ad0f3", "question": {"question_concept": "competing", "choices": [{"label": "A", "text": "tension"}, {"label": "B", "text": "perform better"}, {"label": "C", "text": "releases heat"}, {"label": "D", "text": "winning or losing"}, {"label": "E", "text": "sweat"}], "stem": "When someone is physically competing what does their body do?"}}
{"answerKey": "C", "id": "114d310d1198abffaf8b88dab5a55aa7", "question": {"question_concept": "express information", "choices": [{"label": "A", "text": "summarize main points"}, {"label": "B", "text": "close mouth"}, {"label": "C", "text": "write down"}, {"label": "D", "text": "may disagree"}, {"label": "E", "text": "talk"}], "stem": "How would you express information to a deaf person?"}}
{"answerKey": "B", "id": "0f79faf5337706f2e0e39c15bbd2e99a", "question": {"question_concept": "printing on printer", "choices": [{"label": "A", "text": "explode"}, {"label": "B", "text": "use paper"}, {"label": "C", "text": "store information"}, {"label": "D", "text": "queue"}, {"label": "E", "text": "noise"}], "stem": "Printing on a printer can get expensive because it does what?"}}
{"answerKey": "B", "id": "b62d7d1b5eec31be0b65146a9fc069e0", "question": {"question_concept": "god", "choices": [{"label": "A", "text": "anything"}, {"label": "B", "text": "judge people"}, {"label": "C", "text": "work miracles"}, {"label": "D", "text": "judge men"}, {"label": "E", "text": "everywhere"}], "stem": "What will god never do according to religion?"}}
{"answerKey": "D", "id": "1342c6aec9f5179d6ea6fa5fefbe5188", "question": {"question_concept": "attending school", "choices": [{"label": "A", "text": "cooties"}, {"label": "B", "text": "get smart"}, {"label": "C", "text": "boredom"}, {"label": "D", "text": "colds and flu"}, {"label": "E", "text": "taking tests"}], "stem": "One of the potential hazards of attending school is what?"}}
{"answerKey": "A", "id": "c74ae684ba6c76e2a913493483678c9d", "question": {"question_concept": "surface", "choices": [{"label": "A", "text": "tetrahedron"}, {"label": "B", "text": "object"}, {"label": "C", "text": "geometry problem"}, {"label": "D", "text": "lake"}, {"label": "E", "text": "triangle"}], "stem": "What has a surface with many sides?"}}
{"answerKey": "C", "id": "411e50225637b76187cc36b24fe3127c", "question": {"question_concept": "container", "choices": [{"label": "A", "text": "food"}, {"label": "B", "text": "refrigerator"}, {"label": "C", "text": "cargo ship"}, {"label": "D", "text": "port"}, {"label": "E", "text": "fuel"}], "stem": "What could bring a container from one place to another?"}}
{"answerKey": "C", "id": "2a0e82bbf1471290c93c8f2a11af197f", "question": {"question_concept": "see story", "choices": [{"label": "A", "text": "giggle"}, {"label": "B", "text": "visualize"}, {"label": "C", "text": "open book"}, {"label": "D", "text": "reading"}, {"label": "E", "text": "go to movies"}], "stem": "The old style pop ups literally let you see the story when you did what?"}}
{"answerKey": "A", "id": "eaadd7a4b18cb48c00f85c3975750fe7", "question": {"question_concept": "talking to", "choices": [{"label": "A", "text": "communication"}, {"label": "B", "text": "quiet"}, {"label": "C", "text": "boredom"}, {"label": "D", "text": "persuaded"}, {"label": "E", "text": "learn"}], "stem": "What is it called when you are talking to someone?"}}
{"answerKey": "B", "id": "403c9b067ef7363efffa822bb08c5426", "question": {"question_concept": "dirty dishes", "choices": [{"label": "A", "text": "restaurant kitchen"}, {"label": "B", "text": "dishwasher"}, {"label": "C", "text": "son's room"}, {"label": "D", "text": "cabinet"}, {"label": "E", "text": "party"}], "stem": "The family finished dinner, the child's chore was to load the dirty dishes where?"}}
{"answerKey": "E", "id": "adf228312401c9ff421a4da1b46bb70a", "question": {"question_concept": "bureau", "choices": [{"label": "A", "text": "each city"}, {"label": "B", "text": "office building"}, {"label": "C", "text": "a zoo"}, {"label": "D", "text": "french government"}, {"label": "E", "text": "washington dc"}], "stem": "Where could you find a bureau as well as many politicians?"}}
{"answerKey": "B", "id": "57c85e4c7ea2501ef9d8f304b524e2e4", "question": {"question_concept": "check", "choices": [{"label": "A", "text": "cash register"}, {"label": "B", "text": "desk drawer"}, {"label": "C", "text": "fish tank"}, {"label": "D", "text": "bank"}, {"label": "E", "text": "pay envelope"}], "stem": "Dad wanted to hide the check in his office, where did he put it?"}}
{"answerKey": "A", "id": "c22f30eee57f7191ee07e9a916460f68", "question": {"question_concept": "buying products", "choices": [{"label": "A", "text": "pleasure"}, {"label": "B", "text": "owning"}, {"label": "C", "text": "debt"}, {"label": "D", "text": "spending money"}, {"label": "E", "text": "smart"}], "stem": "For some reason she was devoid of regular emotions, buying products was the only way she could feel what?"}}
{"answerKey": "B", "id": "026cb9c07a583ec933f2c4c67ae73836", "question": {"question_concept": "horses", "choices": [{"label": "A", "text": "race track"}, {"label": "B", "text": "fair"}, {"label": "C", "text": "raised by humans"}, {"label": "D", "text": "in a field"}, {"label": "E", "text": "countryside"}], "stem": "Where are horses judged on appearance?"}}
{"answerKey": "C", "id": "c57ed32566a2db1ec3d6e4fd595b9d05", "question": {"question_concept": "read", "choices": [{"label": "A", "text": "having fun"}, {"label": "B", "text": "it's more relatable"}, {"label": "C", "text": "learn new things"}, {"label": "D", "text": "becoming absorbed"}, {"label": "E", "text": "falling asleep"}], "stem": "Why do people read non fiction?"}}
{"answerKey": "A", "id": "93b52e7ea1acf10db891e9355e234123", "question": {"question_concept": "knitting", "choices": [{"label": "A", "text": "listen to music"}, {"label": "B", "text": "watch television"}, {"label": "C", "text": "making blankets"}, {"label": "D", "text": "eat"}, {"label": "E", "text": "watching tv"}], "stem": "While knitting you can do what using a radio?"}}
{"answerKey": "A", "id": "dbdad44029098d4b1d202d6d857d6092", "question": {"question_concept": "papers", "choices": [{"label": "A", "text": "table"}, {"label": "B", "text": "meeting"}, {"label": "C", "text": "drawer"}, {"label": "D", "text": "toilet"}, {"label": "E", "text": "garage"}], "stem": "Where are you likely to set papers while working on them?"}}
{"answerKey": "C", "id": "69d0f70c173dda17934836d618ca7093", "question": {"question_concept": "massive", "choices": [{"label": "A", "text": "dwarf"}, {"label": "B", "text": "inconsequential"}, {"label": "C", "text": "insubstantial"}, {"label": "D", "text": "lame"}, {"label": "E", "text": "tiny"}], "stem": "John had a massive debt to 50 million dollars.  Compared to that, Leo's 2000 dollar debt seemed what?"}}
{"answerKey": "C", "id": "e5697a25935c5249d2108f55e245f3e4", "question": {"question_concept": "pollution", "choices": [{"label": "A", "text": "forest"}, {"label": "B", "text": "street"}, {"label": "C", "text": "air"}, {"label": "D", "text": "caused by humans"}, {"label": "E", "text": "car show"}], "stem": "The man flew his airplane over the city and saw pollution visibly in the sky, what was polluted?"}}
{"answerKey": "E", "id": "99af85081085e6228c6d78c95be01968", "question": {"question_concept": "becoming inebriated", "choices": [{"label": "A", "text": "fights"}, {"label": "B", "text": "drunkenness"}, {"label": "C", "text": "staggering"}, {"label": "D", "text": "puke"}, {"label": "E", "text": "paralysis"}], "stem": "What is a very unlikely side effect of becoming inebriated?"}}
{"answerKey": "B", "id": "235094c966bcbdc94701b41b969f9c75", "question": {"question_concept": "communicating", "choices": [{"label": "A", "text": "misunderstandings"}, {"label": "B", "text": "transfer of information"}, {"label": "C", "text": "learning"}, {"label": "D", "text": "confusion"}, {"label": "E", "text": "silence"}], "stem": "when communicating with my boss what should i do"}}
{"answerKey": "C", "id": "99789083502af9bf111876a00fae44ac", "question": {"question_concept": "fish", "choices": [{"label": "A", "text": "stream"}, {"label": "B", "text": "aquarium"}, {"label": "C", "text": "refrigerator"}, {"label": "D", "text": "boat ride"}, {"label": "E", "text": "market"}], "stem": "If not in a stream but in a market where will you find fish?"}}
{"answerKey": "E", "id": "1d44fb5f4b7f1e23ff6c1c083db81ba1", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "own land"}, {"label": "B", "text": "own home"}, {"label": "C", "text": "talk to each other"}, {"label": "D", "text": "believe in god"}, {"label": "E", "text": "spend time"}], "stem": "What are people likely to want to do with their friends?"}}
{"answerKey": "E", "id": "194b66240f6fab75749c1e30ed09ea09", "question": {"question_concept": "shark", "choices": [{"label": "A", "text": "marine museum"}, {"label": "B", "text": "pool hall"}, {"label": "C", "text": "noodle house"}, {"label": "D", "text": "bad movie"}, {"label": "E", "text": "outside"}], "stem": "During a shark filled tornado where should you not be?"}}
{"answerKey": "E", "id": "83dad4fe630fddbdcd5b18ef890c66f2", "question": {"question_concept": "buying products", "choices": [{"label": "A", "text": "running out of money"}, {"label": "B", "text": "spending money"}, {"label": "C", "text": "poverty"}, {"label": "D", "text": "comparison shopping"}, {"label": "E", "text": "overstocking"}], "stem": "What is the likely result of buying products in excess?"}}
{"answerKey": "B", "id": "3ebc5ddd2e97fe37fcb52aa2a9e2e1a7", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "acceptance"}, {"label": "B", "text": "avoid pain"}, {"label": "C", "text": "acknowledgment"}, {"label": "D", "text": "passing grade"}, {"label": "E", "text": "intellectual challenge"}], "stem": "What is a person trying to accomplish when taking analgesics?"}}
{"answerKey": "D", "id": "9ed019338a48216de9eadf64faaf1ce0", "question": {"question_concept": "glass", "choices": [{"label": "A", "text": "ocean"}, {"label": "B", "text": "water cooler"}, {"label": "C", "text": "cabinet"}, {"label": "D", "text": "dishwasher"}, {"label": "E", "text": "dining room"}], "stem": "Where would you put a glass after drinking from it?"}}
{"answerKey": "B", "id": "d1d2585e0ba1160948b7c5822a99b7a1", "question": {"question_concept": "food", "choices": [{"label": "A", "text": "freezer"}, {"label": "B", "text": "store"}, {"label": "C", "text": "home"}, {"label": "D", "text": "hatred"}, {"label": "E", "text": "kitchen"}], "stem": "Where would you buy food?"}}
{"answerKey": "E", "id": "e34a0d1331c6bd4574ffe308e3fbd389", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "accident"}, {"label": "B", "text": "learn to swim"}, {"label": "C", "text": "thank god"}, {"label": "D", "text": "feel relieved"}, {"label": "E", "text": "act responsibly"}], "stem": "When a person admits his mistakes, what are they doing?"}}
{"answerKey": "A", "id": "4858669d0193e5d9384dc37d4bb5c00c", "question": {"question_concept": "game", "choices": [{"label": "A", "text": "casino"}, {"label": "B", "text": "football ground"}, {"label": "C", "text": "ballpark"}, {"label": "D", "text": "family room"}, {"label": "E", "text": "toy store"}], "stem": "Where do play a game for money?"}}
{"answerKey": "E", "id": "8fd82cdc253835814153fe7222e9967c", "question": {"question_concept": "travel", "choices": [{"label": "A", "text": "go somewhere"}, {"label": "B", "text": "energy"}, {"label": "C", "text": "spend frivilously"}, {"label": "D", "text": "fly in airplane"}, {"label": "E", "text": "have money"}], "stem": "When you travel you should what in case of unexpected costs?"}}
{"answerKey": "E", "id": "66458bf8599c3ef1e7b50fa527531882", "question": {"question_concept": "government", "choices": [{"label": "A", "text": "everything"}, {"label": "B", "text": "capitol building"}, {"label": "C", "text": "tourist sites"}, {"label": "D", "text": "canada"}, {"label": "E", "text": "washington d.c"}], "stem": "Donald is a prominent figure for the federal government, so in what city does he likely spend a lot of time?"}}
{"answerKey": "C", "id": "879239b8a788f3c9e3dfdd0862f3d7c5", "question": {"question_concept": "bum", "choices": [{"label": "A", "text": "train station"}, {"label": "B", "text": "beach"}, {"label": "C", "text": "bus depot"}, {"label": "D", "text": "bridge"}, {"label": "E", "text": "stumblebum"}], "stem": "There was more than one bum asking for change or a ticket, it was the cheapest way to travel so it was no surprise sight at the what?"}}
{"answerKey": "D", "id": "8a69e6df5e8ad6c9e6828aa66c59d046", "question": {"question_concept": "playing game", "choices": [{"label": "A", "text": "anger"}, {"label": "B", "text": "good natured ribbing."}, {"label": "C", "text": "enjoying"}, {"label": "D", "text": "injury"}, {"label": "E", "text": "enjoyment"}], "stem": "John and Joe like planning games but Joe  was hit by a ball and fell down. What might have happened to Joe."}}
{"answerKey": "B", "id": "8d275acea05fd16295c659c504576a9b", "question": {"question_concept": "jeans", "choices": [{"label": "A", "text": "gap"}, {"label": "B", "text": "shopping mall"}, {"label": "C", "text": "bedroom"}, {"label": "D", "text": "laundromat"}, {"label": "E", "text": "bathroom"}], "stem": "Where can you buy jeans at one of may indoor merchants?"}}
{"answerKey": "D", "id": "91629c6f9e4af3e6acf385eb23fd8068", "question": {"question_concept": "letter", "choices": [{"label": "A", "text": "syllable"}, {"label": "B", "text": "post office"}, {"label": "C", "text": "envelope"}, {"label": "D", "text": "english alphabet"}, {"label": "E", "text": "word"}], "stem": "What do you write letter in in America?"}}
{"answerKey": "E", "id": "59eb56f366407ac7db72996be265883b", "question": {"question_concept": "back", "choices": [{"label": "A", "text": "anterior"}, {"label": "B", "text": "front"}, {"label": "C", "text": "main"}, {"label": "D", "text": "front"}, {"label": "E", "text": "current"}], "stem": "Joe owned back taxes as well as what other type of taxes?"}}
{"answerKey": "C", "id": "4ab069f2e979d51f2c5929f590d09982", "question": {"question_concept": "broadcast studio", "choices": [{"label": "A", "text": "microphone"}, {"label": "B", "text": "arena"}, {"label": "C", "text": "radio station"}, {"label": "D", "text": "trees"}, {"label": "E", "text": "town"}], "stem": "Where is a broadcast studio likely to be heard?"}}
{"answerKey": "B", "id": "d6bb990e8c409d2b3af37a2da198e01f", "question": {"question_concept": "book", "choices": [{"label": "A", "text": "counter"}, {"label": "B", "text": "coffee table"}, {"label": "C", "text": "school room"}, {"label": "D", "text": "backpack"}, {"label": "E", "text": "bedside table"}], "stem": "Kramer wrote a self-referential book.  What might that book be about?"}}
{"answerKey": "E", "id": "c5ad166ab5c5f5f067aa02b20f482523", "question": {"question_concept": "sports", "choices": [{"label": "A", "text": "very entertaining"}, {"label": "B", "text": "fun"}, {"label": "C", "text": "slow"}, {"label": "D", "text": "competitive"}, {"label": "E", "text": "violent"}], "stem": "Of all the sports, Billy enjoys football, but what does his concerned mother think of the sport?"}}
{"answerKey": "A", "id": "ceafca2445b1b974d085a8cce38e8e44", "question": {"question_concept": "parking structure", "choices": [{"label": "A", "text": "chicago"}, {"label": "B", "text": "big city"}, {"label": "C", "text": "large city building"}, {"label": "D", "text": "environment"}, {"label": "E", "text": "college campus"}], "stem": "What city will likely have many parking structures?"}}
{"answerKey": "D", "id": "2ef2ae21a2d3a9ecbd5c45ff378d10e3", "question": {"question_concept": "danger", "choices": [{"label": "A", "text": "fight enemy"}, {"label": "B", "text": "secure"}, {"label": "C", "text": "being safe"}, {"label": "D", "text": "safety"}, {"label": "E", "text": "vicinity"}], "stem": "Sally was afraid of danger and always double checked what?"}}
{"answerKey": "E", "id": "793672da43fbc609e8c5760630c7e239", "question": {"question_concept": "fox", "choices": [{"label": "A", "text": "hen house"}, {"label": "B", "text": "burrow"}, {"label": "C", "text": "california"}, {"label": "D", "text": "england"}, {"label": "E", "text": "mountains"}], "stem": "What is the habitat of the fox?"}}
{"answerKey": "B", "id": "558cb0bc25387ce38d71f64ef6f1fa57", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "eat eggs"}, {"label": "B", "text": "make tools"}, {"label": "C", "text": "eat dosa"}, {"label": "D", "text": "talk to each other"}, {"label": "E", "text": "smoke pot"}], "stem": "People are very much like the animals, but one thing has secured or dominance over the planet.  We're better at doing what?"}}
{"answerKey": "B", "id": "2c9f4a98ce774cd734b6e384d95051a7", "question": {"question_concept": "back yard", "choices": [{"label": "A", "text": "suburb"}, {"label": "B", "text": "neighborhood"}, {"label": "C", "text": "back of house"}, {"label": "D", "text": "roundabout"}, {"label": "E", "text": "property"}], "stem": "They children loved having a back yard, and the parents loved that it was a safe what?"}}
{"answerKey": "D", "id": "33c84708785f88c19737ef5b0e31a64b", "question": {"question_concept": "pail", "choices": [{"label": "A", "text": "garage"}, {"label": "B", "text": "utility room"}, {"label": "C", "text": "slide"}, {"label": "D", "text": "wishing well"}, {"label": "E", "text": "garden"}], "stem": "While people just throw coins down them now, what originally had a pail to be lowered for it's intended use?"}}
{"answerKey": "D", "id": "d867f76d000bdb59b9b4cb982bd7f0a0", "question": {"question_concept": "water", "choices": [{"label": "A", "text": "surface of earth"}, {"label": "B", "text": "teardrops"}, {"label": "C", "text": "snowflake"}, {"label": "D", "text": "typhoon"}, {"label": "E", "text": "motor"}], "stem": "Joe was thrown from his boat into the water.  The water was cold because it was the middle of winter and he cried out to his crew for help.  They couldn't hear him over the sound of the what?"}}
{"answerKey": "D", "id": "8c607d2e2e897d74048fcc794137b683", "question": {"question_concept": "human", "choices": [{"label": "A", "text": "deep thought"}, {"label": "B", "text": "park"}, {"label": "C", "text": "friend's house"}, {"label": "D", "text": "place of work"}, {"label": "E", "text": "school"}], "stem": "When a human is earning money, where are they often found?"}}
{"answerKey": "B", "id": "5215e26c99b2a9b376fb1c70096a388a", "question": {"question_concept": "apple tree", "choices": [{"label": "A", "text": "maryland"}, {"label": "B", "text": "indiana"}, {"label": "C", "text": "on tv"}, {"label": "D", "text": "park"}, {"label": "E", "text": "new jersey"}], "stem": "They passed a apple tree on their way to the racetrack, the were going to watch the biggest motorsport spectacle in the world where?"}}
{"answerKey": "B", "id": "668dc6bce771b10cbf6336f3ec76520a", "question": {"question_concept": "play chess", "choices": [{"label": "A", "text": "satisfaction"}, {"label": "B", "text": "have fun"}, {"label": "C", "text": "thrilling"}, {"label": "D", "text": "made"}, {"label": "E", "text": "smart"}], "stem": "Why do people play chess on the weekends?"}}
{"answerKey": "D", "id": "a339fe08f1f50463ee180b797e99ebcc", "question": {"question_concept": "energy", "choices": [{"label": "A", "text": "work"}, {"label": "B", "text": "tacos"}, {"label": "C", "text": "mass"}, {"label": "D", "text": "play sports"}, {"label": "E", "text": "wrestle"}], "stem": "What do you need energy to do in gym class?"}}
{"answerKey": "A", "id": "526cd34f5b2afefbbb7830434785f298", "question": {"question_concept": "marble", "choices": [{"label": "A", "text": "game"}, {"label": "B", "text": "pouch"}, {"label": "C", "text": "home"}, {"label": "D", "text": "store"}, {"label": "E", "text": "jar"}], "stem": "Sarah dropped the marble because she wanted to do what?"}}
{"answerKey": "E", "id": "6c1c1c282cebe8917f607f0dbc1c102e", "question": {"question_concept": "human", "choices": [{"label": "A", "text": "write"}, {"label": "B", "text": "eat cake"}, {"label": "C", "text": "smile"}, {"label": "D", "text": "think critically"}, {"label": "E", "text": "die"}], "stem": "We are all human, and we all what?"}}
{"answerKey": "D", "id": "b5baf77d3855935c87f01f5fb2216667", "question": {"question_concept": "going to bed", "choices": [{"label": "A", "text": "lazy"}, {"label": "B", "text": "insomnia"}, {"label": "C", "text": "rest"}, {"label": "D", "text": "falling asleep"}, {"label": "E", "text": "dreaming of"}], "stem": "If a person were going to bed, what would be their goal?"}}
{"answerKey": "E", "id": "83808e92381b2e5f4cdf55d1391645ae", "question": {"question_concept": "candle", "choices": [{"label": "A", "text": "shelf"}, {"label": "B", "text": "board"}, {"label": "C", "text": "church"}, {"label": "D", "text": "table"}, {"label": "E", "text": "dark"}], "stem": "What are candles good for eliminating?"}}
{"answerKey": "B", "id": "1a86310d7279097205a3403752c3b914", "question": {"question_concept": "death", "choices": [{"label": "A", "text": "poisonous gas"}, {"label": "B", "text": "homicide"}, {"label": "C", "text": "cinder"}, {"label": "D", "text": "nuclear weapons"}, {"label": "E", "text": "cyanide"}], "stem": "WHat leads to an early death?"}}
{"answerKey": "A", "id": "b4130d1790948134f3aeab9d3d79c181", "question": {"question_concept": "bookcase", "choices": [{"label": "A", "text": "study"}, {"label": "B", "text": "house"}, {"label": "C", "text": "homw"}, {"label": "D", "text": "kitchen"}, {"label": "E", "text": "den"}], "stem": "What room would you find many bookcases and is used for contemplation?"}}
{"answerKey": "C", "id": "a5097b7f56d20217679f28201801476f", "question": {"question_concept": "star", "choices": [{"label": "A", "text": "night sky"}, {"label": "B", "text": "galaxy"}, {"label": "C", "text": "outer space"}, {"label": "D", "text": "hollywood"}, {"label": "E", "text": "eat cake"}], "stem": "Where do you head to travel to a star?"}}
{"answerKey": "C", "id": "bcc5dd6292a64d8fa17cd07c360b335d", "question": {"question_concept": "cornet", "choices": [{"label": "A", "text": "museum"}, {"label": "B", "text": "high school band"}, {"label": "C", "text": "marching band"}, {"label": "D", "text": "orchestra"}, {"label": "E", "text": "band"}], "stem": "The player lifted his cornet and walked in rhythm, what was the player a member of?"}}
{"answerKey": "B", "id": "cfc7fccb8449a2a950c9d2a50991420e", "question": {"question_concept": "living", "choices": [{"label": "A", "text": "expiration"}, {"label": "B", "text": "growing older"}, {"label": "C", "text": "sometimes bad"}, {"label": "D", "text": "death"}, {"label": "E", "text": "start reproduction"}], "stem": "What happens at soon as a living being is born?"}}
{"answerKey": "B", "id": "2e83c5989a018bec6d5f5ac7d3b72f49", "question": {"question_concept": "talking", "choices": [{"label": "A", "text": "walking"}, {"label": "B", "text": "ask question"}, {"label": "C", "text": "think"}, {"label": "D", "text": "write question in crayon"}, {"label": "E", "text": "sneeze"}], "stem": "When someone is talking and you missed something, what can you do to get them to repeat it?"}}
{"answerKey": "A", "id": "34b2d6aecdb5af8efacf0b0aa7e3989f", "question": {"question_concept": "fabric", "choices": [{"label": "A", "text": "sewing room"}, {"label": "B", "text": "clothing store"}, {"label": "C", "text": "tailor shop"}, {"label": "D", "text": "clothes store"}, {"label": "E", "text": "cotton mill"}], "stem": "Where does one store fabric in their own home?"}}
{"answerKey": "A", "id": "2ec7f8fe7948f9997e73f9bff7ba6e05", "question": {"question_concept": "want", "choices": [{"label": "A", "text": "oversupply"}, {"label": "B", "text": "plentitude"}, {"label": "C", "text": "stockpile"}, {"label": "D", "text": "superabundance"}, {"label": "E", "text": "busy"}], "stem": "What do most companies not want to have relative to demand?"}}
{"answerKey": "A", "id": "651785ed4f7b0bd2e7ca9f70a42acea5", "question": {"question_concept": "playing basketball", "choices": [{"label": "A", "text": "sweating"}, {"label": "B", "text": "pain"}, {"label": "C", "text": "having fun"}, {"label": "D", "text": "medium"}, {"label": "E", "text": "knee injury"}], "stem": "What is happening while he's playing basketball for such a long time?"}}
{"answerKey": "A", "id": "ee46995407eb6357bb5410d49d378629", "question": {"question_concept": "traveller", "choices": [{"label": "A", "text": "bus stop"}, {"label": "B", "text": "library"}, {"label": "C", "text": "motel"}, {"label": "D", "text": "airport"}, {"label": "E", "text": "subway"}], "stem": "A traveler laments the fact that mass transit is limited in his city when his groceries get soaked by the rain as he waits where?"}}
{"answerKey": "C", "id": "303aedda3a5ab8d853cbe4edc4b914c6", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "synagogue"}, {"label": "B", "text": "for help"}, {"label": "C", "text": "hospital"}, {"label": "D", "text": "bus stop"}, {"label": "E", "text": "building"}], "stem": "The person was in physical distress, where should he go?"}}
{"answerKey": "B", "id": "720b98fbc365736597147c984f6bd301", "question": {"question_concept": "die", "choices": [{"label": "A", "text": "not to live"}, {"label": "B", "text": "write will"}, {"label": "C", "text": "never want"}, {"label": "D", "text": "seek help"}, {"label": "E", "text": "go to hell"}], "stem": "The cancer patient was expecting to die, so he made out his what?"}}
{"answerKey": "B", "id": "c611875b43b67b91030b889b267bbcb3", "question": {"question_concept": "toll road", "choices": [{"label": "A", "text": "massachusetts"}, {"label": "B", "text": "new england"}, {"label": "C", "text": "my house"}, {"label": "D", "text": "new jersey"}, {"label": "E", "text": "connecticut"}], "stem": "There was a toll road that meandered from Maine to New Hampshire, where was it?"}}
{"answerKey": "A", "id": "0547da29ffab9b441bae8870cd0f9dab", "question": {"question_concept": "beginning work", "choices": [{"label": "A", "text": "getting tired"}, {"label": "B", "text": "working"}, {"label": "C", "text": "procrastination"}, {"label": "D", "text": "jumping"}, {"label": "E", "text": "sitting down"}], "stem": "If you partied all night you could find yourself already what, even when just beginning work?"}}
{"answerKey": "A", "id": "21e312c7fd1a52341ce35b66457eab36", "question": {"question_concept": "cat", "choices": [{"label": "A", "text": "get wet"}, {"label": "B", "text": "eat vegetables"}, {"label": "C", "text": "falling"}, {"label": "D", "text": "wool sweater"}, {"label": "E", "text": "sharp claws"}], "stem": "The cat carefully navigated the area, they do everything they can to avoid what?"}}
{"answerKey": "A", "id": "82e26bc22af89c38d54aa2d00dcb8a2b", "question": {"question_concept": "talking to", "choices": [{"label": "A", "text": "listening"}, {"label": "B", "text": "language"}, {"label": "C", "text": "looking at eyes"}, {"label": "D", "text": "planning the perfect murder"}, {"label": "E", "text": "voice"}], "stem": "What is someone usually doing if someone else is talking to him or her?"}}
{"answerKey": "E", "id": "f75357e48c3026cfa4da3dba9f91bb21", "question": {"question_concept": "sky", "choices": [{"label": "A", "text": "appear beautiful"}, {"label": "B", "text": "appear blue"}, {"label": "C", "text": "shows a rainbow"}, {"label": "D", "text": "rain water"}, {"label": "E", "text": "cloud over"}], "stem": "What does the sky do before a rain?"}}
{"answerKey": "B", "id": "64931f9097155672bfe3e16f03b2c195", "question": {"question_concept": "paper clips", "choices": [{"label": "A", "text": "desktop"}, {"label": "B", "text": "university"}, {"label": "C", "text": "drawer"}, {"label": "D", "text": "table"}, {"label": "E", "text": "work"}], "stem": "Pens, computers, text books and paper clips can all be found where?"}}
{"answerKey": "B", "id": "5de3248caa2e5ed83dd0ec45a15eae18", "question": {"question_concept": "lizard", "choices": [{"label": "A", "text": "ball stopped"}, {"label": "B", "text": "west texas"}, {"label": "C", "text": "arid regions"}, {"label": "D", "text": "garden"}, {"label": "E", "text": "warm place"}], "stem": "What geographic area is a lizard likely to be?"}}
{"answerKey": "E", "id": "0611dfbf5114084723d75f59b4f67412", "question": {"question_concept": "briefcase", "choices": [{"label": "A", "text": "office building"}, {"label": "B", "text": "school"}, {"label": "C", "text": "courtroom"}, {"label": "D", "text": "airport"}, {"label": "E", "text": "hand"}], "stem": "What do you use to carry your briefcase?"}}
{"answerKey": "E", "id": "5b8d76889510384b38b72945e8d28f53", "question": {"question_concept": "run", "choices": [{"label": "A", "text": "learn to walk"}, {"label": "B", "text": "frightened"}, {"label": "C", "text": "get away from"}, {"label": "D", "text": "exercise"}, {"label": "E", "text": "go faster"}], "stem": "He picked up his pace to a run, he wanted to do what?"}}
{"answerKey": "D", "id": "d81f5c49bc060dc799681bf4cacac73a", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "talk to people"}, {"label": "B", "text": "try again"}, {"label": "C", "text": "fall asleep"}, {"label": "D", "text": "stand alone"}, {"label": "E", "text": "thank god"}], "stem": "What would a person do if they do not have any friends?"}}
{"answerKey": "D", "id": "aaf4fa38433c84b3bd0a86551259ce62", "question": {"question_concept": "dying", "choices": [{"label": "A", "text": "change of color"}, {"label": "B", "text": "stop breathing"}, {"label": "C", "text": "wake up"}, {"label": "D", "text": "death and decay"}, {"label": "E", "text": "getting cold"}], "stem": "As a result of dying, what happens to organic material?"}}
{"answerKey": "A", "id": "33ea932a876ac0361c9eefeff1d24e92", "question": {"question_concept": "everyone", "choices": [{"label": "A", "text": "feelings"}, {"label": "B", "text": "food"}, {"label": "C", "text": "unique personality"}, {"label": "D", "text": "different standards"}, {"label": "E", "text": "values"}], "stem": "What does everyone have in relation to other people?"}}
{"answerKey": "D", "id": "aead08289ca9abfcd169f935ea228ee5", "question": {"question_concept": "child", "choices": [{"label": "A", "text": "ask questions"}, {"label": "B", "text": "count to ten"}, {"label": "C", "text": "costume"}, {"label": "D", "text": "state name"}, {"label": "E", "text": "dress herself"}], "stem": "What do you ask a child to do when you first meet her?"}}
{"answerKey": "E", "id": "adbddc80b10bf25f09c6c2bee4e3c59b", "question": {"question_concept": "clock", "choices": [{"label": "A", "text": "counter"}, {"label": "B", "text": "train station"}, {"label": "C", "text": "school room"}, {"label": "D", "text": "desk"}, {"label": "E", "text": "department store"}], "stem": "Where can you buy a clock, clothing and wrenches?"}}
{"answerKey": "B", "id": "1caf93d6a22dc8190e19c14bbe1fafda", "question": {"question_concept": "see new", "choices": [{"label": "A", "text": "interesting"}, {"label": "B", "text": "look around"}, {"label": "C", "text": "take pictures"}, {"label": "D", "text": "change of surroundings"}, {"label": "E", "text": "new experience"}], "stem": "What do you do when you're in a new place and want to see new things?"}}
{"answerKey": "B", "id": "0bf4d64ad0eee7224acb3a4eb85accb2", "question": {"question_concept": "ice", "choices": [{"label": "A", "text": "carved"}, {"label": "B", "text": "melted"}, {"label": "C", "text": "ice cream"}, {"label": "D", "text": "antarctica"}, {"label": "E", "text": "sculptured"}], "stem": "What happens when to ice when it is in the sun?"}}
{"answerKey": "B", "id": "b93532cae23e505628dd88568da3337e", "question": {"question_concept": "dishes", "choices": [{"label": "A", "text": "drawer"}, {"label": "B", "text": "shelf"}, {"label": "C", "text": "pantry"}, {"label": "D", "text": "apartment"}, {"label": "E", "text": "cabinet"}], "stem": "Where can you store your dishes in your dwelling?"}}
{"answerKey": "B", "id": "2d3c9d3dff1a7a8253180cb3de1ceeea", "question": {"question_concept": "moss", "choices": [{"label": "A", "text": "niagra falls"}, {"label": "B", "text": "forest"}, {"label": "C", "text": "waterfall"}, {"label": "D", "text": "ground"}, {"label": "E", "text": "tree"}], "stem": "The man laid on the soft moss and looked up at the trees, where was the man?"}}
{"answerKey": "A", "id": "70701f5d1d62e58d5c74e2e303bb4065", "question": {"question_concept": "sitting quietly", "choices": [{"label": "A", "text": "reading"}, {"label": "B", "text": "meditate"}, {"label": "C", "text": "fall asleep"}, {"label": "D", "text": "bunk"}, {"label": "E", "text": "think"}], "stem": "What is someone doing if he or she is sitting quietly and his or her eyes are moving?"}}
{"answerKey": "B", "id": "eacd87f297193033669a93160ae3776f", "question": {"question_concept": "stapler", "choices": [{"label": "A", "text": "desk drawer"}, {"label": "B", "text": "office building"}, {"label": "C", "text": "manual"}, {"label": "D", "text": "office supply store"}, {"label": "E", "text": "desktop"}], "stem": "Where can I find a stapler in many places?"}}
{"answerKey": "B", "id": "8e1b0792e441a5d54ae47a4b24f48977", "question": {"question_concept": "seat", "choices": [{"label": "A", "text": "in cinema"}, {"label": "B", "text": "martorell"}, {"label": "C", "text": "falling down"}, {"label": "D", "text": "show"}, {"label": "E", "text": "airplane"}], "stem": "A man takes a seat at a museum outside of Barcelona, where is he likely?"}}
{"answerKey": "C", "id": "b4cde6a56fb19afc84876ebf2fb9e71a", "question": {"question_concept": "toy soldier", "choices": [{"label": "A", "text": "toy box"}, {"label": "B", "text": "movies"}, {"label": "C", "text": "child's hand"}, {"label": "D", "text": "toybos"}, {"label": "E", "text": "child park"}], "stem": "Where would you find a toy soldier that is being played with?"}}
{"answerKey": "C", "id": "095c5bc5fbaf12b384e9f7df47fdec16", "question": {"question_concept": "plane ticket", "choices": [{"label": "A", "text": "pocket"}, {"label": "B", "text": "terrorists hands"}, {"label": "C", "text": "airport"}, {"label": "D", "text": "sea ship"}, {"label": "E", "text": "briefcase"}], "stem": "Where are you when you're about to use your plane ticket?"}}
{"answerKey": "B", "id": "494c501dbbfd36c602aae9e5b8e0cfff", "question": {"question_concept": "flowers", "choices": [{"label": "A", "text": "market"}, {"label": "B", "text": "table"}, {"label": "C", "text": "countryside"}, {"label": "D", "text": "anthology"}, {"label": "E", "text": "vase"}], "stem": "Flowers make a good center focal point, just one of many arrangements that look good on a what?"}}
{"answerKey": "D", "id": "5a7f6fd97b2c9ad05f773bc8b2ecf441", "question": {"question_concept": "river", "choices": [{"label": "A", "text": "wisconsin"}, {"label": "B", "text": "waterfall"}, {"label": "C", "text": "hatred"}, {"label": "D", "text": "bridge"}, {"label": "E", "text": "valley"}], "stem": "How can a human cross a river and not mess up their hair?"}}
{"answerKey": "D", "id": "5279a2ea333ba8a5bf3a7637a7279da1", "question": {"question_concept": "beer", "choices": [{"label": "A", "text": "shelf"}, {"label": "B", "text": "soccer game"}, {"label": "C", "text": "keg"}, {"label": "D", "text": "can"}, {"label": "E", "text": "refrigerator"}], "stem": "Batman bought beer.  There were no bottles available.  He had to settle for what?."}}
{"answerKey": "E", "id": "42c46e28baf0fc617a07419286178c0a", "question": {"question_concept": "monkey", "choices": [{"label": "A", "text": "south american country"}, {"label": "B", "text": "rain forest"}, {"label": "C", "text": "pay debts"}, {"label": "D", "text": "works"}, {"label": "E", "text": "nigeria"}], "stem": "You can find a monkey in what West African region on the Gulf of Guinea"}}
{"answerKey": "B", "id": "c76304b4962f94ab9f20f09cf4a1a7c1", "question": {"question_concept": "surprising", "choices": [{"label": "A", "text": "humor"}, {"label": "B", "text": "fight"}, {"label": "C", "text": "jocose"}, {"label": "D", "text": "laughter"}, {"label": "E", "text": "accidents"}], "stem": "Surprising an angry person could lead to what?"}}
{"answerKey": "B", "id": "8b23cd355ffc8b6e7aa5459ffb21b4e0", "question": {"question_concept": "dining area", "choices": [{"label": "A", "text": "cruise ship"}, {"label": "B", "text": "home"}, {"label": "C", "text": "mall"}, {"label": "D", "text": "restaurant"}, {"label": "E", "text": "dark cave"}], "stem": "Where is a dining area likely to be small?"}}
{"answerKey": "C", "id": "c35f7de9e9005fcf654cb0b23f17acd6", "question": {"question_concept": "killing people", "choices": [{"label": "A", "text": "vengeance"}, {"label": "B", "text": "going to prison"}, {"label": "C", "text": "joy"}, {"label": "D", "text": "afraid"}, {"label": "E", "text": "terrible"}], "stem": "Killing people should not cause what emotion?"}}
{"answerKey": "C", "id": "d910859b9d1acae40456dbeaa8334bc0", "question": {"question_concept": "playing football", "choices": [{"label": "A", "text": "exhilaration"}, {"label": "B", "text": "interactions"}, {"label": "C", "text": "head injuries"}, {"label": "D", "text": "death"}, {"label": "E", "text": "having fun"}], "stem": "James slamed into someone playing football, and not for the first time.  He was concerned about the consequences of many what?"}}
{"answerKey": "A", "id": "6ca8439d062de4d43d7d471c508b78db", "question": {"question_concept": "have fun", "choices": [{"label": "A", "text": "enjoy living"}, {"label": "B", "text": "happy"}, {"label": "C", "text": "enjoyable"}, {"label": "D", "text": "get laid"}, {"label": "E", "text": "do enjoy"}], "stem": "More people should lower the guard and just have fun, we don't got long just what?"}}
{"answerKey": "C", "id": "ddd8c62ec94b4f94eeefdd05b9208a71", "question": {"question_concept": "lizard", "choices": [{"label": "A", "text": "desert country"}, {"label": "B", "text": "dessert"}, {"label": "C", "text": "pet shop"}, {"label": "D", "text": "tropical areas"}, {"label": "E", "text": "zoo"}], "stem": "Where can you get a lizard to keep in your home?"}}
{"answerKey": "C", "id": "72b638200414a526b598de0e01a044df", "question": {"question_concept": "musical instrument", "choices": [{"label": "A", "text": "guitar"}, {"label": "B", "text": "music room"}, {"label": "C", "text": "orchestra"}, {"label": "D", "text": "case"}, {"label": "E", "text": "movie"}], "stem": "What would use a musical instrument?"}}
{"answerKey": "B", "id": "c770870c88f35f9d110217049c5a7334", "question": {"question_concept": "affair", "choices": [{"label": "A", "text": "relationship"}, {"label": "B", "text": "marriage"}, {"label": "C", "text": "fidelity"}, {"label": "D", "text": "love"}, {"label": "E", "text": "divorce"}], "stem": "She was in an affair, what did that end upon discovery by her husband?"}}
{"answerKey": "B", "id": "1d8d9e3504c8c58a3b923ddc155c19b0", "question": {"question_concept": "earth", "choices": [{"label": "A", "text": "one moon"}, {"label": "B", "text": "milky way"}, {"label": "C", "text": "god's creation"}, {"label": "D", "text": "stars"}, {"label": "E", "text": "universe"}], "stem": "What is the most famous constellation out of earth?"}}
{"answerKey": "C", "id": "95acebea992a26c3a7c3bfb45845fa83", "question": {"question_concept": "reception", "choices": [{"label": "A", "text": "room service"}, {"label": "B", "text": "church basement"}, {"label": "C", "text": "lobby"}, {"label": "D", "text": "large room"}, {"label": "E", "text": "country club"}], "stem": "If a reception is held with hotel guests walking by, what is the likely venue?"}}
{"answerKey": "B", "id": "c2c2a387fd9a6a26cff636008de21f71", "question": {"question_concept": "beer", "choices": [{"label": "A", "text": "refrigerator"}, {"label": "B", "text": "friend's house"}, {"label": "C", "text": "keg"}, {"label": "D", "text": "neighbor's house"}, {"label": "E", "text": "kitchen"}], "stem": "What is a place that is far away from your house and where you could consume beer?"}}
{"answerKey": "A", "id": "57e96118fee6e2bbac5f59790fc833c0", "question": {"question_concept": "hearing testimony", "choices": [{"label": "A", "text": "change of heart"}, {"label": "B", "text": "anguish"}, {"label": "C", "text": "anger"}, {"label": "D", "text": "boredom"}, {"label": "E", "text": "anxiety"}], "stem": "If a court case is dismissed after hearing testimony, what would be a likely cause?"}}
{"answerKey": "A", "id": "b9b82aa4c236cd342ff95455b8516a42", "question": {"question_concept": "sitting down", "choices": [{"label": "A", "text": "flatulence"}, {"label": "B", "text": "happiness"}, {"label": "C", "text": "laziness"}, {"label": "D", "text": "fall asleep"}, {"label": "E", "text": "comfort"}], "stem": "Sitting down quickly after eating beans could lead to what?"}}
{"answerKey": "B", "id": "41fac392c6a5827c1b6682d5d3798e59", "question": {"question_concept": "neighbour", "choices": [{"label": "A", "text": "away"}, {"label": "B", "text": "distant"}, {"label": "C", "text": "remote person"}, {"label": "D", "text": "bore"}, {"label": "E", "text": "foe"}], "stem": "John was my neighbor, it was easy to talk to him. He was never what?"}}
{"answerKey": "B", "id": "5c224410a40c9269b1e542cfcb430d35", "question": {"question_concept": "cup of coffee", "choices": [{"label": "A", "text": "table"}, {"label": "B", "text": "office"}, {"label": "C", "text": "desk"}, {"label": "D", "text": "kitchen"}, {"label": "E", "text": "ocean"}], "stem": "Where do people want to have a lot of coffee?"}}
{"answerKey": "E", "id": "0b90c6710a65eb55fea4cc92895bf601", "question": {"question_concept": "have food", "choices": [{"label": "A", "text": "stay alive"}, {"label": "B", "text": "wanted to survive"}, {"label": "C", "text": "nutrition"}, {"label": "D", "text": "grew"}, {"label": "E", "text": "full"}], "stem": "You stop and have food all around you, what are you?"}}
{"answerKey": "A", "id": "70af2b5df22ec96901350dfa3c9ee74f", "question": {"question_concept": "meeting friend", "choices": [{"label": "A", "text": "relaxation"}, {"label": "B", "text": "panic"}, {"label": "C", "text": "alarm"}, {"label": "D", "text": "joy"}, {"label": "E", "text": "cheer"}], "stem": "James was meeting a friend.  They had planed a slow day. They didn't want to do much.  They just wanted what?"}}
{"answerKey": "D", "id": "f9243ef9f0037657c337d3c6a9832f05", "question": {"question_concept": "loose", "choices": [{"label": "A", "text": "sturdy"}, {"label": "B", "text": "faithful"}, {"label": "C", "text": "bound"}, {"label": "D", "text": "compact"}, {"label": "E", "text": "packaged"}], "stem": "The car's steering seem quite loose, but he still considered purchasing it because he needed something small and what?"}}
{"answerKey": "C", "id": "27f2074270ea8a5e8f5ec2a017ec4a50", "question": {"question_concept": "heifer", "choices": [{"label": "A", "text": "arizona"}, {"label": "B", "text": "farm yard"}, {"label": "C", "text": "michigan"}, {"label": "D", "text": "german field"}, {"label": "E", "text": "dairy farm"}], "stem": "Dan was a farmer with just one heifer.  But that was okay, he only kept her for milk, and he didn't think he'd find good farmland in a place as cold as where?"}}
{"answerKey": "B", "id": "63b3652d54c8c0e571f6bb50de318bf0", "question": {"question_concept": "going to bed", "choices": [{"label": "A", "text": "hatred"}, {"label": "B", "text": "sleeping in"}, {"label": "C", "text": "rest"}, {"label": "D", "text": "making love"}, {"label": "E", "text": "insomnia"}], "stem": "It's Friday night and Alice puts off going to bed because she plans on doing what Saturday?"}}
{"answerKey": "C", "id": "0843c51212a3c2eee660fab5648c9e19", "question": {"question_concept": "expressway", "choices": [{"label": "A", "text": "eastern united states"}, {"label": "B", "text": "michigan"}, {"label": "C", "text": "map"}, {"label": "D", "text": "choppers"}, {"label": "E", "text": "american city"}], "stem": "His phone was dead and they couldn't find the expressway, he opened up the glove compartment and handed his passenger the what to navigate?"}}
{"answerKey": "C", "id": "1b3d286458a7e7f069222de0376d06da", "question": {"question_concept": "key", "choices": [{"label": "A", "text": "car stand"}, {"label": "B", "text": "at hotel"}, {"label": "C", "text": "own home"}, {"label": "D", "text": "front door"}, {"label": "E", "text": "bus depot"}], "stem": "What would someone use a personal key for?"}}
{"answerKey": "C", "id": "86e2aabfb9d401567f04d87a648ff776", "question": {"question_concept": "cat", "choices": [{"label": "A", "text": "litter tray"}, {"label": "B", "text": "whiskers"}, {"label": "C", "text": "hungry"}, {"label": "D", "text": "feline"}, {"label": "E", "text": "thirsty"}], "stem": "The cat kept pestering it's owner, it was that time of the day and it was what?"}}
{"answerKey": "C", "id": "092c24369367b3c7457198f3ce160fe3", "question": {"question_concept": "alto", "choices": [{"label": "A", "text": "symphony"}, {"label": "B", "text": "concerto"}, {"label": "C", "text": "choir"}, {"label": "D", "text": "theater troupe"}, {"label": "E", "text": "marching band"}], "stem": "Her voice lent her to the alto section, what group did she join?"}}
{"answerKey": "A", "id": "cab9eea2a91b1bd5c0a01b11f594f154", "question": {"question_concept": "japanese restaurant", "choices": [{"label": "A", "text": "california"}, {"label": "B", "text": "downtown"}, {"label": "C", "text": "large town"}, {"label": "D", "text": "tokio"}, {"label": "E", "text": "china town"}], "stem": "Where are you likely to find a Japanese restaurant not run by people from Japan?"}}
{"answerKey": "C", "id": "6e77de03bee86d6c20780e14f00944d0", "question": {"question_concept": "animals", "choices": [{"label": "A", "text": "reproduce asexually"}, {"label": "B", "text": "males"}, {"label": "C", "text": "mammals"}, {"label": "D", "text": "attack"}, {"label": "E", "text": "ocean"}], "stem": "Animals who have hair and don't lay eggs are what?"}}
{"answerKey": "B", "id": "7f25dbab26165b3c8800c2733ca759d6", "question": {"question_concept": "fox", "choices": [{"label": "A", "text": "england"}, {"label": "B", "text": "new hampshire"}, {"label": "C", "text": "street"}, {"label": "D", "text": "arkansas"}, {"label": "E", "text": "north dakota"}], "stem": "John was an aristocratic fox hunter.  Where might he live?"}}
{"answerKey": "E", "id": "9024493a3edbaf555fda5b477e835bf5", "question": {"question_concept": "grape", "choices": [{"label": "A", "text": "field"}, {"label": "B", "text": "bathroom"}, {"label": "C", "text": "michigan"}, {"label": "D", "text": "minnesota"}, {"label": "E", "text": "painting"}], "stem": "Where is a grape likely to be being fed to someone else?"}}
{"answerKey": "D", "id": "fc59ab1a9e6d2b51126dd828d30e9167", "question": {"question_concept": "food", "choices": [{"label": "A", "text": "shop"}, {"label": "B", "text": "bookcase"}, {"label": "C", "text": "shelf"}, {"label": "D", "text": "refrigerators"}, {"label": "E", "text": "kitchen"}], "stem": "Some food can be stored at room temperature until you open it, then you should keep it in what?"}}
{"answerKey": "E", "id": "5a50ea4bb2d13dc4f620ebd45025d445", "question": {"question_concept": "dream", "choices": [{"label": "A", "text": "awake"}, {"label": "B", "text": "horror"}, {"label": "C", "text": "dreamworker"}, {"label": "D", "text": "reality"}, {"label": "E", "text": "nightmare"}], "stem": "Sam couldn't get back to sleep because of a dream he had.  It was a what?"}}
{"answerKey": "B", "id": "8becd2ee4e86258566a9c2b0e6d9544e", "question": {"question_concept": "going to party", "choices": [{"label": "A", "text": "getting drunk"}, {"label": "B", "text": "making new friends"}, {"label": "C", "text": "new contacts"}, {"label": "D", "text": "doing drugs"}, {"label": "E", "text": "set home"}], "stem": "If you're going to a party in a new town what are you hoping to make?"}}
{"answerKey": "E", "id": "2a21820a135e1a49883525c055c74a0b", "question": {"question_concept": "riding bike", "choices": [{"label": "A", "text": "practice"}, {"label": "B", "text": "sense of balance"}, {"label": "C", "text": "driving"}, {"label": "D", "text": "good balance"}, {"label": "E", "text": "pedalling"}], "stem": "How is riding a bike getting it to move?"}}
{"answerKey": "D", "id": "e5adfec0b5ba691ec752f9b5e0fb8084", "question": {"question_concept": "literature", "choices": [{"label": "A", "text": "books and magazines"}, {"label": "B", "text": "own home"}, {"label": "C", "text": "kitchen"}, {"label": "D", "text": "shelf"}, {"label": "E", "text": "meeting"}], "stem": "Where does one usually keep literature?"}}
{"answerKey": "E", "id": "406e15b76269d20b5448a91648094291", "question": {"question_concept": "keyboard", "choices": [{"label": "A", "text": "killing"}, {"label": "B", "text": "typewriter"}, {"label": "C", "text": "office"}, {"label": "D", "text": "terminal"}, {"label": "E", "text": "organ"}], "stem": "WHat type of keyboard is made up of one or more pipe divisions?"}}
{"answerKey": "C", "id": "9c596382ea15768f95b5ef9ceec191dc", "question": {"question_concept": "bell", "choices": [{"label": "A", "text": "run away"}, {"label": "B", "text": "wind instrument"}, {"label": "C", "text": "funnel"}, {"label": "D", "text": "blunderbuss"}, {"label": "E", "text": "associated with telephones"}], "stem": "The bell rang, and the congregation began to what in to the church?"}}
{"answerKey": "A", "id": "7a3d0c94438a5c8a09364aaebb848a2c", "question": {"question_concept": "smooth", "choices": [{"label": "A", "text": "rough"}, {"label": "B", "text": "non smooth"}, {"label": "C", "text": "uneven"}, {"label": "D", "text": "plastic"}, {"label": "E", "text": "bumpy"}], "stem": "James needed smooth sandpaper, but instead he got what type?"}}
{"answerKey": "B", "id": "1ef68db97654f30cd3701b942fadc934", "question": {"question_concept": "furniture", "choices": [{"label": "A", "text": "sewer"}, {"label": "B", "text": "neighbor's house"}, {"label": "C", "text": "apartment"}, {"label": "D", "text": "room"}, {"label": "E", "text": "floor"}], "stem": "Where would you borrow furniture if you do not have any?"}}
{"answerKey": "C", "id": "abb090bbc572be1016bcd5f261f28e76", "question": {"question_concept": "living", "choices": [{"label": "A", "text": "death"}, {"label": "B", "text": "flying"}, {"label": "C", "text": "reproducing"}, {"label": "D", "text": "food consumed"}, {"label": "E", "text": "eventually die"}], "stem": "What must happen for an animal to and it's offspring to continue livng?"}}
{"answerKey": "E", "id": "91f2532a832a35cba1b08a3c767be6da", "question": {"question_concept": "darkness", "choices": [{"label": "A", "text": "movies"}, {"label": "B", "text": "bed"}, {"label": "C", "text": "moon"}, {"label": "D", "text": "vault"}, {"label": "E", "text": "cellar"}], "stem": "I want my wine stored in darkness, where should it go?"}}
{"answerKey": "E", "id": "f8544c9679d27b747dfad3b8d7aac87a", "question": {"question_concept": "steakhouse", "choices": [{"label": "A", "text": "michigan"}, {"label": "B", "text": "florida"}, {"label": "C", "text": "wine"}, {"label": "D", "text": "texas"}, {"label": "E", "text": "building"}], "stem": "If I want to open a steakhouse, what should I get first?"}}
{"answerKey": "C", "id": "a7f423c1636ba9e36d18e381928c5dcc", "question": {"question_concept": "play", "choices": [{"label": "A", "text": "serious"}, {"label": "B", "text": "longplay"}, {"label": "C", "text": "musical"}, {"label": "D", "text": "eat cake"}, {"label": "E", "text": "doing nothing"}], "stem": "Sarah didn't like to play but she didn't want to be sedentary and bored, either, so she took up what?"}}
{"answerKey": "D", "id": "e1d354cbfcd620e5dacf83c17746c4b3", "question": {"question_concept": "spiders", "choices": [{"label": "A", "text": "cupboard"}, {"label": "B", "text": "closet"}, {"label": "C", "text": "storage bag"}, {"label": "D", "text": "mail box"}, {"label": "E", "text": "garage"}], "stem": "Joe found spiders while checking something outside.  What might that be?"}}
{"answerKey": "A", "id": "53e1e50d204f6ad5a0f69429eadae82e", "question": {"question_concept": "date", "choices": [{"label": "A", "text": "wait for"}, {"label": "B", "text": "bathe"}, {"label": "C", "text": "go for haircut"}, {"label": "D", "text": "plan revenge"}, {"label": "E", "text": "dress nice"}], "stem": "What would you do if your date does not show up?"}}
{"answerKey": "B", "id": "48205cc84aab5e455b22e17c3cc7277d", "question": {"question_concept": "adult", "choices": [{"label": "A", "text": "work"}, {"label": "B", "text": "dress himself"}, {"label": "C", "text": "marry"}, {"label": "D", "text": "dress herself"}, {"label": "E", "text": "drive train"}], "stem": "What did the adult do before the job interview?"}}
{"answerKey": "A", "id": "0f7419d25337e0a75503a015ae777905", "question": {"question_concept": "sale", "choices": [{"label": "A", "text": "overpriced"}, {"label": "B", "text": "purchase"}, {"label": "C", "text": "expensive"}, {"label": "D", "text": "park"}, {"label": "E", "text": "buying"}], "stem": "Most items in retail stores are what even when they are on sale?"}}
{"answerKey": "D", "id": "5cac4da628f0a58db980649079bd5784", "question": {"question_concept": "anemone", "choices": [{"label": "A", "text": "michigan"}, {"label": "B", "text": "swimming pool"}, {"label": "C", "text": "atlantic ocean"}, {"label": "D", "text": "nursery"}, {"label": "E", "text": "gulf of mexico"}], "stem": "John farms anemone in what type of facility?"}}
{"answerKey": "C", "id": "78d1218aeff70a70904767349e3c4c53", "question": {"question_concept": "sun", "choices": [{"label": "A", "text": "dry clothes"}, {"label": "B", "text": "warm house"}, {"label": "C", "text": "warm room"}, {"label": "D", "text": "shine brightly"}, {"label": "E", "text": "get dark"}], "stem": "Brawn opened the curtains so that the sun could do what?"}}
{"answerKey": "B", "id": "cce13a32fedb997c017d3fac87c34912", "question": {"question_concept": "releasing energy", "choices": [{"label": "A", "text": "damage"}, {"label": "B", "text": "wonderful"}, {"label": "C", "text": "exhaustion"}, {"label": "D", "text": "orgasm"}, {"label": "E", "text": "lazy"}], "stem": "How might releasing energy that has built up feel?"}}
{"answerKey": "B", "id": "6714487b839f648e348ac972ed114af3", "question": {"question_concept": "curiosity", "choices": [{"label": "A", "text": "hear news"}, {"label": "B", "text": "analyse"}, {"label": "C", "text": "go somewhere"}, {"label": "D", "text": "examine thing"}, {"label": "E", "text": "see favorite show"}], "stem": "What would you do if you have curiosity but are blind and paralyzed?"}}
{"answerKey": "B", "id": "3e536d9253bfac45de83e8ee291ca143", "question": {"question_concept": "furniture", "choices": [{"label": "A", "text": "apartment"}, {"label": "B", "text": "loft"}, {"label": "C", "text": "store"}, {"label": "D", "text": "rug"}, {"label": "E", "text": "stairs"}], "stem": "Where might it be hard to get furniture to?"}}
{"answerKey": "D", "id": "9f830faa0f8e3d7fb3a658c15a5fbe63", "question": {"question_concept": "attending school", "choices": [{"label": "A", "text": "detention"}, {"label": "B", "text": "graduate"}, {"label": "C", "text": "follower"}, {"label": "D", "text": "inspiration"}, {"label": "E", "text": "boredom"}], "stem": "A great teacher can be what when you are attending school?"}}
{"answerKey": "D", "id": "bbcef409e0acb71b515acc144d5b402c_1", "question": {"question_concept": "jeans", "choices": [{"label": "A", "text": "shopping mall"}, {"label": "B", "text": "museum"}, {"label": "C", "text": "laundromat"}, {"label": "D", "text": "clothing store"}, {"label": "E", "text": "bedroom"}], "stem": "Where would you get jeans and other wearable items to take home with you?"}}
{"answerKey": "C", "id": "cbb0c9a69ca0922371a48177087ef407", "question": {"question_concept": "clouds", "choices": [{"label": "A", "text": "sky"}, {"label": "B", "text": "top of mountain"}, {"label": "C", "text": "air"}, {"label": "D", "text": "ground level"}, {"label": "E", "text": "outer space"}], "stem": "In what substance do clouds float?"}}
{"answerKey": "E", "id": "b92f786638796fc028947ac0e9a44fef", "question": {"question_concept": "empire state building", "choices": [{"label": "A", "text": "manhattan"}, {"label": "B", "text": "office"}, {"label": "C", "text": "the city"}, {"label": "D", "text": "fifth avenue"}, {"label": "E", "text": "new york city"}], "stem": "Where is the large area location of the empire state building?"}}
{"answerKey": "E", "id": "5abeb4a2126597d4ef7b5a32e9e22abf", "question": {"question_concept": "cup of coffee", "choices": [{"label": "A", "text": "coffee shop"}, {"label": "B", "text": "office"}, {"label": "C", "text": "table"}, {"label": "D", "text": "washing"}, {"label": "E", "text": "kitchen"}], "stem": "Where do most people make coffee?"}}
{"answerKey": "D", "id": "8d4b0312f02be445e09a9462873d02bb", "question": {"question_concept": "body", "choices": [{"label": "A", "text": "bodycam"}, {"label": "B", "text": "home"}, {"label": "C", "text": "coffin"}, {"label": "D", "text": "funeral"}, {"label": "E", "text": "graveyard"}], "stem": "What kind of service is my body a part of when I'm no longer here?"}}
{"answerKey": "D", "id": "f7140f00ddd8d1c5d93b05ea32ad1fff", "question": {"question_concept": "row house", "choices": [{"label": "A", "text": "living less expensively"}, {"label": "B", "text": "england"}, {"label": "C", "text": "prison"}, {"label": "D", "text": "city"}, {"label": "E", "text": "town"}], "stem": "Many people wanted to leave their country estates for row houses, what did they need to move to?"}}
{"answerKey": "A", "id": "8b3b598a647dfd2d63fcedce5f461040", "question": {"question_concept": "saw", "choices": [{"label": "A", "text": "hardware store"}, {"label": "B", "text": "toolbox"}, {"label": "C", "text": "logging camp"}, {"label": "D", "text": "tool kit"}, {"label": "E", "text": "auger"}], "stem": "Where can someone get a new saw?"}}
{"answerKey": "E", "id": "7a900bc3a373806b6c56f0e19534005f", "question": {"question_concept": "question", "choices": [{"label": "A", "text": "express information"}, {"label": "B", "text": "touch everything"}, {"label": "C", "text": "think"}, {"label": "D", "text": "give clue"}, {"label": "E", "text": "analyse"}], "stem": "What would you do to a crime scene before asking a question?"}}
{"answerKey": "B", "id": "3d79c10ddf26a5ed7dc0bb168fb0b3ed", "question": {"question_concept": "college", "choices": [{"label": "A", "text": "big city"}, {"label": "B", "text": "fraternity house"}, {"label": "C", "text": "school"}, {"label": "D", "text": "building"}, {"label": "E", "text": "big town"}], "stem": "The man didn't do great in college, all his best memories were late night with his brothers at the what?"}}
{"answerKey": "C", "id": "b7091d2bfcea421d787ce9e7982f104a", "question": {"question_concept": "run", "choices": [{"label": "A", "text": "frightened"}, {"label": "B", "text": "run up stairs"}, {"label": "C", "text": "get away from"}, {"label": "D", "text": "go quickly"}, {"label": "E", "text": "go faster"}], "stem": "In a horror movie victims usually trip when the run in order to do what in regards to the killer?"}}
{"answerKey": "B", "id": "d060ab71d0efff3cab5960089a6bb3a2", "question": {"question_concept": "change", "choices": [{"label": "A", "text": "stagnant"}, {"label": "B", "text": "stagnation"}, {"label": "C", "text": "tradition"}, {"label": "D", "text": "hunger"}, {"label": "E", "text": "paper money"}], "stem": "The coach decided to make a lineup change, the team's effort was suffering from what?"}}
{"answerKey": "A", "id": "b399f6008d90dbd92bcce5abed4c1fd1", "question": {"question_concept": "goods", "choices": [{"label": "A", "text": "mall"}, {"label": "B", "text": "grocery store"}, {"label": "C", "text": "grocery store"}, {"label": "D", "text": "shop"}, {"label": "E", "text": "supermarket"}], "stem": "Where would you go if you want to buy some clothes?"}}
{"answerKey": "B", "id": "80c19c62338edae0e8a1f5c6fec0d29a", "question": {"question_concept": "food", "choices": [{"label": "A", "text": "etna"}, {"label": "B", "text": "cupboard"}, {"label": "C", "text": "oven"}, {"label": "D", "text": "stomach"}, {"label": "E", "text": "fridge"}], "stem": "Where is food likely to stay dry?"}}
{"answerKey": "A", "id": "1a4e83b433620cb2d7d806882f8d57e4", "question": {"question_concept": "mental illness", "choices": [{"label": "A", "text": "managed"}, {"label": "B", "text": "effectively treated"}, {"label": "C", "text": "recur"}, {"label": "D", "text": "cause delusion"}, {"label": "E", "text": "illusion"}], "stem": "What is it called when a person with mental illness is able to lead a relatively normal life?"}}
{"answerKey": "C", "id": "b9e04a53c0ee7325b901de4d12d56884", "question": {"question_concept": "musical instrument", "choices": [{"label": "A", "text": "bank"}, {"label": "B", "text": "orchestra"}, {"label": "C", "text": "case"}, {"label": "D", "text": "music room"}, {"label": "E", "text": "movie"}], "stem": "Where do you keep musical instrument so it doesn't get scratched?"}}
{"answerKey": "E", "id": "7490aa460f66000555a8a94008179cbb", "question": {"question_concept": "watching television", "choices": [{"label": "A", "text": "entertainment"}, {"label": "B", "text": "falling asleep"}, {"label": "C", "text": "getting fat"}, {"label": "D", "text": "crying"}, {"label": "E", "text": "relaxation"}], "stem": "The woman is watching television and trying to forget her day, what is her goal?"}}
{"answerKey": "E", "id": "ad8ee2965a33ff4b0e3d2ac732676594", "question": {"question_concept": "gazelle", "choices": [{"label": "A", "text": "eastern hemisphere"}, {"label": "B", "text": "the city"}, {"label": "C", "text": "open plain"}, {"label": "D", "text": "television program"}, {"label": "E", "text": "great outdoors"}], "stem": "While John Candy and Dan Aykroyd didn't run into a gazelle, you'd have to go where to see one?"}}
{"answerKey": "D", "id": "64d2310eff6b661baeb41b4ccc392e35", "question": {"question_concept": "run", "choices": [{"label": "A", "text": "stretches"}, {"label": "B", "text": "running from police"}, {"label": "C", "text": "learn to walk"}, {"label": "D", "text": "go quickly"}, {"label": "E", "text": "get out of bed"}], "stem": "When we are running what are we doing?"}}
{"answerKey": "C", "id": "6b1f5ebd9d0dbc7e34a598456a6091a8", "question": {"question_concept": "free", "choices": [{"label": "A", "text": "slavery"}, {"label": "B", "text": "caught"}, {"label": "C", "text": "caged in"}, {"label": "D", "text": "topfree"}, {"label": "E", "text": "prisoner"}], "stem": "It's dangerous to let pet birds free so it's better to keep them what?"}}
{"answerKey": "C", "id": "080ef6941410139d6869e78122bc741e", "question": {"question_concept": "beaver", "choices": [{"label": "A", "text": "british columbia"}, {"label": "B", "text": "body of water"}, {"label": "C", "text": "wooded area"}, {"label": "D", "text": "pay debts"}, {"label": "E", "text": "zoo"}], "stem": "A beaver is know for building prowess, their supplies come from where?"}}
{"answerKey": "D", "id": "6c70d98cfb8e97fda8caefcee761a229", "question": {"question_concept": "answering questions", "choices": [{"label": "A", "text": "panic"}, {"label": "B", "text": "discussion"}, {"label": "C", "text": "attention"}, {"label": "D", "text": "confusion"}, {"label": "E", "text": "satisfaction"}], "stem": "Zane doesn't like answering questions.  He's not good at it because he suffers from what?"}}
{"answerKey": "A", "id": "75ac594b4fdbfba006e61315d1b2c815", "question": {"question_concept": "going public", "choices": [{"label": "A", "text": "wide acceptance"}, {"label": "B", "text": "a degree"}, {"label": "C", "text": "pain"}, {"label": "D", "text": "getting high"}, {"label": "E", "text": "press coverage"}], "stem": "Going public about a common problem can gain what for a celebrity?"}}
{"answerKey": "A", "id": "5a8e7d2f97f76adb23fbd59a009d16f0", "question": {"question_concept": "electricity", "choices": [{"label": "A", "text": "opera"}, {"label": "B", "text": "concert"}, {"label": "C", "text": "basement"}, {"label": "D", "text": "bedroom"}, {"label": "E", "text": "grand canyon"}], "stem": "The electricity went out and everyone was shrouded in darkness.  They all remained in their seats, because it would have been dangerous to try to find there way out.  Where mihgt they have been?"}}
{"answerKey": "D", "id": "178cb8153123716aa94f286b615149d4", "question": {"question_concept": "beauty salon", "choices": [{"label": "A", "text": "clerk"}, {"label": "B", "text": "mall"}, {"label": "C", "text": "strip mall"}, {"label": "D", "text": "city"}, {"label": "E", "text": "neighborhood"}], "stem": "Where could you find hundreds of beauty salon?"}}
{"answerKey": "A", "id": "cc917ca0e03c91a5141920f5a902a36c", "question": {"question_concept": "christmas", "choices": [{"label": "A", "text": "halloween"}, {"label": "B", "text": "summer"}, {"label": "C", "text": "easter"}, {"label": "D", "text": "kwaanza"}, {"label": "E", "text": "give gift"}], "stem": "If it is Chrismas time what came most recently before?"}}
{"answerKey": "D", "id": "a7d51b753c2113d8b2dbd0ebb5375855_1", "question": {"question_concept": "niece", "choices": [{"label": "A", "text": "family picture book"}, {"label": "B", "text": "family reunion"}, {"label": "C", "text": "brother's house"}, {"label": "D", "text": "family tree"}, {"label": "E", "text": "baby shower"}], "stem": "If someone found out their brother was having a daughter, they would have to add a niece limb to the what?"}}
{"answerKey": "E", "id": "e71da9e95b321763c86e879a47bbd327", "question": {"question_concept": "must", "choices": [{"label": "A", "text": "willing"}, {"label": "B", "text": "optional"}, {"label": "C", "text": "should not"}, {"label": "D", "text": "have to"}, {"label": "E", "text": "unnecessary"}], "stem": "The criminal insisted he must do the crime to the bank teller, but she tried to convince him there were other ways in life and this was what?"}}
{"answerKey": "B", "id": "ec86900559a0faf2aef066e511a4cfa6", "question": {"question_concept": "ink", "choices": [{"label": "A", "text": "squid"}, {"label": "B", "text": "fountain pen"}, {"label": "C", "text": "pencil case"}, {"label": "D", "text": "newspaper"}, {"label": "E", "text": "printer"}], "stem": "what do you fill with ink to write?"}}
{"answerKey": "E", "id": "d312741df1b14bcbe358f4f30aff3994", "question": {"question_concept": "shock", "choices": [{"label": "A", "text": "expected"}, {"label": "B", "text": "wanting"}, {"label": "C", "text": "calm"}, {"label": "D", "text": "thundershock"}, {"label": "E", "text": "surprised"}], "stem": "He walked into the room and had a great shock, his friends had what him?"}}
{"answerKey": "E", "id": "0df3f58645b4bc306093845fb297a50e", "question": {"question_concept": "meet friend", "choices": [{"label": "A", "text": "have sex"}, {"label": "B", "text": "smile"}, {"label": "C", "text": "hug each other"}, {"label": "D", "text": "conversation"}, {"label": "E", "text": "handshake"}], "stem": "He wasn't the hugging type, even when he meet friend he'd just do what?"}}
{"answerKey": "E", "id": "27d9b4df2ca50112d282331df4923e96", "question": {"question_concept": "map", "choices": [{"label": "A", "text": "truck stop"}, {"label": "B", "text": "amusement park"}, {"label": "C", "text": "atlas"}, {"label": "D", "text": "mall"}, {"label": "E", "text": "gas station"}], "stem": "If you were lost you might need a map, the best place to find one on the road is at any what?"}}
{"answerKey": "D", "id": "ab755203f41a2e241f0ee8a53c54f287", "question": {"question_concept": "net", "choices": [{"label": "A", "text": "sports"}, {"label": "B", "text": "fishing gear"}, {"label": "C", "text": "soccer game"}, {"label": "D", "text": "fishing boat"}, {"label": "E", "text": "badminton"}], "stem": "Where would you put a net if you wanted to use it?"}}
{"answerKey": "A", "id": "f13efb91090dd28fd2b3c1f4dde680fd", "question": {"question_concept": "communicating", "choices": [{"label": "A", "text": "exchanging ideas"}, {"label": "B", "text": "confusion"}, {"label": "C", "text": "peer pressure"}, {"label": "D", "text": "response"}, {"label": "E", "text": "learning"}], "stem": "Sage loved communicating  He liked doing what with his peers?"}}
{"answerKey": "D", "id": "e98031901c815e55040d9fe28c4d9387", "question": {"question_concept": "cat", "choices": [{"label": "A", "text": "floor"}, {"label": "B", "text": "humane society"}, {"label": "C", "text": "bed"}, {"label": "D", "text": "comfortable chair"}, {"label": "E", "text": "window sill"}], "stem": "Where would a cat snuggle up with their human?"}}
{"answerKey": "D", "id": "fb64149cf01c5b496d986f56852273e9", "question": {"question_concept": "cable", "choices": [{"label": "A", "text": "radio shack"}, {"label": "B", "text": "electrical device"}, {"label": "C", "text": "shower"}, {"label": "D", "text": "substation"}, {"label": "E", "text": "television"}], "stem": "What is a place that has large cable hanging overhead?"}}
{"answerKey": "C", "id": "2ac72eaf30a633c410b1bd658bbef0ba", "question": {"question_concept": "car", "choices": [{"label": "A", "text": "freeway"}, {"label": "B", "text": "road"}, {"label": "C", "text": "race track"}, {"label": "D", "text": "alley"}, {"label": "E", "text": "parking lot"}], "stem": "Where do cars usually travel at very high speeds?"}}
{"answerKey": "B", "id": "22fc45d9e6d0baea4a5b0526504225b8", "question": {"question_concept": "suitcase", "choices": [{"label": "A", "text": "baggage compartment"}, {"label": "B", "text": "movie"}, {"label": "C", "text": "subway"}, {"label": "D", "text": "airplane"}, {"label": "E", "text": "cargo hold"}], "stem": "What might a person be watching if they see a man with a suitcase full of money?"}}
{"answerKey": "C", "id": "4ef3d70648ee3cea028bc5ed0fdfda28", "question": {"question_concept": "eating breakfast in bed", "choices": [{"label": "A", "text": "mess"}, {"label": "B", "text": "hungry"}, {"label": "C", "text": "feel guilty"}, {"label": "D", "text": "indigestion"}, {"label": "E", "text": "spills"}], "stem": "Eating breakfast in bed while seeing a homeless person shivering outside your window may cause you to what?"}}
{"answerKey": "C", "id": "059155c50d1b04da7373e309868e67d2", "question": {"question_concept": "hinged door", "choices": [{"label": "A", "text": "kitchen"}, {"label": "B", "text": "safe"}, {"label": "C", "text": "own house"}, {"label": "D", "text": "building"}, {"label": "E", "text": "pantry"}], "stem": "If I put in my key and open a hinged door, where am I likely entering?"}}
{"answerKey": "C", "id": "33d023a6806390eb8195380331e17404_1", "question": {"question_concept": "reception desk", "choices": [{"label": "A", "text": "motel"}, {"label": "B", "text": "hostel"}, {"label": "C", "text": "building"}, {"label": "D", "text": "lobby"}, {"label": "E", "text": "office park"}], "stem": "If somebody is working at a reception desk, they are located at the front entrance of the what?"}}
{"answerKey": "A", "id": "63f7ad481a63fc8c6dffe00519d4a167", "question": {"question_concept": "reading newspaper", "choices": [{"label": "A", "text": "learning about world"}, {"label": "B", "text": "education"}, {"label": "C", "text": "get angry"}, {"label": "D", "text": "concern"}, {"label": "E", "text": "eat cake"}], "stem": "If you're reading a newspaper from another country what are you doing?"}}
{"answerKey": "A", "id": "a2daf73d33541af0846673afd8e49abe", "question": {"question_concept": "name", "choices": [{"label": "A", "text": "certificate"}, {"label": "B", "text": "directory"}, {"label": "C", "text": "phone book"}, {"label": "D", "text": "lineup"}, {"label": "E", "text": "roster"}], "stem": "They wanted to recognize his accomplishment, where should they put his name?"}}
{"answerKey": "A", "id": "7d70208061ae3185bcfc9e912ee9e141", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "procrastinate"}, {"label": "B", "text": "complete collection"}, {"label": "C", "text": "headache"}, {"label": "D", "text": "good time management"}, {"label": "E", "text": "have to hold"}], "stem": "What is it called when a person tends to leave things to the last minute?"}}
{"answerKey": "C", "id": "9003c4748b08d5a734747e499599ff20", "question": {"question_concept": "settle", "choices": [{"label": "A", "text": "walk"}, {"label": "B", "text": "agitate"}, {"label": "C", "text": "wander"}, {"label": "D", "text": "remove"}, {"label": "E", "text": "disturb"}], "stem": "What will you do if you do not want to settle in one place?"}}
{"answerKey": "C", "id": "28aac6d39cdd270d2a6a28e1985484cb", "question": {"question_concept": "bungalow", "choices": [{"label": "A", "text": "woods"}, {"label": "B", "text": "bed"}, {"label": "C", "text": "suburbs"}, {"label": "D", "text": "rural"}, {"label": "E", "text": "neighborhood"}], "stem": "Where would a person live that isn't in the metro area but still has good schools?"}}
{"answerKey": "A", "id": "8bdbb8caefcc607a9ec7579aa0c87cba", "question": {"question_concept": "government", "choices": [{"label": "A", "text": "capitol building"}, {"label": "B", "text": "everything"}, {"label": "C", "text": "washington d.c"}, {"label": "D", "text": "russia"}, {"label": "E", "text": "canada"}], "stem": "Jane works for the government as a senator, where does she spend a lot of time?"}}
{"answerKey": "B", "id": "95a85df48902d23eb3fda25a99fca1a0", "question": {"question_concept": "love", "choices": [{"label": "A", "text": "take oath"}, {"label": "B", "text": "procreate"}, {"label": "C", "text": "matrimony"}, {"label": "D", "text": "please parents"}, {"label": "E", "text": "live life"}], "stem": "What is it called when two people in love have children?"}}
{"answerKey": "E", "id": "79c3378b7660d328902d7c0ad442a37f", "question": {"question_concept": "policemen", "choices": [{"label": "A", "text": "run away"}, {"label": "B", "text": "hurry along"}, {"label": "C", "text": "fine motorists"}, {"label": "D", "text": "direct traffic"}, {"label": "E", "text": "help"}], "stem": "What did the policemen do when they heard a cry from a distance?"}}
{"answerKey": "B", "id": "8c12e5864463cfcd03f4d0ab67949d01", "question": {"question_concept": "ambition", "choices": [{"label": "A", "text": "take care of proposals"}, {"label": "B", "text": "begin work"}, {"label": "C", "text": "in charge of project"}, {"label": "D", "text": "eat cake"}, {"label": "E", "text": "go to school"}], "stem": "It takes ambition to complete a job, but the first step is to what?"}}
{"answerKey": "A", "id": "e145618c2062eb9ea8928fdb0d42185e", "question": {"question_concept": "fox", "choices": [{"label": "A", "text": "hen house"}, {"label": "B", "text": "english hunt"}, {"label": "C", "text": "mountains"}, {"label": "D", "text": "outside bedroom window"}, {"label": "E", "text": "england"}], "stem": "Where would I not want a fox?"}}
{"answerKey": "C", "id": "35872be88df5f6c4a6600020266a5458", "question": {"question_concept": "top floor", "choices": [{"label": "A", "text": "go down"}, {"label": "B", "text": "apartment building"}, {"label": "C", "text": "tall building"}, {"label": "D", "text": "office building"}, {"label": "E", "text": "cabin"}], "stem": "What type of building has the most top floor?"}}
{"answerKey": "B", "id": "055817d8d703d3c2802545e3fccdcde3", "question": {"question_concept": "death", "choices": [{"label": "A", "text": "celebrate"}, {"label": "B", "text": "burial"}, {"label": "C", "text": "life"}, {"label": "D", "text": "rebirth"}, {"label": "E", "text": "decomposition"}], "stem": "What do humans do to other humans after death?"}}
{"answerKey": "A", "id": "5ef6cdb85468df482e3aa6fa339d6e41", "question": {"question_concept": "restaurant", "choices": [{"label": "A", "text": "yellow pages"}, {"label": "B", "text": "town"}, {"label": "C", "text": "business sector"}, {"label": "D", "text": "town"}, {"label": "E", "text": "at hotel"}], "stem": "Where can you find a restaurant's phone number?"}}
{"answerKey": "B", "id": "1e939cc6fef999953d692b57caab254b", "question": {"question_concept": "coins", "choices": [{"label": "A", "text": "stove"}, {"label": "B", "text": "water fountain"}, {"label": "C", "text": "desk"}, {"label": "D", "text": "purse"}, {"label": "E", "text": "jar"}], "stem": "What would you put coins into to make it work?"}}
{"answerKey": "E", "id": "3a3b5d4a517ef70d25eb558f1a622937", "question": {"question_concept": "bald eagle", "choices": [{"label": "A", "text": "city"}, {"label": "B", "text": "canada"}, {"label": "C", "text": "minnesota"}, {"label": "D", "text": "thermal"}, {"label": "E", "text": "photograph"}], "stem": "A patriotic guy with a camera is looking for a bald eagle, what is he likely to do with the eagle if he finds one?"}}
{"answerKey": "C", "id": "a943522f7d407cef369d5d3f1bf48589", "question": {"question_concept": "piano", "choices": [{"label": "A", "text": "music school"}, {"label": "B", "text": "music store"}, {"label": "C", "text": "neighbor's house"}, {"label": "D", "text": "lunch"}, {"label": "E", "text": "drawing room"}], "stem": "Where can you go to use a piano in your neighborhood if you don't have one?"}}
{"answerKey": "A", "id": "57a343d72031b668e5eb91868420e915", "question": {"question_concept": "shower curtain", "choices": [{"label": "A", "text": "department store"}, {"label": "B", "text": "restaurant"}, {"label": "C", "text": "hotel"}, {"label": "D", "text": "dime store"}, {"label": "E", "text": "bathtub"}], "stem": "Where would you get a shower curtain if you do not have one?"}}
{"answerKey": "B", "id": "c4b1a57e7880b9cb367f9c67abf5605f", "question": {"question_concept": "kissing", "choices": [{"label": "A", "text": "anus"}, {"label": "B", "text": "partner"}, {"label": "C", "text": "arousal"}, {"label": "D", "text": "trust"}, {"label": "E", "text": "cooperation"}], "stem": "Kissing is normally an activity reserved for your romantic what?"}}
{"answerKey": "C", "id": "e313d7967f72c2b880213daaaf4b7181", "question": {"question_concept": "child", "choices": [{"label": "A", "text": "count to ten"}, {"label": "B", "text": "state name"}, {"label": "C", "text": "dress herself"}, {"label": "D", "text": "clean room"}, {"label": "E", "text": "socialize"}], "stem": "What does a child learn to do before school?"}}
{"answerKey": "C", "id": "3c7992df7fda23bcdeacb1f1f6b73448", "question": {"question_concept": "talking to", "choices": [{"label": "A", "text": "get tired of"}, {"label": "B", "text": "small talk"}, {"label": "C", "text": "eye contact"}, {"label": "D", "text": "friendship"}, {"label": "E", "text": "social life"}], "stem": "He was getting advice for the job interview, they told him when talking to the interviewer always make what?"}}
{"answerKey": "A", "id": "d6644eacdb543a60545d2eb1ac7e6dbd", "question": {"question_concept": "apple tree", "choices": [{"label": "A", "text": "bible"}, {"label": "B", "text": "spain"}, {"label": "C", "text": "harry potter"}, {"label": "D", "text": "new york"}, {"label": "E", "text": "woods"}], "stem": "According to what book did an apple tree lead to the downfall of man?"}}
{"answerKey": "E", "id": "d1ad9b79f54205b6b9ac19a27f9c2be5", "question": {"question_concept": "landing", "choices": [{"label": "A", "text": "stairwell"}, {"label": "B", "text": "arena"}, {"label": "C", "text": "ocean"}, {"label": "D", "text": "airport"}, {"label": "E", "text": "apartment building"}], "stem": "The neighborhood had a great sense of community, there was always a crowd at the landing of the what?"}}
{"answerKey": "C", "id": "f116ee6620c0f171e5db54bc03a5f2e2", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "cross street"}, {"label": "B", "text": "talk to themselves"}, {"label": "C", "text": "open doors"}, {"label": "D", "text": "throw away"}, {"label": "E", "text": "study greek"}], "stem": "What might a kind person do?"}}
{"answerKey": "C", "id": "ea82f9e938cbfce85fb498ce46264253", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "cross street"}, {"label": "B", "text": "draw attention to themselves"}, {"label": "C", "text": "make money"}, {"label": "D", "text": "falling down"}, {"label": "E", "text": "come home"}], "stem": "What will a person do at work?"}}
{"answerKey": "B", "id": "edbb57ac2f476679ae547f75ec2bef3e", "question": {"question_concept": "fox", "choices": [{"label": "A", "text": "tennessee"}, {"label": "B", "text": "south carolina"}, {"label": "C", "text": "louisiana"}, {"label": "D", "text": "oklahoma"}, {"label": "E", "text": "mountains"}], "stem": "John saw a fox running along the beach and was glad to be on the east coast.  Where might he have been?"}}
{"answerKey": "B", "id": "07a99d5f2ca7028febeb9f09604b36c8", "question": {"question_concept": "mice", "choices": [{"label": "A", "text": "loft"}, {"label": "B", "text": "attic"}, {"label": "C", "text": "bell cat"}, {"label": "D", "text": "countryside"}, {"label": "E", "text": "laboratory"}], "stem": "Name a location where you would not want to find mice."}}
{"answerKey": "B", "id": "b42ef8be1748c19fa5938de5396f8fad", "question": {"question_concept": "jogging", "choices": [{"label": "A", "text": "exhaustion"}, {"label": "B", "text": "getting in shape"}, {"label": "C", "text": "fitness"}, {"label": "D", "text": "injure himself"}, {"label": "E", "text": "fatigue"}], "stem": "The man started to learn jogging, what was he hoping to do?"}}
{"answerKey": "D", "id": "236691d38665d7bcdd0c9b9834252a51", "question": {"question_concept": "information", "choices": [{"label": "A", "text": "internet"}, {"label": "B", "text": "book"}, {"label": "C", "text": "online"}, {"label": "D", "text": "google"}, {"label": "E", "text": "manual"}], "stem": "Where do most people turn to get information on their phones?"}}
{"answerKey": "D", "id": "8ef78abb86fc282ccb02bbc495f13030", "question": {"question_concept": "death", "choices": [{"label": "A", "text": "rebirth"}, {"label": "B", "text": "human experience"}, {"label": "C", "text": "sadness"}, {"label": "D", "text": "decomposition"}, {"label": "E", "text": "obesity"}], "stem": "What happens to a body after death?"}}
{"answerKey": "C", "id": "313d033c33ec475e04e628f87c5686bd", "question": {"question_concept": "potato", "choices": [{"label": "A", "text": "beef stew"}, {"label": "B", "text": "own kitchen"}, {"label": "C", "text": "clam chowder"}, {"label": "D", "text": "kitchen cabinet"}, {"label": "E", "text": "pantry"}], "stem": "What type of non-vegetarian soup is one likely to find a potato?"}}
{"answerKey": "E", "id": "d581e0ad6a4c89465dc1a527bd2d3f77", "question": {"question_concept": "disability", "choices": [{"label": "A", "text": "qualification"}, {"label": "B", "text": "strength"}, {"label": "C", "text": "pity"}, {"label": "D", "text": "competence"}, {"label": "E", "text": "potential"}], "stem": "Though she had a disability, what did her encouraging and positive coach see in her?"}}
{"answerKey": "B", "id": "f232bfea2a7611999688a252e476c040", "question": {"question_concept": "theory", "choices": [{"label": "A", "text": "park"}, {"label": "B", "text": "practice"}, {"label": "C", "text": "fact"}, {"label": "D", "text": "practical"}, {"label": "E", "text": "practise"}], "stem": "They had a theory of what they could do in t he big game, so over and over they would what?"}}
{"answerKey": "D", "id": "91756d8e475d8d59fa0a4e35f408e366", "question": {"question_concept": "rise", "choices": [{"label": "A", "text": "sun set"}, {"label": "B", "text": "near"}, {"label": "C", "text": "fall"}, {"label": "D", "text": "below"}, {"label": "E", "text": "lower"}], "stem": "When you see something rise, you are where in relation to it?"}}
{"answerKey": "D", "id": "866ea9c668c0b42df19fa20865e31f77", "question": {"question_concept": "food can", "choices": [{"label": "A", "text": "cabinet"}, {"label": "B", "text": "house"}, {"label": "C", "text": "recycling center"}, {"label": "D", "text": "backpack"}, {"label": "E", "text": "make person sick"}], "stem": "They were getting ready for a really long hike, he put the food can in his what?"}}
{"answerKey": "D", "id": "22015315e7ff79386877828b4fa27799", "question": {"question_concept": "rug", "choices": [{"label": "A", "text": "persia"}, {"label": "B", "text": "desk"}, {"label": "C", "text": "table"}, {"label": "D", "text": "living room"}, {"label": "E", "text": "hall"}], "stem": "Where would you keep a rug near your front door?"}}
{"answerKey": "B", "id": "484f6e4fb8e6431b010c299490b72e3c", "question": {"question_concept": "anything", "choices": [{"label": "A", "text": "obesity"}, {"label": "B", "text": "fall down"}, {"label": "C", "text": "matter to"}, {"label": "D", "text": "whatever"}, {"label": "E", "text": "surprise"}], "stem": "When you slip from a ladder propped on anything what will you do?"}}
{"answerKey": "E", "id": "7322d0dcf2e27c7032626a3639f5696b", "question": {"question_concept": "food", "choices": [{"label": "A", "text": "table"}, {"label": "B", "text": "disneyland"}, {"label": "C", "text": "refrigerators"}, {"label": "D", "text": "pantry"}, {"label": "E", "text": "shop"}], "stem": "What do you do when you need to get food?"}}
{"answerKey": "C", "id": "0519b0b0869681c2884f53dbfa43e538", "question": {"question_concept": "arise", "choices": [{"label": "A", "text": "go down"}, {"label": "B", "text": "fall down"}, {"label": "C", "text": "lie down"}, {"label": "D", "text": "lie to himself"}, {"label": "E", "text": "sit down"}], "stem": "Brad tried to arise from bed but he could not.  Instead, he just continued to do what?"}}
{"answerKey": "C", "id": "1ab04c0501b815b2a48f2581f04215a8", "question": {"question_concept": "heifer", "choices": [{"label": "A", "text": "home"}, {"label": "B", "text": "dairy farm"}, {"label": "C", "text": "cattle show"}, {"label": "D", "text": "dairy barn"}, {"label": "E", "text": "corral"}], "stem": "If a heifer is really high quality, you might take her where?"}}
{"answerKey": "B", "id": "7776b10c7bb96f3fe5e026678673634d", "question": {"question_concept": "opening business", "choices": [{"label": "A", "text": "home"}, {"label": "B", "text": "wealth"}, {"label": "C", "text": "bankruptcy"}, {"label": "D", "text": "lose money"}, {"label": "E", "text": "get rich"}], "stem": "What do people want to acquire from opening business?"}}
{"answerKey": "A", "id": "f7c005244d406b9bde48dc8c22003af1", "question": {"question_concept": "undergraduate", "choices": [{"label": "A", "text": "graduated"}, {"label": "B", "text": "masters"}, {"label": "C", "text": "postgraduate"}, {"label": "D", "text": "phd"}, {"label": "E", "text": "professor"}], "stem": "What has someone who had finished their undergraduate done?"}}
{"answerKey": "D", "id": "88501d528c855e2b533b3fea2f86183d", "question": {"question_concept": "bus stop", "choices": [{"label": "A", "text": "ocean"}, {"label": "B", "text": "getting off of bus"}, {"label": "C", "text": "airport"}, {"label": "D", "text": "urban area"}, {"label": "E", "text": "towns"}], "stem": "Where are bus stops more common in what parts?"}}
{"answerKey": "D", "id": "3d9c3253e24fb108cea9083e8a853cf2", "question": {"question_concept": "stranger", "choices": [{"label": "A", "text": "bus station"}, {"label": "B", "text": "paradise"}, {"label": "C", "text": "train station"}, {"label": "D", "text": "park"}, {"label": "E", "text": "sea"}], "stem": "Bill wanted to pick up a stranger, preferably a responsible one with kids.  Where might he look for one?"}}
{"answerKey": "C", "id": "9808782b2e2e1bfbfa27c41e605bfffe", "question": {"question_concept": "lemur", "choices": [{"label": "A", "text": "desert"}, {"label": "B", "text": "hole"}, {"label": "C", "text": "india"}, {"label": "D", "text": "cage"}, {"label": "E", "text": "rain forest"}], "stem": "Where might a lemur frolic in the market?"}}
{"answerKey": "A", "id": "c432b860fcd7297751ff5254ec4a7956", "question": {"question_concept": "furniture", "choices": [{"label": "A", "text": "rug"}, {"label": "B", "text": "room"}, {"label": "C", "text": "toy"}, {"label": "D", "text": "friend's house"}, {"label": "E", "text": "building"}], "stem": "What might I place under the furniture?"}}
{"answerKey": "D", "id": "732af155f677a51d05d0c9e080d598b6", "question": {"question_concept": "performing", "choices": [{"label": "A", "text": "fear"}, {"label": "B", "text": "injury"}, {"label": "C", "text": "happiness"}, {"label": "D", "text": "action"}, {"label": "E", "text": "cut"}], "stem": "Everybody began performing once their director stated what?"}}
{"answerKey": "C", "id": "48abc2c113623fd72f758502529f93a5", "question": {"question_concept": "learning about world", "choices": [{"label": "A", "text": "pleasure"}, {"label": "B", "text": "greater mobility"}, {"label": "C", "text": "desire to travel"}, {"label": "D", "text": "global warming"}, {"label": "E", "text": "increased security"}], "stem": "By learning about the world, many poor college students gain what?"}}
{"answerKey": "B", "id": "03f06f77aaf80b5f5e296ffbd11e9d82", "question": {"question_concept": "books", "choices": [{"label": "A", "text": "friend's house"}, {"label": "B", "text": "university"}, {"label": "C", "text": "large city"}, {"label": "D", "text": "storage"}, {"label": "E", "text": "table"}], "stem": "Where are required to carry books all day?"}}
{"answerKey": "C", "id": "e7084c166ec67d0f983a26e055e845c6", "question": {"question_concept": "seaweed", "choices": [{"label": "A", "text": "beach"}, {"label": "B", "text": "sea"}, {"label": "C", "text": "ocean"}, {"label": "D", "text": "water"}, {"label": "E", "text": "sea plant"}], "stem": "where is seaweed from?"}}
{"answerKey": "B", "id": "c55c31b5a2aa996f3b75ad88c017a6b9", "question": {"question_concept": "steak", "choices": [{"label": "A", "text": "oven"}, {"label": "B", "text": "freezer"}, {"label": "C", "text": "plate"}, {"label": "D", "text": "tupperware"}, {"label": "E", "text": "grill"}], "stem": "how can i store cooked steak?"}}
{"answerKey": "D", "id": "463521a93ae71e93bea8b97cdf7a6792", "question": {"question_concept": "dust", "choices": [{"label": "A", "text": "closet"}, {"label": "B", "text": "under the bed"}, {"label": "C", "text": "television"}, {"label": "D", "text": "attic"}, {"label": "E", "text": "most buildings"}], "stem": "John wanted to clean all of the dust out of his place before settling down to watch his favorite shows.  What might he hardest do dust?"}}
{"answerKey": "B", "id": "c036ce033bc429ac1aba0a6ac8d057e1", "question": {"question_concept": "nerve", "choices": [{"label": "A", "text": "eyes"}, {"label": "B", "text": "animal"}, {"label": "C", "text": "fingertips"}, {"label": "D", "text": "brainstem"}, {"label": "E", "text": "human body"}], "stem": "Something had the nerve to break into the garbage last night, what did it?"}}
{"answerKey": "A", "id": "db7f2bfdabcf53d6778fd7af80b603d2", "question": {"question_concept": "pamphlets", "choices": [{"label": "A", "text": "bookstore"}, {"label": "B", "text": "drawer"}, {"label": "C", "text": "health department"}, {"label": "D", "text": "mail box"}, {"label": "E", "text": "library"}], "stem": "Where would you go to get some pamphlets if you want to own them?"}}
{"answerKey": "B", "id": "8605fd2affc796d79073d0f3ef0761c9", "question": {"question_concept": "audience", "choices": [{"label": "A", "text": "school"}, {"label": "B", "text": "sporting event"}, {"label": "C", "text": "concert hall"}, {"label": "D", "text": "show"}, {"label": "E", "text": "television"}], "stem": "The audience cheered when a goal was scored, what were they spectating?"}}
{"answerKey": "D", "id": "ad37795fd9e3a65553683ff305b5113d", "question": {"question_concept": "shore", "choices": [{"label": "A", "text": "picture of sea side"}, {"label": "B", "text": "seaside town"}, {"label": "C", "text": "beach"}, {"label": "D", "text": "california"}, {"label": "E", "text": "see side picture"}], "stem": "What western state has thousands of miles of shore?"}}
{"answerKey": "D", "id": "bcd51af35d691f5c3b6b548096ab1559", "question": {"question_concept": "holy", "choices": [{"label": "A", "text": "profane"}, {"label": "B", "text": "halibut"}, {"label": "C", "text": "damaged"}, {"label": "D", "text": "common"}, {"label": "E", "text": "halibut"}], "stem": "Everybody seemed to be crying at the holy site, the tour guide explained that this was what?"}}
{"answerKey": "C", "id": "b5345f15d5b451562ab9e0851e7f394f", "question": {"question_concept": "smile", "choices": [{"label": "A", "text": "manual"}, {"label": "B", "text": "rainbow"}, {"label": "C", "text": "cry"}, {"label": "D", "text": "frown"}, {"label": "E", "text": "make others happy too"}], "stem": "The smile gave away that the what was one of happiness?"}}
{"answerKey": "A", "id": "6a884d5d8febfdd86fcf68ff1a904d9b", "question": {"question_concept": "monument", "choices": [{"label": "A", "text": "municipal park"}, {"label": "B", "text": "office"}, {"label": "C", "text": "state park"}, {"label": "D", "text": "cemetary"}, {"label": "E", "text": "public gardens"}], "stem": "Where is a public monument likely to be erected by a city?"}}
{"answerKey": "D", "id": "a1303b5177df0a5b653c9abd7d5f5e08", "question": {"question_concept": "bungalow", "choices": [{"label": "A", "text": "housing estate"}, {"label": "B", "text": "neighborhood"}, {"label": "C", "text": "mars"}, {"label": "D", "text": "woods"}, {"label": "E", "text": "suburbs"}], "stem": "Where would a person live if they wanted no neighbors?"}}
{"answerKey": "E", "id": "315baf79f8dd3673f67a90de0758240e", "question": {"question_concept": "control room", "choices": [{"label": "A", "text": "building"}, {"label": "B", "text": "factory"}, {"label": "C", "text": "window"}, {"label": "D", "text": "prison"}, {"label": "E", "text": "nuclear power plant"}], "stem": "Where is the control room that controls a PWR located?"}}
{"answerKey": "A", "id": "01f01cc3ad152773ef42b30e926912bf", "question": {"question_concept": "dogs", "choices": [{"label": "A", "text": "get lost"}, {"label": "B", "text": "require water"}, {"label": "C", "text": "trained"}, {"label": "D", "text": "bark"}, {"label": "E", "text": "roll over"}], "stem": "What happens to a dog before someone puts up posters of them?"}}
{"answerKey": "A", "id": "f192cfacbaa2f7e0e879f673c8e076a7", "question": {"question_concept": "steakhouse", "choices": [{"label": "A", "text": "texas"}, {"label": "B", "text": "building"}, {"label": "C", "text": "kansas city"}, {"label": "D", "text": "maine"}, {"label": "E", "text": "falling down"}], "stem": "Where are the most famous BBQ steakhouses in america?"}}
{"answerKey": "B", "id": "ab8d5e21a2cf34b60a04768b01f1f8e9", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "building"}, {"label": "B", "text": "conference"}, {"label": "C", "text": "assessment"}, {"label": "D", "text": "demonstration"}, {"label": "E", "text": "garage"}], "stem": "He kept plugging away in his cubicle, it seemed he was the only person not called into the what?"}}
{"answerKey": "A", "id": "5d1df1daa886efb78db2103ddc1398eb", "question": {"question_concept": "attending school", "choices": [{"label": "A", "text": "boredom"}, {"label": "B", "text": "malaria"}, {"label": "C", "text": "graduate"}, {"label": "D", "text": "inspiration"}, {"label": "E", "text": "detention"}], "stem": "If you're attending school and are falling asleep you're likely experiencing what?"}}
{"answerKey": "E", "id": "2f8b35d352097cc9277599be49fab0b3", "question": {"question_concept": "gong", "choices": [{"label": "A", "text": "orchestra"}, {"label": "B", "text": "church"}, {"label": "C", "text": "chinese temple"}, {"label": "D", "text": "chinatown"}, {"label": "E", "text": "music store"}], "stem": "I want to buy a gong, where should I look for one?"}}
{"answerKey": "B", "id": "18eb6a3b54ccf4989e268cfb9ea90f9c", "question": {"question_concept": "friends", "choices": [{"label": "A", "text": "call each other"}, {"label": "B", "text": "group together"}, {"label": "C", "text": "understand each other"}, {"label": "D", "text": "meet for lunch"}, {"label": "E", "text": "part company"}], "stem": "What would friends do if they need each others' help?"}}
{"answerKey": "C", "id": "3e12400bc5a2038a747edf2605787fe8", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "believe in god"}, {"label": "B", "text": "dance"}, {"label": "C", "text": "desire to win"}, {"label": "D", "text": "destroy each other"}, {"label": "E", "text": "run amok"}], "stem": "When people are playing a game, what is their motivation to play?"}}
{"answerKey": "A", "id": "72baf6ca5c4daa01c2cc7fda22183db8", "question": {"question_concept": "battle", "choices": [{"label": "A", "text": "court room"}, {"label": "B", "text": "war"}, {"label": "C", "text": "video game"}, {"label": "D", "text": "iraq"}, {"label": "E", "text": "church"}], "stem": "Where could there be a battle that involves words?"}}
{"answerKey": "B", "id": "9bac07574c966cae34c85e9f25538cba", "question": {"question_concept": "getting in line", "choices": [{"label": "A", "text": "have to wait for"}, {"label": "B", "text": "standing in line"}, {"label": "C", "text": "eat cake"}, {"label": "D", "text": "less confusion"}, {"label": "E", "text": "being ordered"}], "stem": "John didn't mind getting in line.  It was what game after that he hated.  The time, the sore feet. He did not like doing what?"}}
{"answerKey": "D", "id": "fe2a21ddb1bde76025a961126044a9a3", "question": {"question_concept": "going somewhere", "choices": [{"label": "A", "text": "fire"}, {"label": "B", "text": "energy"}, {"label": "C", "text": "car"}, {"label": "D", "text": "transporting"}, {"label": "E", "text": "staying in place"}], "stem": "What is the process of going somewhere?"}}
{"answerKey": "E", "id": "d03e09b22927542d6b0d5ebe233e467c", "question": {"question_concept": "have rest", "choices": [{"label": "A", "text": "lay in bed"}, {"label": "B", "text": "lay in bed"}, {"label": "C", "text": "go to bed"}, {"label": "D", "text": "relax"}, {"label": "E", "text": "lie down"}], "stem": "The old man needed to have rest multiple times a day, he would do what on the couch and catnap?"}}
{"answerKey": "C", "id": "e63a210053cf7f961ca0b5a7e6eb355d", "question": {"question_concept": "bell", "choices": [{"label": "A", "text": "barbell"}, {"label": "B", "text": "funnel"}, {"label": "C", "text": "blunderbuss"}, {"label": "D", "text": "wind instrument"}, {"label": "E", "text": "kettlebell"}], "stem": "The end of the barrel of what primitive firearm is bell shaped?"}}
{"answerKey": "D", "id": "a4b4242fab25e86a9d7ffedcaecdcdbe", "question": {"question_concept": "pamphlets", "choices": [{"label": "A", "text": "library"}, {"label": "B", "text": "health department"}, {"label": "C", "text": "mail box"}, {"label": "D", "text": "drawer"}, {"label": "E", "text": "bookstore"}], "stem": "Where is a good place to store pamphlets in your home or office?"}}
{"answerKey": "D", "id": "ec8797b12e3c6666ebe70b2a7680b66f", "question": {"question_concept": "fishing", "choices": [{"label": "A", "text": "getting tied up lure."}, {"label": "B", "text": "looking for information"}, {"label": "C", "text": "get wet"}, {"label": "D", "text": "drink beer"}, {"label": "E", "text": "sit quietly"}], "stem": "Many humans enjoy fishing and enjoy another relaxing activity at the same time, what activity is it?"}}
{"answerKey": "D", "id": "4536489e5d8e02aadc3fcc7a55effe20", "question": {"question_concept": "maps", "choices": [{"label": "A", "text": "bookstore"}, {"label": "B", "text": "library"}, {"label": "C", "text": "electrical circuit"}, {"label": "D", "text": "cabinet"}, {"label": "E", "text": "important when traveling"}], "stem": "Where would you get some maps that you own?"}}
{"answerKey": "C", "id": "0854478d174c9127064f0d4b58df7e62", "question": {"question_concept": "hamburger", "choices": [{"label": "A", "text": "resturant"}, {"label": "B", "text": "fast food restaurant"}, {"label": "C", "text": "mouth"}, {"label": "D", "text": "kitchen"}, {"label": "E", "text": "pizza"}], "stem": "Where is a good place to put a hamburger?"}}
{"answerKey": "B", "id": "4b7d1b70060cd1f1a7321795f62a7325", "question": {"question_concept": "steel pen", "choices": [{"label": "A", "text": "car shop"}, {"label": "B", "text": "desk drawer"}, {"label": "C", "text": "car."}, {"label": "D", "text": "warehouse"}, {"label": "E", "text": "hand"}], "stem": "Where is a handy place to store a steel pen in your office?"}}
{"answerKey": "D", "id": "0e6a005eec5e6746f3facf4d608bfd8b", "question": {"question_concept": "story", "choices": [{"label": "A", "text": "book or library"}, {"label": "B", "text": "book or magazine"}, {"label": "C", "text": "newspaper"}, {"label": "D", "text": "past"}, {"label": "E", "text": "future"}], "stem": "A story about World War II would be set when?"}}
{"answerKey": "A", "id": "2d2b69ad187b7c40273ab13caab7dc19", "question": {"question_concept": "marmot", "choices": [{"label": "A", "text": "mountainous area"}, {"label": "B", "text": "wood pile"}, {"label": "C", "text": "jungle"}, {"label": "D", "text": "petting zoo"}, {"label": "E", "text": "animals"}], "stem": "What type of geographic area will you find a marmot?"}}
{"answerKey": "B", "id": "fde1f9bfc33da302449c0b950d16c0ea", "question": {"question_concept": "most people", "choices": [{"label": "A", "text": "set table"}, {"label": "B", "text": "think"}, {"label": "C", "text": "read books"}, {"label": "D", "text": "play games"}, {"label": "E", "text": "lie"}], "stem": "Most people make stupid assumptions that are based on their prejudices.  What might they do instead to achieve better outcomes?"}}
{"answerKey": "A", "id": "3c90a632f46aeab11fbb73aa59a33892", "question": {"question_concept": "children", "choices": [{"label": "A", "text": "listen to music"}, {"label": "B", "text": "watch television"}, {"label": "C", "text": "play chess"}, {"label": "D", "text": "walk"}, {"label": "E", "text": "play basketball"}], "stem": "What is something children can do while traveling in a car?"}}
{"answerKey": "C", "id": "1f3ccb722600da7d862531416934949a", "question": {"question_concept": "trumpet", "choices": [{"label": "A", "text": "music store"}, {"label": "B", "text": "bass"}, {"label": "C", "text": "brass band"}, {"label": "D", "text": "orchestra"}, {"label": "E", "text": "marching band"}], "stem": "Where would you hear a trumpet along with other instruments made from the same material?"}}
{"answerKey": "A", "id": "46ba5d2b8cfc6708e5e2618568d8730e", "question": {"question_concept": "audience", "choices": [{"label": "A", "text": "concert hall"}, {"label": "B", "text": "museum"}, {"label": "C", "text": "school"}, {"label": "D", "text": "hockey game"}, {"label": "E", "text": "sporting event"}], "stem": "The audience listened to the orchestra play, where were they watching the performance?"}}
{"answerKey": "B", "id": "f8a2cbc7189b92a809ce9cd857030621", "question": {"question_concept": "stabbing to death", "choices": [{"label": "A", "text": "pool of blood"}, {"label": "B", "text": "gruesome"}, {"label": "C", "text": "charming"}, {"label": "D", "text": "being arrested"}, {"label": "E", "text": "killing"}], "stem": "Stabbing to death of a person is what sort of way to die?"}}
{"answerKey": "B", "id": "225287e06c993feee34e0f06b25f6ba8", "question": {"question_concept": "getting", "choices": [{"label": "A", "text": "asking for"}, {"label": "B", "text": "money"}, {"label": "C", "text": "food"}, {"label": "D", "text": "work"}, {"label": "E", "text": "energy"}], "stem": "What are you getting from you boss at the end of the week?"}}
{"answerKey": "C", "id": "e211b1a3f3401d164c8b0bfc10160caa", "question": {"question_concept": "ticket", "choices": [{"label": "A", "text": "lottery"}, {"label": "B", "text": "person's hand"}, {"label": "C", "text": "baseball stadium"}, {"label": "D", "text": "movie"}, {"label": "E", "text": "kitchen"}], "stem": "If you have a ticket and you are planning to eat hot dogs, where would you go?"}}
{"answerKey": "E", "id": "fce1c5d069758aea57a787fc98dcf7a9", "question": {"question_concept": "fruit", "choices": [{"label": "A", "text": "san francisco"}, {"label": "B", "text": "refrigerator"}, {"label": "C", "text": "big box retailer"}, {"label": "D", "text": "tree"}, {"label": "E", "text": "market"}], "stem": "Where is a great place to buy fresh fruit?"}}
{"answerKey": "B", "id": "c0d75f9fbf30aa3a612f16edb20d6b8d", "question": {"question_concept": "paperwork", "choices": [{"label": "A", "text": "desk"}, {"label": "B", "text": "meeting"}, {"label": "C", "text": "office"}, {"label": "D", "text": "table"}, {"label": "E", "text": "work"}], "stem": "The man took paperwork to other people to consult over it, where was he heading?"}}
{"answerKey": "B", "id": "d07f149d8d953dcc45dda432194c375e", "question": {"question_concept": "having fun", "choices": [{"label": "A", "text": "painting his nails"}, {"label": "B", "text": "playing marbles"}, {"label": "C", "text": "constructing"}, {"label": "D", "text": "need for rest"}, {"label": "E", "text": "wild ride"}], "stem": "Stark was just having fun, and he wasn't hurting anyone.  What might have he been doing?"}}
{"answerKey": "D", "id": "080a9cf2d6447a9a4d98b0af311e10da", "question": {"question_concept": "giving assistance", "choices": [{"label": "A", "text": "exhilliration"}, {"label": "B", "text": "hardship"}, {"label": "C", "text": "risk taking"}, {"label": "D", "text": "helping others"}, {"label": "E", "text": "happiness"}], "stem": "The church was giving assistance, what were they hoping to accomplish?"}}
{"answerKey": "A", "id": "111501a49dd41ceed9c2073eed5d2b72", "question": {"question_concept": "god", "choices": [{"label": "A", "text": "heaven"}, {"label": "B", "text": "church"}, {"label": "C", "text": "imagination"}, {"label": "D", "text": "synagogue"}, {"label": "E", "text": "monastery"}], "stem": "I you believe in god, where will you go when you die?"}}
{"answerKey": "D", "id": "7bb87c6d8eab57d4e983f60025b1f0dc", "question": {"question_concept": "eating hamburger", "choices": [{"label": "A", "text": "tasty"}, {"label": "B", "text": "health problems"}, {"label": "C", "text": "eat cake"}, {"label": "D", "text": "indigestion"}, {"label": "E", "text": "gain weight"}], "stem": "What can eating hamburger cause immediately after eating it?"}}
{"answerKey": "C", "id": "5c2bc4335c8860342ec2d568ceb6ac6b", "question": {"question_concept": "shelf", "choices": [{"label": "A", "text": "refrigerator"}, {"label": "B", "text": "bookstore"}, {"label": "C", "text": "cupboard"}, {"label": "D", "text": "school building"}, {"label": "E", "text": "wardrobe"}], "stem": "Where is a shelf likely to be hidden behind a door?"}}
{"answerKey": "A", "id": "083861fc5ebb9226fff70544f3f83d2b", "question": {"question_concept": "pail", "choices": [{"label": "A", "text": "garage"}, {"label": "B", "text": "hardware store"}, {"label": "C", "text": "utility room"}, {"label": "D", "text": "wishing well"}, {"label": "E", "text": "laundry"}], "stem": "The man got a pail to catch the draining motor oil, where was he likely doing this at home?"}}
{"answerKey": "E", "id": "520b0eea9148e3cb4d45aa69a55491eb", "question": {"question_concept": "cold storage", "choices": [{"label": "A", "text": "ice pack"}, {"label": "B", "text": "freezer"}, {"label": "C", "text": "laboratory"}, {"label": "D", "text": "warehouse"}, {"label": "E", "text": "refrigerator"}], "stem": "What kind of cold storage could you find in your house?"}}
{"answerKey": "A", "id": "ef6ede0af827ddd1dc7bbeb36a6fdd22", "question": {"question_concept": "restaurant", "choices": [{"label": "A", "text": "big city"}, {"label": "B", "text": "town"}, {"label": "C", "text": "small town"}, {"label": "D", "text": "canada"}, {"label": "E", "text": "yellow pages"}], "stem": "Where could you go to between 1000 and 10000 restaurant?"}}
{"answerKey": "E", "id": "d47986deb91d64b2b15d385da3d2f483", "question": {"question_concept": "mound", "choices": [{"label": "A", "text": "hell"}, {"label": "B", "text": "baseball stadium"}, {"label": "C", "text": "golf course"}, {"label": "D", "text": "africa"}, {"label": "E", "text": "baseball diamond"}], "stem": "The pitcher stepped on the mound ready to throw, where was he located specifically?"}}
{"answerKey": "D", "id": "c3b7f4196b12714940ac1b9417194df4", "question": {"question_concept": "platform", "choices": [{"label": "A", "text": "below"}, {"label": "B", "text": "arena"}, {"label": "C", "text": "concert hall"}, {"label": "D", "text": "museum"}, {"label": "E", "text": "building"}], "stem": "Where is a statute found on a platform?"}}
{"answerKey": "C", "id": "5d03ad171fd661a28da5b6eb79967a6b", "question": {"question_concept": "round brush", "choices": [{"label": "A", "text": "hair brush"}, {"label": "B", "text": "ladies bathroom"}, {"label": "C", "text": "art supplies"}, {"label": "D", "text": "shower"}, {"label": "E", "text": "hair salon"}], "stem": "If it's not used for hair a round brush is an example of what?"}}
{"answerKey": "C", "id": "7c95d753943c58757fe6e1ccff8aea14", "question": {"question_concept": "boredom", "choices": [{"label": "A", "text": "meet interesting people"}, {"label": "B", "text": "lift weights"}, {"label": "C", "text": "listen to music"}, {"label": "D", "text": "play chess"}, {"label": "E", "text": "entertain"}], "stem": "His parents thought he was suffering from boredom, but the teen loved to lay in bed and just do what?"}}
{"answerKey": "D", "id": "88d8bfb9dc8e77ef642acbe1a129f3db", "question": {"question_concept": "eating hamburger", "choices": [{"label": "A", "text": "eat cake"}, {"label": "B", "text": "have fun"}, {"label": "C", "text": "food poisoning"}, {"label": "D", "text": "heartburn"}, {"label": "E", "text": "gain weight"}], "stem": "At the picnic she was stuck eating hamburger, she was worried because she forgot her chewables to prevent what?"}}
{"answerKey": "A", "id": "b1a9b20793b46e46e1beedadbf852f84", "question": {"question_concept": "electrode", "choices": [{"label": "A", "text": "battery"}, {"label": "B", "text": "electronic equipment"}, {"label": "C", "text": "electrolytic cell"}, {"label": "D", "text": "charge"}, {"label": "E", "text": "tube"}], "stem": "The electrode wouldn't spark, it turned out that the what hadn't been connected?"}}
{"answerKey": "A", "id": "81e016974d33fe383c848b6c819791cd", "question": {"question_concept": "government", "choices": [{"label": "A", "text": "country"}, {"label": "B", "text": "democracy"}, {"label": "C", "text": "canada"}, {"label": "D", "text": "civilization"}, {"label": "E", "text": "tax office"}], "stem": "For what entity should the government work?"}}
{"answerKey": "C", "id": "7cf54544d54818d53e7088c0749a3eca", "question": {"question_concept": "student", "choices": [{"label": "A", "text": "answer question"}, {"label": "B", "text": "learn language"}, {"label": "C", "text": "do mathematics"}, {"label": "D", "text": "be able to count"}, {"label": "E", "text": "begin to study"}], "stem": "What must a student in engineering do?"}}
{"answerKey": "A", "id": "6acd88b9b5dd15e23bbcc3fd679100a8", "question": {"question_concept": "division", "choices": [{"label": "A", "text": "multiplication"}, {"label": "B", "text": "multiply"}, {"label": "C", "text": "putting together"}, {"label": "D", "text": "unity"}, {"label": "E", "text": "pay debts"}], "stem": "The teacher knew her students understood division, what was she hoping they would learn next?"}}
{"answerKey": "B", "id": "c96a86957a9ab1d8ca0aeeb7f040d87a_1", "question": {"question_concept": "dictionary", "choices": [{"label": "A", "text": "pocket"}, {"label": "B", "text": "classroom"}, {"label": "C", "text": "table"}, {"label": "D", "text": "library"}, {"label": "E", "text": "shelf"}], "stem": "There were times where kids wanted to know a definition, so there was a nice big dictionary in the what?"}}
{"answerKey": "B", "id": "6a1bf527af9ed0685ac5e2bf0bd76647", "question": {"question_concept": "riding bike", "choices": [{"label": "A", "text": "enjoyment"}, {"label": "B", "text": "fatigue"}, {"label": "C", "text": "falling down"}, {"label": "D", "text": "getting lost"}, {"label": "E", "text": "thirst"}], "stem": "Riding a bike for a long time can cause what?"}}
{"answerKey": "B", "id": "094fe91b20b03c647325fa2ee94470b3", "question": {"question_concept": "cat", "choices": [{"label": "A", "text": "feline"}, {"label": "B", "text": "thirsty"}, {"label": "C", "text": "sharp claws"}, {"label": "D", "text": "pussycat"}, {"label": "E", "text": "hungry"}], "stem": "What could happen to a cat other than wanting food?"}}
{"answerKey": "A", "id": "bee2a6eadfaf7a4fa0a214e341ddbe5b", "question": {"question_concept": "music", "choices": [{"label": "A", "text": "silent"}, {"label": "B", "text": "opera"}, {"label": "C", "text": "silence"}, {"label": "D", "text": "television"}, {"label": "E", "text": "elevator"}], "stem": "If you turn off the music in a room with no other noise that room would be what?"}}
{"answerKey": "D", "id": "2f97a77d155cb99092e8a7c055737b03_1", "question": {"question_concept": "fast food restaurant", "choices": [{"label": "A", "text": "new york"}, {"label": "B", "text": "blocks of flats"}, {"label": "C", "text": "center of town"}, {"label": "D", "text": "america"}, {"label": "E", "text": "big cities"}], "stem": "In what country are the most fast food restaurants?"}}
{"answerKey": "A", "id": "bc268cd19e2c95c78967fd6b9092fb90", "question": {"question_concept": "string", "choices": [{"label": "A", "text": "tie around"}, {"label": "B", "text": "wind around"}, {"label": "C", "text": "weave"}, {"label": "D", "text": "stringbed"}, {"label": "E", "text": "ball up"}], "stem": "I want to use string to keep something from moving, how should I do it?"}}
{"answerKey": "D", "id": "060cad0d3c007ceb151db9907bfcb214", "question": {"question_concept": "central passage", "choices": [{"label": "A", "text": "tomb"}, {"label": "B", "text": "arena"}, {"label": "C", "text": "access rooms"}, {"label": "D", "text": "public building"}, {"label": "E", "text": "house"}], "stem": "Where would walk through a central passage to catch an elevator?"}}
{"answerKey": "D", "id": "29c2cc0ba85b4afb9c9d29801469a68f", "question": {"question_concept": "potato", "choices": [{"label": "A", "text": "farmer's market"}, {"label": "B", "text": "grocery bag"}, {"label": "C", "text": "pantry"}, {"label": "D", "text": "bushel basket"}, {"label": "E", "text": "fridge"}], "stem": "A potato is kept in the cellar, where is likely to be stored?"}}
{"answerKey": "A", "id": "6cb895ce89995f6be422f7c4167c7638", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "build trust"}, {"label": "B", "text": "hurry home"}, {"label": "C", "text": "ignore people"}, {"label": "D", "text": "believe in god"}, {"label": "E", "text": "jump to conclusions"}], "stem": "What do people do when networking?"}}
{"answerKey": "D", "id": "839f3c37622c1ed5eebc9cd0b9d658e8", "question": {"question_concept": "linen", "choices": [{"label": "A", "text": "hospital"}, {"label": "B", "text": "chest"}, {"label": "C", "text": "home"}, {"label": "D", "text": "dresser drawers"}, {"label": "E", "text": "cabinet"}], "stem": "Where can you store you spare linens near your socks?"}}
{"answerKey": "A", "id": "3957ac6bab96fc9d4f173ada4692d16b", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "jump to conclusions"}, {"label": "B", "text": "hurry home"}, {"label": "C", "text": "build trust"}, {"label": "D", "text": "pay bills"}, {"label": "E", "text": "sing"}], "stem": "What do people do when they think too quickly?"}}
{"answerKey": "D", "id": "a4f5e5412f0f8ac9190db1730db07a90", "question": {"question_concept": "sex", "choices": [{"label": "A", "text": "sexploiter"}, {"label": "B", "text": "chicken"}, {"label": "C", "text": "reproductive cycle"}, {"label": "D", "text": "procreation"}, {"label": "E", "text": "human experience"}], "stem": "What is someone likely to want as a result of sex?"}}
{"answerKey": "B", "id": "cb5b39878be0e05a3ffe783801adbc3b", "question": {"question_concept": "creating art", "choices": [{"label": "A", "text": "frustration"}, {"label": "B", "text": "relax"}, {"label": "C", "text": "eat"}, {"label": "D", "text": "enlightenment"}, {"label": "E", "text": "communication"}], "stem": "What might someone do after they finish creating art?"}}
{"answerKey": "D", "id": "985a4f1a3f31f1ba6654f4fc48f504df", "question": {"question_concept": "clean clothes", "choices": [{"label": "A", "text": "get dirty"}, {"label": "B", "text": "writing"}, {"label": "C", "text": "use water"}, {"label": "D", "text": "launder"}, {"label": "E", "text": "soap"}], "stem": "To get clean clothes you to what to them?"}}
{"answerKey": "C", "id": "5d687fe9c95436ce84230c996d34382d", "question": {"question_concept": "reduce", "choices": [{"label": "A", "text": "grow"}, {"label": "B", "text": "gain weight"}, {"label": "C", "text": "make larger"}, {"label": "D", "text": "augment"}, {"label": "E", "text": "get bigger"}], "stem": "The person tried to reduce his weight with a shrink ray, but he got it backwards and only did what?"}}
{"answerKey": "A", "id": "af11faa29097b71141fe192ad019d1dd", "question": {"question_concept": "baby", "choices": [{"label": "A", "text": "old person"}, {"label": "B", "text": "begin to talk"}, {"label": "C", "text": "adult"}, {"label": "D", "text": "old man"}, {"label": "E", "text": "girl"}], "stem": "Christine couldn't be having a baby at her age, she thought to herself. What was Christine?"}}
{"answerKey": "D", "id": "07fd8b0aed06406fedb137d11b07a890", "question": {"question_concept": "percussion instrument", "choices": [{"label": "A", "text": "own home"}, {"label": "B", "text": "music store"}, {"label": "C", "text": "marching band"}, {"label": "D", "text": "orchestra"}, {"label": "E", "text": "party"}], "stem": "Joe plays a percussion instrument in something.  What might be play in?"}}
{"answerKey": "C", "id": "7044d82a456d0fa6f0210abb03cbf2c4", "question": {"question_concept": "playing ball", "choices": [{"label": "A", "text": "losing"}, {"label": "B", "text": "competition"}, {"label": "C", "text": "having fun"}, {"label": "D", "text": "win"}, {"label": "E", "text": "injury"}], "stem": "If I'm playing ball, I'm mostly trying to do what?"}}
{"answerKey": "B", "id": "e53ba4c7d2a818bdb6001e6924bc8896", "question": {"question_concept": "compete against", "choices": [{"label": "A", "text": "cheat"}, {"label": "B", "text": "fair"}, {"label": "C", "text": "in competition"}, {"label": "D", "text": "practice"}, {"label": "E", "text": "sabotage"}], "stem": "What do the terms need to be in order to compete against someone?"}}
{"answerKey": "D", "id": "ecbc1ab06ad1ed6c53e5293d7a90ebd3", "question": {"question_concept": "silk", "choices": [{"label": "A", "text": "jean"}, {"label": "B", "text": "mulberry tree"}, {"label": "C", "text": "garments"}, {"label": "D", "text": "expensive clothing"}, {"label": "E", "text": "parachutes"}], "stem": "If you wanted to show off silk, what item could it be on?"}}
{"answerKey": "D", "id": "9a356ff463c042d04ba45bfd627bac20", "question": {"question_concept": "information", "choices": [{"label": "A", "text": "park"}, {"label": "B", "text": "internet"}, {"label": "C", "text": "meeting"}, {"label": "D", "text": "library"}, {"label": "E", "text": "book"}], "stem": "Where is known to be a wealth of information?"}}
{"answerKey": "D", "id": "0a5c069836784c3d574828d85a20a074", "question": {"question_concept": "drawer", "choices": [{"label": "A", "text": "file cabinet"}, {"label": "B", "text": "nightstand"}, {"label": "C", "text": "kitchen cabinet"}, {"label": "D", "text": "office desk"}, {"label": "E", "text": "the floor"}], "stem": "I saw the receptionist carelessly toss my resume into the drawer, where did I want it to end up?"}}
{"answerKey": "B", "id": "f996430ce208606452868fd2e739d409", "question": {"question_concept": "water", "choices": [{"label": "A", "text": "dilute"}, {"label": "B", "text": "thin blood"}, {"label": "C", "text": "take several forms"}, {"label": "D", "text": "wet clothes"}, {"label": "E", "text": "move mountains"}], "stem": "What will happen if you inject water into yourself?"}}
{"answerKey": "E", "id": "26c854d933d2115e7636fdcde57eb463", "question": {"question_concept": "playing baseball", "choices": [{"label": "A", "text": "fame"}, {"label": "B", "text": "errors"}, {"label": "C", "text": "pain"}, {"label": "D", "text": "strikes"}, {"label": "E", "text": "sore muscles"}], "stem": "Athletes soak in hot tubs to relieve what after playing baseball?"}}
{"answerKey": "C", "id": "83c25b9a5db5f9b3fd1ff6c7453d23d0", "question": {"question_concept": "gambler", "choices": [{"label": "A", "text": "play cards"}, {"label": "B", "text": "double winnings"}, {"label": "C", "text": "lose money"}, {"label": "D", "text": "play poker"}, {"label": "E", "text": "to win the prize"}], "stem": "What does a gambler do that causes him or her to be unhappy?"}}
{"answerKey": "B", "id": "a0d02fc32878efdf0b0d420972943492", "question": {"question_concept": "eat vegetables", "choices": [{"label": "A", "text": "lose weight"}, {"label": "B", "text": "good for"}, {"label": "C", "text": "bland"}, {"label": "D", "text": "chewing"}, {"label": "E", "text": "fibre"}], "stem": "There's one obvious reason to eat vegetables, they're plain what you?"}}
{"answerKey": "C", "id": "73fbd2caac2c3786ca810adfe7030273", "question": {"question_concept": "thick", "choices": [{"label": "A", "text": "pay debts"}, {"label": "B", "text": "slender"}, {"label": "C", "text": "unacquainted"}, {"label": "D", "text": "free flowing"}, {"label": "E", "text": "sparse"}], "stem": "John was a bit think in the head, but he knew that he never saw the lady before.  They were what?"}}
{"answerKey": "C", "id": "6c515b068b4d3aa88a5382224d9b866d", "question": {"question_concept": "violin", "choices": [{"label": "A", "text": "school"}, {"label": "B", "text": "string quartet"}, {"label": "C", "text": "orchestra"}, {"label": "D", "text": "kitchen"}, {"label": "E", "text": "music room"}], "stem": "Where would you hear a violin along side many string and wind instruments?"}}
{"answerKey": "D", "id": "0af371b94fb414860b13eea6009ccc31", "question": {"question_concept": "sun", "choices": [{"label": "A", "text": "earth warming"}, {"label": "B", "text": "sun tan"}, {"label": "C", "text": "light"}, {"label": "D", "text": "life on earth"}, {"label": "E", "text": "heat"}], "stem": "What is the sun ultimately responsible for?"}}
{"answerKey": "D", "id": "38e61d4be0da46b3cbbd76dc20bce677", "question": {"question_concept": "train station", "choices": [{"label": "A", "text": "downtown area"}, {"label": "B", "text": "centre of town"}, {"label": "C", "text": "bedroom"}, {"label": "D", "text": "europe"}, {"label": "E", "text": "big city"}], "stem": "Mandy lived in a train station.  She longed to see distant places. Where might she imagine going?"}}
{"answerKey": "C", "id": "cebc07bd5080cc72862cb333b10d782d", "question": {"question_concept": "animal", "choices": [{"label": "A", "text": "pet store"}, {"label": "B", "text": "outside"}, {"label": "C", "text": "woodland"}, {"label": "D", "text": "ocean"}, {"label": "E", "text": "cafe"}], "stem": "Joe is a  squirrel, which is an animal. He probably lives in what sort of place."}}
{"answerKey": "C", "id": "de0386024f32cdf277a785a851b97544", "question": {"question_concept": "ficus", "choices": [{"label": "A", "text": "cabin in the woods"}, {"label": "B", "text": "california"}, {"label": "C", "text": "front yard"}, {"label": "D", "text": "conservatory"}, {"label": "E", "text": "tropical forest"}], "stem": "Where could a personal ficus live?"}}
{"answerKey": "A", "id": "9b62cd7f89716f393239e6c6ff3e11d5", "question": {"question_concept": "shark", "choices": [{"label": "A", "text": "court room"}, {"label": "B", "text": "shallow waters"}, {"label": "C", "text": "poker game"}, {"label": "D", "text": "sea world"}, {"label": "E", "text": "pond arena"}], "stem": "The shark actually counted as evidence, so where did the legal team bring it?"}}
{"answerKey": "E", "id": "8b25332de2894ab38784235838d38cec", "question": {"question_concept": "snake", "choices": [{"label": "A", "text": "street"}, {"label": "B", "text": "tropical forest"}, {"label": "C", "text": "garden of eden"}, {"label": "D", "text": "new mexico"}, {"label": "E", "text": "white house"}], "stem": "If the president wanted to ban snakes, where would he issue such a decree?"}}
{"answerKey": "A", "id": "dd4a811d18549f1ae1954cf938b28536", "question": {"question_concept": "rocks", "choices": [{"label": "A", "text": "ground"}, {"label": "B", "text": "drawer"}, {"label": "C", "text": "surface of earth"}, {"label": "D", "text": "pizza"}, {"label": "E", "text": "waterfall"}], "stem": "They were searching for rocks, so they missed the birds overhead as they stared at the what?"}}
{"answerKey": "E", "id": "e2ff952c17faf1c56a970502630d4c86", "question": {"question_concept": "bottle", "choices": [{"label": "A", "text": "supermarket"}, {"label": "B", "text": "diaper bag"}, {"label": "C", "text": "liquor store"}, {"label": "D", "text": "hollow log"}, {"label": "E", "text": "medicine cabinet"}], "stem": "Her son scraped his knee, she fetched a bottle of peroxide from the what?"}}
{"answerKey": "E", "id": "3a6140e475cbbd3ee1da5ba9a6953597_1", "question": {"question_concept": "dictionary", "choices": [{"label": "A", "text": "classroom"}, {"label": "B", "text": "shelf"}, {"label": "C", "text": "explain meaning of words"}, {"label": "D", "text": "table"}, {"label": "E", "text": "library"}], "stem": "Where would you expect to find a dictionary along side other writings you can borrow?"}}
{"answerKey": "B", "id": "e75e0c11e2d5a7b634455a1b4b76856c", "question": {"question_concept": "getting in shape", "choices": [{"label": "A", "text": "good health"}, {"label": "B", "text": "exercise"}, {"label": "C", "text": "muscle tone"}, {"label": "D", "text": "sweat"}, {"label": "E", "text": "feel better"}], "stem": "What would be necessary for getting in shape?"}}
{"answerKey": "A", "id": "3b9ccdcb1c932c46a38e040d3e6c7f5b", "question": {"question_concept": "statue", "choices": [{"label": "A", "text": "water fountain"}, {"label": "B", "text": "large city"}, {"label": "C", "text": "museum"}, {"label": "D", "text": "pool"}, {"label": "E", "text": "central park"}], "stem": "A statue that shoots liquid is called a what?"}}
{"answerKey": "B", "id": "6a29b657b29e1506284d8328dffbbd21", "question": {"question_concept": "trouble", "choices": [{"label": "A", "text": "park"}, {"label": "B", "text": "calm"}, {"label": "C", "text": "being good"}, {"label": "D", "text": "good behavior"}, {"label": "E", "text": "safe"}], "stem": "If you have a child who gets in trouble for being hyperactive you may need to teach them how to what down?"}}
{"answerKey": "B", "id": "96cb628fb7ed2f53245598f707ed2b80", "question": {"question_concept": "paint", "choices": [{"label": "A", "text": "clothes get stained"}, {"label": "B", "text": "with brush"}, {"label": "C", "text": "wallpaper"}, {"label": "D", "text": "electrical circuit"}, {"label": "E", "text": "draw"}], "stem": "John loved to paint houses.  How did he usually do it?"}}
{"answerKey": "C", "id": "bd4e80fa6642a76c064d0bc924411fb0", "question": {"question_concept": "mat", "choices": [{"label": "A", "text": "a chair"}, {"label": "B", "text": "school"}, {"label": "C", "text": "living room"}, {"label": "D", "text": "doorway"}, {"label": "E", "text": "bathroom"}], "stem": "When you wipe you feet on the door mat and walk through the door where do you enter?"}}
{"answerKey": "D", "id": "05490e6c191fbc3c2fe0033ed0bd8aa0", "question": {"question_concept": "book", "choices": [{"label": "A", "text": "library of congress"}, {"label": "B", "text": "pocket"}, {"label": "C", "text": "backpack"}, {"label": "D", "text": "suitcase"}, {"label": "E", "text": "synagogue"}], "stem": "What can you use to store a book while traveling?"}}
{"answerKey": "E", "id": "6abd34442438509b4a00c69d6fd24764", "question": {"question_concept": "gazelle", "choices": [{"label": "A", "text": "open field"}, {"label": "B", "text": "ivory coast"}, {"label": "C", "text": "dictionary"}, {"label": "D", "text": "steppe"}, {"label": "E", "text": "encyclopedia"}], "stem": "Where would you find gazelle under a G?"}}
{"answerKey": "D", "id": "e58eb0ec4197c29e961a7bdd4d67de4e", "question": {"question_concept": "competing", "choices": [{"label": "A", "text": "winning or losing"}, {"label": "B", "text": "aggression"}, {"label": "C", "text": "gain"}, {"label": "D", "text": "defeat"}, {"label": "E", "text": "sweat"}], "stem": "Competing can lead to great highs, and also great lows when suffering what?"}}
{"answerKey": "E", "id": "597d2a1c9df7962218d8b807df1f8212", "question": {"question_concept": "sunshine", "choices": [{"label": "A", "text": "summer"}, {"label": "B", "text": "park"}, {"label": "C", "text": "desktop"}, {"label": "D", "text": "sea"}, {"label": "E", "text": "moon"}], "stem": "What blocks sunshine?"}}
{"answerKey": "E", "id": "68f6ac445cc008d93f931b999b44b0ba", "question": {"question_concept": "heat", "choices": [{"label": "A", "text": "coolness"}, {"label": "B", "text": "fan"}, {"label": "C", "text": "get wet"}, {"label": "D", "text": "coldness"}, {"label": "E", "text": "air conditioning"}], "stem": "When you feel too much heat in your home you can turn on what?"}}
{"answerKey": "D", "id": "aa4c5d2d348796b8d7fa324f27f4c34f", "question": {"question_concept": "pillow case", "choices": [{"label": "A", "text": "kitchen cupboard"}, {"label": "B", "text": "bedding store"}, {"label": "C", "text": "england"}, {"label": "D", "text": "drawer"}, {"label": "E", "text": "bedroom"}], "stem": "Where would you store a pillow case that is not in use?"}}
{"answerKey": "D", "id": "7400e9c4a2c8e600a0f7e2d162a07837", "question": {"question_concept": "kitten", "choices": [{"label": "A", "text": "shelter"}, {"label": "B", "text": "floor"}, {"label": "C", "text": "warm place"}, {"label": "D", "text": "farmhouse"}, {"label": "E", "text": "living room"}], "stem": "If the kitten was going to grow up to be a mouser like it's mother, where should it spend most of it's time?"}}
{"answerKey": "C", "id": "fad197409a977126c9587eccd240ceea", "question": {"question_concept": "human", "choices": [{"label": "A", "text": "space shuttle"}, {"label": "B", "text": "theater"}, {"label": "C", "text": "china"}, {"label": "D", "text": "indian resteraunt"}, {"label": "E", "text": "bar"}], "stem": "Where is that man buying silk from?"}}
{"answerKey": "D", "id": "f09038444aeb1a048f04dedd5b97b769", "question": {"question_concept": "clavichord", "choices": [{"label": "A", "text": "living room"}, {"label": "B", "text": "parlor"}, {"label": "C", "text": "music hall"}, {"label": "D", "text": "music room"}, {"label": "E", "text": "museum"}], "stem": "Where is a teacher likely to keep her clavichord?"}}
{"answerKey": "C", "id": "0aa23ad1ba9f28bc3e0185237a7ce1cc", "question": {"question_concept": "briefcase", "choices": [{"label": "A", "text": "luggage store"}, {"label": "B", "text": "courtroom"}, {"label": "C", "text": "airport"}, {"label": "D", "text": "office building"}, {"label": "E", "text": "hand"}], "stem": "Where are you if your bieifcase is going through an x-ray machine?"}}
{"answerKey": "B", "id": "06be29539ad3e1fbd7b53b05243f4bd7", "question": {"question_concept": "kissing", "choices": [{"label": "A", "text": "partner"}, {"label": "B", "text": "trust"}, {"label": "C", "text": "cooperation"}, {"label": "D", "text": "bricks"}, {"label": "E", "text": "herpes"}], "stem": "They were kissing each other good bye, they had no worries because their relationship had a strong foundation of what?"}}
{"answerKey": "A", "id": "bbe0a1ad733e5699f991ff91b3712a6f", "question": {"question_concept": "take bus", "choices": [{"label": "A", "text": "commute"}, {"label": "B", "text": "flying"}, {"label": "C", "text": "get somewhere"}, {"label": "D", "text": "travel"}, {"label": "E", "text": "go home"}], "stem": "Why would you take a bus to work?"}}
{"answerKey": "C", "id": "9e5ce2b7d9eb404cdf8c7317dd0b5a59", "question": {"question_concept": "going fishing", "choices": [{"label": "A", "text": "to see the fish"}, {"label": "B", "text": "have fun"}, {"label": "C", "text": "catching fish"}, {"label": "D", "text": "wet clothes"}, {"label": "E", "text": "killing"}], "stem": "If you are hungry and going fishing, why would you be going fishing?"}}
{"answerKey": "D", "id": "ffde211723f55e9744f94cbc14488a23", "question": {"question_concept": "dogs", "choices": [{"label": "A", "text": "fleas"}, {"label": "B", "text": "eat cake"}, {"label": "C", "text": "attack"}, {"label": "D", "text": "defend"}, {"label": "E", "text": "run fast"}], "stem": "Dogs are very loyal if they have a good owner, they will always what them?"}}
{"answerKey": "D", "id": "5ff8b0deed53b9ff91d58bd5b6f85bdf", "question": {"question_concept": "farmer", "choices": [{"label": "A", "text": "seed plants"}, {"label": "B", "text": "plant seeds"}, {"label": "C", "text": "garden"}, {"label": "D", "text": "grow corn"}, {"label": "E", "text": "produce food"}], "stem": "What does a farmer need to do to make  a maze on his farm in the fall?"}}
{"answerKey": "C", "id": "36f1ceeecde7abf99dab635239e12442", "question": {"question_concept": "hair", "choices": [{"label": "A", "text": "thin out"}, {"label": "B", "text": "grow in ear"}, {"label": "C", "text": "fall out"}, {"label": "D", "text": "bulge"}, {"label": "E", "text": "composted"}], "stem": "For many males hair is a concern as they get older, it begins to what, causing a receding hairline?"}}
{"answerKey": "E", "id": "e3c9e83c0c62d842de2dfe229f5e6d41", "question": {"question_concept": "play poker", "choices": [{"label": "A", "text": "think"}, {"label": "B", "text": "ante up"}, {"label": "C", "text": "drink"}, {"label": "D", "text": "win money"}, {"label": "E", "text": "losing money"}], "stem": "What happens someone who is bad play poker?"}}
{"answerKey": "C", "id": "c0e4d0118c9cdfe2edc49ef954572b31", "question": {"question_concept": "snake", "choices": [{"label": "A", "text": "sun itself"}, {"label": "B", "text": "tropical forest"}, {"label": "C", "text": "pet"}, {"label": "D", "text": "rude"}, {"label": "E", "text": "sharp"}], "stem": "John loved his snake.  It was the only ting he loved. He hated everyone else and was abrasive to most people, but he loved his snake.   How might you describe the snake?"}}
{"answerKey": "A", "id": "4423c006f2a43f222d4c4e97360c25d3", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "water plants"}, {"label": "B", "text": "believe in god"}, {"label": "C", "text": "drive to the nearest pool"}, {"label": "D", "text": "speaking english"}, {"label": "E", "text": "raise children"}], "stem": "The fresh herbs, flowers, and vegetables will shrivel up if people don't do this?"}}
{"answerKey": "C", "id": "9382bc51ba092f55a494eff8615899de", "question": {"question_concept": "apple tree", "choices": [{"label": "A", "text": "woods"}, {"label": "B", "text": "illinois"}, {"label": "C", "text": "indiana"}, {"label": "D", "text": "washington state"}, {"label": "E", "text": "tampa"}], "stem": "I picked from an apple tree outside of Fort Wayne, where am I?"}}
{"answerKey": "A", "id": "dec1c42628a7448aa364cdada6e82f98", "question": {"question_concept": "paper", "choices": [{"label": "A", "text": "synagogue"}, {"label": "B", "text": "front porch"}, {"label": "C", "text": "classroom"}, {"label": "D", "text": "obesity"}, {"label": "E", "text": "grocery store"}], "stem": "The janitor never had much to clean after services, but there was still always a paper or two to pick up where?"}}
{"answerKey": "D", "id": "07ea8ff6ee916f2bf9aceab1e19ff99a", "question": {"question_concept": "celebrating", "choices": [{"label": "A", "text": "drunkenness"}, {"label": "B", "text": "have fun"}, {"label": "C", "text": "headache"}, {"label": "D", "text": "hang over"}, {"label": "E", "text": "intimacy"}], "stem": "If you're celebrating with too many cocktails what may you have in the morning?"}}
{"answerKey": "D", "id": "a328285c6212c899e335c45db3c49ffd", "question": {"question_concept": "film", "choices": [{"label": "A", "text": "clingfilm"}, {"label": "B", "text": "disneyland"}, {"label": "C", "text": "cave"}, {"label": "D", "text": "cabinet"}, {"label": "E", "text": "movie"}], "stem": "Danny found an old film in a sealed what?"}}
{"answerKey": "D", "id": "e248968fec422e1fab0f0561fedff76e", "question": {"question_concept": "drop of blood", "choices": [{"label": "A", "text": "crime scene"}, {"label": "B", "text": "vein"}, {"label": "C", "text": "blood bank"}, {"label": "D", "text": "slaughter house"}, {"label": "E", "text": "needle"}], "stem": "Where are you likely to find much more than a drop of blood on the floor?"}}
{"answerKey": "C", "id": "2067720531fc03c017af941cec2f6f40", "question": {"question_concept": "planet", "choices": [{"label": "A", "text": "pay debts"}, {"label": "B", "text": "galaxy"}, {"label": "C", "text": "outer space"}, {"label": "D", "text": "orbit"}, {"label": "E", "text": "universe"}], "stem": "Where is the first place someone leaving the planet ends up?"}}
{"answerKey": "B", "id": "70d3ebc00b165d9d08f9491a1dd85034", "question": {"question_concept": "mailbox", "choices": [{"label": "A", "text": "apartment building"}, {"label": "B", "text": "front door"}, {"label": "C", "text": "back door"}, {"label": "D", "text": "street corner"}, {"label": "E", "text": "porch"}], "stem": "The town house went right to the curb, a slot effectively made a mailbox of the what?"}}
{"answerKey": "E", "id": "41bab71fea3fa04e5a4e10a2f86996df", "question": {"question_concept": "mezzanine", "choices": [{"label": "A", "text": "actors"}, {"label": "B", "text": "theater"}, {"label": "C", "text": "concert hall"}, {"label": "D", "text": "floors"}, {"label": "E", "text": "school"}], "stem": "The architect thought that a mezzanine would look good, but the planning committee rejected it.  They told the architect that they felt it was a potential hazard given the ages of the people who would be using it.  What might they be designing?"}}
{"answerKey": "E", "id": "e18dd9ffc7b7934c39f2b5e9dee5a8c2", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "read book"}, {"label": "B", "text": "own house"}, {"label": "C", "text": "apartment"}, {"label": "D", "text": "more rice"}, {"label": "E", "text": "warm coat"}], "stem": "The person wasn't bothered by the weather, she had remembered to bring her what?"}}
{"answerKey": "A", "id": "449de58e919975867255218484a9fc89", "question": {"question_concept": "learning about world", "choices": [{"label": "A", "text": "enlightenment"}, {"label": "B", "text": "open mind"}, {"label": "C", "text": "confusion"}, {"label": "D", "text": "smartness"}, {"label": "E", "text": "anger"}], "stem": "If you want to learn about the world and understand the real reasons behind cultural norms and mores, you have achieved a sense of what?"}}
{"answerKey": "B", "id": "9698232e3599157431c9dc8f2fe179cd", "question": {"question_concept": "going to play", "choices": [{"label": "A", "text": "sit"}, {"label": "B", "text": "being entertained"}, {"label": "C", "text": "jobless"}, {"label": "D", "text": "meet"}, {"label": "E", "text": "laugh"}], "stem": "What is the hopeful result of going to see a play?"}}
{"answerKey": "D", "id": "0b5d0c3bafbe06dd5334c20cd8ea7fe2", "question": {"question_concept": "finding information", "choices": [{"label": "A", "text": "ulcers"}, {"label": "B", "text": "degree"}, {"label": "C", "text": "understanding of"}, {"label": "D", "text": "gaining knowledge"}, {"label": "E", "text": "happiness"}], "stem": "A person would join a trade school for finding information related to what?"}}
{"answerKey": "E", "id": "7fe53bf68ec57a52a508611acf5b279e", "question": {"question_concept": "baby", "choices": [{"label": "A", "text": "arrive early"}, {"label": "B", "text": "learn to walk"}, {"label": "C", "text": "boy or girl"}, {"label": "D", "text": "bring joy"}, {"label": "E", "text": "talk nonsense"}], "stem": "Joan was a baby, so there were many things she couldn't do, which caused problems for her parents.  Name one thing that makes raising a baby difficult."}}
{"answerKey": "B", "id": "68c41ec8415eab50620eb9ecf6f35a6a", "question": {"question_concept": "ham", "choices": [{"label": "A", "text": "hamshackle"}, {"label": "B", "text": "pizza"}, {"label": "C", "text": "fridge"}, {"label": "D", "text": "refrigerator"}, {"label": "E", "text": "part of meal"}], "stem": "Where would you put some ham if you want to cook it?"}}
{"answerKey": "C", "id": "6c4b2c93a4bdafb6cbf2b2ef2439b06f", "question": {"question_concept": "running errands", "choices": [{"label": "A", "text": "efficiency"}, {"label": "B", "text": "insanity"}, {"label": "C", "text": "aggravation"}, {"label": "D", "text": "tiredness"}, {"label": "E", "text": "stress"}], "stem": "Running errands with screaming kids will likely cause what?"}}
{"answerKey": "B", "id": "51e2da7396ab7045533e885dbb98a424", "question": {"question_concept": "lying", "choices": [{"label": "A", "text": "dishonesty"}, {"label": "B", "text": "deceitful"}, {"label": "C", "text": "imagination"}, {"label": "D", "text": "deceptive"}, {"label": "E", "text": "poker face"}], "stem": "Sam wasn't lying, but he left out important details. He was being what?"}}
{"answerKey": "A", "id": "3f6157968fcf50d257ec3d8c729b7443", "question": {"question_concept": "committing murder", "choices": [{"label": "A", "text": "problems"}, {"label": "B", "text": "distress"}, {"label": "C", "text": "fear"}, {"label": "D", "text": "go to jail"}, {"label": "E", "text": "killer"}], "stem": "what does someone have that causes them committing murder?"}}
{"answerKey": "D", "id": "4768aa28fa14569d830f8947565296c1", "question": {"question_concept": "leader", "choices": [{"label": "A", "text": "army"}, {"label": "B", "text": "battle"}, {"label": "C", "text": "wildlife"}, {"label": "D", "text": "country"}, {"label": "E", "text": "organization"}], "stem": "What kind of place has a leader?"}}
{"answerKey": "A", "id": "5516b1c93f94aaa0bf9a4c7b124788d4", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "say words"}, {"label": "B", "text": "meet friends"}, {"label": "C", "text": "open mouth"}, {"label": "D", "text": "thank god"}, {"label": "E", "text": "die of cancer"}], "stem": "How is a person likely to communicatewith others?"}}
{"answerKey": "E", "id": "96ea2c3174229c4a6a0e2ffaed2df378", "question": {"question_concept": "corner shop", "choices": [{"label": "A", "text": "england"}, {"label": "B", "text": "town"}, {"label": "C", "text": "desert"}, {"label": "D", "text": "kentucky"}, {"label": "E", "text": "iowa"}], "stem": "Where may you be if you're buying pork chops at a corner shop?"}}
{"answerKey": "A", "id": "7905b9f4ba503b0ce13b576808e99c42", "question": {"question_concept": "toy car", "choices": [{"label": "A", "text": "child's room"}, {"label": "B", "text": "boy's bedroom"}, {"label": "C", "text": "own home"}, {"label": "D", "text": "toy store"}, {"label": "E", "text": "house"}], "stem": "Where is a well used toy car likely to be found?"}}
{"answerKey": "C", "id": "e0a7d1df3ce14b27888e785e6636d5f0", "question": {"question_concept": "rod", "choices": [{"label": "A", "text": "hardware store"}, {"label": "B", "text": "engine"}, {"label": "C", "text": "fishing camp"}, {"label": "D", "text": "lake"}, {"label": "E", "text": "sporting goods store"}], "stem": "Where can fisherman store their rods when on a fishing trip?"}}
{"answerKey": "C", "id": "3eb397b96b6c3a245c81ab30205943f1", "question": {"question_concept": "having fun", "choices": [{"label": "A", "text": "injuries"}, {"label": "B", "text": "smiling"}, {"label": "C", "text": "being happy"}, {"label": "D", "text": "glee"}, {"label": "E", "text": "jump"}], "stem": "Danny is having fun just dancing and singing with his friends. He wasn't concerned with things that weren't fun. For him having fun is the same as what?"}}
{"answerKey": "A", "id": "536c9af0fae0aa75b32874dfcac66353", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "water cooler"}, {"label": "B", "text": "space shuttle"}, {"label": "C", "text": "baby shower"}, {"label": "D", "text": "bus stop"}, {"label": "E", "text": "family"}], "stem": "Where would you find an office worker gossiping with their colleagues?"}}
{"answerKey": "C", "id": "dc36293f603cf230f8059fc6f2e5660d", "question": {"question_concept": "nails", "choices": [{"label": "A", "text": "pocket"}, {"label": "B", "text": "container"}, {"label": "C", "text": "cabinet"}, {"label": "D", "text": "jar"}, {"label": "E", "text": "store"}], "stem": "Where would you put nails if they are already packaged?"}}
{"answerKey": "C", "id": "1510f5183095466e4fe41b82501a9dd0", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "lazy"}, {"label": "B", "text": "own house"}, {"label": "C", "text": "talented"}, {"label": "D", "text": "affluent"}, {"label": "E", "text": "reproduce"}], "stem": "What is a person who is good at sports considered?"}}
{"answerKey": "C", "id": "1fcc547e4e6813afc1a66717248d6c62", "question": {"question_concept": "ridiculous", "choices": [{"label": "A", "text": "straightforward"}, {"label": "B", "text": "serious"}, {"label": "C", "text": "solemn"}, {"label": "D", "text": "somber"}, {"label": "E", "text": "funny"}], "stem": "The man acted ridiculous at the funeral, what attitude should he have taken?"}}
{"answerKey": "B", "id": "68a911b64dc943b5f81c0f8dec7faed7", "question": {"question_concept": "pencil sharpener", "choices": [{"label": "A", "text": "home"}, {"label": "B", "text": "library"}, {"label": "C", "text": "stationery store"}, {"label": "D", "text": "cabinet"}, {"label": "E", "text": "desk drawer"}], "stem": "The pencil sharpener was broken in the classroom, where did the teacher recommend the student go?"}}
{"answerKey": "B", "id": "92f423de9a556a66c3eb73e9ddf9399a", "question": {"question_concept": "desk", "choices": [{"label": "A", "text": "furniture store"}, {"label": "B", "text": "schoolroom"}, {"label": "C", "text": "patio"}, {"label": "D", "text": "office building"}, {"label": "E", "text": "library"}], "stem": "Where does a child likely sit at a desk?"}}
{"answerKey": "D", "id": "1cd94405124031e8681cd12bd25e2d61", "question": {"question_concept": "procreate", "choices": [{"label": "A", "text": "moaning"}, {"label": "B", "text": "die"}, {"label": "C", "text": "kiss"}, {"label": "D", "text": "std"}, {"label": "E", "text": "sanity"}], "stem": "He was trying to procreate with many individuals, this led to a what?"}}
{"answerKey": "A", "id": "64ab884bd870f6f68146636b4cce921c", "question": {"question_concept": "playing soccer", "choices": [{"label": "A", "text": "excitement"}, {"label": "B", "text": "getting tired"}, {"label": "C", "text": "overtime"}, {"label": "D", "text": "anger"}, {"label": "E", "text": "fights"}], "stem": "What does playing soccer and winning lead to?"}}
{"answerKey": "A", "id": "66275550d64d16339c944e6a6d63eb5b", "question": {"question_concept": "map", "choices": [{"label": "A", "text": "amusement park"}, {"label": "B", "text": "truck stop"}, {"label": "C", "text": "mcdonalds"}, {"label": "D", "text": "backpack"}, {"label": "E", "text": "classroom"}], "stem": "What attraction is sometimes so large that you need a map to find your way around?"}}
{"answerKey": "B", "id": "9b26329d74a6159ab9af4f899303de39", "question": {"question_concept": "doing housework", "choices": [{"label": "A", "text": "boredom"}, {"label": "B", "text": "arguments"}, {"label": "C", "text": "headache"}, {"label": "D", "text": "exhaustion"}, {"label": "E", "text": "park"}], "stem": "If my husband never helps me doing housework, what might that lead to?"}}
{"answerKey": "A", "id": "f74b7f268d3c190a13f99ede6d2359e1", "question": {"question_concept": "advertisement", "choices": [{"label": "A", "text": "web page"}, {"label": "B", "text": "store"}, {"label": "C", "text": "la ville"}, {"label": "D", "text": "bus"}, {"label": "E", "text": "email"}], "stem": "The advertisement came in the form of a pop-up, where did it appear?"}}
{"answerKey": "E", "id": "22458fdcead20e2def0df0d92d5806f6", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "apartment"}, {"label": "B", "text": "eat cake"}, {"label": "C", "text": "bus depot"}, {"label": "D", "text": "football stadium"}, {"label": "E", "text": "surface of earth"}], "stem": "WHere do people live?"}}
{"answerKey": "B", "id": "f7b96f195a7adfe0c74924a165cfd055", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "train"}, {"label": "B", "text": "strange"}, {"label": "C", "text": "human"}, {"label": "D", "text": "stupid"}, {"label": "E", "text": "dangerous"}], "stem": "People are what when you're a stranger?"}}
{"answerKey": "B", "id": "9b631734e72a0e559da153492c1e7894", "question": {"question_concept": "hearing testimony", "choices": [{"label": "A", "text": "take notes"}, {"label": "B", "text": "nodding"}, {"label": "C", "text": "change of heart"}, {"label": "D", "text": "writing down"}, {"label": "E", "text": "listening"}], "stem": "The juror was quite bored and zoning out but wanted to convey he was hearing testimony, so he just sat there doing what?"}}
{"answerKey": "E", "id": "caccaa51ee960a92d44e5b949fc35a66", "question": {"question_concept": "blowfish", "choices": [{"label": "A", "text": "atlantic ocean"}, {"label": "B", "text": "books"}, {"label": "C", "text": "france"}, {"label": "D", "text": "aquarium"}, {"label": "E", "text": "fish market"}], "stem": "They wanted to try blowfish, so they went to get some where?"}}
{"answerKey": "D", "id": "def936fda9f6ccee01f57c0f804fabd0", "question": {"question_concept": "main artery", "choices": [{"label": "A", "text": "neck"}, {"label": "B", "text": "busy city"}, {"label": "C", "text": "own brain"}, {"label": "D", "text": "thruway"}, {"label": "E", "text": "food"}], "stem": "When a main artery is used to expedite travel what would it be referred to as?"}}
{"answerKey": "C", "id": "761b0f6c68b1540949b70f76a9e67c78", "question": {"question_concept": "rule", "choices": [{"label": "A", "text": "classroom"}, {"label": "B", "text": "football game"}, {"label": "C", "text": "everything"}, {"label": "D", "text": "text book"}, {"label": "E", "text": "lawbook"}], "stem": "If someone rules the universe of what are they in charge?"}}
{"answerKey": "B", "id": "8c11546468a2595b29a1297e73334fc4", "question": {"question_concept": "bare", "choices": [{"label": "A", "text": "full"}, {"label": "B", "text": "ample"}, {"label": "C", "text": "covered"}, {"label": "D", "text": "bareword"}, {"label": "E", "text": "ample"}], "stem": "The butt was bare, and Sam couldn't stop staring at it.  It was very what?"}}
{"answerKey": "B", "id": "a5dcac512870e79f5aa2b22dbd662404", "question": {"question_concept": "clothing", "choices": [{"label": "A", "text": "shop"}, {"label": "B", "text": "mall"}, {"label": "C", "text": "department store"}, {"label": "D", "text": "drawer"}, {"label": "E", "text": "library"}], "stem": "Where can many stores with clothing be found?"}}
{"answerKey": "E", "id": "870b07a1c5af2e956673a9680da99852", "question": {"question_concept": "car", "choices": [{"label": "A", "text": "going too fast"}, {"label": "B", "text": "last several years"}, {"label": "C", "text": "honk the horn"}, {"label": "D", "text": "go fast"}, {"label": "E", "text": "start running"}], "stem": "After working on the car, what did it end up doing?"}}
{"answerKey": "C", "id": "f48528156632b9c5b18af9ce2095509b", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "creativity"}, {"label": "B", "text": "hatred"}, {"label": "C", "text": "caregiver"}, {"label": "D", "text": "own house"}, {"label": "E", "text": "much money"}], "stem": "When an elderly person needs help performing daily tasks, who might they call?"}}
{"answerKey": "C", "id": "5496c7293f653120e5a5213db2d7b103", "question": {"question_concept": "beer", "choices": [{"label": "A", "text": "bottle"}, {"label": "B", "text": "refrigerator"}, {"label": "C", "text": "hockey game"}, {"label": "D", "text": "casino"}, {"label": "E", "text": "bar"}], "stem": "Where is beer drank by people watching sticks and pucks?"}}
{"answerKey": "A", "id": "9d97e2bb458d93a8bafe4380b08727e3", "question": {"question_concept": "telephone book", "choices": [{"label": "A", "text": "at hotel"}, {"label": "B", "text": "house"}, {"label": "C", "text": "library"}, {"label": "D", "text": "bedsit"}, {"label": "E", "text": "closet"}], "stem": "Where is there a telephone book in almost every room?"}}
{"answerKey": "D", "id": "26d7d59ef7b9f2e0c2d47419fa5bca91", "question": {"question_concept": "field", "choices": [{"label": "A", "text": "kansas"}, {"label": "B", "text": "meadow"}, {"label": "C", "text": "farmland"}, {"label": "D", "text": "countryside"}, {"label": "E", "text": "rural area"}], "stem": "Where might you see a green field while driving?"}}
{"answerKey": "B", "id": "c6f10fd07348bf2cf5488b0d9f38d806", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "state facts"}, {"label": "B", "text": "talking loudly"}, {"label": "C", "text": "making money"}, {"label": "D", "text": "amount to nothing"}, {"label": "E", "text": "believe in god"}], "stem": "Some people got escorted out of the library, they were probably what?"}}
{"answerKey": "C", "id": "8ebf9d24719649a0b041aea02a6e46af", "question": {"question_concept": "pond", "choices": [{"label": "A", "text": "ground"}, {"label": "B", "text": "bathroom"}, {"label": "C", "text": "forest"}, {"label": "D", "text": "countryside"}, {"label": "E", "text": "rural area"}], "stem": "If there is a pond with trees around it, where it it likely located?"}}
{"answerKey": "A", "id": "c961578f4c5768b67b843e5d2ce18452", "question": {"question_concept": "blowfish", "choices": [{"label": "A", "text": "sea water"}, {"label": "B", "text": "hatred"}, {"label": "C", "text": "fish market"}, {"label": "D", "text": "body of water"}, {"label": "E", "text": "jungle"}], "stem": "Blowfish require what specific thing to live?"}}
{"answerKey": "B", "id": "cce1b59f7c4f540a84a1a7d6d88548c4", "question": {"question_concept": "eating hamburger", "choices": [{"label": "A", "text": "nausea"}, {"label": "B", "text": "death"}, {"label": "C", "text": "illness"}, {"label": "D", "text": "health problems"}, {"label": "E", "text": "gain weight"}], "stem": "What is the least likely immediate side effect of eating hamburger?"}}
{"answerKey": "A", "id": "60848ce50295fc745756fbe960e78b88", "question": {"question_concept": "going to work", "choices": [{"label": "A", "text": "listen to radio"}, {"label": "B", "text": "solve problems"}, {"label": "C", "text": "driving"}, {"label": "D", "text": "walk"}, {"label": "E", "text": "being late"}], "stem": "What would I be doing while going to work and walking?"}}
{"answerKey": "A", "id": "3fdc0c422c524c994b9911a17f1f1834", "question": {"question_concept": "showroom", "choices": [{"label": "A", "text": "appliance store"}, {"label": "B", "text": "vegas"}, {"label": "C", "text": "electronics store"}, {"label": "D", "text": "car dealership"}, {"label": "E", "text": "kitchen"}], "stem": "A showroom feature washers and refrigerators, where is this showroom located?"}}
{"answerKey": "E", "id": "cc8eac9956f645533b8d7b99702e3507", "question": {"question_concept": "mexican restaurant", "choices": [{"label": "A", "text": "city"}, {"label": "B", "text": "mexica"}, {"label": "C", "text": "san diego"}, {"label": "D", "text": "spain"}, {"label": "E", "text": "mexico"}], "stem": "The man often made smart remarks, like that any restaurant is a mexican restaurant where?"}}
{"answerKey": "B", "id": "c0e7fa3e39a2d9af2c323416015729dc", "question": {"question_concept": "honey", "choices": [{"label": "A", "text": "last all night"}, {"label": "B", "text": "beehive"}, {"label": "C", "text": "farmer's market"}, {"label": "D", "text": "jar"}, {"label": "E", "text": "honeyful"}], "stem": "I am looking for honey right from the source, where should I look?"}}
{"answerKey": "C", "id": "335b51bd3a8ada014bbe6754dcbd425f", "question": {"question_concept": "flat", "choices": [{"label": "A", "text": "london"}, {"label": "B", "text": "apartment building"}, {"label": "C", "text": "city"}, {"label": "D", "text": "falling down"}, {"label": "E", "text": "town"}], "stem": "Where are there likely to be a variety of flats to choose from?"}}
{"answerKey": "E", "id": "c7327a1a7d12b6cc0740fc9446270e02", "question": {"question_concept": "weasel", "choices": [{"label": "A", "text": "tree"}, {"label": "B", "text": "mulberry bush"}, {"label": "C", "text": "chicken coop"}, {"label": "D", "text": "viking ship"}, {"label": "E", "text": "rabbit warren"}], "stem": "A weasel has a thin body and short legs to easier burrow after prey in a what?"}}
{"answerKey": "C", "id": "2729d8502208c25d8e9293cd4e8ecbb5", "question": {"question_concept": "disease", "choices": [{"label": "A", "text": "rug"}, {"label": "B", "text": "third world country"}, {"label": "C", "text": "human body"}, {"label": "D", "text": "hospital"}, {"label": "E", "text": "building"}], "stem": "What can disease destroy?"}}
{"answerKey": "C", "id": "7ea57ee4580042b0a6a40479c8ace3e4", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "pain"}, {"label": "B", "text": "meaningful work"}, {"label": "C", "text": "english house"}, {"label": "D", "text": "cotton candy"}, {"label": "E", "text": "headache"}], "stem": "What does a person from Avalon live in?"}}
{"answerKey": "A", "id": "65432eb6e617514d863a465f38865fde", "question": {"question_concept": "fan", "choices": [{"label": "A", "text": "appliance store"}, {"label": "B", "text": "sports stadium"}, {"label": "C", "text": "dress emporium"}, {"label": "D", "text": "hot room"}, {"label": "E", "text": "football stadium"}], "stem": "Where is one likely to find a fan for their stove?"}}
{"answerKey": "C", "id": "316a8dee8a4dde7d95cf503a715104be", "question": {"question_concept": "chest", "choices": [{"label": "A", "text": "istanbul"}, {"label": "B", "text": "concert"}, {"label": "C", "text": "bedroom"}, {"label": "D", "text": "antique shop"}, {"label": "E", "text": "human being"}], "stem": "Jodie felt a tightness in her chest. She was worried but didn't want to go to the hospital. Where might she go instead?"}}
{"answerKey": "B", "id": "520972425aed0e532fa28a91c9b55b30", "question": {"question_concept": "buying beer", "choices": [{"label": "A", "text": "get arrested"}, {"label": "B", "text": "have fun"}, {"label": "C", "text": "get sick"}, {"label": "D", "text": "spend money"}, {"label": "E", "text": "stupidity"}], "stem": "If you're buying beer for a float trip what are you preparing to do?"}}
{"answerKey": "C", "id": "4d67cdb4ba1b0058e383c212303a9f4e", "question": {"question_concept": "marmot", "choices": [{"label": "A", "text": "north america"}, {"label": "B", "text": "united states"}, {"label": "C", "text": "vancouver island"}, {"label": "D", "text": "american"}, {"label": "E", "text": "cage"}], "stem": "Piece of land in Canada where you can find marmot?"}}
{"answerKey": "C", "id": "95d1d968ee66b6054cbb16b58a7c6455", "question": {"question_concept": "reduce", "choices": [{"label": "A", "text": "reduction"}, {"label": "B", "text": "make larger"}, {"label": "C", "text": "augment"}, {"label": "D", "text": "gain weight"}, {"label": "E", "text": "expand"}], "stem": "The surgeon's clients had begun to reduce, it seemed girls no longer want to what?"}}
{"answerKey": "A", "id": "c43b60be106662de1863097ee3ddb4d2", "question": {"question_concept": "magazines", "choices": [{"label": "A", "text": "doctor"}, {"label": "B", "text": "train station"}, {"label": "C", "text": "newsagent"}, {"label": "D", "text": "market"}, {"label": "E", "text": "table"}], "stem": "While waiting for this appointment, people often read magazines."}}
{"answerKey": "A", "id": "456f2fb41cac8c028dcfe2f48637e473", "question": {"question_concept": "fox", "choices": [{"label": "A", "text": "storybook"}, {"label": "B", "text": "woods"}, {"label": "C", "text": "hen house"}, {"label": "D", "text": "natural habitat"}, {"label": "E", "text": "back yard"}], "stem": "Where would you find a fox that is made up?"}}
{"answerKey": "B", "id": "a5d853d1c2fb3ef160218fb91110fbe5", "question": {"question_concept": "build", "choices": [{"label": "A", "text": "destroying"}, {"label": "B", "text": "tear down"}, {"label": "C", "text": "raze"}, {"label": "D", "text": "mutilate"}, {"label": "E", "text": "demolition"}], "stem": "In basic training they build you up only to do what, all in hopes of building you up even stronger the next time?"}}
{"answerKey": "B", "id": "3df1b88da6a90c9526be2c8a6cc736dc", "question": {"question_concept": "dog", "choices": [{"label": "A", "text": "kennel"}, {"label": "B", "text": "table"}, {"label": "C", "text": "porch"}, {"label": "D", "text": "backyard"}, {"label": "E", "text": "park"}], "stem": "Billy saw a dog running from him and did his best to get away from it.  The leaped up to where the dog couldn't reach and was stuck.  Where might he have been trapped?"}}
{"answerKey": "B", "id": "f912bcd7479b76db9b1c57a612b90f00", "question": {"question_concept": "parents", "choices": [{"label": "A", "text": "control children"}, {"label": "B", "text": "guide children"}, {"label": "C", "text": "speak freely"}, {"label": "D", "text": "cry"}, {"label": "E", "text": "understand children"}], "stem": "John and Judy were parents.  They had two wonderful kids who weren't always well behaved.  They were light tough, though.  They felt it was a parent's job to do what?"}}
{"answerKey": "A", "id": "94f34cc1e6aa9eefe06563cce8225658", "question": {"question_concept": "fiddling", "choices": [{"label": "A", "text": "bluegrass music"}, {"label": "B", "text": "make music"}, {"label": "C", "text": "drop"}, {"label": "D", "text": "string instrument"}, {"label": "E", "text": "troubles"}], "stem": "What are you playing if you're fiddling on a violin?"}}
{"answerKey": "C", "id": "bb503ece4eac41dfe608a1dcb654e6bf", "question": {"question_concept": "free", "choices": [{"label": "A", "text": "deadly"}, {"label": "B", "text": "imprisoned"}, {"label": "C", "text": "paid for"}, {"label": "D", "text": "expensive"}, {"label": "E", "text": "in prison"}], "stem": "If somebody buys something and gives it to me as a free gift, what is the cost status of the gift?"}}
{"answerKey": "D", "id": "5502dc807d4921679ae1abd0dc9570d6", "question": {"question_concept": "flirt", "choices": [{"label": "A", "text": "have sex"}, {"label": "B", "text": "get laid"}, {"label": "C", "text": "were lonely"}, {"label": "D", "text": "attract attention"}, {"label": "E", "text": "dance"}], "stem": "Why does someone flirt with many people at once?"}}
{"answerKey": "D", "id": "a7e3de0719fe30e7048f67426e29fdd1", "question": {"question_concept": "channel", "choices": [{"label": "A", "text": "river"}, {"label": "B", "text": "television"}, {"label": "C", "text": "india"}, {"label": "D", "text": "england"}, {"label": "E", "text": "europe"}], "stem": "James tore the antenna off of his boat due to bad reception as he was crossing the channel from France.  Where was he going?"}}
{"answerKey": "A", "id": "d6107d454181b701ddcaa449a1e422a3", "question": {"question_concept": "band", "choices": [{"label": "A", "text": "record album"}, {"label": "B", "text": "play music"}, {"label": "C", "text": "hold concert"}, {"label": "D", "text": "blaring"}, {"label": "E", "text": "practice"}], "stem": "Why would a band be performing when there are no people nearby?"}}
{"answerKey": "E", "id": "ab2eb930b29bb6d5e94a6cd3b04ba01e", "question": {"question_concept": "dogs", "choices": [{"label": "A", "text": "bad breath"}, {"label": "B", "text": "defend"}, {"label": "C", "text": "run fast"}, {"label": "D", "text": "ocean"}, {"label": "E", "text": "attack"}], "stem": "The dogs were protecting their own when they decided to what the bad man?"}}
{"answerKey": "E", "id": "92869fc0be5dc45f407700692ffd80a0", "question": {"question_concept": "wheat", "choices": [{"label": "A", "text": "farmer's field"}, {"label": "B", "text": "countryside"}, {"label": "C", "text": "cereal packets"}, {"label": "D", "text": "bread"}, {"label": "E", "text": "mill"}], "stem": "What is used to grind wheat for bread?"}}
{"answerKey": "B", "id": "6a0177586d506cb7b741f4207b428e42", "question": {"question_concept": "satchel", "choices": [{"label": "A", "text": "airport"}, {"label": "B", "text": "luggage compartment"}, {"label": "C", "text": "with the pilot"}, {"label": "D", "text": "room"}, {"label": "E", "text": "clothing store"}], "stem": "If you have a large satchel with you when you fly you'll be asked to store it where?"}}
{"answerKey": "E", "id": "584188da9a429f1bc319abda5e5c7a76", "question": {"question_concept": "nylon", "choices": [{"label": "A", "text": "stockings"}, {"label": "B", "text": "rope"}, {"label": "C", "text": "car"}, {"label": "D", "text": "clothing"}, {"label": "E", "text": "drawer"}], "stem": "Where would someone keep their nylon leggings?"}}
{"answerKey": "E", "id": "e480d4a672af0194e0a6ccdb8c37499b", "question": {"question_concept": "running after ball", "choices": [{"label": "A", "text": "laughter"}, {"label": "B", "text": "sweating"}, {"label": "C", "text": "embarrassed"}, {"label": "D", "text": "breathing heavily"}, {"label": "E", "text": "tiredness"}], "stem": "If you spend a long time running after a ball how are you likely to feel?"}}
{"answerKey": "E", "id": "275c859994f7d3acd3c8863be591ab2c", "question": {"question_concept": "rest", "choices": [{"label": "A", "text": "action"}, {"label": "B", "text": "sleep"}, {"label": "C", "text": "sleeping"}, {"label": "D", "text": "in motion"}, {"label": "E", "text": "using energy"}], "stem": "When you need to rest it's often because you have been doing what?"}}
{"answerKey": "E", "id": "32758ab86d888be680845b0dfe7de35e", "question": {"question_concept": "boredom", "choices": [{"label": "A", "text": "new moon"}, {"label": "B", "text": "play cards"}, {"label": "C", "text": "read book"}, {"label": "D", "text": "see art"}, {"label": "E", "text": "grocery shop"}], "stem": "Boredom and hunger led to a wandering waste of time and a cart full of unhealthy snacks during her trip to where?"}}
{"answerKey": "D", "id": "69335eb9bc5b7b5df840c38a086bf8b2", "question": {"question_concept": "standing in queue", "choices": [{"label": "A", "text": "frustration"}, {"label": "B", "text": "waiting"}, {"label": "C", "text": "hair"}, {"label": "D", "text": "time"}, {"label": "E", "text": "patience"}], "stem": "He was beginning to worry they wouldn't get on the ride before closing, they had been standing in queue for a long what?"}}
{"answerKey": "B", "id": "4396cb65629672723c7b184424e139bb", "question": {"question_concept": "running", "choices": [{"label": "A", "text": "breathlessness"}, {"label": "B", "text": "increased heart rate"}, {"label": "C", "text": "falling down"}, {"label": "D", "text": "muscle bulk"}, {"label": "E", "text": "calluses"}], "stem": "This is an unavoidable physiological consequence of running.  What is it?"}}
{"answerKey": "E", "id": "2a58e81a9c4ce095d099e0d785fc2da4", "question": {"question_concept": "having bath", "choices": [{"label": "A", "text": "flooding"}, {"label": "B", "text": "drowning"}, {"label": "C", "text": "wet skin"}, {"label": "D", "text": "get wet"}, {"label": "E", "text": "rash"}], "stem": "Sometimes a person has a fear of water or a dislike of being wet, it is still important to make sure they are having a bath why?"}}
{"answerKey": "C", "id": "07f108d5321a66f460685f5c7499ecb2", "question": {"question_concept": "auditorium", "choices": [{"label": "A", "text": "lights"}, {"label": "B", "text": "crowd"}, {"label": "C", "text": "university campus"}, {"label": "D", "text": "theater"}, {"label": "E", "text": "park"}], "stem": "Where would there be an auditorium with only a single person speaking?"}}
{"answerKey": "A", "id": "69bef3eb55463d040bdf98e2c97bfe1f", "question": {"question_concept": "walking", "choices": [{"label": "A", "text": "moving forward"}, {"label": "B", "text": "locomotion"}, {"label": "C", "text": "blisters"}, {"label": "D", "text": "rollerskate"}, {"label": "E", "text": "exercise"}], "stem": "To get out of there the person had to keep on walking, they had to keep on what?"}}
{"answerKey": "A", "id": "912676495cceefadccbbf8c604486f97", "question": {"question_concept": "bee", "choices": [{"label": "A", "text": "united states"}, {"label": "B", "text": "space station"}, {"label": "C", "text": "trash can"}, {"label": "D", "text": "field of flowers"}, {"label": "E", "text": "bouquet of flowers"}], "stem": "What very large group of western citizens has bees everywhere?"}}
{"answerKey": "A", "id": "bdf92566f14599f1606109677206001f", "question": {"question_concept": "glue stick", "choices": [{"label": "A", "text": "desk drawer"}, {"label": "B", "text": "kitchen drawer"}, {"label": "C", "text": "classroom"}, {"label": "D", "text": "pocket"}, {"label": "E", "text": "office"}], "stem": "Miss Grady took a stick from Bob because he was playing with it during class.  She wanted to make sure that he couldn't get to it so she put it where?"}}
{"answerKey": "B", "id": "0df042743128b57e874bd5d79b7aae7a", "question": {"question_concept": "reproducing", "choices": [{"label": "A", "text": "genetic mutation"}, {"label": "B", "text": "have sex"}, {"label": "C", "text": "kiss"}, {"label": "D", "text": "flirting"}, {"label": "E", "text": "going on a date"}], "stem": "How does a person begin reproducing?"}}
{"answerKey": "D", "id": "866ef7266d34c11e5a1b3df49fab96a4", "question": {"question_concept": "children", "choices": [{"label": "A", "text": "play sports"}, {"label": "B", "text": "throw things"}, {"label": "C", "text": "reading"}, {"label": "D", "text": "watch tv"}, {"label": "E", "text": "play with toys"}], "stem": "Joe and Jill didn't want their children to be sedentary.  They might limit the time they children spend doing what?"}}
{"answerKey": "A", "id": "67ffcb4c3f2c6a1155e356f8a15ed250", "question": {"question_concept": "liquid", "choices": [{"label": "A", "text": "jar"}, {"label": "B", "text": "drinking glass"}, {"label": "C", "text": "pot"}, {"label": "D", "text": "container"}, {"label": "E", "text": "can"}], "stem": "They were making sauerkraut, the instructor explained the liquid should be above the cabbage in the what?"}}
{"answerKey": "E", "id": "87a133afae5d9d29d634f3384f28ef24", "question": {"question_concept": "cup", "choices": [{"label": "A", "text": "dishwasher"}, {"label": "B", "text": "water fountain"}, {"label": "C", "text": "sand box"}, {"label": "D", "text": "toilet"}, {"label": "E", "text": "kitchen cabinet"}], "stem": "From where would you normally take a cup when you're about to get a drink?"}}
{"answerKey": "B", "id": "4779be55f47a301debfc472e4fc2c7b6", "question": {"question_concept": "speakers", "choices": [{"label": "A", "text": "take it all in"}, {"label": "B", "text": "headphones"}, {"label": "C", "text": "desktop"}, {"label": "D", "text": "conference"}, {"label": "E", "text": "concert"}], "stem": "What are you using if there are speakers strapped on your ears?"}}
{"answerKey": "D", "id": "7a28d31e66d870370642de3be47b9ef9", "question": {"question_concept": "anger", "choices": [{"label": "A", "text": "cool off"}, {"label": "B", "text": "write letter"}, {"label": "C", "text": "get mad"}, {"label": "D", "text": "illustrate point"}, {"label": "E", "text": "destroy enemy"}], "stem": "Because of his anger he couldn't clearly explain or what?"}}
{"answerKey": "D", "id": "042898e0c71adac5d123aaa6221c9754", "question": {"question_concept": "kosher restaurant", "choices": [{"label": "A", "text": "jerusalem"}, {"label": "B", "text": "jewish neighborhoods"}, {"label": "C", "text": "dining in"}, {"label": "D", "text": "new york city"}, {"label": "E", "text": "dining"}], "stem": "Where is likely to not just have a kosher restaurant?"}}
{"answerKey": "A", "id": "93bbaccb1c46d22124a846b8514de5bc", "question": {"question_concept": "bald eagle", "choices": [{"label": "A", "text": "washington state"}, {"label": "B", "text": "utah"}, {"label": "C", "text": "pacific northwest"}, {"label": "D", "text": "northern california"}, {"label": "E", "text": "the desert"}], "stem": "The bald eagle flew from Mount St Helen's to the Puget Sound and all over what?"}}
{"answerKey": "C", "id": "ef889edd1b57d8d0c81e43f73c98c8e9", "question": {"question_concept": "knives", "choices": [{"label": "A", "text": "sporting goods store"}, {"label": "B", "text": "backpack"}, {"label": "C", "text": "kitchen"}, {"label": "D", "text": "sharp edges"}, {"label": "E", "text": "dog house"}], "stem": "Where could you get some knives if you are planning to bring them outside with you?"}}
{"answerKey": "B", "id": "f4bb8ecacb9ce89e040f5f76bc79afb3", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "compete with each other"}, {"label": "B", "text": "feed themselves"}, {"label": "C", "text": "feel lonely"}, {"label": "D", "text": "talk to each other"}, {"label": "E", "text": "ask a doctor"}], "stem": "How can people fulfill their own calorie requirements?"}}
{"answerKey": "B", "id": "ec2e18fd8c18a4ebe5a091e0c8b94462", "question": {"question_concept": "stove", "choices": [{"label": "A", "text": "cool house"}, {"label": "B", "text": "warm room"}, {"label": "C", "text": "gas or electric"}, {"label": "D", "text": "burn child"}, {"label": "E", "text": "brown meat"}], "stem": "What does a stove do to the place that it's in?"}}
{"answerKey": "B", "id": "07b51b231a9d6a143d8a73e69121e1b1", "question": {"question_concept": "going into trance", "choices": [{"label": "A", "text": "religious experience"}, {"label": "B", "text": "closed eyes"}, {"label": "C", "text": "loss of control"}, {"label": "D", "text": "sleep"}, {"label": "E", "text": "hallucination"}], "stem": "What is the best way to begin going into trance?"}}
{"answerKey": "A", "id": "e1744fc698cffb574e5cf4b29a81ce76", "question": {"question_concept": "computer user", "choices": [{"label": "A", "text": "office building"}, {"label": "B", "text": "internet cafe"}, {"label": "C", "text": "house"}, {"label": "D", "text": "school"}, {"label": "E", "text": "internet cafe"}], "stem": "A computer user working on an important work assignment is located in what structure?"}}
{"answerKey": "A", "id": "27604394ccee83e089f9ffae1883cf07", "question": {"question_concept": "music", "choices": [{"label": "A", "text": "carnival"}, {"label": "B", "text": "night club"}, {"label": "C", "text": "theatre"}, {"label": "D", "text": "opera"}, {"label": "E", "text": "ringmaster"}], "stem": "The music was festive but why are the horses dancing in circles"}}
{"answerKey": "A", "id": "1272e693cf9152e7ac71095c643676b5", "question": {"question_concept": "mezzanine", "choices": [{"label": "A", "text": "theater"}, {"label": "B", "text": "floors"}, {"label": "C", "text": "concert hall"}, {"label": "D", "text": "education"}, {"label": "E", "text": "school"}], "stem": "In the building where James worked there was a small mezzanine in the auditorium to make more space for seats.  Where might James work?"}}
{"answerKey": "D", "id": "7bff23f6c12e9136f0961514bebb8cd3", "question": {"question_concept": "rainy day", "choices": [{"label": "A", "text": "sleep"}, {"label": "B", "text": "write"}, {"label": "C", "text": "make bread"}, {"label": "D", "text": "stay in bed"}, {"label": "E", "text": "enjoy film"}], "stem": "If you aren't well rested and it's a rainy day what might you do?"}}
{"answerKey": "E", "id": "20ae70b9b157b298569cd761787833e7", "question": {"question_concept": "stove", "choices": [{"label": "A", "text": "tent"}, {"label": "B", "text": "car"}, {"label": "C", "text": "living room"}, {"label": "D", "text": "friend's house"}, {"label": "E", "text": "apartment"}], "stem": "Where would you have a stove if you don't live in a detached dwelling?"}}
{"answerKey": "D", "id": "bdd29d7c12e3d795b78ffc048631e7e7", "question": {"question_concept": "revolving door", "choices": [{"label": "A", "text": "new york"}, {"label": "B", "text": "public place"}, {"label": "C", "text": "bank"}, {"label": "D", "text": "mall"}, {"label": "E", "text": "supermarket door"}], "stem": "What kind of place has a revolving door and has things to buy in it?"}}
{"answerKey": "C", "id": "cc1a547bdfdcc95e4d632453af14bc96", "question": {"question_concept": "books", "choices": [{"label": "A", "text": "cabinet"}, {"label": "B", "text": "backpack"}, {"label": "C", "text": "table"}, {"label": "D", "text": "shelf"}, {"label": "E", "text": "sink"}], "stem": "Where can books be read?"}}
{"answerKey": "C", "id": "896b25dc41f84357add1c798d4a96cd8", "question": {"question_concept": "seaweed", "choices": [{"label": "A", "text": "ocean"}, {"label": "B", "text": "found in ocean"}, {"label": "C", "text": "water"}, {"label": "D", "text": "found in sea"}, {"label": "E", "text": "beach"}], "stem": "Where is seaweed usually found alive?"}}
{"answerKey": "E", "id": "1ca3cd9475d7e9da2ddb74911ee2bb68", "question": {"question_concept": "lizard", "choices": [{"label": "A", "text": "documentary"}, {"label": "B", "text": "costa rica"}, {"label": "C", "text": "garden"}, {"label": "D", "text": "encouragement"}, {"label": "E", "text": "captivity"}], "stem": "If a lizard is fed by people every day, what has happened to it?"}}
{"answerKey": "C", "id": "129ec46cc2541b73198d774ee632c8d7", "question": {"question_concept": "elevate", "choices": [{"label": "A", "text": "sadden"}, {"label": "B", "text": "demote"}, {"label": "C", "text": "depress"}, {"label": "D", "text": "drop"}, {"label": "E", "text": "decrease"}], "stem": "What will happen to someone if his or her spirits cannot elevate?"}}
{"answerKey": "B", "id": "0e5c7c0cec5b693e52f74f5f879d84fb", "question": {"question_concept": "crab", "choices": [{"label": "A", "text": "most offices"}, {"label": "B", "text": "fish department"}, {"label": "C", "text": "fancy restaurant"}, {"label": "D", "text": "government submarine"}, {"label": "E", "text": "chesapeake bay"}], "stem": "If you wanted a license to catch crabs, what government office would you go to?"}}
{"answerKey": "B", "id": "af035b75b6f7a1927b1648963f281c5e", "question": {"question_concept": "side chair", "choices": [{"label": "A", "text": "bedroom"}, {"label": "B", "text": "table"}, {"label": "C", "text": "wheel barrow"}, {"label": "D", "text": "building"}, {"label": "E", "text": "office"}], "stem": "What furniture will you normally find near a side chair?"}}
{"answerKey": "C", "id": "32d5b7fcae24f0d4871cfb219c5a4b47", "question": {"question_concept": "metal", "choices": [{"label": "A", "text": "junkyard"}, {"label": "B", "text": "ore"}, {"label": "C", "text": "instruments"}, {"label": "D", "text": "metal fabrication shop"}, {"label": "E", "text": "bowls"}], "stem": "Metal is used to make what?"}}
{"answerKey": "D", "id": "87505da761eaa5c3c4703d02a12d46bc", "question": {"question_concept": "manchester", "choices": [{"label": "A", "text": "england"}, {"label": "B", "text": "united kingdome"}, {"label": "C", "text": "lancashire"}, {"label": "D", "text": "greater manchester"}, {"label": "E", "text": "cheshire"}], "stem": "What is the word added to Manchester that signifies what county it is in?"}}
{"answerKey": "E", "id": "ef3d5d35128678937c36438466e0fc93", "question": {"question_concept": "program", "choices": [{"label": "A", "text": "get mad"}, {"label": "B", "text": "compile"}, {"label": "C", "text": "debug"}, {"label": "D", "text": "write code"}, {"label": "E", "text": "get frustrated"}], "stem": "The program kept getting errors, the amateur end user began to what?"}}
{"answerKey": "B", "id": "4f1d8007b446b0e10f07fd63cbd31b6f", "question": {"question_concept": "sun", "choices": [{"label": "A", "text": "ocean"}, {"label": "B", "text": "heat"}, {"label": "C", "text": "life on earth"}, {"label": "D", "text": "wrinkles"}, {"label": "E", "text": "light"}], "stem": "John knew that the sun produced a massive amount of energy in two forms.  If you were on the surface of the sun, what would kill you first?"}}
{"answerKey": "B", "id": "4c30d5eed4137cba89747510973f37a3", "question": {"question_concept": "lawyers", "choices": [{"label": "A", "text": "work"}, {"label": "B", "text": "courtroom"}, {"label": "C", "text": "office building"}, {"label": "D", "text": "press charges"}, {"label": "E", "text": "theatre"}], "stem": "Lawyers often talk in front of an audience where?"}}
{"answerKey": "B", "id": "515834727e23e30ab7c8fe5ba7e9a765", "question": {"question_concept": "chain", "choices": [{"label": "A", "text": "gear shift"}, {"label": "B", "text": "garage"}, {"label": "C", "text": "kitchen"}, {"label": "D", "text": "jewelry store"}, {"label": "E", "text": "hardware store"}], "stem": "James bought a new set of tire chains and put them somewhere he could find them.  Where would he put them?"}}
{"answerKey": "A", "id": "34ec6393db5a01f689c11fac153e31c1", "question": {"question_concept": "plants", "choices": [{"label": "A", "text": "roots"}, {"label": "B", "text": "millions of cells"}, {"label": "C", "text": "see work"}, {"label": "D", "text": "leaves to gather light"}, {"label": "E", "text": "flowers on"}], "stem": "If I wanted to eat something that is made from plants and needs to be washed, what would it be?"}}
{"answerKey": "A", "id": "0f0e339412f719a019bf373e6daf2530", "question": {"question_concept": "ficus", "choices": [{"label": "A", "text": "shady places"}, {"label": "B", "text": "screened porch"}, {"label": "C", "text": "pots"}, {"label": "D", "text": "ceramics"}, {"label": "E", "text": "clay pot"}], "stem": "Ficus can be planted in a yard to make summer more bearable, what sort of areas do they create?"}}
{"answerKey": "A", "id": "489a082aab43dd1a53f3f1f89c2365ed", "question": {"question_concept": "children", "choices": [{"label": "A", "text": "parents"}, {"label": "B", "text": "old people"}, {"label": "C", "text": "play ball"}, {"label": "D", "text": "many adults"}, {"label": "E", "text": "grown ups"}], "stem": "Children's behavior is a direct reflection of their what?"}}
{"answerKey": "E", "id": "7c45033e9fd9f1a759923971b14390ed", "question": {"question_concept": "most people", "choices": [{"label": "A", "text": "apartments"}, {"label": "B", "text": "listen to music"}, {"label": "C", "text": "have friends"}, {"label": "D", "text": "know what ophiolites"}, {"label": "E", "text": "hug"}], "stem": "Most people who are family like to greet each other with a what?"}}
{"answerKey": "D", "id": "061f326d2a87a10da6316b55bd5522bd", "question": {"question_concept": "hose", "choices": [{"label": "A", "text": "garden shed"}, {"label": "B", "text": "hardware store"}, {"label": "C", "text": "greenhouse"}, {"label": "D", "text": "garage"}, {"label": "E", "text": "in a van"}], "stem": "John bought a new water hose.  But he found his old one near his car.  Where did he find the old one?"}}
{"answerKey": "D", "id": "d747c4e463b80bfcc49b874063f9fae1", "question": {"question_concept": "control room", "choices": [{"label": "A", "text": "airbase"}, {"label": "B", "text": "prison"}, {"label": "C", "text": "mill"}, {"label": "D", "text": "nuclear plant"}, {"label": "E", "text": "recording studio"}], "stem": "Where is a control room needed to prevent wide spread disaster?"}}
{"answerKey": "B", "id": "df3d27338bcf86b341b8b02d4309daf5", "question": {"question_concept": "pizza", "choices": [{"label": "A", "text": "table"}, {"label": "B", "text": "plate"}, {"label": "C", "text": "restaurant"}, {"label": "D", "text": "oven"}, {"label": "E", "text": "popular"}], "stem": "Where do you keep your pizza slice before you eat it?"}}
{"answerKey": "A", "id": "db63bf66a8bfd16e5103cbdd350f5202", "question": {"question_concept": "dressing room", "choices": [{"label": "A", "text": "theater"}, {"label": "B", "text": "train"}, {"label": "C", "text": "bathhouse"}, {"label": "D", "text": "dwelling"}, {"label": "E", "text": "actors and actresses"}], "stem": "Everybody was changing into costumes in the dressing room, it was almost time to take the what stage?"}}
{"answerKey": "D", "id": "f8a9208665a4f2d64986940456b4b964", "question": {"question_concept": "homeowner", "choices": [{"label": "A", "text": "own home"}, {"label": "B", "text": "mail property tax payments"}, {"label": "C", "text": "board windows"}, {"label": "D", "text": "cut grass"}, {"label": "E", "text": "receive mail"}], "stem": "The homeowner frowned at the price of gas, what did he have to do later?"}}
{"answerKey": "C", "id": "1bf4c6b5bd870b1a079106e1e97e5d09", "question": {"question_concept": "thoroughfare", "choices": [{"label": "A", "text": "move about"}, {"label": "B", "text": "city"}, {"label": "C", "text": "country"}, {"label": "D", "text": "town"}, {"label": "E", "text": "new york city"}], "stem": "A thoroughfare meandered through fields and woods, where was it passing though?"}}
{"answerKey": "A", "id": "c1c73ef0ff662a76cd42c3500240974a", "question": {"question_concept": "ottoman", "choices": [{"label": "A", "text": "furniture store"}, {"label": "B", "text": "parlor"}, {"label": "C", "text": "turkey"}, {"label": "D", "text": "living room"}, {"label": "E", "text": "den"}], "stem": "If I want a new ottoman, where should I go?"}}
{"answerKey": "A", "id": "aefa60233f3c5c4966f8ac22e901a1c7", "question": {"question_concept": "roadway", "choices": [{"label": "A", "text": "neighborhood"}, {"label": "B", "text": "city"}, {"label": "C", "text": "fate"}, {"label": "D", "text": "countryside"}, {"label": "E", "text": "maps"}], "stem": "Sean was leaving work and took the roadway that led to his what?"}}
{"answerKey": "C", "id": "9221962ed3a6094e5c8f33785ce048cd", "question": {"question_concept": "jellyfish", "choices": [{"label": "A", "text": "adriatic sea"}, {"label": "B", "text": "mediterranean sea"}, {"label": "C", "text": "hand"}, {"label": "D", "text": "see"}, {"label": "E", "text": "atlantic ocean"}], "stem": "What can you use to get a jellyfish?"}}
{"answerKey": "A", "id": "8c8052980e357545398d27d1c3c832d8", "question": {"question_concept": "shelf", "choices": [{"label": "A", "text": "chest of drawers"}, {"label": "B", "text": "stove"}, {"label": "C", "text": "hold alcohol"}, {"label": "D", "text": "bookcase"}, {"label": "E", "text": "grocery store"}], "stem": "What has a shelf that does not allow you to see what is inside of it?"}}
{"answerKey": "B", "id": "418913999c665ae9527fd14a6132da39", "question": {"question_concept": "stabbing to death", "choices": [{"label": "A", "text": "gruesome"}, {"label": "B", "text": "being arrested"}, {"label": "C", "text": "pool of blood"}, {"label": "D", "text": "mess"}, {"label": "E", "text": "grisly"}], "stem": "What will likely happen after stabbing to death a person?"}}
{"answerKey": "E", "id": "2634468d21fa33a88cefe28a5d613f59", "question": {"question_concept": "blowfish", "choices": [{"label": "A", "text": "cuba"}, {"label": "B", "text": "styx"}, {"label": "C", "text": "atlantic ocean"}, {"label": "D", "text": "france"}, {"label": "E", "text": "jungle"}], "stem": "The boat passenger was explaining his fear of blowfish, but the captain figured he meant piranhas since they were on a river in the what?"}}
{"answerKey": "C", "id": "66bfb6e209c94e6be5b0d04b0c7e2064", "question": {"question_concept": "office", "choices": [{"label": "A", "text": "skyscraper"}, {"label": "B", "text": "new york"}, {"label": "C", "text": "school building"}, {"label": "D", "text": "city"}, {"label": "E", "text": "work"}], "stem": "Where could you find only a few office?"}}
{"answerKey": "E", "id": "3163910d665c139a1f6f07d85803baba", "question": {"question_concept": "gentleman", "choices": [{"label": "A", "text": "club"}, {"label": "B", "text": "restaurant"}, {"label": "C", "text": "university"}, {"label": "D", "text": "pub"}, {"label": "E", "text": "church"}], "stem": "Where can I go to be a religious gentleman?"}}
{"answerKey": "A", "id": "0e52659484f2f6d763cf0d38d4c5999d", "question": {"question_concept": "lens", "choices": [{"label": "A", "text": "microscope"}, {"label": "B", "text": "abbreviate"}, {"label": "C", "text": "glasses"}, {"label": "D", "text": "camera"}, {"label": "E", "text": "telescope"}], "stem": "I want to see a prepared slide up close, what would I use to help?"}}
{"answerKey": "D", "id": "167d2cfa04bfaea0e0b5bac3598d5769", "question": {"question_concept": "magazine", "choices": [{"label": "A", "text": "bank"}, {"label": "B", "text": "rack"}, {"label": "C", "text": "bed"}, {"label": "D", "text": "newsstand"}, {"label": "E", "text": "bus depot"}], "stem": "Where can you buy a magazine, paper or gum?"}}
{"answerKey": "D", "id": "39572e0ba1db51fa74f7fc2d90c5ec7f", "question": {"question_concept": "wood", "choices": [{"label": "A", "text": "carpet"}, {"label": "B", "text": "boat"}, {"label": "C", "text": "river"}, {"label": "D", "text": "lumberyard"}, {"label": "E", "text": "synagogue"}], "stem": "Where would you get some wood if you do not have any?"}}
{"answerKey": "C", "id": "2a32b1e541b1daae04690d0d3a4b3310", "question": {"question_concept": "mound", "choices": [{"label": "A", "text": "desert"}, {"label": "B", "text": "baseball field"}, {"label": "C", "text": "hell"}, {"label": "D", "text": "baseball diamond"}, {"label": "E", "text": "baseball stadium"}], "stem": "The pitcher felt stress and tension on the mound, what did he feel like?"}}
{"answerKey": "D", "id": "71cbfeb995b06b21e890c91040722252", "question": {"question_concept": "competing", "choices": [{"label": "A", "text": "enemies"}, {"label": "B", "text": "perform better"}, {"label": "C", "text": "sweat"}, {"label": "D", "text": "tension"}, {"label": "E", "text": "frostbite"}], "stem": "What negative effect can competing in a chess game on a cold day have?"}}
{"answerKey": "E", "id": "a15d564d0be6996251b5d523ac62db2a", "question": {"question_concept": "book", "choices": [{"label": "A", "text": "knowledge"}, {"label": "B", "text": "cover"}, {"label": "C", "text": "no pictures"}, {"label": "D", "text": "past"}, {"label": "E", "text": "many words"}], "stem": "Why is it hard for a young child to read a long book?"}}
{"answerKey": "E", "id": "6bd170c8d3d99d3c47b3e96427bacaeb", "question": {"question_concept": "hot day", "choices": [{"label": "A", "text": "dive"}, {"label": "B", "text": "cool off"}, {"label": "C", "text": "fresh cake"}, {"label": "D", "text": "go for swim"}, {"label": "E", "text": "eat ice cream"}], "stem": "On a hot day what can you do to enjoy something cool and sweet?"}}
{"answerKey": "E", "id": "7bc1198664b376f79d584725ad7f874b", "question": {"question_concept": "foreword", "choices": [{"label": "A", "text": "last word"}, {"label": "B", "text": "conclusion"}, {"label": "C", "text": "ikea instructions"}, {"label": "D", "text": "afterword"}, {"label": "E", "text": "epilogue"}], "stem": "What is likely to be found in a book that is not a foreword?"}}
{"answerKey": "D", "id": "d6c002d46d9bfa466637cec4a134f332", "question": {"question_concept": "day", "choices": [{"label": "A", "text": "week"}, {"label": "B", "text": "bright"}, {"label": "C", "text": "night"}, {"label": "D", "text": "twenty four"}, {"label": "E", "text": "year"}], "stem": "How many hours are in a day?"}}
{"answerKey": "E", "id": "8cb45b421375243e788cfc64bd77b051", "question": {"question_concept": "religion", "choices": [{"label": "A", "text": "both positive and negative"}, {"label": "B", "text": "unknowable"}, {"label": "C", "text": "important to people"}, {"label": "D", "text": "ocean"}, {"label": "E", "text": "confusing"}], "stem": "Why is religion so hard to understand?"}}
{"answerKey": "B", "id": "d6ff2d749494d89e9c7a53f587c519f4", "question": {"question_concept": "communicating", "choices": [{"label": "A", "text": "thinking"}, {"label": "B", "text": "effort"}, {"label": "C", "text": "laugh"}, {"label": "D", "text": "force"}, {"label": "E", "text": "medium"}], "stem": "The couple explained they were having trouble communicating, it seemed every conversation took great what?"}}
{"answerKey": "E", "id": "6974d215428a974641c1df18678522f5", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "cross street"}, {"label": "B", "text": "have a party"}, {"label": "C", "text": "experience life"}, {"label": "D", "text": "cross road"}, {"label": "E", "text": "man crew"}], "stem": "What would a person need to do if his or her captain dies at sea?"}}
{"answerKey": "A", "id": "b94a9764acff078b52a9cbae04661dc9", "question": {"question_concept": "children", "choices": [{"label": "A", "text": "need care"}, {"label": "B", "text": "come home"}, {"label": "C", "text": "fast food"}, {"label": "D", "text": "watch television"}, {"label": "E", "text": "wash dishes"}], "stem": "What do children require to grow up healthy?"}}
{"answerKey": "B", "id": "80930e9df9ac4ad752749a54e7fc124f_1", "question": {"question_concept": "house", "choices": [{"label": "A", "text": "city"}, {"label": "B", "text": "subdivision"}, {"label": "C", "text": "newspaper"}, {"label": "D", "text": "residential area"}, {"label": "E", "text": "street"}], "stem": "I house outside the center of a community is said to be where?"}}
{"answerKey": "C", "id": "3310b5b24f03d67179fababf9ae95144", "question": {"question_concept": "letter", "choices": [{"label": "A", "text": "syllable"}, {"label": "B", "text": "english alphabet"}, {"label": "C", "text": "word"}, {"label": "D", "text": "email"}, {"label": "E", "text": "invitation"}], "stem": "The field general began to write out a letter to the king, he was told to send what when the enemy was near?"}}
{"answerKey": "D", "id": "846bc47ced7119ad2ee19a8780d7fe18", "question": {"question_concept": "pens", "choices": [{"label": "A", "text": "write sentences on paper"}, {"label": "B", "text": "ink in"}, {"label": "C", "text": "ink cartridges"}, {"label": "D", "text": "caps"}, {"label": "E", "text": "cling film"}], "stem": "What will you put on a pen to prevent it from drying out?"}}
{"answerKey": "E", "id": "fd5a34e94303d7fd343de2a8f36943d5", "question": {"question_concept": "cave", "choices": [{"label": "A", "text": "west virginia"}, {"label": "B", "text": "kentucky"}, {"label": "C", "text": "desert"}, {"label": "D", "text": "sea"}, {"label": "E", "text": "rocky hills"}], "stem": "After climbing the mountains, the explored found the cave, what was the general goegraphy of the region he found it in?"}}
{"answerKey": "B", "id": "4e87db4771f2d6423034935446e3fff1", "question": {"question_concept": "fire extinguisher", "choices": [{"label": "A", "text": "hospital"}, {"label": "B", "text": "chemistry lab"}, {"label": "C", "text": "most businesses"}, {"label": "D", "text": "classroom"}, {"label": "E", "text": "public building"}], "stem": "They dealt with combustible mixtures in their experiments, this is why they kept a fire extinguisher where?"}}
{"answerKey": "A", "id": "a585df0818180ce3c06f963a4c3c810a", "question": {"question_concept": "fruit", "choices": [{"label": "A", "text": "gay bar"}, {"label": "B", "text": "market"}, {"label": "C", "text": "grocery store"}, {"label": "D", "text": "refrigerator"}, {"label": "E", "text": "container"}], "stem": "If someone mean wanted to insult somebody by calling them a fruit, where is probably not the smartest place to do it?"}}
{"answerKey": "B", "id": "c9f7d07e6d363a99f5fadd68a4dfa35a", "question": {"question_concept": "toothpick", "choices": [{"label": "A", "text": "box"}, {"label": "B", "text": "grocery store"}, {"label": "C", "text": "eyes"}, {"label": "D", "text": "chewing"}, {"label": "E", "text": "mouth"}], "stem": "Where would you get a toothpick if you do not have any?"}}
{"answerKey": "E", "id": "c7cb327fa4c0008efaa7741081a365d4", "question": {"question_concept": "mosquitoes", "choices": [{"label": "A", "text": "spread disease"}, {"label": "B", "text": "swamp"}, {"label": "C", "text": "fly away"}, {"label": "D", "text": "cat condo"}, {"label": "E", "text": "bug campers"}], "stem": "What would you be building if you designed a place for an annoying critter to stay?"}}
{"answerKey": "A", "id": "c54ddc0f9d170ba65d9f4f2e0bb41d1c", "question": {"question_concept": "bee", "choices": [{"label": "A", "text": "swarm"}, {"label": "B", "text": "pack"}, {"label": "C", "text": "countryside"}, {"label": "D", "text": "soft drink"}, {"label": "E", "text": "field of flowers"}], "stem": "The man working in the attic swatted away a bee, but soon the single bee was an entire what?"}}
{"answerKey": "C", "id": "1729c737ff92cf558efecde2c6cafc5e", "question": {"question_concept": "hiking", "choices": [{"label": "A", "text": "cast iron stomach"}, {"label": "B", "text": "physical exertion"}, {"label": "C", "text": "shin splints"}, {"label": "D", "text": "adventure"}, {"label": "E", "text": "fatigue"}], "stem": "What do you need to wear when hiking?"}}
{"answerKey": "D", "id": "19dfd55e967dacd6f5700a62c1e14eee", "question": {"question_concept": "sports equipment", "choices": [{"label": "A", "text": "mall"}, {"label": "B", "text": "office supply store"}, {"label": "C", "text": "school"}, {"label": "D", "text": "sporting goods store"}, {"label": "E", "text": "sporting event"}], "stem": "What type of store would have lots of sports equipment?"}}
{"answerKey": "B", "id": "b9bed83138901f4a45041b02c5b242c1", "question": {"question_concept": "wristwatch", "choices": [{"label": "A", "text": "case"}, {"label": "B", "text": "jewelry store"}, {"label": "C", "text": "shopping"}, {"label": "D", "text": "jewelery box"}, {"label": "E", "text": "hock"}], "stem": "The business man was promoted recently, to celebrate he went where to buy an expensive wristwatch?"}}
{"answerKey": "B", "id": "b9d22425a3d5810be9528a55245c8f09", "question": {"question_concept": "going to play", "choices": [{"label": "A", "text": "slowly"}, {"label": "B", "text": "rush"}, {"label": "C", "text": "being entertained"}, {"label": "D", "text": "have fun"}, {"label": "E", "text": "enjoyment"}], "stem": "How is a child eager to be going to play likely to get there?"}}
{"answerKey": "B", "id": "2af70107e04e61e3c7884bc743901c02", "question": {"question_concept": "buying products", "choices": [{"label": "A", "text": "tax return"}, {"label": "B", "text": "bankruptcy"}, {"label": "C", "text": "pleasure"}, {"label": "D", "text": "debt"}, {"label": "E", "text": "spending money"}], "stem": "There's some new buying products designed to get you money if you have none. The first step is that it will show you how to declare what?"}}
{"answerKey": "B", "id": "be2cb9c96069ac355a7ccef262743d14", "question": {"question_concept": "handle", "choices": [{"label": "A", "text": "bathroom"}, {"label": "B", "text": "hardware store"}, {"label": "C", "text": "water fountain"}, {"label": "D", "text": "grocery store"}, {"label": "E", "text": "fridge"}], "stem": "Where can you buy a replacement ax handle?"}}
{"answerKey": "B", "id": "799e48ec7fb16415c8f82828c5761ed1", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "make mistakes"}, {"label": "B", "text": "ridiculous"}, {"label": "C", "text": "have no home"}, {"label": "D", "text": "mentally unhinged"}, {"label": "E", "text": "schizophrenia"}], "stem": "Is that person acting as silly as a clown?"}}
{"answerKey": "D", "id": "a5db1e9677af118deb8e4add8bc18db2", "question": {"question_concept": "louisiana", "choices": [{"label": "A", "text": "deep south"}, {"label": "B", "text": "98 of world's crayfish"}, {"label": "C", "text": "united states"}, {"label": "D", "text": "gulf states"}, {"label": "E", "text": "bible belt"}], "stem": "Which group of states is Louisiana part of?"}}
{"answerKey": "D", "id": "28357ebf85f8bb82b6a3210c4397e0aa", "question": {"question_concept": "plate", "choices": [{"label": "A", "text": "kitchen cupboard"}, {"label": "B", "text": "floor"}, {"label": "C", "text": "table"}, {"label": "D", "text": "dishwasher"}, {"label": "E", "text": "flea market"}], "stem": "Where would you put a plate immediately after eating from it?"}}
{"answerKey": "D", "id": "7b95825a19d6930d6aed35c7c57a2d82", "question": {"question_concept": "dirt", "choices": [{"label": "A", "text": "ground"}, {"label": "B", "text": "subway"}, {"label": "C", "text": "bank"}, {"label": "D", "text": "bed"}, {"label": "E", "text": "street"}], "stem": "James couldn't get comfortable.  There was too much dirt.  He needed to clean out what?"}}
{"answerKey": "E", "id": "6b270159bd402ddd498a38153f9d1efe", "question": {"question_concept": "rats", "choices": [{"label": "A", "text": "sewers"}, {"label": "B", "text": "laboratory"}, {"label": "C", "text": "basement"}, {"label": "D", "text": "clinic"}, {"label": "E", "text": "cellar"}], "stem": "The rats were hiding in the house, where were they?"}}
{"answerKey": "A", "id": "eae0e03773365064ce915603c7addc91", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "ask questions"}, {"label": "B", "text": "experience joy"}, {"label": "C", "text": "believe in god"}, {"label": "D", "text": "talk to each other"}, {"label": "E", "text": "get sick"}], "stem": "What do people do when they don't understand something?"}}
{"answerKey": "E", "id": "a5ca7c89196e54938b5827814d0071d4", "question": {"question_concept": "kite", "choices": [{"label": "A", "text": "end of line"}, {"label": "B", "text": "hobby shop"}, {"label": "C", "text": "his hand"}, {"label": "D", "text": "toy store"}, {"label": "E", "text": "child's hand"}], "stem": "James saw a kite flying in the sky.  He traced the string back to its origin and found it.  Where did the string begin?"}}
{"answerKey": "D", "id": "ffc3461d437a1c6c22d1c4f6439ebd9c", "question": {"question_concept": "child", "choices": [{"label": "A", "text": "loved"}, {"label": "B", "text": "learn"}, {"label": "C", "text": "eat cake"}, {"label": "D", "text": "balloon"}, {"label": "E", "text": "become adult"}], "stem": "What rubber toy filled with helium will make a child happy?"}}
{"answerKey": "C", "id": "aa2dcd9bcce5e4445bd3bacbf0bb11d3", "question": {"question_concept": "beer", "choices": [{"label": "A", "text": "bottle"}, {"label": "B", "text": "grocery store"}, {"label": "C", "text": "casino"}, {"label": "D", "text": "spaceship"}, {"label": "E", "text": "hockey game"}], "stem": "Where do people get beer after a bit of gambling?"}}
{"answerKey": "E", "id": "6cc797ec148c1fc74592957a55bd0951", "question": {"question_concept": "using television", "choices": [{"label": "A", "text": "turn off"}, {"label": "B", "text": "functions"}, {"label": "C", "text": "turning off"}, {"label": "D", "text": "entertainment"}, {"label": "E", "text": "fall asleep"}], "stem": "What can happen to you when you are using television and it is not interesting?"}}
{"answerKey": "E", "id": "64dbe5cb840ef4f1d25f8b68db8d5fed", "question": {"question_concept": "dressing room", "choices": [{"label": "A", "text": "brush hair"}, {"label": "B", "text": "theater"}, {"label": "C", "text": "house"}, {"label": "D", "text": "dwelling"}, {"label": "E", "text": "bathhouse"}], "stem": "The business men left the discussion in the dressing room, now they just wanted to relax in the sauna of the what?"}}
{"answerKey": "D", "id": "a74753bf249c1cbcff632c5c16b0397b", "question": {"question_concept": "plant", "choices": [{"label": "A", "text": "flower pot"}, {"label": "B", "text": "shelf"}, {"label": "C", "text": "windowsill"}, {"label": "D", "text": "outside"}, {"label": "E", "text": "sill"}], "stem": "Where is a likely place for an ivy plant?"}}
{"answerKey": "A", "id": "9190efbd77fe10b989fcaae35e208a0f", "question": {"question_concept": "baseball stadium", "choices": [{"label": "A", "text": "phoenix"}, {"label": "B", "text": "chicago"}, {"label": "C", "text": "antarctica"}, {"label": "D", "text": "san francisco"}, {"label": "E", "text": "urban areas"}], "stem": "Where has the newest baseball stadium?"}}
{"answerKey": "A", "id": "ff0303db294a823d4138fb81a6ee6438", "question": {"question_concept": "ground floor", "choices": [{"label": "A", "text": "brownstone"}, {"label": "B", "text": "hotel"}, {"label": "C", "text": "condominium"}, {"label": "D", "text": "entering building"}, {"label": "E", "text": "office building"}], "stem": "What type of residence has a ground floor with a stoop?"}}
{"answerKey": "B", "id": "63963c9c15835d451aac2e1e0b116388", "question": {"question_concept": "wood", "choices": [{"label": "A", "text": "gilded"}, {"label": "B", "text": "porous"}, {"label": "C", "text": "solid"}, {"label": "D", "text": "painted"}, {"label": "E", "text": "less dense than water"}], "stem": "If the wood texture is not smooth it is what?"}}
{"answerKey": "D", "id": "cc8324b73ed9625e723ef041dfc77a37", "question": {"question_concept": "losing weight", "choices": [{"label": "A", "text": "loose skin"}, {"label": "B", "text": "beauty"}, {"label": "C", "text": "miss universe"}, {"label": "D", "text": "death"}, {"label": "E", "text": "healthier"}], "stem": "What might happen if someone is not losing weight?"}}
{"answerKey": "A", "id": "684dbde19719e8224113433981d6e01e", "question": {"question_concept": "capital", "choices": [{"label": "A", "text": "small town"}, {"label": "B", "text": "jail"}, {"label": "C", "text": "lower case"}, {"label": "D", "text": "contain governmental activities"}, {"label": "E", "text": "lowercase"}], "stem": "Billy lived in the capital of his country, then he moved.  Where might he move to?"}}
{"answerKey": "B", "id": "21450618657881d8c5af73691f3423a7_1", "question": {"question_concept": "knowledge", "choices": [{"label": "A", "text": "color"}, {"label": "B", "text": "class"}, {"label": "C", "text": "meeting"}, {"label": "D", "text": "university"}, {"label": "E", "text": "encyclopedia"}], "stem": "Making a schedule was easy to pick, the major called for knowledge that required a certain what?"}}
{"answerKey": "E", "id": "8b94b61b604ec0d7508804033eec6d23", "question": {"question_concept": "getting in shape", "choices": [{"label": "A", "text": "eat more"}, {"label": "B", "text": "starve"}, {"label": "C", "text": "give up"}, {"label": "D", "text": "period of recovery"}, {"label": "E", "text": "jogging"}], "stem": "When getting in shape, this is something that does wonders?"}}
{"answerKey": "A", "id": "52ecf169febc95a7f5ccb048fc85857d", "question": {"question_concept": "driving car", "choices": [{"label": "A", "text": "automobile accidents"}, {"label": "B", "text": "backache"}, {"label": "C", "text": "pollution"}, {"label": "D", "text": "smoke"}, {"label": "E", "text": "low fuel tank"}], "stem": "What could prevent a driving car from continuing to drive?"}}
{"answerKey": "E", "id": "e408a5a031caec33782cb3b3a005eecc", "question": {"question_concept": "large container", "choices": [{"label": "A", "text": "supermarket"}, {"label": "B", "text": "factory"}, {"label": "C", "text": "juice"}, {"label": "D", "text": "hostel"}, {"label": "E", "text": "cabinet"}], "stem": "Where do you store a large container?"}}
{"answerKey": "C", "id": "31bd05ba62a16ee35217224b98c6baea", "question": {"question_concept": "stopping being married to", "choices": [{"label": "A", "text": "isolation"}, {"label": "B", "text": "grief"}, {"label": "C", "text": "happiness"}, {"label": "D", "text": "relief"}, {"label": "E", "text": "angry"}], "stem": "What is a person likely to experience after they stop being married to a mean person?"}}
{"answerKey": "C", "id": "b4043bd1f65a8ad088e62042eca259c2", "question": {"question_concept": "crowd", "choices": [{"label": "A", "text": "small group"}, {"label": "B", "text": "alone"}, {"label": "C", "text": "solitary"}, {"label": "D", "text": "solitude"}, {"label": "E", "text": "panic"}], "stem": "Despite the large crowds, how did the depressed man feel?"}}
{"answerKey": "D", "id": "4302e727e47f464511d4d04f22bed0d2", "question": {"question_concept": "trash can", "choices": [{"label": "A", "text": "bus stop"}, {"label": "B", "text": "corner"}, {"label": "C", "text": "hockey game"}, {"label": "D", "text": "motel"}, {"label": "E", "text": "alley"}], "stem": "Where does a maid empty a trash can?"}}
{"answerKey": "A", "id": "f0d473701d52125dd055d23042de1b0d", "question": {"question_concept": "dog", "choices": [{"label": "A", "text": "walked"}, {"label": "B", "text": "petted"}, {"label": "C", "text": "affection"}, {"label": "D", "text": "go outside"}, {"label": "E", "text": "scratch"}], "stem": "The dog curled up for a nap, it was tuckered out because it had just been what?"}}
{"answerKey": "D", "id": "d35112a99ab3983fb51c3adae80bc2da", "question": {"question_concept": "umbrella", "choices": [{"label": "A", "text": "waves"}, {"label": "B", "text": "seattle"}, {"label": "C", "text": "suitcase"}, {"label": "D", "text": "beach"}, {"label": "E", "text": "jacket closet"}], "stem": "He used an umbrella while tanning, where was he likely?"}}
{"answerKey": "D", "id": "661474a1a0c29dd7a243b284535ac934", "question": {"question_concept": "birds", "choices": [{"label": "A", "text": "pretty smart"}, {"label": "B", "text": "singing"}, {"label": "C", "text": "dark"}, {"label": "D", "text": "very colorful"}, {"label": "E", "text": "light"}], "stem": "What do the feathers look like on birds found in the rainforest?"}}
{"answerKey": "E", "id": "6416dcdf9b8d7d2787f07e7426f86fe4", "question": {"question_concept": "weasel", "choices": [{"label": "A", "text": "rabbit warren"}, {"label": "B", "text": "used car lot"}, {"label": "C", "text": "chicken coop"}, {"label": "D", "text": "cruise"}, {"label": "E", "text": "viking ship"}], "stem": "The ancient seafaring Norse tribesman brought pelts of weasel aboard his what?"}}
{"answerKey": "C", "id": "0f54a1ee30a0034a3d2db1bfdef9ca85", "question": {"question_concept": "elevation", "choices": [{"label": "A", "text": "disgust"}, {"label": "B", "text": "reduction"}, {"label": "C", "text": "depression"}, {"label": "D", "text": "demotion"}, {"label": "E", "text": "diminishment"}], "stem": "What is the opposite of an area of elevation?"}}
{"answerKey": "A", "id": "7850beb1209c41fabe385cbedc96a61a", "question": {"question_concept": "singers", "choices": [{"label": "A", "text": "warm up"}, {"label": "B", "text": "use microphones"}, {"label": "C", "text": "clear throats"}, {"label": "D", "text": "create music"}, {"label": "E", "text": "sound beautiful"}], "stem": "What do singers need to do before a show?"}}
{"answerKey": "C", "id": "cdb06b28b9c4e7ef7e880d1f096fd409", "question": {"question_concept": "mental illness", "choices": [{"label": "A", "text": "cause irrational behaviour"}, {"label": "B", "text": "recur"}, {"label": "C", "text": "effectively treated"}, {"label": "D", "text": "managed"}, {"label": "E", "text": "cause suffering"}], "stem": "When a person with mental illness receives medication and therapy, what has happened?"}}
{"answerKey": "E", "id": "14309d9bd3c13d1c0efb625198f6304a", "question": {"question_concept": "performing", "choices": [{"label": "A", "text": "action"}, {"label": "B", "text": "butterflies"}, {"label": "C", "text": "happiness"}, {"label": "D", "text": "a sense of calm"}, {"label": "E", "text": "anxiety"}], "stem": "What type of feeling is performing for the first time likely to produce?"}}
{"answerKey": "B", "id": "a00276c6db928900772c0320aeff77c0", "question": {"question_concept": "committing murder", "choices": [{"label": "A", "text": "misery"}, {"label": "B", "text": "kill"}, {"label": "C", "text": "distress"}, {"label": "D", "text": "tickel"}, {"label": "E", "text": "go to jail"}], "stem": "If someone is found to be committing murder, what did they do to someone?"}}
{"answerKey": "E", "id": "4706be6e24f1fafd9ff9fe63583acffd", "question": {"question_concept": "computer", "choices": [{"label": "A", "text": "process information"}, {"label": "B", "text": "believe in god"}, {"label": "C", "text": "make decisions"}, {"label": "D", "text": "process information"}, {"label": "E", "text": "receive data"}], "stem": "The computer was hooked up to the internet, what could it do as a result?"}}
{"answerKey": "B", "id": "ee8819b2da5453848c1cbb9d9c93403b", "question": {"question_concept": "mercury", "choices": [{"label": "A", "text": "toxic"}, {"label": "B", "text": "uninhabitable"}, {"label": "C", "text": "mercury sulphide"}, {"label": "D", "text": "poisonous"}, {"label": "E", "text": "jupiter"}], "stem": "The planet Mercury is unsuitable for human life or what?"}}
{"answerKey": "B", "id": "84ea43b967259814d939c62131f74df0", "question": {"question_concept": "seeing idea become reality", "choices": [{"label": "A", "text": "build"}, {"label": "B", "text": "anxiety"}, {"label": "C", "text": "celebrate"}, {"label": "D", "text": "very nice"}, {"label": "E", "text": "ocean"}], "stem": "Seeing idea become reality was a dream of hers for a long time, but as the time came to get on stage she had more what?"}}
{"answerKey": "E", "id": "60e7338e9e6bfc746a15a161eb12706c", "question": {"question_concept": "creek", "choices": [{"label": "A", "text": "meadow"}, {"label": "B", "text": "stick"}, {"label": "C", "text": "valley"}, {"label": "D", "text": "forest"}, {"label": "E", "text": "countryside"}], "stem": "A creek could be located in the opposite for the city which is called what?"}}
{"answerKey": "C", "id": "a0f5414bf98e094f4d807abee28861a4", "question": {"question_concept": "anemone", "choices": [{"label": "A", "text": "flower bed"}, {"label": "B", "text": "tide pool"}, {"label": "C", "text": "florida keys"}, {"label": "D", "text": "coral sea"}, {"label": "E", "text": "aquarium"}], "stem": "Where off the eastern U.S. would you find an anemone?"}}
{"answerKey": "A", "id": "44120a9443c619d98ce5bfe4bb219c43", "question": {"question_concept": "clothes", "choices": [{"label": "A", "text": "suitcase"}, {"label": "B", "text": "bedroom"}, {"label": "C", "text": "closet"}, {"label": "D", "text": "draws"}, {"label": "E", "text": "dresser"}], "stem": "Where are traveling clothes often kept?"}}
{"answerKey": "B", "id": "38ab26e29a0984b212006d39185c43f3", "question": {"question_concept": "bathroom", "choices": [{"label": "A", "text": "school"}, {"label": "B", "text": "convenience store"}, {"label": "C", "text": "rest area"}, {"label": "D", "text": "mall"}, {"label": "E", "text": "theater"}], "stem": "If one needed the bathroom they needed a key, to get it they had to also buy something from the what?"}}
{"answerKey": "B", "id": "a5e207803684eea8a43ca6670c50b354", "question": {"question_concept": "rise", "choices": [{"label": "A", "text": "lay"}, {"label": "B", "text": "go down"}, {"label": "C", "text": "fall"}, {"label": "D", "text": "below"}, {"label": "E", "text": "sundown"}], "stem": "Although the sun did rise, what did the pessimist warn everyone it would do?"}}
{"answerKey": "E", "id": "af3b9a8b1962cd3bcd19e644d873e7bc", "question": {"question_concept": "shelf", "choices": [{"label": "A", "text": "chest of drawers"}, {"label": "B", "text": "grocery store"}, {"label": "C", "text": "hold alcohol"}, {"label": "D", "text": "nightstand"}, {"label": "E", "text": "bookcase"}], "stem": "The hardcovers were especially tall, so he removed a shelf on the what to make room?"}}
{"answerKey": "D", "id": "43a91955fd0717997a16897c3324e095", "question": {"question_concept": "watching film", "choices": [{"label": "A", "text": "park"}, {"label": "B", "text": "insight"}, {"label": "C", "text": "being entertained"}, {"label": "D", "text": "laughter"}, {"label": "E", "text": "fear"}], "stem": "If you're watching a comedy film what would you expect to hear from the audience?"}}
{"answerKey": "C", "id": "7f7a6f2b3087bf37dadbe8aa8d358047", "question": {"question_concept": "eating lunch", "choices": [{"label": "A", "text": "headache"}, {"label": "B", "text": "bad breath"}, {"label": "C", "text": "heartburn"}, {"label": "D", "text": "gain weight"}, {"label": "E", "text": "farts"}], "stem": "What can eating lunch cause that is painful?"}}
{"answerKey": "E", "id": "37d88a9bb24913c1973cc26d4ce3394f", "question": {"question_concept": "launch platform", "choices": [{"label": "A", "text": "cape canaveral florida"}, {"label": "B", "text": "nasa"}, {"label": "C", "text": "battleship"}, {"label": "D", "text": "ocean"}, {"label": "E", "text": "trapeze"}], "stem": "The performer was ready to put on a show and stepped onto the launch platform, what was his job?"}}
{"answerKey": "E", "id": "001b0f5a841fd81d13fbe67c7c7179d6", "question": {"question_concept": "eating", "choices": [{"label": "A", "text": "reduced"}, {"label": "B", "text": "getting full"}, {"label": "C", "text": "becoming full"}, {"label": "D", "text": "chewing"}, {"label": "E", "text": "defecating"}], "stem": "Eating is part of living, but your body doesn't use it all and the next day you will be doing what?"}}
{"answerKey": "C", "id": "9f9ca9bb06d6afc31b19c365fb29a1c9", "question": {"question_concept": "pizza", "choices": [{"label": "A", "text": "popular"}, {"label": "B", "text": "baked in oven"}, {"label": "C", "text": "restaurant"}, {"label": "D", "text": "oven"}, {"label": "E", "text": "plate"}], "stem": "Where are you if you've paid to get a pizza?"}}
{"answerKey": "B", "id": "d60c5a494539c66982c0f692afde9499", "question": {"question_concept": "place to stay", "choices": [{"label": "A", "text": "mexico"}, {"label": "B", "text": "phone book"}, {"label": "C", "text": "town"}, {"label": "D", "text": "city"}, {"label": "E", "text": "sun dial"}], "stem": "What would you use to find a place to stay?"}}
{"answerKey": "A", "id": "a6d3a2cb250a6310b8cabd31dbe2138c", "question": {"question_concept": "connection", "choices": [{"label": "A", "text": "computer network"}, {"label": "B", "text": "electrical circuit"}, {"label": "C", "text": "lineage"}, {"label": "D", "text": "company"}, {"label": "E", "text": "wall"}], "stem": "If you're seeking a connection for your laptop, what are you trying to hook up with?"}}
{"answerKey": "C", "id": "27c523eb9099d2eec66296558eb4448e", "question": {"question_concept": "child", "choices": [{"label": "A", "text": "care"}, {"label": "B", "text": "balloon"}, {"label": "C", "text": "loved"}, {"label": "D", "text": "become adult"}, {"label": "E", "text": "learn"}], "stem": "The child didn't know the problems his mother was going through, all he had was what for her?"}}
{"answerKey": "B", "id": "2509fdd7d94afe9d0c021654ce0ba93f", "question": {"question_concept": "see new", "choices": [{"label": "A", "text": "open eyes"}, {"label": "B", "text": "go to movies"}, {"label": "C", "text": "kick ball"}, {"label": "D", "text": "make art"}, {"label": "E", "text": "look for"}], "stem": "To see new films you must?"}}
{"answerKey": "A", "id": "75b8195e23c6bada574f1e41471b8f23", "question": {"question_concept": "contemplate", "choices": [{"label": "A", "text": "daydream"}, {"label": "B", "text": "headache"}, {"label": "C", "text": "get ideas"}, {"label": "D", "text": "sleep"}, {"label": "E", "text": "become distracted"}], "stem": "What can happen when you contemplate alone for a long time?"}}
{"answerKey": "B", "id": "df1bf6f3f87975aa0c1b6d6153d9ecef", "question": {"question_concept": "general store", "choices": [{"label": "A", "text": "checkers"}, {"label": "B", "text": "barrels"}, {"label": "C", "text": "baking soda"}, {"label": "D", "text": "buffalo"}, {"label": "E", "text": "salt"}], "stem": "The pioneer went to the general store for storage measures, what was he looking for?"}}
{"answerKey": "B", "id": "e99d4cb2e69d3e020ee9e4e9a84ac45b", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "care less"}, {"label": "B", "text": "play golf"}, {"label": "C", "text": "shake hands"}, {"label": "D", "text": "believe in god"}, {"label": "E", "text": "trip over"}], "stem": "I was apprehensive to buy the expensive equipment to play a game with so much walking and swinging around in grass, but now I understand why people what?"}}
{"answerKey": "A", "id": "b1274d6f5969dea4d46f43fbdc28fd97", "question": {"question_concept": "newspaper", "choices": [{"label": "A", "text": "announce"}, {"label": "B", "text": "communicate"}, {"label": "C", "text": "educate"}, {"label": "D", "text": "inform"}, {"label": "E", "text": "cancel"}], "stem": "What can a newspaper be used to do to an engagement?"}}
{"answerKey": "A", "id": "001cb999a61a5c8b4031ff53cf261714", "question": {"question_concept": "straight", "choices": [{"label": "A", "text": "bent"}, {"label": "B", "text": "bent"}, {"label": "C", "text": "crooked"}, {"label": "D", "text": "straightforth"}, {"label": "E", "text": "curved"}], "stem": "John needed a straight wire.  Unfortunately, this one had endured some abuse and had become what?"}}
{"answerKey": "C", "id": "18ee7a93410a6b4c9cec5d4894775991_1", "question": {"question_concept": "metal", "choices": [{"label": "A", "text": "dirt"}, {"label": "B", "text": "instruments"}, {"label": "C", "text": "ore"}, {"label": "D", "text": "car"}, {"label": "E", "text": "junkyard"}], "stem": "Metal is taken from what which is pulled from the ground?"}}
{"answerKey": "D", "id": "3b8be90fdd8c67571d8d692eaa6dd87b", "question": {"question_concept": "bucket", "choices": [{"label": "A", "text": "utility closet"}, {"label": "B", "text": "outside"}, {"label": "C", "text": "well"}, {"label": "D", "text": "garden shed"}, {"label": "E", "text": "garage"}], "stem": "When not in use where on your property would you store you bucket?"}}
{"answerKey": "D", "id": "300bd7704ae8c5fcef618902f18fd01d", "question": {"question_concept": "relax", "choices": [{"label": "A", "text": "find time"}, {"label": "B", "text": "check mail"}, {"label": "C", "text": "listen to music"}, {"label": "D", "text": "go to bed"}, {"label": "E", "text": "stop worrying"}], "stem": "What does someone do to relax at night?"}}
{"answerKey": "C", "id": "f18833ace65a54709377134168b457a9", "question": {"question_concept": "stapler", "choices": [{"label": "A", "text": "office building"}, {"label": "B", "text": "office supply store"}, {"label": "C", "text": "desk drawer"}, {"label": "D", "text": "with dwight"}, {"label": "E", "text": "desktop"}], "stem": "Where might the stapler be if I cannot find it?"}}
{"answerKey": "B", "id": "5bba03b425f5abc6e017f194cf074b06", "question": {"question_concept": "courtyard", "choices": [{"label": "A", "text": "candidate"}, {"label": "B", "text": "spain"}, {"label": "C", "text": "lawn"}, {"label": "D", "text": "asshole"}, {"label": "E", "text": "office complex"}], "stem": "Many homes in this country are built around a courtyard. Where is it?"}}
{"answerKey": "C", "id": "78276a4eab6e8d6b9ae3749211816977", "question": {"question_concept": "wreck", "choices": [{"label": "A", "text": "stand up"}, {"label": "B", "text": "produce"}, {"label": "C", "text": "construct"}, {"label": "D", "text": "make"}, {"label": "E", "text": "build"}], "stem": "Sean was a wreck.  He  loved to build houses, but in his current state, he couldn't do what?"}}
{"answerKey": "C", "id": "cf33e0f5891ce53a716432be06a46ee1", "question": {"question_concept": "pretending", "choices": [{"label": "A", "text": "fighting"}, {"label": "B", "text": "misunderstanding"}, {"label": "C", "text": "deception"}, {"label": "D", "text": "play"}, {"label": "E", "text": "distrust"}], "stem": "What would be happening if you are pretending to be a police officer?"}}
{"answerKey": "C", "id": "3938d6e50d38b1f8774b4f00a89bdb39", "question": {"question_concept": "writing instrument", "choices": [{"label": "A", "text": "nasa"}, {"label": "B", "text": "classroom"}, {"label": "C", "text": "stationery store"}, {"label": "D", "text": "purse"}, {"label": "E", "text": "office supply store"}], "stem": "Where would you buy a finely crafted writing instrument?"}}
{"answerKey": "C", "id": "cabefb7063a728e77abd44d97397a2a4", "question": {"question_concept": "finding information", "choices": [{"label": "A", "text": "fun"}, {"label": "B", "text": "ulcers"}, {"label": "C", "text": "get answers"}, {"label": "D", "text": "happiness"}, {"label": "E", "text": "power"}], "stem": "The detective was finding information from witnesses, why would he do that?"}}
{"answerKey": "D", "id": "60b909ad1d7956218a5d99954fdebecd", "question": {"question_concept": "spiders", "choices": [{"label": "A", "text": "cupboard"}, {"label": "B", "text": "toolbox"}, {"label": "C", "text": "closet"}, {"label": "D", "text": "garage"}, {"label": "E", "text": "mail box"}], "stem": "Joe found spiders in the place where he keeps his tools.  Where might that be?"}}
{"answerKey": "A", "id": "9fdebd1c2cf498f1d726a025b780a39a", "question": {"question_concept": "bald eagle", "choices": [{"label": "A", "text": "everglades"}, {"label": "B", "text": "high places"}, {"label": "C", "text": "natural habitat"}, {"label": "D", "text": "new york"}, {"label": "E", "text": "colorado"}], "stem": "While on the fan boat he thought he'd see swamps and gators, but he was surprised to spot a bald eagle in what nature area?"}}
{"answerKey": "B", "id": "f36027954e43cfd926451bdf7cb0c3ac", "question": {"question_concept": "supermarket", "choices": [{"label": "A", "text": "buy food for family"}, {"label": "B", "text": "city or town"}, {"label": "C", "text": "get supplies"}, {"label": "D", "text": "strip mall"}, {"label": "E", "text": "vermont"}], "stem": "Where are you likely to find a supermarket?"}}
{"answerKey": "D", "id": "7ec14907622c6d5a6087cd59a22d8c9d", "question": {"question_concept": "lantern", "choices": [{"label": "A", "text": "grocery store"}, {"label": "B", "text": "antique shop"}, {"label": "C", "text": "house"}, {"label": "D", "text": "dark place"}, {"label": "E", "text": "street"}], "stem": "Where would you need to use a lantern?"}}
{"answerKey": "C", "id": "efe488f67b53a4b6e69782c01c84f06c", "question": {"question_concept": "police officer", "choices": [{"label": "A", "text": "direct traffic"}, {"label": "B", "text": "city"}, {"label": "C", "text": "beat"}, {"label": "D", "text": "street"}, {"label": "E", "text": "park"}], "stem": "What area does a police officer patrol?"}}
{"answerKey": "A", "id": "7c62637437ad7515452886074010a438", "question": {"question_concept": "kill", "choices": [{"label": "A", "text": "being raped"}, {"label": "B", "text": "get rid of"}, {"label": "C", "text": "they didn't know the passcode"}, {"label": "D", "text": "get revenge"}, {"label": "E", "text": "were evil"}], "stem": "Why would a woman kill a stranger she met in a dark alley?"}}
{"answerKey": "B", "id": "4f7be1c68654e2924c161c8eca652928", "question": {"question_concept": "eat breakfast", "choices": [{"label": "A", "text": "buy food"}, {"label": "B", "text": "open mouth"}, {"label": "C", "text": "get out of bed"}, {"label": "D", "text": "cry"}, {"label": "E", "text": "wake up"}], "stem": "The baby was cranky, it needed to eat breakfast but refused to what?"}}
{"answerKey": "B", "id": "e4976ee741cf4b28b8a42780ffb15774", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "buildings"}, {"label": "B", "text": "audience"}, {"label": "C", "text": "apartment"}, {"label": "D", "text": "classroom"}, {"label": "E", "text": "falling down"}], "stem": "What is made up of people?"}}
{"answerKey": "E", "id": "14e75a42a416d32a24e2826cae34d2bf", "question": {"question_concept": "die", "choices": [{"label": "A", "text": "ocean"}, {"label": "B", "text": "write will"}, {"label": "C", "text": "never want"}, {"label": "D", "text": "were shot"}, {"label": "E", "text": "seek help"}], "stem": "He was afraid he would die from his cold, so he wisely decided to what?"}}
{"answerKey": "C", "id": "004607228ad49b69eac932c1005d6106", "question": {"question_concept": "pen", "choices": [{"label": "A", "text": "briefcase"}, {"label": "B", "text": "desk drawer"}, {"label": "C", "text": "friend's house"}, {"label": "D", "text": "pocket"}, {"label": "E", "text": "sidewalk"}], "stem": "Where would you get a pen if you do not have one?"}}
{"answerKey": "C", "id": "a7f54ee1866d5db34eacf40efa53c93e", "question": {"question_concept": "small dog", "choices": [{"label": "A", "text": "outside"}, {"label": "B", "text": "europe"}, {"label": "C", "text": "heat"}, {"label": "D", "text": "wet"}, {"label": "E", "text": "dog show"}], "stem": "Why would a small dog pant if it's hot outside?"}}
{"answerKey": "D", "id": "e56c56c3cfe50ba0c787c2bd67255be8", "question": {"question_concept": "why", "choices": [{"label": "A", "text": "case"}, {"label": "B", "text": "reason"}, {"label": "C", "text": "how"}, {"label": "D", "text": "because"}, {"label": "E", "text": "answer"}], "stem": "She asked her little boy why, he replied that he didn't know and it was just what?"}}
{"answerKey": "C", "id": "6f48ee564a48293eb501cc0d8197bdd9", "question": {"question_concept": "picture", "choices": [{"label": "A", "text": "microwave"}, {"label": "B", "text": "desktop"}, {"label": "C", "text": "shelf"}, {"label": "D", "text": "art show"}, {"label": "E", "text": "wall"}], "stem": "Where would you display a picture on a horizontal surface?"}}
{"answerKey": "E", "id": "13d2a103abbed930cabc9567a1ba12f2", "question": {"question_concept": "riding bike", "choices": [{"label": "A", "text": "wheels"}, {"label": "B", "text": "feet"}, {"label": "C", "text": "pedalling"}, {"label": "D", "text": "practice"}, {"label": "E", "text": "good balance"}], "stem": "What skill is needed for riding a bike?"}}
{"answerKey": "D", "id": "0c1efb38e023ee9725486fbec4f2d797", "question": {"question_concept": "oil", "choices": [{"label": "A", "text": "manual"}, {"label": "B", "text": "street"}, {"label": "C", "text": "restaurant"}, {"label": "D", "text": "ground"}, {"label": "E", "text": "service station"}], "stem": "He looked at the field of pumps, all slowing churning oil out of the what?"}}
{"answerKey": "C", "id": "b7ab4a5e0c19a98f41cd1ba3176f2dff", "question": {"question_concept": "deliver", "choices": [{"label": "A", "text": "delivered"}, {"label": "B", "text": "take away"}, {"label": "C", "text": "receiving"}, {"label": "D", "text": "pick up"}, {"label": "E", "text": "keep"}], "stem": "The department to where vendors deliver goods for sale is called what?"}}
{"answerKey": "D", "id": "8bcbb5098876940b2382db3a9a0b1beb", "question": {"question_concept": "ticket office", "choices": [{"label": "A", "text": "at the top"}, {"label": "B", "text": "movie theaters"}, {"label": "C", "text": "train station"}, {"label": "D", "text": "end of line"}, {"label": "E", "text": "opera house"}], "stem": "Where is the worst place to be in a ticket office?"}}
{"answerKey": "A", "id": "c7ce02d9365fe9275f88338ad51cbde6", "question": {"question_concept": "exercise", "choices": [{"label": "A", "text": "stretch"}, {"label": "B", "text": "lower cholesterol"}, {"label": "C", "text": "weigh"}, {"label": "D", "text": "track"}, {"label": "E", "text": "expend energy"}], "stem": "Exercise is very good for you, for faster recovery you should always do what afterwards?"}}
{"answerKey": "D", "id": "fb54a118d46b2776e435d411ae3dd9c8", "question": {"question_concept": "go somewhere", "choices": [{"label": "A", "text": "arriving"}, {"label": "B", "text": "arrive there"}, {"label": "C", "text": "turn around"}, {"label": "D", "text": "go back"}, {"label": "E", "text": "fart"}], "stem": "What happens when you go somewhere and forget something at home?"}}
{"answerKey": "C", "id": "2c13e6d61e3733db90a9fd22d72b3337", "question": {"question_concept": "wind instrument", "choices": [{"label": "A", "text": "band practice"}, {"label": "B", "text": "concert"}, {"label": "C", "text": "music store"}, {"label": "D", "text": "symphony"}, {"label": "E", "text": "music room"}], "stem": "Where would you acquire a wind instrument for you own use?"}}
{"answerKey": "E", "id": "350292ae429060a00ff2cf64d71558e4", "question": {"question_concept": "alcohol", "choices": [{"label": "A", "text": "supermarket"}, {"label": "B", "text": "bar"}, {"label": "C", "text": "pub"}, {"label": "D", "text": "restaurants"}, {"label": "E", "text": "chemistry lab"}], "stem": "Where would a person light alcohol on fire to observe the reaction?"}}
{"answerKey": "D", "id": "********************************", "question": {"question_concept": "storey", "choices": [{"label": "A", "text": "horizontal room"}, {"label": "B", "text": "storey book"}, {"label": "C", "text": "mall"}, {"label": "D", "text": "tall building"}, {"label": "E", "text": "book of stories"}], "stem": "If a storey contained a panoramic view, what kind of structure would it be in?"}}
{"answerKey": "B", "id": "81cc0d320488c7bacafb285cf7db5fbd", "question": {"question_concept": "lettuce", "choices": [{"label": "A", "text": "kitchen"}, {"label": "B", "text": "supermarket"}, {"label": "C", "text": "farmer's market"}, {"label": "D", "text": "salad"}, {"label": "E", "text": "refrigerator"}], "stem": "Where does lettuce arrive by large trucks?"}}
{"answerKey": "D", "id": "26c8a7165d0ed7250b9328f90d83ba83", "question": {"question_concept": "dying", "choices": [{"label": "A", "text": "rejuvenation"}, {"label": "B", "text": "born again"}, {"label": "C", "text": "no longer exist"}, {"label": "D", "text": "unable to work"}, {"label": "E", "text": "change of color"}], "stem": "Why do people who are dying receive social security payments?"}}
{"answerKey": "E", "id": "636fc69dee35cd357b4191b47e64d0e5", "question": {"question_concept": "jumping rope", "choices": [{"label": "A", "text": "fatigue"}, {"label": "B", "text": "sweating"}, {"label": "C", "text": "get tired"}, {"label": "D", "text": "tiredness"}, {"label": "E", "text": "hopping"}], "stem": "What should I do with a jumping rope?"}}
{"answerKey": "E", "id": "f0c4622a082eb9ad0690dd36dcf61297", "question": {"question_concept": "geese", "choices": [{"label": "A", "text": "guard house"}, {"label": "B", "text": "fly"}, {"label": "C", "text": "eat"}, {"label": "D", "text": "follow ultralight airplane"}, {"label": "E", "text": "group together"}], "stem": "What do geese do every fall in fields?"}}
{"answerKey": "E", "id": "4499ebd5e8188b0d5fdef6afd893017a", "question": {"question_concept": "seat", "choices": [{"label": "A", "text": "airplane"}, {"label": "B", "text": "movie"}, {"label": "C", "text": "auditorium"}, {"label": "D", "text": "theatre"}, {"label": "E", "text": "show"}], "stem": "I took my seat, the curtains drew back and I enjoyed the what?"}}
{"answerKey": "C", "id": "230cc491829307e8edb5423c8d09f945", "question": {"question_concept": "everyone", "choices": [{"label": "A", "text": "explicate"}, {"label": "B", "text": "pay tribute to king"}, {"label": "C", "text": "hope for peace"}, {"label": "D", "text": "wear shoes"}, {"label": "E", "text": "do well"}], "stem": "What should everyone do who doesn't want to fight anymore?"}}
{"answerKey": "E", "id": "6163a897cd7eac1deddd4c002a1930ae", "question": {"question_concept": "post office", "choices": [{"label": "A", "text": "building"}, {"label": "B", "text": "business district"}, {"label": "C", "text": "above ground"}, {"label": "D", "text": "most towns"}, {"label": "E", "text": "center of town"}], "stem": "Where is the ideal location for a post office?"}}
{"answerKey": "C", "id": "55478486079423907508a06be13ca536", "question": {"question_concept": "squirrel", "choices": [{"label": "A", "text": "roof"}, {"label": "B", "text": "inside home"}, {"label": "C", "text": "forest"}, {"label": "D", "text": "yard"}, {"label": "E", "text": "park"}], "stem": "Where outside of a city would a squirrel live?"}}
{"answerKey": "D", "id": "4fa0d61ec82eb1e238d8938d5f43f392", "question": {"question_concept": "snake", "choices": [{"label": "A", "text": "wet grass"}, {"label": "B", "text": "western texas"}, {"label": "C", "text": "high grass"}, {"label": "D", "text": "amazon river"}, {"label": "E", "text": "tree"}], "stem": "You should watch out for snakes if floating down what African body of water?"}}
{"answerKey": "A", "id": "b4f79ca5f3595248ee25292ab60ad105", "question": {"question_concept": "eat", "choices": [{"label": "A", "text": "cook dinner"}, {"label": "B", "text": "did chores"}, {"label": "C", "text": "make food"}, {"label": "D", "text": "stretch out"}, {"label": "E", "text": "get food"}], "stem": "At the end of the day as he began to eat he paused and thanked her, it wasn't often she would what?"}}
{"answerKey": "E", "id": "c39131d979c9205c11d0e109e18188e4", "question": {"question_concept": "trees", "choices": [{"label": "A", "text": "yard"}, {"label": "B", "text": "orchard"}, {"label": "C", "text": "museum"}, {"label": "D", "text": "countryside"}, {"label": "E", "text": "surface of earth"}], "stem": "To what do trees roots cling?"}}
{"answerKey": "A", "id": "bd773d64f4e22db2358c6e00cbdf2d83", "question": {"question_concept": "dust", "choices": [{"label": "A", "text": "closet"}, {"label": "B", "text": "door"}, {"label": "C", "text": "corner"}, {"label": "D", "text": "shelf"}, {"label": "E", "text": "library"}], "stem": "What probably has a lot of dust in the back?"}}
{"answerKey": "D", "id": "2b416120e2fbd84b44b5dcd4eb42ed5c", "question": {"question_concept": "making friends", "choices": [{"label": "A", "text": "smiling"}, {"label": "B", "text": "smile"}, {"label": "C", "text": "open mind"}, {"label": "D", "text": "common interests"}, {"label": "E", "text": "laughter"}], "stem": "At the new comic store he found himself making friends, it was nice to meet people with what?"}}
{"answerKey": "C", "id": "cef855ec07c66a731741026c2839b0d3", "question": {"question_concept": "neuroepithelium", "choices": [{"label": "A", "text": "tastebud"}, {"label": "B", "text": "retina"}, {"label": "C", "text": "inner ear"}, {"label": "D", "text": "nasal cavity"}, {"label": "E", "text": "autistic"}], "stem": "The student explained he had a clue what neuroepithelium was and got really nervous, he then lost his balance because a what issue?"}}
{"answerKey": "A", "id": "0bbb82c1dc4bfd3b0e0c409a0afd248b", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "confession"}, {"label": "B", "text": "state park"}, {"label": "C", "text": "sing"}, {"label": "D", "text": "carnival"}, {"label": "E", "text": "opera"}], "stem": "What could people do that involves talking?"}}
{"answerKey": "C", "id": "67beae081a9b5ef56988f205f80cf129", "question": {"question_concept": "answering questions", "choices": [{"label": "A", "text": "discussion"}, {"label": "B", "text": "explaning"}, {"label": "C", "text": "teaching"}, {"label": "D", "text": "confusion"}, {"label": "E", "text": "correct"}], "stem": "If you're a child answering questions and an adult is asking them that adult is doing what?"}}
{"answerKey": "D", "id": "3b4dcfcab4726496bdbe9535cc669082", "question": {"question_concept": "eating dinner", "choices": [{"label": "A", "text": "digestive"}, {"label": "B", "text": "feel better"}, {"label": "C", "text": "sleepiness"}, {"label": "D", "text": "indigestion"}, {"label": "E", "text": "illness"}], "stem": "He has lactose intolerant, but was eating dinner made of cheese, what followed for him?"}}
{"answerKey": "A", "id": "eebddf5f35d85e9fe2ecbd9b56c1db60", "question": {"question_concept": "upright piano", "choices": [{"label": "A", "text": "music room"}, {"label": "B", "text": "bathroom"}, {"label": "C", "text": "house"}, {"label": "D", "text": "living room"}, {"label": "E", "text": "music store"}], "stem": "The teacher played on the upright piano, she was explaining the song to all the students in the what?"}}
{"answerKey": "D", "id": "5393ba1ce298bd1ac4744c07d7373a9c", "question": {"question_concept": "fail", "choices": [{"label": "A", "text": "passed"}, {"label": "B", "text": "completing"}, {"label": "C", "text": "passed"}, {"label": "D", "text": "passing"}, {"label": "E", "text": "succeeding"}], "stem": "When you get an F, you fail. If you get A's you are?"}}
{"answerKey": "A", "id": "fde48d43e27cefed6ed9c52514e0bb6d", "question": {"question_concept": "having bath", "choices": [{"label": "A", "text": "cleanness"}, {"label": "B", "text": "wetness"}, {"label": "C", "text": "exfoliation"}, {"label": "D", "text": "use water"}, {"label": "E", "text": "hygiene"}], "stem": "What is the main purpose of having a bath?"}}
{"answerKey": "A", "id": "da83d85e28778c082d9a63f5b890b26d", "question": {"question_concept": "boundary", "choices": [{"label": "A", "text": "sporting event"}, {"label": "B", "text": "sporting"}, {"label": "C", "text": "basketball"}, {"label": "D", "text": "society"}, {"label": "E", "text": "ranch country"}], "stem": "The ball was hit over a boundary and struck an audience member.  What kind of game were they playing?"}}
{"answerKey": "D", "id": "cfa980561efe82e7ae7080d4f081b463", "question": {"question_concept": "becoming inebriated", "choices": [{"label": "A", "text": "punish"}, {"label": "B", "text": "arrest"}, {"label": "C", "text": "automobile accidents"}, {"label": "D", "text": "drunk driving"}, {"label": "E", "text": "talking nonsense"}], "stem": "What is someone operating a vehicle likely to be accused of after becoming inebriated?"}}
{"answerKey": "D", "id": "384b89e789e0f4b4796120394fb6303b", "question": {"question_concept": "jewelry", "choices": [{"label": "A", "text": "vault"}, {"label": "B", "text": "suitcase"}, {"label": "C", "text": "neighbour's house"}, {"label": "D", "text": "department store"}, {"label": "E", "text": "safe deposit box"}], "stem": "Where would you get jewelry if you do not have any?"}}
{"answerKey": "E", "id": "0d66d33a17e41eaa3278ca7b3930c5ea", "question": {"question_concept": "waiting for", "choices": [{"label": "A", "text": "job"}, {"label": "B", "text": "boredom"}, {"label": "C", "text": "anxiety"}, {"label": "D", "text": "impatience"}, {"label": "E", "text": "wisdom"}], "stem": "What is a philosopher waiting for to eventually gain through his studies?"}}
{"answerKey": "B", "id": "732183ead4206e51ed4df18b9c9f14fe", "question": {"question_concept": "winter", "choices": [{"label": "A", "text": "ski"}, {"label": "B", "text": "play hockey"}, {"label": "C", "text": "summer"}, {"label": "D", "text": "knit"}, {"label": "E", "text": "warm"}], "stem": "What do young boys do on the ice in the winter?"}}
{"answerKey": "B", "id": "2632ff6c9b781d3aa74e8dd36b990871", "question": {"question_concept": "spending money", "choices": [{"label": "A", "text": "poverty"}, {"label": "B", "text": "clutter"}, {"label": "C", "text": "getting"}, {"label": "D", "text": "satisfaction"}, {"label": "E", "text": "more happiness"}], "stem": "She loved spending money at the thrift store on knickknacks, this resulted in a lot of what on every shelf in her house?"}}
{"answerKey": "B", "id": "63db79b940f36f0333377f85c19eacb2", "question": {"question_concept": "listen", "choices": [{"label": "A", "text": "gain confidence"}, {"label": "B", "text": "concentrate"}, {"label": "C", "text": "get attention"}, {"label": "D", "text": "pay attention"}, {"label": "E", "text": "stop talking"}], "stem": "I listened to lecture intensely, what is my goal?"}}
{"answerKey": "C", "id": "1520a8fd3116e7b856947c5e308d7ce5", "question": {"question_concept": "using computer", "choices": [{"label": "A", "text": "program created"}, {"label": "B", "text": "stress"}, {"label": "C", "text": "happiness"}, {"label": "D", "text": "ocean"}, {"label": "E", "text": "headache"}], "stem": "If a person is using a computer to talk to their granddaughter, what might the computer cause for them?"}}
{"answerKey": "C", "id": "bd780fea2d4dd262583446e64c0f314d", "question": {"question_concept": "entrance hall", "choices": [{"label": "A", "text": "person"}, {"label": "B", "text": "box"}, {"label": "C", "text": "convention center"}, {"label": "D", "text": "public building"}, {"label": "E", "text": "large building"}], "stem": "Joe was there to meet a large number of people.  As he filed though the entrance hall, he saw many strangers who came from far away.  What sort of building is he probably in?"}}
{"answerKey": "E", "id": "99e0b2ddf88ebed98b977043b7c2331b", "question": {"question_concept": "lake", "choices": [{"label": "A", "text": "mountains"}, {"label": "B", "text": "dead body"}, {"label": "C", "text": "pay debts"}, {"label": "D", "text": "state park"}, {"label": "E", "text": "new york"}], "stem": "John wanted scatter his wife's remains in a lake in the wilderness.  He had to delay before of where he lived.  Where did he live?"}}
{"answerKey": "C", "id": "eb0e0c4eaf19c1e9b4df3b4d3a11be3d", "question": {"question_concept": "trash can", "choices": [{"label": "A", "text": "hospital"}, {"label": "B", "text": "park"}, {"label": "C", "text": "corner"}, {"label": "D", "text": "motel"}, {"label": "E", "text": "office"}], "stem": "Many towns and cities have trash cans where on sidewalks?"}}
{"answerKey": "B", "id": "467a3b464b08b3ffc9922e2a726554f6", "question": {"question_concept": "adopt", "choices": [{"label": "A", "text": "orphan"}, {"label": "B", "text": "biological child"}, {"label": "C", "text": "give away"}, {"label": "D", "text": "foster child"}, {"label": "E", "text": "abandon"}], "stem": "The family wanted to adopt for enviro-ethical reasons, what did they abhor?"}}
{"answerKey": "D", "id": "dea70fe40fac9ad03bf319bf8a480efa", "question": {"question_concept": "airplanes", "choices": [{"label": "A", "text": "stall"}, {"label": "B", "text": "start melting"}, {"label": "C", "text": "taxi"}, {"label": "D", "text": "crash"}, {"label": "E", "text": "speed up"}], "stem": "What happens when airplane engines cut off and are unable to be restarted in flight?"}}
{"answerKey": "E", "id": "2f1680da0d388a8453150ff3637e4689", "question": {"question_concept": "cavity", "choices": [{"label": "A", "text": "solid object"}, {"label": "B", "text": "molar"}, {"label": "C", "text": "dentist"}, {"label": "D", "text": "unbrushed tooth"}, {"label": "E", "text": "teeth"}], "stem": "Where would you be concerned about finding a cavity?"}}
{"answerKey": "C", "id": "8369adc4b4710d00f917d80a75d844d7", "question": {"question_concept": "human beings", "choices": [{"label": "A", "text": "question authority"}, {"label": "B", "text": "melt"}, {"label": "C", "text": "read newspapers"}, {"label": "D", "text": "act"}, {"label": "E", "text": "dictionary"}], "stem": "Human beings learn about current events from what print item?"}}
{"answerKey": "D", "id": "20a3bb788cf408d9a3e25e610fe60905", "question": {"question_concept": "anemone", "choices": [{"label": "A", "text": "nursery"}, {"label": "B", "text": "south pacific"}, {"label": "C", "text": "desert"}, {"label": "D", "text": "sea water"}, {"label": "E", "text": "atlantic ocean"}], "stem": "In what kind of environment does an anemone live?"}}
{"answerKey": "B", "id": "36c1f50eec01c287b8ef6ffe69fe0528", "question": {"question_concept": "lodgings", "choices": [{"label": "A", "text": "a yurt"}, {"label": "B", "text": "resort area"}, {"label": "C", "text": "big city"}, {"label": "D", "text": "michigan"}, {"label": "E", "text": "going on vacation"}], "stem": "He wanted lodging in the actual what, so that he was already where he needed to be?"}}
{"answerKey": "A", "id": "5f4825137a27f369fe859e85dfe1793f", "question": {"question_concept": "boredom", "choices": [{"label": "A", "text": "see art"}, {"label": "B", "text": "see ghost"}, {"label": "C", "text": "watch film"}, {"label": "D", "text": "grocery shop"}, {"label": "E", "text": "do crossword puzzle"}], "stem": "If I am suffering from boredom, and I want to see something beautiful, what should I do?"}}
{"answerKey": "A", "id": "b3dc6d6a5e2f9d7da8eb72816c80b3f8_1", "question": {"question_concept": "projectile ball", "choices": [{"label": "A", "text": "motion"}, {"label": "B", "text": "ocean"}, {"label": "C", "text": "flintlock"}, {"label": "D", "text": "arcade"}, {"label": "E", "text": "tennis court"}], "stem": "The goal was to hit the target, but a projectile ball can't hit anything if it isn't in what?"}}
{"answerKey": "D", "id": "63bb6128026ce24209583d0eea75fc27", "question": {"question_concept": "cup of coffee", "choices": [{"label": "A", "text": "coffee shop"}, {"label": "B", "text": "kitchen"}, {"label": "C", "text": "hand"}, {"label": "D", "text": "table"}, {"label": "E", "text": "office"}], "stem": "Where is a good place to set a cup of coffee while relaxing?"}}
{"answerKey": "C", "id": "e8a9142d2402f818273dd62cf5a7b559_1", "question": {"question_concept": "egg", "choices": [{"label": "A", "text": "henhouse"}, {"label": "B", "text": "garden"}, {"label": "C", "text": "plate"}, {"label": "D", "text": "supermarket"}, {"label": "E", "text": "bird's nest"}], "stem": "If a fried egg was runny and there was no toast to sop it up, after the meal there'd be a messy what?"}}
{"answerKey": "A", "id": "ead9c9744aee08678759158efe005175", "question": {"question_concept": "proper", "choices": [{"label": "A", "text": "inappropriate"}, {"label": "B", "text": "incomplete"}, {"label": "C", "text": "impolite"}, {"label": "D", "text": "none"}, {"label": "E", "text": "incorrect"}], "stem": "If I want to behave with proper aplomb, what manners should I avoid?"}}
{"answerKey": "D", "id": "ab8bf60f76bc6119459271140ccae781", "question": {"question_concept": "squash court", "choices": [{"label": "A", "text": "swimming pool"}, {"label": "B", "text": "rich person's house"}, {"label": "C", "text": "country club"}, {"label": "D", "text": "fitness center"}, {"label": "E", "text": "park"}], "stem": "Before lifting weights he liked to warm up on the squash court, he really enjoyed the facilities of the what?"}}
{"answerKey": "C", "id": "3c6e2d95a63316b31986e8c7979582c9", "question": {"question_concept": "animals", "choices": [{"label": "A", "text": "bite"}, {"label": "B", "text": "digestion"}, {"label": "C", "text": "feel pleasure"}, {"label": "D", "text": "pass water"}, {"label": "E", "text": "listen to each other"}], "stem": "What will happen to animals after eating food?"}}
{"answerKey": "D", "id": "5c171b9837af49211891ce40e4a10204", "question": {"question_concept": "dirt", "choices": [{"label": "A", "text": "corner"}, {"label": "B", "text": "street"}, {"label": "C", "text": "closet"}, {"label": "D", "text": "garden"}, {"label": "E", "text": "bathtub"}], "stem": "If I wanted to grow plants, where could I put a lot of dirt?"}}
{"answerKey": "C", "id": "56d0fc282a144565f2c852415c6fa92c", "question": {"question_concept": "judging", "choices": [{"label": "A", "text": "controversy"}, {"label": "B", "text": "responsibility"}, {"label": "C", "text": "resentment"}, {"label": "D", "text": "judge feelings"}, {"label": "E", "text": "hurt feelings"}], "stem": "What does a person often feel about someone judging them guilty?"}}
{"answerKey": "A", "id": "5b8a3081c3235d62bc77e2d15f3ad454", "question": {"question_concept": "town", "choices": [{"label": "A", "text": "valley"}, {"label": "B", "text": "hospital"}, {"label": "C", "text": "state"}, {"label": "D", "text": "train station"}, {"label": "E", "text": "michigan"}], "stem": "A town between two mountains is located in a what?"}}
{"answerKey": "A", "id": "e43c4eaa04243ddee30f29171718eb92", "question": {"question_concept": "toilet", "choices": [{"label": "A", "text": "motel room"}, {"label": "B", "text": "apartment"}, {"label": "C", "text": "bathroom"}, {"label": "D", "text": "games"}, {"label": "E", "text": "house"}], "stem": "James need to use a toilet but there were no public ones in sight.  Eventually he broke down and did something very expensive so that he could get a toilet.  Where might he have gone?"}}
{"answerKey": "A", "id": "84a736d4b702a6869d8fa8523aee6f1b", "question": {"question_concept": "electricity", "choices": [{"label": "A", "text": "concert"}, {"label": "B", "text": "bedroom"}, {"label": "C", "text": "make person sick"}, {"label": "D", "text": "building"}, {"label": "E", "text": "church"}], "stem": "Why did the heavy metal band need electricity at the stadium?"}}
{"answerKey": "C", "id": "72611791cdcb040f2d699827fb9cebc4", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "avoid pain"}, {"label": "B", "text": "compliments"}, {"label": "C", "text": "intellectual challenge"}, {"label": "D", "text": "passing grade"}, {"label": "E", "text": "attention"}], "stem": "What is a person looking for when completing puzzles or riddles?"}}
{"answerKey": "D", "id": "4477fb61fde4bb8695c241dfc366b554", "question": {"question_concept": "bread", "choices": [{"label": "A", "text": "plastic bag"}, {"label": "B", "text": "pantry"}, {"label": "C", "text": "supermarket"}, {"label": "D", "text": "toaster"}, {"label": "E", "text": "prison"}], "stem": "If someone was making breakfast, they'd probably put two slices of bread in the what?"}}
{"answerKey": "E", "id": "ce246bc94a54431b9c0530e71d2456b5", "question": {"question_concept": "doing housework", "choices": [{"label": "A", "text": "boredom"}, {"label": "B", "text": "nice home"}, {"label": "C", "text": "michigan"}, {"label": "D", "text": "feeling satisfied"}, {"label": "E", "text": "house clean"}], "stem": "His house was a mess, he began doing housework to get what?"}}
{"answerKey": "C", "id": "2eef2d255fe629414f4d24ade8590102", "question": {"question_concept": "blanket", "choices": [{"label": "A", "text": "bath store"}, {"label": "B", "text": "bedroom"}, {"label": "C", "text": "hospital"}, {"label": "D", "text": "flower garden"}, {"label": "E", "text": "michigan"}], "stem": "Where would a corpse be covered by a blanket?"}}
{"answerKey": "A", "id": "2f85d53721ccc8b3fa4cfc184186d124", "question": {"question_concept": "break", "choices": [{"label": "A", "text": "accelerate"}, {"label": "B", "text": "putting together"}, {"label": "C", "text": "working"}, {"label": "D", "text": "construct"}, {"label": "E", "text": "train"}], "stem": "The man  tried to break the glass in order to make his escape in time, but he could not.  The person in the cat, trying to kill him, did what?"}}
{"answerKey": "B", "id": "2192c5c2145a6e03755ad89a02e64055", "question": {"question_concept": "bench", "choices": [{"label": "A", "text": "bordello"}, {"label": "B", "text": "rest area"}, {"label": "C", "text": "garden"}, {"label": "D", "text": "bus stop"}, {"label": "E", "text": "state park"}], "stem": "The trucker plopped on the bench with a sense of relief, where did he arrive?"}}
{"answerKey": "A", "id": "bea07406aaadeef50110883b6932d86a", "question": {"question_concept": "republic", "choices": [{"label": "A", "text": "state"}, {"label": "B", "text": "democratic"}, {"label": "C", "text": "kingdom"}, {"label": "D", "text": "democracy"}, {"label": "E", "text": "dictatorship"}], "stem": "What is part of a republic like the USA?"}}
{"answerKey": "E", "id": "7a58e7e7bf76658751e850f790922aba", "question": {"question_concept": "clothing", "choices": [{"label": "A", "text": "person"}, {"label": "B", "text": "hamper"}, {"label": "C", "text": "closet"}, {"label": "D", "text": "upstairs"}, {"label": "E", "text": "backpack"}], "stem": "Where do you keep extra clothing on a hike?"}}
{"answerKey": "B", "id": "76b2c6d254f9127b4fd66d90e1a330e7", "question": {"question_concept": "apple tree", "choices": [{"label": "A", "text": "new hampshire"}, {"label": "B", "text": "bloom"}, {"label": "C", "text": "washington state"}, {"label": "D", "text": "sunshine"}, {"label": "E", "text": "spontaneously combust"}], "stem": "What could an apple tree do?"}}
{"answerKey": "C", "id": "cdd3d074031fbd3efeb4f9408abef04e", "question": {"question_concept": "crab", "choices": [{"label": "A", "text": "fish market"}, {"label": "B", "text": "shallow waters"}, {"label": "C", "text": "atlantic ocean"}, {"label": "D", "text": "fresh water"}, {"label": "E", "text": "shore line"}], "stem": "What very cold area in the east can a crab be found?"}}
{"answerKey": "C", "id": "359aed918343d228e67cef329b693904", "question": {"question_concept": "chef", "choices": [{"label": "A", "text": "thin potatos"}, {"label": "B", "text": "prepare food"}, {"label": "C", "text": "study french cooking"}, {"label": "D", "text": "drink"}, {"label": "E", "text": "cook dinner"}], "stem": "The chef wanted to perfect his craft, what did he do?"}}
{"answerKey": "A", "id": "cf02cca40a47c2deefd8b2e5a5ff2f70", "question": {"question_concept": "puppy", "choices": [{"label": "A", "text": "one choice for pet"}, {"label": "B", "text": "cute"}, {"label": "C", "text": "kennel"}, {"label": "D", "text": "soft"}, {"label": "E", "text": "waxy"}], "stem": "She wanted a kitten and puppy so why did she only get the puppy?"}}
{"answerKey": "A", "id": "ac1abecdbbd7bcde6592ca645c2ecb1e", "question": {"question_concept": "shade", "choices": [{"label": "A", "text": "full sunlight"}, {"label": "B", "text": "bright sunshine"}, {"label": "C", "text": "sunny place"}, {"label": "D", "text": "eat cake"}, {"label": "E", "text": "direct sunlight"}], "stem": "There was no shade for Jenny.  She was forced to lie there exposed to what?"}}
{"answerKey": "D", "id": "2adbb4fc0d5249dc411dda433f378591", "question": {"question_concept": "cleaning house", "choices": [{"label": "A", "text": "neatness"}, {"label": "B", "text": "tiredness"}, {"label": "C", "text": "order"}, {"label": "D", "text": "exhaustion"}, {"label": "E", "text": "sneezing"}], "stem": "What could happen to you after you are cleaning house for a long time?"}}
{"answerKey": "E", "id": "5a1c8a9dbbb60e523cc1ba14a370729c", "question": {"question_concept": "going to party", "choices": [{"label": "A", "text": "rumpspringa"}, {"label": "B", "text": "meeting new people"}, {"label": "C", "text": "having fun"}, {"label": "D", "text": "meet new people"}, {"label": "E", "text": "plan"}], "stem": "What is someone doing when scheduling when to go to party?"}}
{"answerKey": "B", "id": "3665b329f93f7c84edeabe394140f8d2", "question": {"question_concept": "comets", "choices": [{"label": "A", "text": "ice"}, {"label": "B", "text": "set orbits"}, {"label": "C", "text": "universe"}, {"label": "D", "text": "space"}, {"label": "E", "text": "solid nucleus"}], "stem": "What kind of path do comets tend to have?"}}
{"answerKey": "E", "id": "dbcedaa6a6f1f68bc8f2bf7aef23294e", "question": {"question_concept": "sex", "choices": [{"label": "A", "text": "bedroom"}, {"label": "B", "text": "pleasant"}, {"label": "C", "text": "obesity"}, {"label": "D", "text": "painful"}, {"label": "E", "text": "dirty"}], "stem": "What do people feel after having sex that requires them to shower?"}}
{"answerKey": "C", "id": "ba3a2b9ff289c106051163f840a6f5ba", "question": {"question_concept": "animals", "choices": [{"label": "A", "text": "euthanasia"}, {"label": "B", "text": "pass water"}, {"label": "C", "text": "die of cancer"}, {"label": "D", "text": "feel pain"}, {"label": "E", "text": "feel pleasure"}], "stem": "The vet found malignant tumors on the animals, what is their likely fate?"}}
{"answerKey": "E", "id": "13fc28f53423a9b3a656c9431df1b3b5", "question": {"question_concept": "kissing", "choices": [{"label": "A", "text": "sexual stimulation"}, {"label": "B", "text": "herpes"}, {"label": "C", "text": "headache"}, {"label": "D", "text": "catch cold"}, {"label": "E", "text": "happiness"}], "stem": "What is the thing that is agitated in your head when kissing?"}}
{"answerKey": "A", "id": "3f4b48708d08f8bf7bec796531023f9c", "question": {"question_concept": "newspaper", "choices": [{"label": "A", "text": "trash"}, {"label": "B", "text": "floor"}, {"label": "C", "text": "subway"}, {"label": "D", "text": "ground"}, {"label": "E", "text": "lawn"}], "stem": "Billy was reading the newspaper as he commuted to work, but once he got to his destination he balled it up and put it somewhere. Where did it put it?"}}
{"answerKey": "C", "id": "c61790eb63ff6652b878ca051493c07d", "question": {"question_concept": "pail", "choices": [{"label": "A", "text": "garage"}, {"label": "B", "text": "pool"}, {"label": "C", "text": "utility room"}, {"label": "D", "text": "hardware store"}, {"label": "E", "text": "wishing well"}], "stem": "Where do you keep a pail in your house?"}}
{"answerKey": "C", "id": "e5ebbe0ea4097bb197ac525b49108362", "question": {"question_concept": "ink", "choices": [{"label": "A", "text": "fountain pen"}, {"label": "B", "text": "squid"}, {"label": "C", "text": "newspaper"}, {"label": "D", "text": "book"}, {"label": "E", "text": "printer"}], "stem": "what is printed with ink and distributed daily?"}}
{"answerKey": "B", "id": "029e36d8f65982b142c319064dc5e32f", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "kill each other"}, {"label": "B", "text": "thank god"}, {"label": "C", "text": "experience pain"}, {"label": "D", "text": "hatred"}, {"label": "E", "text": "talk to each other"}], "stem": "What are people likely to do when an unexpected decent outcome occurs?"}}
{"answerKey": "A", "id": "3d1a67f87b34303f97549ba83e5521c2", "question": {"question_concept": "terrace", "choices": [{"label": "A", "text": "japan"}, {"label": "B", "text": "rice paddy"}, {"label": "C", "text": "garden"}, {"label": "D", "text": "michigan"}, {"label": "E", "text": "italy"}], "stem": "The terrace had Kanji written on it, indicating that it was made where?"}}
{"answerKey": "A", "id": "e050bce7048da1b3743a54153e91694e", "question": {"question_concept": "cardboard", "choices": [{"label": "A", "text": "packaging materials"}, {"label": "B", "text": "recycle bin"}, {"label": "C", "text": "box factory"}, {"label": "D", "text": "warehouse"}, {"label": "E", "text": "bowler hats"}], "stem": "The company sent off many purchases, they used recycled cardboard as their what?"}}
{"answerKey": "B", "id": "8233ccb60dd0c0ff3b7ca5d73e5681f2", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "have no home"}, {"label": "B", "text": "false information"}, {"label": "C", "text": "hungry"}, {"label": "D", "text": "made fun of"}, {"label": "E", "text": "brain tumor"}], "stem": "Why might a person be known as a liar?"}}
{"answerKey": "E", "id": "eb4b2cd0f2a69686e5a82250c5806b84", "question": {"question_concept": "waiting for", "choices": [{"label": "A", "text": "timing"}, {"label": "B", "text": "expenditure of time"}, {"label": "C", "text": "getting bored"}, {"label": "D", "text": "anger"}, {"label": "E", "text": "patience"}], "stem": "The child was politely waiting for dessert, he was eventually rewarded for his what?"}}
{"answerKey": "D", "id": "d0bda97a087904320216e4d0b8a08a8d", "question": {"question_concept": "giving assistance", "choices": [{"label": "A", "text": "feeling good"}, {"label": "B", "text": "killing"}, {"label": "C", "text": "law suits"}, {"label": "D", "text": "out of pocket"}, {"label": "E", "text": "feel loved"}], "stem": "The man was giving assistance to a pan handler in the streets, how did he give assistance?"}}
{"answerKey": "E", "id": "e216381e9f0ddd1d248ee25fccca2b1f", "question": {"question_concept": "child", "choices": [{"label": "A", "text": "principal"}, {"label": "B", "text": "birth"}, {"label": "C", "text": "loving couple"}, {"label": "D", "text": "act of sex"}, {"label": "E", "text": "parents"}], "stem": "What do you call the caretakers of a child?"}}
{"answerKey": "A", "id": "b1fba9ad6193c6751ddb3f58f7f39b35", "question": {"question_concept": "niece", "choices": [{"label": "A", "text": "family reunion"}, {"label": "B", "text": "brother's house"}, {"label": "C", "text": "family picture book"}, {"label": "D", "text": "family tree"}, {"label": "E", "text": "party"}], "stem": "Where would you run in to a niece you only see every one and a while?"}}
{"answerKey": "B", "id": "3ceae7a18073050bd2c0448abef1f393", "question": {"question_concept": "working", "choices": [{"label": "A", "text": "holding"}, {"label": "B", "text": "concentration"}, {"label": "C", "text": "energy"}, {"label": "D", "text": "job"}, {"label": "E", "text": "energh"}], "stem": "Working on the elaborate task was taxing, it require extreme what?"}}
{"answerKey": "D", "id": "f1182e3a070f5a1be529843aa6e5c20c", "question": {"question_concept": "awaking", "choices": [{"label": "A", "text": "get up"}, {"label": "B", "text": "discomfort"}, {"label": "C", "text": "discomfort"}, {"label": "D", "text": "headache"}, {"label": "E", "text": "shock"}], "stem": "What may you have after awaking after a night of heavy drinking?"}}
{"answerKey": "E", "id": "5799089c131e26473697afc54d5f6964", "question": {"question_concept": "ribbon", "choices": [{"label": "A", "text": "wrapping paper"}, {"label": "B", "text": "girl's hair"}, {"label": "C", "text": "bath"}, {"label": "D", "text": "floral arrangement"}, {"label": "E", "text": "typewriter"}], "stem": "What uses a ribbon to put words on paper?"}}
{"answerKey": "D", "id": "7ce1f99e8185489a7113e6d18c71abb0", "question": {"question_concept": "sheep", "choices": [{"label": "A", "text": "school"}, {"label": "B", "text": "meadow"}, {"label": "C", "text": "lamb"}, {"label": "D", "text": "farm"}, {"label": "E", "text": "fairgrounds"}], "stem": "Where are sheep likely to live?"}}
{"answerKey": "B", "id": "69425fb4cd2dc034e9ff223d2d5676ec", "question": {"question_concept": "fan", "choices": [{"label": "A", "text": "hockey game"}, {"label": "B", "text": "living room"}, {"label": "C", "text": "bathroom"}, {"label": "D", "text": "football stadium"}, {"label": "E", "text": "hot room"}], "stem": "If I was watching TV on the couch and the air was stuffy, I might turn the fan on to make the what more comfortable?"}}
{"answerKey": "E", "id": "f75b22d5b88ac56ae7df030c1ebeded5", "question": {"question_concept": "writing instrument", "choices": [{"label": "A", "text": "desk drawer"}, {"label": "B", "text": "cabinet"}, {"label": "C", "text": "purse"}, {"label": "D", "text": "classroom"}, {"label": "E", "text": "pocket"}], "stem": "While walking the student needed to store his writing insturment away, where did he put it?"}}
{"answerKey": "B", "id": "4eb3e69c0d42a2287692d2b9d2cb5979", "question": {"question_concept": "auditorium", "choices": [{"label": "A", "text": "building"}, {"label": "B", "text": "crowd"}, {"label": "C", "text": "city"}, {"label": "D", "text": "group"}, {"label": "E", "text": "high school"}], "stem": "Who watches a play in an auditorium?"}}
{"answerKey": "D", "id": "7d937233b4a9043da0b976dbd42d141b", "question": {"question_concept": "committing murder", "choices": [{"label": "A", "text": "problems"}, {"label": "B", "text": "incarceration"}, {"label": "C", "text": "trial"}, {"label": "D", "text": "imprisonment"}, {"label": "E", "text": "prosecution"}], "stem": "What is a possible outcome for committing murder?"}}
{"answerKey": "A", "id": "6bd176cc91a2a2088807ec446c008856", "question": {"question_concept": "soap", "choices": [{"label": "A", "text": "supermarket"}, {"label": "B", "text": "washing"}, {"label": "C", "text": "cabinet"}, {"label": "D", "text": "own home"}, {"label": "E", "text": "sink"}], "stem": "where is a good place to obtain new soap?"}}
{"answerKey": "D", "id": "c3890d43b84635d9e61c007ca2521d5b", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "talk to each other"}, {"label": "B", "text": "complete job"}, {"label": "C", "text": "wear hats"}, {"label": "D", "text": "kill animals"}, {"label": "E", "text": "believe in god"}], "stem": "What do people do for food?"}}
{"answerKey": "D", "id": "6195ed74cf445cb5d991e1076a080dde", "question": {"question_concept": "bottle", "choices": [{"label": "A", "text": "grocery store"}, {"label": "B", "text": "diaper bag"}, {"label": "C", "text": "gas station"}, {"label": "D", "text": "liquor store"}, {"label": "E", "text": "medicine cabinet"}], "stem": "There was many a bottle to choose from behind the cashier where?"}}
{"answerKey": "B", "id": "37644422df4bcd28b3f54bbf3fc2c0f8", "question": {"question_concept": "national highway", "choices": [{"label": "A", "text": "canada"}, {"label": "B", "text": "atlas"}, {"label": "C", "text": "united states"}, {"label": "D", "text": "major cities"}, {"label": "E", "text": "book"}], "stem": "They had to know where to go, they got on the national highway after consulting the what?"}}
{"answerKey": "E", "id": "23d97480fe45bace231503f8fc367a5b", "question": {"question_concept": "professors", "choices": [{"label": "A", "text": "master physics"}, {"label": "B", "text": "state facts"}, {"label": "C", "text": "wear wrinkled tweed jackets"}, {"label": "D", "text": "school students"}, {"label": "E", "text": "teach courses"}], "stem": "What do professors primarily do?"}}
{"answerKey": "A", "id": "15556e26feaa5a8a29c9f30896e535d4", "question": {"question_concept": "ball", "choices": [{"label": "A", "text": "bowling alley"}, {"label": "B", "text": "football stadium"}, {"label": "C", "text": "soccer field"}, {"label": "D", "text": "sporting event"}, {"label": "E", "text": "sporting goods store"}], "stem": "Where do you throw a ball at pins?"}}
{"answerKey": "E", "id": "6be05d227f4f6fe727218fc8be9df340", "question": {"question_concept": "cleaning", "choices": [{"label": "A", "text": "sing a song"}, {"label": "B", "text": "neatness"}, {"label": "C", "text": "allergies"}, {"label": "D", "text": "healthy living"}, {"label": "E", "text": "using water"}], "stem": "What might you need to do cleaning?"}}
{"answerKey": "B", "id": "3f3ba1d9a3bfe63df11247a968eaddce", "question": {"question_concept": "spitting", "choices": [{"label": "A", "text": "phlegm"}, {"label": "B", "text": "saliva nd mouth"}, {"label": "C", "text": "disease"}, {"label": "D", "text": "germs"}, {"label": "E", "text": "spittle"}], "stem": "If i were to spit a lot without noticing i may have extra what?"}}
{"answerKey": "C", "id": "ca9a3ccfb140aa66816f96ac983b6d9f_1", "question": {"question_concept": "pencils", "choices": [{"label": "A", "text": "classroom"}, {"label": "B", "text": "parking garage"}, {"label": "C", "text": "store"}, {"label": "D", "text": "backpack"}, {"label": "E", "text": "cabinet"}], "stem": "If student got a list of supplies from class like paper and pencils, their parent would have to go where?"}}
{"answerKey": "C", "id": "487cabfcd776d89748ee7e7bb681ad59", "question": {"question_concept": "swallow semen", "choices": [{"label": "A", "text": "you're into"}, {"label": "B", "text": "prostitute"}, {"label": "C", "text": "you're curious"}, {"label": "D", "text": "curiosity"}, {"label": "E", "text": "heterosexual woman in love"}], "stem": "Why do young people swallow semen ?"}}
{"answerKey": "A", "id": "6915dfdefe3b1cd5fd8886c8bb84929a", "question": {"question_concept": "standing in queue", "choices": [{"label": "A", "text": "frustration"}, {"label": "B", "text": "delays"}, {"label": "C", "text": "being annoyed"}, {"label": "D", "text": "moving forward"}, {"label": "E", "text": "progress"}], "stem": "Sally was standing in queue.  The line was very, very slow.  What was she feeling?"}}
{"answerKey": "B", "id": "ec224c1dbfb569cce7ec317fe987ae68", "question": {"question_concept": "animal", "choices": [{"label": "A", "text": "sand trap"}, {"label": "B", "text": "live long"}, {"label": "C", "text": "leave home"}, {"label": "D", "text": "feel pain"}, {"label": "E", "text": "eating"}], "stem": "What is the animal trying to accomplish?"}}
{"answerKey": "B", "id": "0cba8ddda21e29c8c53482e131d741cd", "question": {"question_concept": "dancing", "choices": [{"label": "A", "text": "euphoria"}, {"label": "B", "text": "moving body"}, {"label": "C", "text": "rhythmic movement"}, {"label": "D", "text": "happiness"}, {"label": "E", "text": "fatigue"}], "stem": "James and Holly went dancing together. As they danced, he  pressed himself against her what?"}}
{"answerKey": "B", "id": "e65559cd9f5d96b577caeb78d9033502", "question": {"question_concept": "house", "choices": [{"label": "A", "text": "subdivision"}, {"label": "B", "text": "newspaper"}, {"label": "C", "text": "street"}, {"label": "D", "text": "laundry mat"}, {"label": "E", "text": "surface of earth"}], "stem": "If a house has a subscription, what likely shows up in the driveway every morning?"}}
{"answerKey": "C", "id": "b8937a30f25093910c040f4e63e1d352", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "feel lucky"}, {"label": "B", "text": "cross street"}, {"label": "C", "text": "wash themselves"}, {"label": "D", "text": "eat"}, {"label": "E", "text": "wonder what happened"}], "stem": "What does a person do when they feel dirty?"}}
{"answerKey": "B", "id": "aabe8eb218468fc63b6c9aa6d428c951", "question": {"question_concept": "energy", "choices": [{"label": "A", "text": "work"}, {"label": "B", "text": "wrestle"}, {"label": "C", "text": "play sports"}, {"label": "D", "text": "matter"}, {"label": "E", "text": "sleep"}], "stem": "After the weight cut he was worried about his energy levels, but this was part of participating in a what?"}}
{"answerKey": "B", "id": "43ba9669564217f2f909f33acbedaf95", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "fever"}, {"label": "B", "text": "eat every day"}, {"label": "C", "text": "excited"}, {"label": "D", "text": "headache"}, {"label": "E", "text": "expressive"}], "stem": "what does a person do to stay healthy?"}}
{"answerKey": "D", "id": "2b9b625c788584b8d41f1a74d740e126", "question": {"question_concept": "guard", "choices": [{"label": "A", "text": "man post"}, {"label": "B", "text": "attack"}, {"label": "C", "text": "intimidation"}, {"label": "D", "text": "prisoner"}, {"label": "E", "text": "unprotected"}], "stem": "Who is the guard here for?"}}
{"answerKey": "A", "id": "eb6807290df71b040e2c7bcc5d11fdea", "question": {"question_concept": "excitement", "choices": [{"label": "A", "text": "express information"}, {"label": "B", "text": "dance"}, {"label": "C", "text": "library"}, {"label": "D", "text": "go somewhere"}, {"label": "E", "text": "study"}], "stem": "If a person stutters when he experiences anxiety or excitement, he'll have difficult doing what?"}}
{"answerKey": "C", "id": "f06852fb4bb2764dc208a991d037f211", "question": {"question_concept": "letter opener", "choices": [{"label": "A", "text": "office supply store"}, {"label": "B", "text": "stationery store"}, {"label": "C", "text": "dek"}, {"label": "D", "text": "martyr's chest"}, {"label": "E", "text": "refrigerator"}], "stem": "Where can you keep letter opener when it likely to be needed soon?"}}
{"answerKey": "E", "id": "5efadabaf61b5174916e3ab659bcd283", "question": {"question_concept": "carpet", "choices": [{"label": "A", "text": "brothel"}, {"label": "B", "text": "restaurant"}, {"label": "C", "text": "building"}, {"label": "D", "text": "bowling alley"}, {"label": "E", "text": "at hotel"}], "stem": "Danny found that the carpet did not ,match the drapes, which was disappointing, because this place was expensive.  But it was the only place in town that wasn't booked solid for the week and he needed it while he was in town, so he couldn't complain.   Where might this place be?"}}
{"answerKey": "A", "id": "e9d4c747018ff81b8c0aefb5abc3c539", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "face problems"}, {"label": "B", "text": "better themselves"}, {"label": "C", "text": "pay bills"}, {"label": "D", "text": "become disillusioned"}, {"label": "E", "text": "eat chicken"}], "stem": "What do people need to do to change their lives?"}}
{"answerKey": "B", "id": "30a8cfd186f1aae5acd425a52d058863", "question": {"question_concept": "human", "choices": [{"label": "A", "text": "underpass"}, {"label": "B", "text": "homes"}, {"label": "C", "text": "workplace"}, {"label": "D", "text": "school"}, {"label": "E", "text": "space shuttle"}], "stem": "Humans need shelter to survive.  They usually find shelter where?"}}
{"answerKey": "A", "id": "9e7805871c8a276300a89fe910a90949", "question": {"question_concept": "bad", "choices": [{"label": "A", "text": "first class"}, {"label": "B", "text": "propitious"}, {"label": "C", "text": "reputable"}, {"label": "D", "text": "one"}, {"label": "E", "text": "sufficient"}], "stem": "Someone who had a very bad flight might be given a trip in this to make up for it?"}}
{"answerKey": "B", "id": "047c2d8c65d297b39aa42821c1ca76a9", "question": {"question_concept": "hike", "choices": [{"label": "A", "text": "seeing bear"}, {"label": "B", "text": "see beautiful views"}, {"label": "C", "text": "get wet"}, {"label": "D", "text": "getting lost"}, {"label": "E", "text": "murdered by a landshark"}], "stem": "Nature can be good and bad for the person who walks, what are some things?"}}
{"answerKey": "A", "id": "0bed77da54b6c54facd0ee6614aad72e", "question": {"question_concept": "exercise", "choices": [{"label": "A", "text": "need for food"}, {"label": "B", "text": "fitness"}, {"label": "C", "text": "sweating"}, {"label": "D", "text": "fastfood"}, {"label": "E", "text": "thirst"}], "stem": "Jim decided to lose weight.  He thought that exercise is the best way to lose weight because you can't get rid of what?"}}
{"answerKey": "D", "id": "32e2adee67aace0a98c830fb39463015", "question": {"question_concept": "nature", "choices": [{"label": "A", "text": "artificial"}, {"label": "B", "text": "indoors"}, {"label": "C", "text": "city"}, {"label": "D", "text": "man made"}, {"label": "E", "text": "eat cake"}], "stem": "Nature creates more beautiful structures than those that are what?"}}
{"answerKey": "B", "id": "8272f08792b873885f93d4c148e307e5", "question": {"question_concept": "water", "choices": [{"label": "A", "text": "typhoon"}, {"label": "B", "text": "snowflake"}, {"label": "C", "text": "laddle"}, {"label": "D", "text": "teardrops"}, {"label": "E", "text": "sink"}], "stem": "The water in clouds turn in to what when it gets cold?"}}
{"answerKey": "D", "id": "bc05bc6b4df7a3d25a361515fe8912ad", "question": {"question_concept": "swamp", "choices": [{"label": "A", "text": "wetlands"}, {"label": "B", "text": "new york"}, {"label": "C", "text": "michigan"}, {"label": "D", "text": "louisiana"}, {"label": "E", "text": "river delta"}], "stem": "What southern U.S. state is know for having many swamps?"}}
{"answerKey": "D", "id": "b893a6e7a2b172bd71f03c9dbee4f960", "question": {"question_concept": "going to sleep", "choices": [{"label": "A", "text": "snoring"}, {"label": "B", "text": "latency"}, {"label": "C", "text": "dreams"}, {"label": "D", "text": "relaxation"}, {"label": "E", "text": "dreaming"}], "stem": "When going to sleep what happens to your body?"}}
{"answerKey": "A", "id": "cf8e30dd6956d03e3f0f0397112a8696", "question": {"question_concept": "monkey", "choices": [{"label": "A", "text": "banana tree"}, {"label": "B", "text": "sailor suit"}, {"label": "C", "text": "theatre"}, {"label": "D", "text": "mulberry bush"}, {"label": "E", "text": "research laboratory"}], "stem": "Where is a monkey likely to enjoy being?"}}
{"answerKey": "E", "id": "159d50e325b59c6d29ec371500e173b4", "question": {"question_concept": "exercising", "choices": [{"label": "A", "text": "shortness of breath"}, {"label": "B", "text": "lift weights"}, {"label": "C", "text": "error"}, {"label": "D", "text": "fall down"}, {"label": "E", "text": "run"}], "stem": "What is a form of anaerobic exercising?"}}
{"answerKey": "C", "id": "17eafc807b198236faf06a66f4c05313", "question": {"question_concept": "earth", "choices": [{"label": "A", "text": "tree"}, {"label": "B", "text": "orbit"}, {"label": "C", "text": "solar system"}, {"label": "D", "text": "fotograph"}, {"label": "E", "text": "dreams"}], "stem": "The earth is one planet in what?"}}
{"answerKey": "E", "id": "24eebfa678112100803da16dde148b2d", "question": {"question_concept": "container can", "choices": [{"label": "A", "text": "pantry"}, {"label": "B", "text": "store"}, {"label": "C", "text": "gas"}, {"label": "D", "text": "liquid"}, {"label": "E", "text": "garage"}], "stem": "Where would you put a container can after you buy it?"}}
{"answerKey": "B", "id": "ec882fc3a9bfaeae2a26fe31c2ef2c07", "question": {"question_concept": "friends", "choices": [{"label": "A", "text": "friend's house"}, {"label": "B", "text": "school"}, {"label": "C", "text": "fraternity house"}, {"label": "D", "text": "internet cafe"}, {"label": "E", "text": "airplane"}], "stem": "Where did you meet your best friend since Kindergarten?"}}
{"answerKey": "E", "id": "0a006d16d9042e0c170935e5fbf7f9af", "question": {"question_concept": "below", "choices": [{"label": "A", "text": "upstairs"}, {"label": "B", "text": "aloft"}, {"label": "C", "text": "diagonal"}, {"label": "D", "text": "upstream"}, {"label": "E", "text": "upwards"}], "stem": "James was below the balloon.  He watched it rise.  What direction did he look in?"}}
{"answerKey": "A", "id": "d33a81660058e570a18fb2eafa284a78", "question": {"question_concept": "playing", "choices": [{"label": "A", "text": "feeling happy"}, {"label": "B", "text": "learning"}, {"label": "C", "text": "injury"}, {"label": "D", "text": "burn"}, {"label": "E", "text": "get hungry"}], "stem": "John and Tim like playing. It makes them what?"}}
{"answerKey": "E", "id": "1e09c3136a743b862e783700b7667028", "question": {"question_concept": "seeing new", "choices": [{"label": "A", "text": "envy"}, {"label": "B", "text": "jealousy"}, {"label": "C", "text": "education"}, {"label": "D", "text": "fear"}, {"label": "E", "text": "excitement"}], "stem": "What could happen if someone is seeing new presents at a birthday party?"}}
{"answerKey": "C", "id": "5e851c47682bdf79ec7c139ecf124c9a", "question": {"question_concept": "cat", "choices": [{"label": "A", "text": "meat loaf"}, {"label": "B", "text": "bedroom"}, {"label": "C", "text": "microwave"}, {"label": "D", "text": "living room"}, {"label": "E", "text": "floor"}], "stem": "Joe's cat smelled something delicious and jumped into this, causing him to panic and fear for its life. Where might it have jumped?"}}
{"answerKey": "D", "id": "b148f18fb8b5a504b67078ef6ac29717", "question": {"question_concept": "flowers", "choices": [{"label": "A", "text": "continue to grow"}, {"label": "B", "text": "plant themselves"}, {"label": "C", "text": "many colors"}, {"label": "D", "text": "smell good"}, {"label": "E", "text": "make pretty"}], "stem": "Why would a person put flowers in a room with dirty gym socks?"}}
{"answerKey": "C", "id": "b6bbe013995fdb5def3d504319af0791", "question": {"question_concept": "level", "choices": [{"label": "A", "text": "electrical circuit"}, {"label": "B", "text": "build evenly"}, {"label": "C", "text": "uneven"}, {"label": "D", "text": "unbalanced"}, {"label": "E", "text": "tilted"}], "stem": "The table wasn't level.  some parts were higher and some were lower with no rhyme or reason.   It was very what?"}}
{"answerKey": "A", "id": "0c2fa15a02d0b6ca6707e98fac7589e4", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "financial security"}, {"label": "B", "text": "live well"}, {"label": "C", "text": "good relationship"}, {"label": "D", "text": "compliments"}, {"label": "E", "text": "discounted furniture"}], "stem": "The person signed up for home insurance, what is he seeking?"}}
{"answerKey": "C", "id": "a656e74a943f9e2698a25bbcfb4e96db", "question": {"question_concept": "committing murder", "choices": [{"label": "A", "text": "happiness"}, {"label": "B", "text": "problems"}, {"label": "C", "text": "prosecution"}, {"label": "D", "text": "distress"}, {"label": "E", "text": "misery"}], "stem": "James know that committing murder was wrong, but he thought that he could get away with it.  He was really troubled  and fearful because of what?"}}
{"answerKey": "C", "id": "8086f022f2d4a4888ae1f8c7e4541ab9", "question": {"question_concept": "eating hamburger", "choices": [{"label": "A", "text": "gas"}, {"label": "B", "text": "getting full"}, {"label": "C", "text": "mad cow disease"}, {"label": "D", "text": "death"}, {"label": "E", "text": "feel full"}], "stem": "How can someone die from eating hamburger?"}}
{"answerKey": "E", "id": "5655a3002dd9a6b7dabede1dd26a5893", "question": {"question_concept": "boat", "choices": [{"label": "A", "text": "water"}, {"label": "B", "text": "ocean"}, {"label": "C", "text": "garage"}, {"label": "D", "text": "harbor"}, {"label": "E", "text": "river"}], "stem": "Where would using a boat not require navigation skills?"}}
{"answerKey": "D", "id": "17d9bfaee1efac51b1ca240125bc5977", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "acknowledgment"}, {"label": "B", "text": "focused"}, {"label": "C", "text": "know what time"}, {"label": "D", "text": "feel important"}, {"label": "E", "text": "trust himself"}], "stem": "What does a self assured person often do?"}}
{"answerKey": "D", "id": "801431167b8bff06b9870abe9721536b", "question": {"question_concept": "making friends", "choices": [{"label": "A", "text": "scary"}, {"label": "B", "text": "having friends"}, {"label": "C", "text": "good feeling"}, {"label": "D", "text": "conflict"}, {"label": "E", "text": "friendship"}], "stem": "He was very outgoing, for him making friends was no personal what?"}}
{"answerKey": "E", "id": "85ebdd4f1a3c2ac900eee8e75e48ccaa", "question": {"question_concept": "giving assistance", "choices": [{"label": "A", "text": "reward"}, {"label": "B", "text": "boredom"}, {"label": "C", "text": "pleasure"}, {"label": "D", "text": "happiness"}, {"label": "E", "text": "satisfaction"}], "stem": "What do you feel when giving assistance to the needy?"}}
{"answerKey": "A", "id": "db1eb157671109bbb9113b0f71a6b957", "question": {"question_concept": "carrots", "choices": [{"label": "A", "text": "refrigerator"}, {"label": "B", "text": "store"}, {"label": "C", "text": "farmer's market"}, {"label": "D", "text": "supermarket"}, {"label": "E", "text": "dryer"}], "stem": "Paul wants carrots and doesn't need to drive anywhere. He gets them from where?"}}
{"answerKey": "D", "id": "c02a3c2d4f726b9e1be99533a24a6ab4", "question": {"question_concept": "mess", "choices": [{"label": "A", "text": "sailboat"}, {"label": "B", "text": "desk"}, {"label": "C", "text": "closet"}, {"label": "D", "text": "table"}, {"label": "E", "text": "apartment"}], "stem": "He was a sloppy eater, so where did he leave a mess?"}}
{"answerKey": "A", "id": "3ed6391c539e6daa5b5fdb1b6d5d8ace", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "life partner"}, {"label": "B", "text": "larger house"}, {"label": "C", "text": "second chances"}, {"label": "D", "text": "money"}, {"label": "E", "text": "headache"}], "stem": "What does every person want?"}}
{"answerKey": "A", "id": "1db19a32a3edbff9981976dc9ec800ce", "question": {"question_concept": "string", "choices": [{"label": "A", "text": "bird's nest"}, {"label": "B", "text": "park"}, {"label": "C", "text": "guitar"}, {"label": "D", "text": "kite"}, {"label": "E", "text": "quark"}], "stem": "If a small flying animal picks up a string, where are they taking it?"}}
{"answerKey": "A", "id": "1e5a138b4c7d456c37abf4990b402bbe", "question": {"question_concept": "committing perjury", "choices": [{"label": "A", "text": "confidence"}, {"label": "B", "text": "go to jail"}, {"label": "C", "text": "telling lies"}, {"label": "D", "text": "lying"}, {"label": "E", "text": "manual"}], "stem": "He had no issue committing perjury, he had a what that he would get away with it?"}}
{"answerKey": "D", "id": "9402864beae075392d2ee6c10115fc21", "question": {"question_concept": "tennis court", "choices": [{"label": "A", "text": "desert"}, {"label": "B", "text": "college campus"}, {"label": "C", "text": "recreational center"}, {"label": "D", "text": "athletic club"}, {"label": "E", "text": "park"}], "stem": "What could go to a tennis court?"}}
{"answerKey": "D", "id": "25136807f7b2e78b115698daa1677b4a", "question": {"question_concept": "cup", "choices": [{"label": "A", "text": "sand box"}, {"label": "B", "text": "kitchen cabinet"}, {"label": "C", "text": "waterfall"}, {"label": "D", "text": "water fountain"}, {"label": "E", "text": "table"}], "stem": "What could you use to fill a cup and then drink from it?"}}
{"answerKey": "B", "id": "bc10bf2bfae26a2226823d42956f6cf0", "question": {"question_concept": "living room", "choices": [{"label": "A", "text": "formal seating"}, {"label": "B", "text": "friend's house"}, {"label": "C", "text": "movies"}, {"label": "D", "text": "home"}, {"label": "E", "text": "apartment"}], "stem": "The two played video games all night in the living room, he enjoyed visiting where?"}}
{"answerKey": "D", "id": "5a6559db6bae37e3a8af7350be212219", "question": {"question_concept": "weasel", "choices": [{"label": "A", "text": "washington dc"}, {"label": "B", "text": "ladder"}, {"label": "C", "text": "natural history museum"}, {"label": "D", "text": "cherry tree"}, {"label": "E", "text": "chicken coop"}], "stem": "The weasel ran up away from danger, somebody joked only our first president could get him down from the what?"}}
{"answerKey": "D", "id": "7ae17f5aecacf18c94a47cc48deb6c36", "question": {"question_concept": "blowfish", "choices": [{"label": "A", "text": "fish market"}, {"label": "B", "text": "jungle"}, {"label": "C", "text": "sea water"}, {"label": "D", "text": "body of water"}, {"label": "E", "text": "soup"}], "stem": "If you were looking for a blowfish, you wouldn't look on dry land, you'd look in a what?"}}
{"answerKey": "A", "id": "5d809e0ee19badc66071653630ea7c51", "question": {"question_concept": "rotor", "choices": [{"label": "A", "text": "jet engine"}, {"label": "B", "text": "helicopter"}, {"label": "C", "text": "electric motor"}, {"label": "D", "text": "rotator"}, {"label": "E", "text": "electrical circuit"}], "stem": "George checked the rotor of the Apache, which wasn't powered by internal combustion, but by what?"}}
{"answerKey": "D", "id": "ad0943fc37034cd2b7e485021f8b1b8c", "question": {"question_concept": "cards", "choices": [{"label": "A", "text": "players"}, {"label": "B", "text": "play games"}, {"label": "C", "text": "casino"}, {"label": "D", "text": "table"}, {"label": "E", "text": "toy store"}], "stem": "The poker dealer spread the flop of cards across the what?"}}
{"answerKey": "B", "id": "c2a8c6814ed3e207771cfc23b3b42cf1", "question": {"question_concept": "saltshaker", "choices": [{"label": "A", "text": "cruet"}, {"label": "B", "text": "table setting"}, {"label": "C", "text": "kitchen cupboard"}, {"label": "D", "text": "cabinet"}, {"label": "E", "text": "store"}], "stem": "Where is a salt shaker most often kept?"}}
{"answerKey": "D", "id": "0b52cc905fff0ca69a45e6353d10e401", "question": {"question_concept": "dollar", "choices": [{"label": "A", "text": "cash drawer"}, {"label": "B", "text": "teh bank"}, {"label": "C", "text": "safety deposit box"}, {"label": "D", "text": "pocket"}, {"label": "E", "text": "piggy bank"}], "stem": "Where would you put a dollar if you want to go to a store and buy something with it?"}}
{"answerKey": "E", "id": "30d0c2006613eec41ae814d76c17a798", "question": {"question_concept": "sideboard", "choices": [{"label": "A", "text": "home"}, {"label": "B", "text": "serve food buffet"}, {"label": "C", "text": "dining room"}, {"label": "D", "text": "living room"}, {"label": "E", "text": "kitchen"}], "stem": "What room is likely to have a sideboard on the counter?"}}
{"answerKey": "A", "id": "f7a6d0d816d14210f3af5dabe21bf804", "question": {"question_concept": "windshield", "choices": [{"label": "A", "text": "airplane"}, {"label": "B", "text": "scooter"}, {"label": "C", "text": "motorboat"}, {"label": "D", "text": "car"}, {"label": "E", "text": "motor vehicle"}], "stem": "What is unlikely to get bugs on its windshield due to bugs' inability to reach it when it is moving?"}}
{"answerKey": "E", "id": "c306ab28498b67c53decb9dde1d78bd5", "question": {"question_concept": "jeans", "choices": [{"label": "A", "text": "clothing store"}, {"label": "B", "text": "bedroom"}, {"label": "C", "text": "thrift store"}, {"label": "D", "text": "apartment"}, {"label": "E", "text": "gap"}], "stem": "What mall store sells jeans for a decent price?"}}
{"answerKey": "B", "id": "637c710ec9582fd9b9e8eaa3f3fe83bb", "question": {"question_concept": "towel", "choices": [{"label": "A", "text": "cupboard"}, {"label": "B", "text": "at hotel"}, {"label": "C", "text": "swimming pool"}, {"label": "D", "text": "clothes line"}, {"label": "E", "text": "backpack"}], "stem": "Where can a bath towel be borrowed?"}}
{"answerKey": "E", "id": "9ae52783d8fdb5cc2e8caa01542c3341", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "no problems"}, {"label": "B", "text": "better themselves"}, {"label": "C", "text": "face problems"}, {"label": "D", "text": "learn from each other"}, {"label": "E", "text": "become disillusioned"}], "stem": "Why do people stop caring about their problems?"}}
{"answerKey": "D", "id": "4f23829b96b38b5633ecc3325281726d", "question": {"question_concept": "plain", "choices": [{"label": "A", "text": "mountain"}, {"label": "B", "text": "fancy"}, {"label": "C", "text": "sandplain"}, {"label": "D", "text": "cliff"}, {"label": "E", "text": "gorge"}], "stem": "John rode on the plain until it reached the ocean and couldn't go any farther. What might he have bee on?"}}
{"answerKey": "A", "id": "3fcdc0b03e3c8b10692d642676931f4b", "question": {"question_concept": "actors", "choices": [{"label": "A", "text": "theater"}, {"label": "B", "text": "opera"}, {"label": "C", "text": "show"}, {"label": "D", "text": "television"}, {"label": "E", "text": "blockbuster feature"}], "stem": "They were never going to be big actors, but they all had passion for the local what?"}}
{"answerKey": "A", "id": "ddd606743cf71679438a85280f64593a", "question": {"question_concept": "folding chair", "choices": [{"label": "A", "text": "beach"}, {"label": "B", "text": "city hall"}, {"label": "C", "text": "closet"}, {"label": "D", "text": "garage"}, {"label": "E", "text": "school"}], "stem": "Where would you use a folding chair but not store one?"}}
{"answerKey": "B", "id": "420641003ba20b966887dfac684efb17", "question": {"question_concept": "shopping", "choices": [{"label": "A", "text": "tiredness"}, {"label": "B", "text": "calluses"}, {"label": "C", "text": "bankruptcy"}, {"label": "D", "text": "standing in line"}, {"label": "E", "text": "sleepyness"}], "stem": "If you spend a long time shopping in uncomfortable shoes, you might develop what?"}}
{"answerKey": "C", "id": "064c3074a682893d49c3c5b4f1e89984", "question": {"question_concept": "president", "choices": [{"label": "A", "text": "vote"}, {"label": "B", "text": "election"}, {"label": "C", "text": "trouble"}, {"label": "D", "text": "board room"}, {"label": "E", "text": "corporation"}], "stem": "What does impeachment mean for the president?"}}
{"answerKey": "D", "id": "c640116ca6905d5256edadb616b3f76e", "question": {"question_concept": "noble", "choices": [{"label": "A", "text": "loser"}, {"label": "B", "text": "ignoble"}, {"label": "C", "text": "peasant"}, {"label": "D", "text": "inferior"}, {"label": "E", "text": "plebeian"}], "stem": "Noble citizen of the Roman empire believed those born with lower status were what to them?"}}
{"answerKey": "E", "id": "35ad89c198d5d6311a71c993bb7b6cba", "question": {"question_concept": "playing baseball", "choices": [{"label": "A", "text": "strikes"}, {"label": "B", "text": "eating"}, {"label": "C", "text": "injury"}, {"label": "D", "text": "sore muscles"}, {"label": "E", "text": "pain"}], "stem": "Spraining an ankle while playing baseball will cause what?"}}
{"answerKey": "E", "id": "916bbd27545446ca5d83d07c10d013ea", "question": {"question_concept": "carpet", "choices": [{"label": "A", "text": "bedroom"}, {"label": "B", "text": "chair"}, {"label": "C", "text": "bowling alley"}, {"label": "D", "text": "at hotel"}, {"label": "E", "text": "restaurant"}], "stem": "John was traveling to a new city and took time to check out a business.  He noticed that its carpet was stained with sauces and ketchup. What type of business might that be?"}}
{"answerKey": "E", "id": "e40fd2c17fe2cde4bd4af540d35fd518", "question": {"question_concept": "condo", "choices": [{"label": "A", "text": "city"}, {"label": "B", "text": "electrical circuit"}, {"label": "C", "text": "residential area"}, {"label": "D", "text": "suburbia"}, {"label": "E", "text": "milwaukee"}], "stem": "If you have a condo in a Wisconsin city known for beer, where are you?"}}
{"answerKey": "E", "id": "98a04457025f18c2287d5c610ff8000d", "question": {"question_concept": "note", "choices": [{"label": "A", "text": "fridge"}, {"label": "B", "text": "sheet music"}, {"label": "C", "text": "desk"}, {"label": "D", "text": "bed"}, {"label": "E", "text": "medical chart"}], "stem": "Where is hard to read note likely to be?"}}
{"answerKey": "E", "id": "f656a475f07d3adba9d1486eda8e834a", "question": {"question_concept": "buying beer", "choices": [{"label": "A", "text": "have no money"}, {"label": "B", "text": "pants"}, {"label": "C", "text": "relaxation"}, {"label": "D", "text": "lose money"}, {"label": "E", "text": "spend money"}], "stem": "How does someone go about buying beer?"}}
{"answerKey": "C", "id": "c865b3547c2a2e3c3916d7be6ab25752", "question": {"question_concept": "gum", "choices": [{"label": "A", "text": "shelf"}, {"label": "B", "text": "movies"}, {"label": "C", "text": "sidewalk"}, {"label": "D", "text": "water fountain"}, {"label": "E", "text": "table"}], "stem": "If there is gum on your shoe where did it likely come from?"}}
{"answerKey": "C", "id": "abd30bab9b96f902fead5378d4f4a1e4", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "know everything"}, {"label": "B", "text": "acknowledgment"}, {"label": "C", "text": "make more money"}, {"label": "D", "text": "throw a party"}, {"label": "E", "text": "spare time"}], "stem": "If a person isn't able to pay their bills what must they do?"}}
{"answerKey": "C", "id": "a4b44a986e7f9045432e20ea75611df4", "question": {"question_concept": "exercising", "choices": [{"label": "A", "text": "losing weight"}, {"label": "B", "text": "healthy"}, {"label": "C", "text": "get in shape"}, {"label": "D", "text": "weight loss"}, {"label": "E", "text": "sweat"}], "stem": "What is main benefit to exercising?"}}
{"answerKey": "C", "id": "1f492f556fae64f72ce36b6caa242dd0", "question": {"question_concept": "possible", "choices": [{"label": "A", "text": "no go"}, {"label": "B", "text": "unable"}, {"label": "C", "text": "unlikely"}, {"label": "D", "text": "impossibility"}, {"label": "E", "text": "cant do"}], "stem": "Steve thought that it was possible, but he agreed that it was what?"}}
{"answerKey": "C", "id": "d0c67c7ae6f2361fe237110455127866", "question": {"question_concept": "japanese restaurant", "choices": [{"label": "A", "text": "california"}, {"label": "B", "text": "tokio"}, {"label": "C", "text": "downtown"}, {"label": "D", "text": "narnia"}, {"label": "E", "text": "large town"}], "stem": "What region of a west coast U.S. city would you find a Japanese restaurant?"}}
{"answerKey": "D", "id": "7bb279e38a1c9eb47a0c7af979a131a2", "question": {"question_concept": "learning about science", "choices": [{"label": "A", "text": "repetition"}, {"label": "B", "text": "sense of wonder"}, {"label": "C", "text": "accidents"}, {"label": "D", "text": "intimidation"}, {"label": "E", "text": "increased knowledge"}], "stem": "What is a tactic used to interfere with learning about science?"}}
{"answerKey": "C", "id": "3095078e4771053d9d5fa8d4f5f3dc38", "question": {"question_concept": "falling in love", "choices": [{"label": "A", "text": "getting married"}, {"label": "B", "text": "pain"}, {"label": "C", "text": "happiness"}, {"label": "D", "text": "getting married"}, {"label": "E", "text": "suffering"}], "stem": "What do people usually feel when falling in love?"}}
{"answerKey": "B", "id": "b23edb651e623e5d1e03e8ed3937e8fc", "question": {"question_concept": "tiger", "choices": [{"label": "A", "text": "jungle"}, {"label": "B", "text": "zoo"}, {"label": "C", "text": "kill"}, {"label": "D", "text": "india"}, {"label": "E", "text": "eat cake"}], "stem": "The tiger was stuck in what animal prison where he got lazy and fat?"}}
{"answerKey": "C", "id": "acf6b667e9353b1743b7c4f60a6a9017", "question": {"question_concept": "child", "choices": [{"label": "A", "text": "study"}, {"label": "B", "text": "begin school"}, {"label": "C", "text": "go out to play"}, {"label": "D", "text": "row boat"}, {"label": "E", "text": "clean room"}], "stem": "What do parents tell a child to do on the weekend?"}}
{"answerKey": "C", "id": "15b090801256085ad465e74af47cbee9", "question": {"question_concept": "dogs", "choices": [{"label": "A", "text": "aggressive"}, {"label": "B", "text": "friendly"}, {"label": "C", "text": "very loyal"}, {"label": "D", "text": "found outside"}, {"label": "E", "text": "very smart"}], "stem": "Why are dogs often known as man's best friend?"}}
{"answerKey": "D", "id": "790b3f583e9bc9424c771691ecc70c20", "question": {"question_concept": "wheel", "choices": [{"label": "A", "text": "boat"}, {"label": "B", "text": "michigan"}, {"label": "C", "text": "train station"}, {"label": "D", "text": "bicycle shop"}, {"label": "E", "text": "trunk of car"}], "stem": "Where can you buy a two wheel transportation machine?"}}
{"answerKey": "A", "id": "22b8219d43a38a1130e0a35ece152337", "question": {"question_concept": "vacuum", "choices": [{"label": "A", "text": "space"}, {"label": "B", "text": "closet"}, {"label": "C", "text": "kitchen"}, {"label": "D", "text": "orbit"}, {"label": "E", "text": "container"}], "stem": "Where might an alien use a vacuum?"}}
{"answerKey": "A", "id": "5d4233146435ab0ca211e8ac9bfce76f", "question": {"question_concept": "condoms", "choices": [{"label": "A", "text": "supermarket"}, {"label": "B", "text": "sock drawer"}, {"label": "C", "text": "cd store"}, {"label": "D", "text": "medicine chest"}, {"label": "E", "text": "bedroom"}], "stem": "Where do you buy condoms?"}}
{"answerKey": "A", "id": "be737cd4db844574ef594442ce6c9453", "question": {"question_concept": "sheep", "choices": [{"label": "A", "text": "goat"}, {"label": "B", "text": "expensive"}, {"label": "C", "text": "lion"}, {"label": "D", "text": "wolf"}, {"label": "E", "text": "meadow"}], "stem": "What animal is known for being a follower?"}}
{"answerKey": "D", "id": "550164b7cf4e03153484136f10122c70", "question": {"question_concept": "soldier", "choices": [{"label": "A", "text": "fight enemy"}, {"label": "B", "text": "go to war"}, {"label": "C", "text": "fight for freedom"}, {"label": "D", "text": "wait for orders"}, {"label": "E", "text": "follow instructions"}], "stem": "The soldier was told to get to the rendezvous point, for there he was suppose to what?"}}
{"answerKey": "C", "id": "a617eb4d27edea93e7fd630ce00c8219", "question": {"question_concept": "kill", "choices": [{"label": "A", "text": "sip through"}, {"label": "B", "text": "damnation"}, {"label": "C", "text": "shoot"}, {"label": "D", "text": "commit crime"}, {"label": "E", "text": "eat breakfast"}], "stem": "If you want to kill someone you can do what to them with a gun?"}}
{"answerKey": "D", "id": "bd47827418d5b8d7fb3502a398644435", "question": {"question_concept": "hostess", "choices": [{"label": "A", "text": "group people"}, {"label": "B", "text": "welcome guests"}, {"label": "C", "text": "occupations"}, {"label": "D", "text": "work room"}, {"label": "E", "text": "seat customer"}], "stem": "The hostess greeted the employees to the program, she then led them to their what?"}}
{"answerKey": "A", "id": "31487ab8b1e8f12e252590cc58bd19c2", "question": {"question_concept": "soap", "choices": [{"label": "A", "text": "cabinet"}, {"label": "B", "text": "supermarket"}, {"label": "C", "text": "jail"}, {"label": "D", "text": "butt"}, {"label": "E", "text": "own home"}], "stem": "Where is a likely place to store unused soap?"}}
{"answerKey": "A", "id": "ce2fd94212243f843b3f357046051f57", "question": {"question_concept": "love", "choices": [{"label": "A", "text": "painful"}, {"label": "B", "text": "happy"}, {"label": "C", "text": "blind"}, {"label": "D", "text": "contagious"}, {"label": "E", "text": "bring joy"}], "stem": "Loss of someone you love can cause what kind of feeling in your heart?"}}
{"answerKey": "C", "id": "f87f40db71a56b5beda3194550202dc9_1", "question": {"question_concept": "ballpoint pen", "choices": [{"label": "A", "text": "backpack"}, {"label": "B", "text": "bank"}, {"label": "C", "text": "desk drawer"}, {"label": "D", "text": "eat cake"}, {"label": "E", "text": "office desk"}], "stem": "Where in your home would you keep a ballpoint pen when not in use?"}}
{"answerKey": "B", "id": "0b25bbd9e9aa976655e1975e31331709", "question": {"question_concept": "truth", "choices": [{"label": "A", "text": "work to advantage"}, {"label": "B", "text": "matter to"}, {"label": "C", "text": "help"}, {"label": "D", "text": "free mind"}, {"label": "E", "text": "further knowledge"}], "stem": "James was someone who was caught in his own delusions.  To him, the truth didn't do what what?"}}
{"answerKey": "C", "id": "925232b4c9bba945a38ac7ef0f15f8d0", "question": {"question_concept": "yard", "choices": [{"label": "A", "text": "city"}, {"label": "B", "text": "three feet"}, {"label": "C", "text": "subdivision"}, {"label": "D", "text": "parking garage"}, {"label": "E", "text": "michigan"}], "stem": "He wanted to live somewhere were every yard was uniform in size and landscaping, where should he look for a house?"}}
{"answerKey": "B", "id": "3338109fcafaaa370c8900a53e1b3ed8", "question": {"question_concept": "flask", "choices": [{"label": "A", "text": "laboratory"}, {"label": "B", "text": "chemistry lab"}, {"label": "C", "text": "coat pocket"}, {"label": "D", "text": "after hours speakeasy"}, {"label": "E", "text": "bordello"}], "stem": "The flasks was used to distill elements, where was is being used?"}}
{"answerKey": "A", "id": "e172a93c72d305ee8262a8deb00d9fc3", "question": {"question_concept": "anger", "choices": [{"label": "A", "text": "cool off"}, {"label": "B", "text": "punch"}, {"label": "C", "text": "illustrate point"}, {"label": "D", "text": "fight"}, {"label": "E", "text": "release energy"}], "stem": "What was the man encouraged to do after he expressed his anger violently?"}}
{"answerKey": "E", "id": "f1c2e37abf17d9e4ad16eb40f966c79f", "question": {"question_concept": "triangle", "choices": [{"label": "A", "text": "math class"}, {"label": "B", "text": "math book"}, {"label": "C", "text": "in pythagorus' band"}, {"label": "D", "text": "orchestra"}, {"label": "E", "text": "music class"}], "stem": "Where can a student learn to play a triangle?"}}
{"answerKey": "B", "id": "d29252ddaf7c7ef491abcce342d7bb98", "question": {"question_concept": "use television", "choices": [{"label": "A", "text": "get wet"}, {"label": "B", "text": "open eyes"}, {"label": "C", "text": "kill"}, {"label": "D", "text": "plug in"}, {"label": "E", "text": "first turn on power"}], "stem": "What do you need to do to use television if it is already turned on?"}}
{"answerKey": "E", "id": "8c3c6b34bdb650a6517bca3786406c99", "question": {"question_concept": "playing poker", "choices": [{"label": "A", "text": "competition"}, {"label": "B", "text": "fun game"}, {"label": "C", "text": "losing money"}, {"label": "D", "text": "fun"}, {"label": "E", "text": "social event"}], "stem": "The guys had a regular poker game, rather than going to the movies this what their what?"}}
{"answerKey": "D", "id": "ff1bf2ec835c9df8695ae0cfb5281646", "question": {"question_concept": "dog", "choices": [{"label": "A", "text": "start fighting"}, {"label": "B", "text": "play"}, {"label": "C", "text": "lots of attention"}, {"label": "D", "text": "petted"}, {"label": "E", "text": "bone"}], "stem": "When you stroke a dogs fur what have you done?"}}
{"answerKey": "B", "id": "c7526b682e64f355384631b35cd78fc9", "question": {"question_concept": "bar stool", "choices": [{"label": "A", "text": "kitchen"}, {"label": "B", "text": "drunker"}, {"label": "C", "text": "tavern"}, {"label": "D", "text": "restaurant"}, {"label": "E", "text": "shorter"}], "stem": "Dan fell off a bar stool.  He did this because he was what than ever before?"}}
{"answerKey": "B", "id": "0fba83d3997f048adcc31937221af77e", "question": {"question_concept": "wood", "choices": [{"label": "A", "text": "petrify"}, {"label": "B", "text": "sanded"}, {"label": "C", "text": "warp"}, {"label": "D", "text": "composted"}, {"label": "E", "text": "clean"}], "stem": "The wood was still rough to the touch, what did the woodworker have to do?"}}
{"answerKey": "D", "id": "a5456dc611aa93b81d7ab6ed8e160f85", "question": {"question_concept": "chief", "choices": [{"label": "A", "text": "peon"}, {"label": "B", "text": "indian"}, {"label": "C", "text": "minister"}, {"label": "D", "text": "follower"}, {"label": "E", "text": "employee"}], "stem": "The chief saw his entire tribe wiped out, he was a leader with a single what?"}}
{"answerKey": "D", "id": "11416df796f63d2f0dddc846b9c139d3", "question": {"question_concept": "flower", "choices": [{"label": "A", "text": "blossom"}, {"label": "B", "text": "park"}, {"label": "C", "text": "open"}, {"label": "D", "text": "cast shadow"}, {"label": "E", "text": "vase"}], "stem": "The flower grew tall to compete for sunlight, what did its neighbor do?"}}
{"answerKey": "E", "id": "c908d7c4633c5e6add9463bdd47cb27e", "question": {"question_concept": "driving to work", "choices": [{"label": "A", "text": "boredom"}, {"label": "B", "text": "happiness"}, {"label": "C", "text": "transportation cost"}, {"label": "D", "text": "getting there"}, {"label": "E", "text": "road rage"}], "stem": "If while driving to work another car makes a careless maneuver, what emotion might you feel?"}}
{"answerKey": "D", "id": "7e522a60756f854c5331125f998bc36b", "question": {"question_concept": "food", "choices": [{"label": "A", "text": "boat"}, {"label": "B", "text": "necessary to live"}, {"label": "C", "text": "edible"}, {"label": "D", "text": "unhealthy"}, {"label": "E", "text": "kitchen"}], "stem": "What kind of food makes someone sick?"}}
{"answerKey": "A", "id": "f4a75bf3f115b826a8097edfd0ff2781", "question": {"question_concept": "triangle", "choices": [{"label": "A", "text": "three vertices"}, {"label": "B", "text": "point"}, {"label": "C", "text": "3 sides"}, {"label": "D", "text": "three sides"}, {"label": "E", "text": "math book"}], "stem": "Where would you find the sharpest parts of a triangle?"}}
{"answerKey": "A", "id": "02f43014a135cbd39f23b044c99de96e", "question": {"question_concept": "automobile", "choices": [{"label": "A", "text": "exit ramp"}, {"label": "B", "text": "garage"}, {"label": "C", "text": "driveway"}, {"label": "D", "text": "repair shop"}, {"label": "E", "text": "stop light"}], "stem": "How might a automobile get off a freeway?"}}
{"answerKey": "E", "id": "8cf478192696744b3427f7c109019af5", "question": {"question_concept": "going to bed", "choices": [{"label": "A", "text": "bad dreams"}, {"label": "B", "text": "a good nights sleep"}, {"label": "C", "text": "rest"}, {"label": "D", "text": "sleepiness"}, {"label": "E", "text": "get pregnant"}], "stem": "What does going to bed with your spouse for sex lead to?"}}
{"answerKey": "C", "id": "4ccd43cdff044bc4c644dadff1ff1e0b", "question": {"question_concept": "surprising", "choices": [{"label": "A", "text": "surprise"}, {"label": "B", "text": "fight"}, {"label": "C", "text": "annoyance"}, {"label": "D", "text": "might scare"}, {"label": "E", "text": "irritated"}], "stem": "What would it be if they get a surprising show over and over?"}}
{"answerKey": "E", "id": "7b7941b883328ad39048d4dfb1eb5623", "question": {"question_concept": "competing", "choices": [{"label": "A", "text": "pressure"}, {"label": "B", "text": "trying harder"}, {"label": "C", "text": "put harder"}, {"label": "D", "text": "enemies"}, {"label": "E", "text": "death"}], "stem": "Sally thought that competing wasn't worth the risk. If she pushed more what might happen?"}}
{"answerKey": "C", "id": "008b7ba0c039f6d0d542c6c90aae173c", "question": {"question_concept": "bathroom", "choices": [{"label": "A", "text": "eating food"}, {"label": "B", "text": "public place"}, {"label": "C", "text": "race track"}, {"label": "D", "text": "at hotel"}, {"label": "E", "text": "public building"}], "stem": "John is sitting in a toilet stall in a bathroom, outside he can hear cars going around in circles.  What is the function of the place he is most likely at?"}}
{"answerKey": "B", "id": "4c968fa73699a38639ba3ffa1745bc21", "question": {"question_concept": "seats", "choices": [{"label": "A", "text": "park"}, {"label": "B", "text": "show"}, {"label": "C", "text": "auditorium"}, {"label": "D", "text": "movies"}, {"label": "E", "text": "rest area"}], "stem": "What event might one buy tickets for seats?"}}
{"answerKey": "A", "id": "b1d5cdbf8ef7b3954a6a352bd4df5866", "question": {"question_concept": "merchant", "choices": [{"label": "A", "text": "mall"}, {"label": "B", "text": "business"}, {"label": "C", "text": "store"}, {"label": "D", "text": "sale"}, {"label": "E", "text": "sell goods"}], "stem": "The merchant wanted to open in a high-traffic space, where did he rent space?"}}
{"answerKey": "A", "id": "c3bc395561113c96ec43afd715da5061", "question": {"question_concept": "copulating", "choices": [{"label": "A", "text": "babies"}, {"label": "B", "text": "odors"}, {"label": "C", "text": "sadness"}, {"label": "D", "text": "rapport"}, {"label": "E", "text": "ejaculation"}], "stem": "The newlyweds began copulating their marriage, they wanted many what?"}}
{"answerKey": "E", "id": "d0bd5b5ee7319d1c4727e38d429dd54e", "question": {"question_concept": "planet", "choices": [{"label": "A", "text": "writing"}, {"label": "B", "text": "universe"}, {"label": "C", "text": "outer space"}, {"label": "D", "text": "outerspace"}, {"label": "E", "text": "orbit"}], "stem": "How does a planet usually move around the sun?"}}
{"answerKey": "C", "id": "81f5e741d970578867495ceea5a0c848", "question": {"question_concept": "talking", "choices": [{"label": "A", "text": "having a concert."}, {"label": "B", "text": "cough"}, {"label": "C", "text": "sharing of ideas"}, {"label": "D", "text": "speak"}, {"label": "E", "text": "sneeze"}], "stem": "When a group of people are talking at work they might be doing what?"}}
{"answerKey": "D", "id": "6714593a8d1f8ae39930c1f0316e9ffc", "question": {"question_concept": "punching", "choices": [{"label": "A", "text": "fists"}, {"label": "B", "text": "hitting"}, {"label": "C", "text": "boxing gloves"}, {"label": "D", "text": "anger"}, {"label": "E", "text": "hands"}], "stem": "What emotion leads to punching?"}}
{"answerKey": "A", "id": "75cb55aec7e64f592c01eee5d4578dcd", "question": {"question_concept": "also", "choices": [{"label": "A", "text": "differently"}, {"label": "B", "text": "otherwise"}, {"label": "C", "text": "expensive"}, {"label": "D", "text": "only"}, {"label": "E", "text": "mere"}], "stem": "They kept doing things the same, she suggested they also try doing things what?"}}
{"answerKey": "B", "id": "0b30831fb1862bc62339bdf930cbc447", "question": {"question_concept": "shark", "choices": [{"label": "A", "text": "pool hall"}, {"label": "B", "text": "tomales bay"}, {"label": "C", "text": "marine museum"}, {"label": "D", "text": "business"}, {"label": "E", "text": "desert"}], "stem": "Where could you find a shark before it was caught?"}}
{"answerKey": "D", "id": "29c194d032a266a7160bff6f546a4d9d", "question": {"question_concept": "chips", "choices": [{"label": "A", "text": "supermarket"}, {"label": "B", "text": "pantry"}, {"label": "C", "text": "motherboard"}, {"label": "D", "text": "bar"}, {"label": "E", "text": "bar"}], "stem": "Where is one likely to find poker chips?"}}
{"answerKey": "D", "id": "ea33206992fb7ad1c3476e9673bb4a9c", "question": {"question_concept": "dance", "choices": [{"label": "A", "text": "falling down"}, {"label": "B", "text": "trip"}, {"label": "C", "text": "fall down"}, {"label": "D", "text": "move around"}, {"label": "E", "text": "celebrate"}], "stem": "Dance can be elegant and specific, or you can just have fun and what?"}}
{"answerKey": "E", "id": "2b7dd91da5dde1560ace2cd82af926de", "question": {"question_concept": "bass fiddle", "choices": [{"label": "A", "text": "jazz band"}, {"label": "B", "text": "string quartet"}, {"label": "C", "text": "group band"}, {"label": "D", "text": "nursery rhyme"}, {"label": "E", "text": "music store"}], "stem": "Where can one obtain a bass fiddle?"}}
{"answerKey": "A", "id": "eb50f536830ba18ab987c7ff652e2aba", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "mentally challenged"}, {"label": "B", "text": "have choice"}, {"label": "C", "text": "lots of space"}, {"label": "D", "text": "hungry"}, {"label": "E", "text": "acknowledgment"}], "stem": "Why does having a disability sometimes making academic tasks hard for a person?"}}
{"answerKey": "B", "id": "6bc3ebcfd04965c25bde71339955746c", "question": {"question_concept": "playing games", "choices": [{"label": "A", "text": "winning"}, {"label": "B", "text": "learning"}, {"label": "C", "text": "losing"}, {"label": "D", "text": "fatigue"}, {"label": "E", "text": "skill"}], "stem": "What is the purpose of playing games for children?"}}
{"answerKey": "B", "id": "163898952cb6baf3a6440696e1352e86", "question": {"question_concept": "killing people", "choices": [{"label": "A", "text": "feelings of guilt"}, {"label": "B", "text": "prison sentence"}, {"label": "C", "text": "terrible"}, {"label": "D", "text": "encouragement"}, {"label": "E", "text": "die"}], "stem": "If for some reason you were to start killing people, what would you be likely to receive?"}}
{"answerKey": "D", "id": "aa984e2b487d08889bc0c73bab5ac945", "question": {"question_concept": "surprising", "choices": [{"label": "A", "text": "joy"}, {"label": "B", "text": "fight"}, {"label": "C", "text": "frightened"}, {"label": "D", "text": "humor"}, {"label": "E", "text": "laughter"}], "stem": "If someone laughs after surprising them they have a good sense of what?"}}
{"answerKey": "C", "id": "d78baca23e0a636a8961e17119047e63", "question": {"question_concept": "soccer field", "choices": [{"label": "A", "text": "town"}, {"label": "B", "text": "beach"}, {"label": "C", "text": "park"}, {"label": "D", "text": "near"}, {"label": "E", "text": "outside"}], "stem": "People played a variety of games in the soccer field.  It was the closest thing they had to what?"}}
{"answerKey": "C", "id": "ac6378b5e8462dc1bde1155d706213d8", "question": {"question_concept": "school cafeteria", "choices": [{"label": "A", "text": "high school"}, {"label": "B", "text": "canteen"}, {"label": "C", "text": "polytechnic"}, {"label": "D", "text": "large room"}, {"label": "E", "text": "all kinds of schools"}], "stem": "What is likely to have a better school cafeteria?"}}
{"answerKey": "D", "id": "c1aebf059c5102f4e773f7fe4afe13f0", "question": {"question_concept": "judging", "choices": [{"label": "A", "text": "objectivity"}, {"label": "B", "text": "knowing yourself"}, {"label": "C", "text": "experience"}, {"label": "D", "text": "ignorance"}, {"label": "E", "text": "introduction"}], "stem": "When someone has little knowledge and is judging someone they are considered what?"}}
{"answerKey": "C", "id": "1017807310a25d3ea4a4ec305e91cba3", "question": {"question_concept": "get in shape", "choices": [{"label": "A", "text": "sweating"}, {"label": "B", "text": "excercise"}, {"label": "C", "text": "work out"}, {"label": "D", "text": "video"}, {"label": "E", "text": "swim"}], "stem": "She wanted to get in shape, but she couldn't stay focused on the hour long what?"}}
{"answerKey": "E", "id": "7192c9f5c513aac9042bad595ff5af9f", "question": {"question_concept": "have fun", "choices": [{"label": "A", "text": "spontaneous"}, {"label": "B", "text": "stop working"}, {"label": "C", "text": "pay for"}, {"label": "D", "text": "do like"}, {"label": "E", "text": "do enjoy"}], "stem": "When you do something and have fun, its something you?"}}
{"answerKey": "D", "id": "7c05e8d5a057085455eea243fbd1cd90", "question": {"question_concept": "salesman", "choices": [{"label": "A", "text": "traveling to chicago"}, {"label": "B", "text": "get fired"}, {"label": "C", "text": "books"}, {"label": "D", "text": "sell products"}, {"label": "E", "text": "service account"}], "stem": "What is a salesman responsible to do at work?"}}
{"answerKey": "A", "id": "3cb91a71a6567da870eedf37becc97ef", "question": {"question_concept": "going jogging", "choices": [{"label": "A", "text": "feel better"}, {"label": "B", "text": "feel pride"}, {"label": "C", "text": "sweating"}, {"label": "D", "text": "ocean"}, {"label": "E", "text": "arthritis"}], "stem": "How does going jogging generally affect one's self esteem?"}}
{"answerKey": "A", "id": "9b4bbf3c4d24ecdb4b27320afb706808", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "bus depot"}, {"label": "B", "text": "end of line"}, {"label": "C", "text": "opera"}, {"label": "D", "text": "neighbor's house"}, {"label": "E", "text": "meeting"}], "stem": "Where would you find people standing in a line outside?"}}
{"answerKey": "D", "id": "43df3a316880d8bab346c06bd43b94dd", "question": {"question_concept": "committing perjury", "choices": [{"label": "A", "text": "crime"}, {"label": "B", "text": "disrespect judge"}, {"label": "C", "text": "embarrassment"}, {"label": "D", "text": "lie"}, {"label": "E", "text": "indictment"}], "stem": "If you are committing perjury you have done what while under oath?"}}
{"answerKey": "A", "id": "858a5eaa587fe0e266722228671a6bd1", "question": {"question_concept": "ficus", "choices": [{"label": "A", "text": "dictionary"}, {"label": "B", "text": "apartment"}, {"label": "C", "text": "libary"}, {"label": "D", "text": "middle east"}, {"label": "E", "text": "arboretum"}], "stem": "Where can you find the meaning of \"ficus\"?"}}
{"answerKey": "B", "id": "34005ef0caafefc8585c9fcd50e94557", "question": {"question_concept": "buying products", "choices": [{"label": "A", "text": "debt"}, {"label": "B", "text": "economic boom"}, {"label": "C", "text": "being able to use"}, {"label": "D", "text": "disagreements"}, {"label": "E", "text": "trading"}], "stem": "When are people buying products more?"}}
{"answerKey": "D", "id": "f61d83f90b92a8d537989e55ee70542d", "question": {"question_concept": "buildings", "choices": [{"label": "A", "text": "large city"}, {"label": "B", "text": "small"}, {"label": "C", "text": "eat cake"}, {"label": "D", "text": "university"}, {"label": "E", "text": "town"}], "stem": "The buildings were intended to not have residential kitchens in them, what were they designed for?"}}
{"answerKey": "B", "id": "3bf06235a537adc9d85431846595b800", "question": {"question_concept": "animals", "choices": [{"label": "A", "text": "tails"}, {"label": "B", "text": "bones"}, {"label": "C", "text": "eyes"}, {"label": "D", "text": "heads"}, {"label": "E", "text": "bodies"}], "stem": "Animals come in all types, some fly thanks to their lightweight hollow what?"}}
{"answerKey": "E", "id": "79ec11d8072ce42779adfe0a19bd5374", "question": {"question_concept": "pretend", "choices": [{"label": "A", "text": "people believe"}, {"label": "B", "text": "daydreams"}, {"label": "C", "text": "transcendentalism"}, {"label": "D", "text": "laughter"}, {"label": "E", "text": "religion"}], "stem": "The child felt like it was all pretend, he didn't understand what?"}}
{"answerKey": "D", "id": "2982d0eae1bf880f5930341af7665716", "question": {"question_concept": "lake", "choices": [{"label": "A", "text": "michigan"}, {"label": "B", "text": "new york"}, {"label": "C", "text": "new york"}, {"label": "D", "text": "mountains"}, {"label": "E", "text": "countryside"}], "stem": "Where is a lake likely to be glacial?"}}
{"answerKey": "E", "id": "ba9132ebf2bc3ad21e6a0631dc4e0a77", "question": {"question_concept": "grape", "choices": [{"label": "A", "text": "field"}, {"label": "B", "text": "restaurant"}, {"label": "C", "text": "salad"}, {"label": "D", "text": "market"}, {"label": "E", "text": "food store"}], "stem": "They needed grape juice for their party, they went to buy it and other snacks at the what?"}}
{"answerKey": "C", "id": "d06de16a4aaeaef32b398c1213257b4a", "question": {"question_concept": "people", "choices": [{"label": "A", "text": "believe in god"}, {"label": "B", "text": "smoke marijuana"}, {"label": "C", "text": "desire to travel"}, {"label": "D", "text": "use weapons"}, {"label": "E", "text": "throw away"}], "stem": "Why do some people get passports and go to different locations?"}}
{"answerKey": "D", "id": "eee9476bf29498b7d74b043afe316fc6", "question": {"question_concept": "apple tree", "choices": [{"label": "A", "text": "south africa"}, {"label": "B", "text": "sunshine"}, {"label": "C", "text": "new york"}, {"label": "D", "text": "bloom"}, {"label": "E", "text": "trunk"}], "stem": "Where do apples form on an apple tree?"}}
{"answerKey": "D", "id": "a85441d6a0e3f871d81a9f19b31360b7", "question": {"question_concept": "nightclub", "choices": [{"label": "A", "text": "manhattan"}, {"label": "B", "text": "drink and dance"}, {"label": "C", "text": "alcohol"}, {"label": "D", "text": "major city"}, {"label": "E", "text": "downtown area"}], "stem": "Where areas are there likely to be many nightclubs?"}}
{"answerKey": "E", "id": "f11a2975898033893d6a38f75d791fdf", "question": {"question_concept": "machines", "choices": [{"label": "A", "text": "fail to work"}, {"label": "B", "text": "perform work"}, {"label": "C", "text": "answering questions"}, {"label": "D", "text": "see work"}, {"label": "E", "text": "fly"}], "stem": "What can machines do that humans cannot?"}}
{"answerKey": "C", "id": "a2977fd575faba162d04a490dabd1b9b", "question": {"question_concept": "dead", "choices": [{"label": "A", "text": "moving"}, {"label": "B", "text": "working"}, {"label": "C", "text": "breathing"}, {"label": "D", "text": "alive"}, {"label": "E", "text": "deadworks"}], "stem": "What does someone stop doing when being dead?"}}
{"answerKey": "D", "id": "cd39e442204d3edf7acc185fd59c8a44", "question": {"question_concept": "linen closet", "choices": [{"label": "A", "text": "house"}, {"label": "B", "text": "home"}, {"label": "C", "text": "pool house"}, {"label": "D", "text": "hallway"}, {"label": "E", "text": "bedroom"}], "stem": "The place where my linen closet is really needs repainting a light color as it only has one overhead light."}}
{"answerKey": "A", "id": "c77e1039d78cdff197a370fcda0f2b9f", "question": {"question_concept": "music", "choices": [{"label": "A", "text": "skate"}, {"label": "B", "text": "listen"}, {"label": "C", "text": "opera"}, {"label": "D", "text": "opera"}, {"label": "E", "text": "relax"}], "stem": "Punk rock music is an important part of what action sport?"}}
{"answerKey": "D", "id": "f537f6bb8527724e0b1e1c1051326cd5", "question": {"question_concept": "mouse", "choices": [{"label": "A", "text": "kitchen"}, {"label": "B", "text": "cook"}, {"label": "C", "text": "computer lab"}, {"label": "D", "text": "old barn"}, {"label": "E", "text": "research laboratory"}], "stem": "Where might a mouse be found to make it country?"}}
{"answerKey": "A", "id": "d3b145911a76fd6fbe9a23ab027be024", "question": {"question_concept": "bird", "choices": [{"label": "A", "text": "forest"}, {"label": "B", "text": "nest"}, {"label": "C", "text": "roof"}, {"label": "D", "text": "leaves"}, {"label": "E", "text": "sky"}], "stem": "Where is a bird likely to make it's home?"}}
{"answerKey": "B", "id": "dc2fa76467ff342abdb4cf142f92dddd", "question": {"question_concept": "hunger", "choices": [{"label": "A", "text": "eat hamburger"}, {"label": "B", "text": "eat breakfast"}, {"label": "C", "text": "open fridge"}, {"label": "D", "text": "buy food"}, {"label": "E", "text": "cook dinner"}], "stem": "When a person suffers from hunger early in the day what do they do?"}}
{"answerKey": "D", "id": "246249cd7976358051a9811ff9c30736", "question": {"question_concept": "express information", "choices": [{"label": "A", "text": "may disagree"}, {"label": "B", "text": "close mouth"}, {"label": "C", "text": "write down"}, {"label": "D", "text": "talk"}, {"label": "E", "text": "eyes"}], "stem": "How would you express information if you do not have a pen or pencil?"}}
{"answerKey": "B", "id": "32be8cbc1b5a967310bcab8b80563481", "question": {"question_concept": "everyone", "choices": [{"label": "A", "text": "looking for love"}, {"label": "B", "text": "afraid of"}, {"label": "C", "text": "good at"}, {"label": "D", "text": "make pet"}, {"label": "E", "text": "different"}], "stem": "What does everyone feel of monsters?"}}
{"answerKey": "B", "id": "ad769851a59375865607452d3bf2a45d", "question": {"question_concept": "examine thing", "choices": [{"label": "A", "text": "buy"}, {"label": "B", "text": "learn about"}, {"label": "C", "text": "buy"}, {"label": "D", "text": "complex"}, {"label": "E", "text": "interesting"}], "stem": "Why does someone want to examine thing closely?"}}
{"answerKey": "B", "id": "5ea6b94d1a911365b06cf776919413e8", "question": {"question_concept": "drinking alcohol", "choices": [{"label": "A", "text": "have fun"}, {"label": "B", "text": "intoxication"}, {"label": "C", "text": "vomiting"}, {"label": "D", "text": "drinking more alcohol"}, {"label": "E", "text": "nausea"}], "stem": "What does \tdrinking alcohol lead to?"}}
{"answerKey": "D", "id": "820df15b615d221e38a71fcc44461085", "question": {"question_concept": "bass clarinet", "choices": [{"label": "A", "text": "opera house"}, {"label": "B", "text": "school band"}, {"label": "C", "text": "music store"}, {"label": "D", "text": "orchestra"}, {"label": "E", "text": "bathroom stall"}], "stem": "Where would your hear a bass clarinet along side other wood wind instruments?"}}
{"answerKey": "E", "id": "0a4a00ba435397c4a0496dd2c2426be7", "question": {"question_concept": "little", "choices": [{"label": "A", "text": "much"}, {"label": "B", "text": "plenty"}, {"label": "C", "text": "more"}, {"label": "D", "text": "big"}, {"label": "E", "text": "lot of"}], "stem": "What is the opposite of a little of something?"}}
{"answerKey": "C", "id": "a7f29f4aebe0e3bcb77038fea71bf28c", "question": {"question_concept": "pure", "choices": [{"label": "A", "text": "dirty"}, {"label": "B", "text": "tarnish"}, {"label": "C", "text": "corrupt"}, {"label": "D", "text": "contaminated"}, {"label": "E", "text": "applied"}], "stem": "The princess was pure, the evil wizard wished to do what to her?"}}
{"answerKey": "C", "id": "ecd32cc0c17d4738a27bba3399f04591", "question": {"question_concept": "paper", "choices": [{"label": "A", "text": "notebook"}, {"label": "B", "text": "copy machine"}, {"label": "C", "text": "stock certificate"}, {"label": "D", "text": "ream"}, {"label": "E", "text": "thumb drive"}], "stem": "The piece of paper was worth a lot of money, it was an old Apple Inc what?"}}
{"answerKey": "D", "id": "8b2af2d865b7dc500427786c846eacaf", "question": {"question_concept": "motion", "choices": [{"label": "A", "text": "being still"}, {"label": "B", "text": "silence"}, {"label": "C", "text": "stationary"}, {"label": "D", "text": "stillness"}, {"label": "E", "text": "standing still"}], "stem": "During the winter hunt he could hear every motion in the woods, this was because of the what of everything?"}}
{"answerKey": "B", "id": "383282aace64dd49138bac2392f8b38e", "question": {"question_concept": "radio", "choices": [{"label": "A", "text": "trunk"}, {"label": "B", "text": "bedroom"}, {"label": "C", "text": "diner"}, {"label": "D", "text": "space shuttle"}, {"label": "E", "text": "shop"}], "stem": "If a car-less person want to listen to talk radio in private, where might they listen to it?"}}
{"answerKey": "B", "id": "eaf6838d29bcd4ebf408da2f75aa65c3", "question": {"question_concept": "world", "choices": [{"label": "A", "text": "diverse"}, {"label": "B", "text": "round"}, {"label": "C", "text": "square"}, {"label": "D", "text": "orange"}, {"label": "E", "text": "complicated"}], "stem": "Billy was an astronaut.  When he looked at the world from space, how did it look?"}}
{"answerKey": "D", "id": "7c8bc9c0e56389eef033bca40c88c151", "question": {"question_concept": "fireplace", "choices": [{"label": "A", "text": "big house"}, {"label": "B", "text": "train"}, {"label": "C", "text": "cabin"}, {"label": "D", "text": "living room"}, {"label": "E", "text": "home"}], "stem": "Where is a good place to have a fireplace in a house?"}}
{"answerKey": "B", "id": "ca60a46c9007e4b6213f50bfb5342fdd", "question": {"question_concept": "cat", "choices": [{"label": "A", "text": "trouble"}, {"label": "B", "text": "dog's mouth"}, {"label": "C", "text": "backyard"}, {"label": "D", "text": "nature"}, {"label": "E", "text": "home"}], "stem": "If you own a cat where is the last place you'd want to find it?"}}
{"answerKey": "B", "id": "f50209f04d11690d7c8f30e29b35ff02", "question": {"question_concept": "kosher deli", "choices": [{"label": "A", "text": "los angeles"}, {"label": "B", "text": "food court"}, {"label": "C", "text": "new york city"}, {"label": "D", "text": "jewish community"}, {"label": "E", "text": "jewish neighborhoods"}], "stem": "Where would you find a kosher deli along side a number of different places to eat?"}}
{"answerKey": "A", "id": "d725f1c2e150a3221de31612123f3f46", "question": {"question_concept": "going to market", "choices": [{"label": "A", "text": "buy food"}, {"label": "B", "text": "see other people"}, {"label": "C", "text": "buying vegetables"}, {"label": "D", "text": "buy a fat pig"}, {"label": "E", "text": "traveling"}], "stem": "What do you do when you're going to market?"}}
{"answerKey": "C", "id": "f7735d721dfdc94621154951d4eaa4cf", "question": {"question_concept": "discovering truth", "choices": [{"label": "A", "text": "conclusion"}, {"label": "B", "text": "pain"}, {"label": "C", "text": "happiness"}, {"label": "D", "text": "relief"}, {"label": "E", "text": "boring"}], "stem": "She feared that she had cancer, but upon discovering truth that she hadn't, what was her attitude toward life?"}}
{"answerKey": "B", "id": "eaf980db7e945b1cf6d648fa55ddcb5e", "question": {"question_concept": "having fun", "choices": [{"label": "A", "text": "smiling"}, {"label": "B", "text": "pleasure"}, {"label": "C", "text": "hurt"}, {"label": "D", "text": "injuries"}, {"label": "E", "text": "laughter"}], "stem": "What is the feeling of one having fun?"}}
{"answerKey": "C", "id": "8bbfe8cd056d612e9d3190f278bef287", "question": {"question_concept": "table", "choices": [{"label": "A", "text": "conference"}, {"label": "B", "text": "neighbor's house"}, {"label": "C", "text": "rug"}, {"label": "D", "text": "net"}, {"label": "E", "text": "card room"}], "stem": "If I keep getting crumbs under my table, what should I put under it?"}}
{"answerKey": "B", "id": "aa7c4c351cf8d59792aa68e3de339db4", "question": {"question_concept": "dying", "choices": [{"label": "A", "text": "unable to work"}, {"label": "B", "text": "born again"}, {"label": "C", "text": "change of color"}, {"label": "D", "text": "dead"}, {"label": "E", "text": "no longer exist"}], "stem": "Christians believe you will go to heaven if you're what?"}}
{"answerKey": "A", "id": "23df3bac9cfcb156f4cfd8a05f21c5e2", "question": {"question_concept": "surf", "choices": [{"label": "A", "text": "wipe out"}, {"label": "B", "text": "enjoy yourself"}, {"label": "C", "text": "start fighting"}, {"label": "D", "text": "get wet"}, {"label": "E", "text": "drown"}], "stem": "James loved to surf but he wasn't good at it. He would always do what?"}}
{"answerKey": "E", "id": "d21777d771dc6fd08e769d378651817e", "question": {"question_concept": "key", "choices": [{"label": "A", "text": "front door"}, {"label": "B", "text": "turn lock"}, {"label": "C", "text": "solution to problem"}, {"label": "D", "text": "install"}, {"label": "E", "text": "open doors"}], "stem": "Sarah gave her brother a guy to her home.  While she was gone, he used it to do what?"}}
{"answerKey": "E", "id": "611a4cc0e288b8a11afa923f48cb2ab4", "question": {"question_concept": "mammoth", "choices": [{"label": "A", "text": "boscage"}, {"label": "B", "text": "forest"}, {"label": "C", "text": "prehistory"}, {"label": "D", "text": "prehistoric times"}, {"label": "E", "text": "ancient times"}], "stem": "When did mammoth's live?"}}
{"answerKey": "B", "id": "8e7941ce31996ca83cc0a68f7313c96d", "question": {"question_concept": "killing people", "choices": [{"label": "A", "text": "murder"}, {"label": "B", "text": "remorse"}, {"label": "C", "text": "religious"}, {"label": "D", "text": "retaliation"}, {"label": "E", "text": "anguish"}], "stem": "After killing people, the murderer went to church after feeling what?"}}
{"answerKey": "B", "id": "ea02772e27f5bd40eced3b65e8c6427f", "question": {"question_concept": "committing suicide", "choices": [{"label": "A", "text": "die"}, {"label": "B", "text": "interruption"}, {"label": "C", "text": "bleed"}, {"label": "D", "text": "hatred"}, {"label": "E", "text": "dying"}], "stem": "What might result in an unsuccessful suicide attempt?"}}
{"answerKey": "B", "id": "de54d03e69d9765872f95ff06ed21499", "question": {"question_concept": "buying products", "choices": [{"label": "A", "text": "joy"}, {"label": "B", "text": "disagreements"}, {"label": "C", "text": "agony"}, {"label": "D", "text": "pleasure"}, {"label": "E", "text": "owning"}], "stem": "What can happen if you are buying products that someone else does not want you to buy?"}}
{"answerKey": "D", "id": "b231a732a3fdf0621391e7e385f8d651", "question": {"question_concept": "getting", "choices": [{"label": "A", "text": "show appreciation"}, {"label": "B", "text": "asking for"}, {"label": "C", "text": "exchanging"}, {"label": "D", "text": "say thank"}, {"label": "E", "text": "smile"}], "stem": "The child was getting many gifts for his birthday, his father reminded him to do what after opening each one?"}}
{"answerKey": "C", "id": "b9121c3228f961c5ad68958c702cd94b", "question": {"question_concept": "grass", "choices": [{"label": "A", "text": "rest area"}, {"label": "B", "text": "desert"}, {"label": "C", "text": "state park"}, {"label": "D", "text": "fairgrounds"}, {"label": "E", "text": "soccer game"}], "stem": "Bob stands in the grass surrounded by trees and nature, where is Bob?"}}
{"answerKey": "E", "id": "4015ab002ff8c233d1c7ef26f5156b88", "question": {"question_concept": "horse", "choices": [{"label": "A", "text": "circus"}, {"label": "B", "text": "in kentucky"}, {"label": "C", "text": "western movie"}, {"label": "D", "text": "central park"}, {"label": "E", "text": "state fair"}], "stem": "Bart entered his horse into the contest.  Where did he do this?"}}
{"answerKey": "A", "id": "0197ade3bb26d163ab2e284c960c626f", "question": {"question_concept": "snowflake", "choices": [{"label": "A", "text": "cloud"}, {"label": "B", "text": "snow storm"}, {"label": "C", "text": "billow"}, {"label": "D", "text": "air"}, {"label": "E", "text": "snowstorm"}], "stem": "From where does a snowflake form?"}}
{"answerKey": "D", "id": "a90f9197a13c64089c9ba95bcba275ad", "question": {"question_concept": "drill", "choices": [{"label": "A", "text": "basement"}, {"label": "B", "text": "work shop"}, {"label": "C", "text": "tool shed"}, {"label": "D", "text": "repair shop"}, {"label": "E", "text": "store room"}], "stem": "All the power tools like the drill used for fixing cars made for a very loud workplace where?"}}
{"answerKey": "E", "id": "684204df916cc58d47293960f9c6ed9f", "question": {"question_concept": "applying for job", "choices": [{"label": "A", "text": "working hard"}, {"label": "B", "text": "frustration"}, {"label": "C", "text": "rejection"}, {"label": "D", "text": "defeat"}, {"label": "E", "text": "stress"}], "stem": "Applying for a job can make someone feel what sort of emotion, even if they get it?"}}
{"answerKey": "D", "id": "a2aa95861ef74bf1ecfc55db505e3982", "question": {"question_concept": "weasel", "choices": [{"label": "A", "text": "chicken coop"}, {"label": "B", "text": "beach"}, {"label": "C", "text": "fairytale"}, {"label": "D", "text": "great outdoors"}, {"label": "E", "text": "corn fields"}], "stem": "A farmer sees a weasel in the woods, where is the farmer?"}}
{"answerKey": "D", "id": "8555dd9667d010018961a2f7d1c22704", "question": {"question_concept": "pebble", "choices": [{"label": "A", "text": "manual"}, {"label": "B", "text": "lake"}, {"label": "C", "text": "aquarium"}, {"label": "D", "text": "pond"}, {"label": "E", "text": "playground"}], "stem": "He picked up the perfect pebble, he planned to skip it across the entire small what?"}}
{"answerKey": "B", "id": "84a761f516efce04ab27d7ca8dd25255", "question": {"question_concept": "traveling", "choices": [{"label": "A", "text": "going somewhere"}, {"label": "B", "text": "exhilarating"}, {"label": "C", "text": "diarrhea"}, {"label": "D", "text": "relocation"}, {"label": "E", "text": "exhausting"}], "stem": "Traveling from new place to new place is likely to be what?"}}
{"answerKey": "C", "id": "45a6becd307342669d9d17474e50b97a", "question": {"question_concept": "turkey", "choices": [{"label": "A", "text": "middle east"}, {"label": "B", "text": "oven"}, {"label": "C", "text": "balkan peninsula"}, {"label": "D", "text": "provide meat"}, {"label": "E", "text": "asia minor"}], "stem": "Turkey only has a small northern part of their country located in part of the what?"}}
{"answerKey": "E", "id": "c509c499bace6de324b39c0d4d0c30fa", "question": {"question_concept": "shopping bag", "choices": [{"label": "A", "text": "supermarket"}, {"label": "B", "text": "home"}, {"label": "C", "text": "mart"}, {"label": "D", "text": "obesity"}, {"label": "E", "text": "closet"}], "stem": "Where might someone store a reusable shopping bag?"}}
{"answerKey": "B", "id": "77ddc9134bb27f9962aa2ed5ec5a5ef9", "question": {"question_concept": "fun", "choices": [{"label": "A", "text": "fairgrounds"}, {"label": "B", "text": "watching television"}, {"label": "C", "text": "tired"}, {"label": "D", "text": "enjoyable"}, {"label": "E", "text": "friend's house"}], "stem": "How could you have fun by yourself with no one around you?"}}
{"answerKey": "E", "id": "715583129369c0c5c9f499c93a1c095e", "question": {"question_concept": "potato", "choices": [{"label": "A", "text": "vegans"}, {"label": "B", "text": "kitchen cupboard"}, {"label": "C", "text": "restaurants"}, {"label": "D", "text": "chicken"}, {"label": "E", "text": "maryland"}], "stem": "The potato might be the official vegetable of what?"}}
{"answerKey": "B", "id": "a478e8b7c049781574f7fbb11ba1eec0", "question": {"question_concept": "sky", "choices": [{"label": "A", "text": "planetarium"}, {"label": "B", "text": "outdoors"}, {"label": "C", "text": "atmosphere"}, {"label": "D", "text": "night"}, {"label": "E", "text": "photo"}], "stem": "Where is the sky most beautiful?"}}
{"answerKey": "B", "id": "f427f9de6bf580314531baf86de8acbc", "question": {"question_concept": "section", "choices": [{"label": "A", "text": "slide"}, {"label": "B", "text": "citrus"}, {"label": "C", "text": "band"}, {"label": "D", "text": "orchestra"}, {"label": "E", "text": "coconut"}], "stem": "What type of fruit is easily broken in to sections?"}}
{"answerKey": "B", "id": "0f7425ecbe369bf41a230aab92d84132", "question": {"question_concept": "running twenty six miles", "choices": [{"label": "A", "text": "excruciating pain"}, {"label": "B", "text": "passing out"}, {"label": "C", "text": "death"}, {"label": "D", "text": "drunk"}, {"label": "E", "text": "exhaustion"}], "stem": "Marathoners feel fatigued after running twenty six miles, but some that have pushed them self too hard might be prone to what?"}}
{"answerKey": "D", "id": "c872c08a95dd28a16479b76f240a4ad5", "question": {"question_concept": "driving car", "choices": [{"label": "A", "text": "transportation"}, {"label": "B", "text": "pollution"}, {"label": "C", "text": "stress"}, {"label": "D", "text": "death"}, {"label": "E", "text": "go somewhere"}], "stem": "Billy liked driving cars.  He was good at it.  But he was rattled ever since his father experienced what?"}}
{"answerKey": "B", "id": "08d908ed723f813574992195d61386a2", "question": {"question_concept": "cold", "choices": [{"label": "A", "text": "stay in bed"}, {"label": "B", "text": "light fire"}, {"label": "C", "text": "freezer"}, {"label": "D", "text": "lay on ice"}, {"label": "E", "text": "spit"}], "stem": "I am cold, what should I do to stay warm?"}}
{"answerKey": "E", "id": "5365fd00ef8cec62ee5685e246a939db", "question": {"question_concept": "copulating", "choices": [{"label": "A", "text": "intense pleasure"}, {"label": "B", "text": "ejaculation"}, {"label": "C", "text": "period of rest"}, {"label": "D", "text": "enjoyment"}, {"label": "E", "text": "skin irritation"}], "stem": "Copulating with the wrong partner may be ill advised, many diseases can be transferred that can cause different types of what?"}}
{"answerKey": "D", "id": "5649bd90dbb57e223fd843b7a4563a0f", "question": {"question_concept": "audience", "choices": [{"label": "A", "text": "cinema"}, {"label": "B", "text": "theatre"}, {"label": "C", "text": "movies"}, {"label": "D", "text": "show"}, {"label": "E", "text": "hockey game"}], "stem": "What do audiences clap for?"}}
{"answerKey": "B", "id": "0a2195ae8d4706abc5721578c9991466", "question": {"question_concept": "balalaika", "choices": [{"label": "A", "text": "orchestra"}, {"label": "B", "text": "music store"}, {"label": "C", "text": "buy music"}, {"label": "D", "text": "make music"}, {"label": "E", "text": "symphony"}], "stem": "Where would you get a balalaika if you do not have one?"}}
{"answerKey": "B", "id": "5d15989039d46156b417c149728591de", "question": {"question_concept": "beautiful", "choices": [{"label": "A", "text": "homely"}, {"label": "B", "text": "overcast"}, {"label": "C", "text": "hideous"}, {"label": "D", "text": "overrated"}, {"label": "E", "text": "misshapen"}], "stem": "Hoping for a beautiful day, what did the clouds do that disappointed everyone?"}}
{"answerKey": "A", "id": "6eb57102b44ab74163d8f9821cbdabd0", "question": {"question_concept": "go off strike", "choices": [{"label": "A", "text": "reasonable"}, {"label": "B", "text": "more money"}, {"label": "C", "text": "not go to work"}, {"label": "D", "text": "return to work"}, {"label": "E", "text": "union"}], "stem": "What type of demands to the unions need to be making to go off strike?"}}
{"answerKey": "E", "id": "63861ac5e633db9090704ae315ef6f93", "question": {"question_concept": "stones", "choices": [{"label": "A", "text": "park"}, {"label": "B", "text": "made from rocks"}, {"label": "C", "text": "balloon"}, {"label": "D", "text": "field"}, {"label": "E", "text": "bridge"}], "stem": "The landscaper was carefully arching stones together, he was creating an elaborate what over the creek?"}}
{"answerKey": "D", "id": "8058c566a4f488033d00e6520b17caea", "question": {"question_concept": "happy", "choices": [{"label": "A", "text": "inappropriate"}, {"label": "B", "text": "sadness"}, {"label": "C", "text": "unsatisfied"}, {"label": "D", "text": "unfortunate"}, {"label": "E", "text": "disenchanted"}], "stem": "John was not happy with his marriage. He and his wife drifted apart.     All and all, recent turns could be described as what?"}}
{"answerKey": "E", "id": "57b83653d82b27d32bc39228130f3516", "question": {"question_concept": "light", "choices": [{"label": "A", "text": "darkness"}, {"label": "B", "text": "cumbersome"}, {"label": "C", "text": "obesity"}, {"label": "D", "text": "forceful"}, {"label": "E", "text": "crucial"}], "stem": "The poor girls needed a light to see, what was the relationship between that light and finishing her homework?"}}
{"answerKey": "D", "id": "410f907f817dd7aa8e73291a918d3d86", "question": {"question_concept": "ticket booth", "choices": [{"label": "A", "text": "clerk"}, {"label": "B", "text": "indoors"}, {"label": "C", "text": "movie theater"}, {"label": "D", "text": "venue"}, {"label": "E", "text": "auditorium"}], "stem": "Where would you find a ticket booth and see a concert?"}}
{"answerKey": "E", "id": "506c2dbfe7b00a82bfdf0507a8de88fb", "question": {"question_concept": "superhighway", "choices": [{"label": "A", "text": "europe"}, {"label": "B", "text": "germany"}, {"label": "C", "text": "industrialized country"}, {"label": "D", "text": "city"}, {"label": "E", "text": "america"}], "stem": "Who is not famous for a superhighway with no speed limit?"}}
{"answerKey": "E", "id": "42520bf3f93f8de23670044e019001a3", "question": {"question_concept": "stone", "choices": [{"label": "A", "text": "ocean"}, {"label": "B", "text": "gallbladder"}, {"label": "C", "text": "driveway"}, {"label": "D", "text": "river bed"}, {"label": "E", "text": "creek bed"}], "stem": "The low trickle of water revealed a stone, where was the stone found?"}}
{"answerKey": "D", "id": "5e260e1d96187716888cbd968010bb65", "question": {"question_concept": "salt", "choices": [{"label": "A", "text": "ocean water"}, {"label": "B", "text": "table"}, {"label": "C", "text": "shaker"}, {"label": "D", "text": "neighbor's house"}, {"label": "E", "text": "lake"}], "stem": "Where is the closest place from where you could borrow salt?"}}
{"answerKey": "A", "id": "ed50555f8db2b8f66caf9868dcd7e13b", "question": {"question_concept": "universe", "choices": [{"label": "A", "text": "very old"}, {"label": "B", "text": "infiniverse"}, {"label": "C", "text": "getting younger"}, {"label": "D", "text": "infinite"}, {"label": "E", "text": "real"}], "stem": "No matter what date you put on it, we all know the universe to be what?"}}
{"answerKey": "D", "id": "a8c284637dabc87745a7eb05d4f7fcbc", "question": {"question_concept": "meteor", "choices": [{"label": "A", "text": "republic of ireland"}, {"label": "B", "text": "sky"}, {"label": "C", "text": "orbit"}, {"label": "D", "text": "universe"}, {"label": "E", "text": "school"}], "stem": "A meteor travels through galaxies which are a part of what?"}}
{"answerKey": "C", "id": "5758a0fb686071e95d95b1cfad5299a0", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "ridiculous"}, {"label": "B", "text": "false information"}, {"label": "C", "text": "made fun of"}, {"label": "D", "text": "brain tumor"}, {"label": "E", "text": "bull rider"}], "stem": "What is a person considered a bully known for?"}}
{"answerKey": "D", "id": "d986f17acb3ed19c77e3ca3f98c026b9", "question": {"question_concept": "interest", "choices": [{"label": "A", "text": "see particular program"}, {"label": "B", "text": "see exhibits"}, {"label": "C", "text": "see people play game"}, {"label": "D", "text": "have conversation"}, {"label": "E", "text": "watch film"}], "stem": "She had an interest in the man, what did she want to do with him?"}}
{"answerKey": "E", "id": "4a4f6408fae400ce0beb5bea0f9913e9", "question": {"question_concept": "drug", "choices": [{"label": "A", "text": "nursery"}, {"label": "B", "text": "ghetto"}, {"label": "C", "text": "cupboard"}, {"label": "D", "text": "pharmacy"}, {"label": "E", "text": "medicine cabinet"}], "stem": "Where is a drug kept in a home bathroom?"}}
{"answerKey": "C", "id": "8c655f3a55bde41aad880f138d7a445d", "question": {"question_concept": "sheep", "choices": [{"label": "A", "text": "ram"}, {"label": "B", "text": "lamb"}, {"label": "C", "text": "done"}, {"label": "D", "text": "ram"}, {"label": "E", "text": "wolf"}], "stem": "When cooking sheep meat a lot of people might want to be well?"}}
{"answerKey": "D", "id": "56417ee33b44f0d916bedfb6fd99b0ec", "question": {"question_concept": "chair", "choices": [{"label": "A", "text": "aeroport"}, {"label": "B", "text": "church"}, {"label": "C", "text": "furniture store"}, {"label": "D", "text": "university"}, {"label": "E", "text": "living room"}], "stem": "Where would you sit in a chair while working toward an advanced degree?"}}
{"answerKey": "D", "id": "43fb083962f825ae651d88648bbd2f74", "question": {"question_concept": "farmland", "choices": [{"label": "A", "text": "countryside"}, {"label": "B", "text": "michigan"}, {"label": "C", "text": "north dakota"}, {"label": "D", "text": "farming areas"}, {"label": "E", "text": "illinois"}], "stem": "Farm land makes use of what?"}}
{"answerKey": "E", "id": "aed771629c8dbd0c2587891e98030607", "question": {"question_concept": "applying for job", "choices": [{"label": "A", "text": "offer"}, {"label": "B", "text": "income"}, {"label": "C", "text": "rejection"}, {"label": "D", "text": "hostile"}, {"label": "E", "text": "hope"}], "stem": "A good interview after applying for a job may cause you to feel what?"}}
{"answerKey": "B", "id": "d0a42c8180b4e080aa071dd70fce7e03", "question": {"question_concept": "computers", "choices": [{"label": "A", "text": "economic boom"}, {"label": "B", "text": "advance knowledge"}, {"label": "C", "text": "produce sound"}, {"label": "D", "text": "teach"}, {"label": "E", "text": "follow instructions"}], "stem": "Computers have allowed everybody to answer questions they have quickly, but still we seem to be getting duller despite access to this what?"}}
{"answerKey": "B", "id": "533599262a5dae7c7137cfe69e0e24fb", "question": {"question_concept": "cottage", "choices": [{"label": "A", "text": "mountains"}, {"label": "B", "text": "countryside"}, {"label": "C", "text": "train"}, {"label": "D", "text": "painting"}, {"label": "E", "text": "village"}], "stem": "There was a long cottage somewhere.  People thought it was haunted.  It was overgrown, there was nothing near it.  It's was far into the what?"}}
{"answerKey": "D", "id": "edd1634d911614590c6b8ca730df95fe", "question": {"question_concept": "knight", "choices": [{"label": "A", "text": "middle ages"}, {"label": "B", "text": "chess board"}, {"label": "C", "text": "kids story"}, {"label": "D", "text": "fairy tale"}, {"label": "E", "text": "castle"}], "stem": "Where is knight always portrayed as a hero?"}}
{"answerKey": "B", "id": "9a544e9f4847c41a15fdf47ae7b98d8a", "question": {"question_concept": "duffel bag", "choices": [{"label": "A", "text": "library"}, {"label": "B", "text": "transit"}, {"label": "C", "text": "bus station"}, {"label": "D", "text": "army barracks"}, {"label": "E", "text": "locker room"}], "stem": "James is carrying a duffel bag with him because he doesn't have a vehicle of his own and needs a bag to carry his things in while he uses what?"}}
{"answerKey": "D", "id": "26bd85f05d29863ed777a4f1a4b8fa63", "question": {"question_concept": "smoke", "choices": [{"label": "A", "text": "you're stupid"}, {"label": "B", "text": "kill yourself"}, {"label": "C", "text": "roll joint"}, {"label": "D", "text": "cigarette"}, {"label": "E", "text": "lighter fluid."}], "stem": "What would you need if you want to smoke?"}}
{"answerKey": "A", "id": "3884d82524f2337ce53ce64776293cf7", "question": {"question_concept": "competing", "choices": [{"label": "A", "text": "might win"}, {"label": "B", "text": "perform better"}, {"label": "C", "text": "enemies"}, {"label": "D", "text": "winners and losers"}, {"label": "E", "text": "lose"}], "stem": "James decided that competing was the right choice.   Not competing has a defined outcome, but if he competes then what could happen?"}}
{"answerKey": "A", "id": "acb3147d946db3b06a596d48e0be56cf", "question": {"question_concept": "airplanes", "choices": [{"label": "A", "text": "taxi"}, {"label": "B", "text": "carry people"}, {"label": "C", "text": "car"}, {"label": "D", "text": "stall"}, {"label": "E", "text": "crash"}], "stem": "What could you use to get to some airplanes?"}}
{"answerKey": "C", "id": "52ab95f9216f1994e37cc08f7f258f13", "question": {"question_concept": "driving", "choices": [{"label": "A", "text": "lack of fuel"}, {"label": "B", "text": "paint scratching"}, {"label": "C", "text": "wheels turning"}, {"label": "D", "text": "tire wear"}, {"label": "E", "text": "traffic accident"}], "stem": "What happens when driving?"}}
{"answerKey": "B", "id": "f60641f550d5ee44ac1bedcaf6ad6357", "question": {"question_concept": "having food", "choices": [{"label": "A", "text": "falling down"}, {"label": "B", "text": "digesting"}, {"label": "C", "text": "gas"}, {"label": "D", "text": "weight gain"}, {"label": "E", "text": "not hungry"}], "stem": "What are our bodies doing after having food?"}}
{"answerKey": "D", "id": "d9835ede7a0ed79325de13ca95b85b78", "question": {"question_concept": "going to work", "choices": [{"label": "A", "text": "making money"}, {"label": "B", "text": "leave home"}, {"label": "C", "text": "success"}, {"label": "D", "text": "malaise"}, {"label": "E", "text": "bad mood"}], "stem": "Why would one try to avoid work?"}}
{"answerKey": "A", "id": "2987db72e66f5fa0015ac64f9b3614ec", "question": {"question_concept": "fly in airplane", "choices": [{"label": "A", "text": "buy tickets"}, {"label": "B", "text": "passenger"}, {"label": "C", "text": "read"}, {"label": "D", "text": "add gas"}, {"label": "E", "text": "run through checklists"}], "stem": "What do you do in order to fly in airplane?"}}
{"answerKey": "A", "id": "8b548832703a8c68a788e2f9c0e222ae", "question": {"question_concept": "small dog", "choices": [{"label": "A", "text": "fair"}, {"label": "B", "text": "basket"}, {"label": "C", "text": "dog hair"}, {"label": "D", "text": "game"}, {"label": "E", "text": "sun"}], "stem": "What is another name for the color of the fur of a dog with light colored fur?"}}
{"answerKey": "E", "id": "1ddd239a2a6438a891cb411b82e7f450", "question": {"question_concept": "junk", "choices": [{"label": "A", "text": "drawer"}, {"label": "B", "text": "garage"}, {"label": "C", "text": "caddy"}, {"label": "D", "text": "bed"}, {"label": "E", "text": "television"}], "stem": "Sally was bored because she didn't like the junk that was on what?"}}
{"answerKey": "D", "id": "6544a50bf9563d52dbd2034e81df0bf3", "question": {"question_concept": "timid", "choices": [{"label": "A", "text": "reckless"}, {"label": "B", "text": "bellicose"}, {"label": "C", "text": "defensive"}, {"label": "D", "text": "aggressive"}, {"label": "E", "text": "dauntless"}], "stem": "The lion sensed his competitor was timid, so what attitude did the lion take?"}}
{"answerKey": "D", "id": "5ff6ce8ad88459272ffe23d33db4970a", "question": {"question_concept": "snake", "choices": [{"label": "A", "text": "pet shops"}, {"label": "B", "text": "oklahoma"}, {"label": "C", "text": "basement"}, {"label": "D", "text": "bedroom"}, {"label": "E", "text": "dreams"}], "stem": "John felt a snake slither over him as he rested.  He was afraid to raise his covers for fear of startling it.  Where might he be?"}}
{"answerKey": "B", "id": "2ca05683157a3cd89d82016f13e560ec", "question": {"question_concept": "place to eat", "choices": [{"label": "A", "text": "city"}, {"label": "B", "text": "downtown"}, {"label": "C", "text": "mall"}, {"label": "D", "text": "shopping center"}, {"label": "E", "text": "own house"}], "stem": "Where can you find a place to eat in an urban area close to local nightlife?"}}
{"answerKey": "E", "id": "1a8fbab20bbdf0bbf3961894662d5f7c", "question": {"question_concept": "thinking", "choices": [{"label": "A", "text": "fatigue"}, {"label": "B", "text": "depression"}, {"label": "C", "text": "best way"}, {"label": "D", "text": "weight"}, {"label": "E", "text": "knowledge"}], "stem": "You have to a lot of thinking while studying a new subject, but it is how you gain what?"}}
{"answerKey": "E", "id": "5b5d2a8b83282f61c68a870116042f64", "question": {"question_concept": "communicate", "choices": [{"label": "A", "text": "think"}, {"label": "B", "text": "talk with people"}, {"label": "C", "text": "talk to people"}, {"label": "D", "text": "speak out"}, {"label": "E", "text": "send email"}], "stem": "How will you communicate if you are far away from who you want to communicate with?"}}
{"answerKey": "E", "id": "cfa081b5ba90dae4d7ddb5b7ad9d369a", "question": {"question_concept": "chatting with friends", "choices": [{"label": "A", "text": "fever"}, {"label": "B", "text": "smoke"}, {"label": "C", "text": "laughing"}, {"label": "D", "text": "coughing"}, {"label": "E", "text": "lie"}], "stem": "Why would you not trust your friends after chatting with friends?"}}
{"answerKey": "C", "id": "009a7aabffe0583fc2df46656b29c326", "question": {"question_concept": "fortune", "choices": [{"label": "A", "text": "eat cake"}, {"label": "B", "text": "cookie"}, {"label": "C", "text": "stock market"}, {"label": "D", "text": "real estate"}, {"label": "E", "text": "treasure chest"}], "stem": "He came from old money and had a fortune, but he made new money making shrewd trades where?"}}
{"answerKey": "C", "id": "2521b3fe6bfd6aeb91f9107dc7c4fbee", "question": {"question_concept": "animal", "choices": [{"label": "A", "text": "carrying cargo"}, {"label": "B", "text": "favorite"}, {"label": "C", "text": "ecosystem"}, {"label": "D", "text": "nature"}, {"label": "E", "text": "ecology"}], "stem": "Animals make up a large part of the?"}}
{"answerKey": "E", "id": "3fe45ab3bd4a844ea290050fc0ece8c1_1", "question": {"question_concept": "shop", "choices": [{"label": "A", "text": "basement"}, {"label": "B", "text": "cardboard box"}, {"label": "C", "text": "ocean floor"}, {"label": "D", "text": "high school"}, {"label": "E", "text": "container"}], "stem": "At a shop what can you buy to put your spare unused things?"}}
{"answerKey": "A", "id": "a2e0f6b5651e5271fcff8d6f5c9adfee", "question": {"question_concept": "eating breakfast", "choices": [{"label": "A", "text": "heartburn"}, {"label": "B", "text": "overeating"}, {"label": "C", "text": "happiness"}, {"label": "D", "text": "being satisfied"}, {"label": "E", "text": "gain energy"}], "stem": "A person with digestion issues eats a meat-filled breakfast, what does he feel?"}}
{"answerKey": "E", "id": "d6900a01a9dd6627b4bb22b0f6d191a5", "question": {"question_concept": "prisoner", "choices": [{"label": "A", "text": "scape jail"}, {"label": "B", "text": "dream of freedom"}, {"label": "C", "text": "become a hairdresser"}, {"label": "D", "text": "attempt to escape"}, {"label": "E", "text": "do time"}], "stem": "What is a prisoner sentenced to do?"}}
{"answerKey": "E", "id": "8f2976690c83be6b8fa3a1196dfd9722", "question": {"question_concept": "remembering", "choices": [{"label": "A", "text": "phoning"}, {"label": "B", "text": "nostalgia"}, {"label": "C", "text": "writing down"}, {"label": "D", "text": "active"}, {"label": "E", "text": "being prepared"}], "stem": "Jesse  enjoyed remembering the past because he helped him understand it.  And understanding the past helped him with doing what?"}}
{"answerKey": "C", "id": "570be8c1edb8c638603dc5c8cae421cc", "question": {"question_concept": "birds", "choices": [{"label": "A", "text": "sky"}, {"label": "B", "text": "vaccation"}, {"label": "C", "text": "forest"}, {"label": "D", "text": "countryside"}, {"label": "E", "text": "roof"}], "stem": "David watched some nesting birds using his binoculars while on vacation.  Where might David be?."}}
{"answerKey": "E", "id": "08d3175de59a639be02f2ebc032d56bd", "question": {"question_concept": "rosebush", "choices": [{"label": "A", "text": "kew gardens"}, {"label": "B", "text": "garder"}, {"label": "C", "text": "backyard"}, {"label": "D", "text": "shop"}, {"label": "E", "text": "beautiful garden"}], "stem": "Where would you find many varieties of plants including a rosebush?"}}
{"answerKey": "C", "id": "549cf641318edfc0510fa7c7dbb359e1", "question": {"question_concept": "rosebush", "choices": [{"label": "A", "text": "pot"}, {"label": "B", "text": "museum"}, {"label": "C", "text": "garden center"}, {"label": "D", "text": "formal garden"}, {"label": "E", "text": "backyard"}], "stem": "If I did not have a rosebush, where would I get one?"}}
{"answerKey": "E", "id": "dfa23d3422b7294843447b6950d2b476", "question": {"question_concept": "person", "choices": [{"label": "A", "text": "feel important"}, {"label": "B", "text": "trust himself"}, {"label": "C", "text": "own house"}, {"label": "D", "text": "electrical circuit"}, {"label": "E", "text": "know what time"}], "stem": "What does a person with a what likely do?"}}
{"answerKey": "E", "id": "1fe90a4aee405e1aa2279442d28803ae", "question": {"question_concept": "cat", "choices": [{"label": "A", "text": "whiskers"}, {"label": "B", "text": "sharp teeth"}, {"label": "C", "text": "purr"}, {"label": "D", "text": "four legs"}, {"label": "E", "text": "sharp claws"}], "stem": "What are cats often known for?"}}
{"answerKey": "E", "id": "01794dde3ca2991615f1aa2f63fb22e3", "question": {"question_concept": "landing", "choices": [{"label": "A", "text": "apartment building"}, {"label": "B", "text": "disembark"}, {"label": "C", "text": "stairwell"}, {"label": "D", "text": "deplane"}, {"label": "E", "text": "airport"}], "stem": "As he looked out the window, he knew the landing was happening soon, and it made him nervous, but where would he be soon?"}}
{"answerKey": "A", "id": "f794e376672c98ac25d8f70506a26e68", "question": {"question_concept": "dogs", "choices": [{"label": "A", "text": "found outside"}, {"label": "B", "text": "faithful"}, {"label": "C", "text": "frightening"}, {"label": "D", "text": "cold"}, {"label": "E", "text": "four legs"}], "stem": "Where can you find a dogs house?"}}
{"answerKey": "E", "id": "ace8fa2943ba8414aebdb74b48906fae", "question": {"question_concept": "tweed", "choices": [{"label": "A", "text": "scotland"}, {"label": "B", "text": "brown"}, {"label": "C", "text": "fabric store"}, {"label": "D", "text": "clothing stores"}, {"label": "E", "text": "eddie bauer"}], "stem": "Tweed is a rare fabric in modern clothing, what brand should I look for when buying it?"}}
{"answerKey": "B", "id": "21ce6f7c5c3d1ad8cf234988c1ad471f", "question": {"question_concept": "grape", "choices": [{"label": "A", "text": "winery"}, {"label": "B", "text": "fruit stand"}, {"label": "C", "text": "field"}, {"label": "D", "text": "kitchen"}, {"label": "E", "text": "food"}], "stem": "If you really wanted a grape, where would you go to get it?"}}
{"answerKey": "A", "id": "6c84e79d0595efd99596faa07c4961d0", "question": {"question_concept": "climb", "choices": [{"label": "A", "text": "grab"}, {"label": "B", "text": "look down"}, {"label": "C", "text": "throw"}, {"label": "D", "text": "falling"}, {"label": "E", "text": "may fall"}], "stem": "What would you do to a rock when climb up a cliff?"}}
{"answerKey": "B", "id": "88f1fe6cfbcb1a25f25454341c789463", "question": {"question_concept": "hose", "choices": [{"label": "A", "text": "garden shed"}, {"label": "B", "text": "hardware store"}, {"label": "C", "text": "brothel"}, {"label": "D", "text": "garage"}, {"label": "E", "text": "greenhouse"}], "stem": "His compressor needed a new hose, where did he go?"}}
{"answerKey": "C", "id": "5074bcaf0f700c9f3c8c563067af156a", "question": {"question_concept": "music", "choices": [{"label": "A", "text": "coma"}, {"label": "B", "text": "enjoyable"}, {"label": "C", "text": "soothing"}, {"label": "D", "text": "universal"}, {"label": "E", "text": "good or bad"}], "stem": "The man closed his eyes as the music played, what effect did the music have?"}}
{"answerKey": "A", "id": "6a253e076cd2af00e17d9950d70daf47", "question": {"question_concept": "beam", "choices": [{"label": "A", "text": "new construction"}, {"label": "B", "text": "warehouse"}, {"label": "C", "text": "driving"}, {"label": "D", "text": "ceiling"}, {"label": "E", "text": "bridge"}], "stem": "Setting up framing, truss and beam are some of the first steps in what?"}}
{"answerKey": "C", "id": "5af7c7860e3be61d4cfd814cc109f9d9", "question": {"question_concept": "disk", "choices": [{"label": "A", "text": "computer store"}, {"label": "B", "text": "computer to store data"}, {"label": "C", "text": "computer hard drive"}, {"label": "D", "text": "cd player"}, {"label": "E", "text": "usb mouse"}], "stem": "What is another name for a disk for storing information?"}}
