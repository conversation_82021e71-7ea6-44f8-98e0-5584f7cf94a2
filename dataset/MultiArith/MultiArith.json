[{"iIndex": 0, "lAlignments": [80, 120, 136], "lEquations": ["X=((32.0+42.0)-35.0)"], "lSolutions": [39.0], "sQuestion": " For Halloween <PERSON><PERSON> and her sister combined the candy they received. <PERSON><PERSON> had 32 pieces of candy while her sister had 42. If they ate 35 pieces the first night, how many pieces do they have left? "}, {"iIndex": 1, "lAlignments": [17, 37, 75], "lEquations": ["X=((13.0****)-10.0)"], "lSolutions": [8.0], "sQuestion": " A pet store had 13 siamese cats and 5 house cats. During a sale they sold 10 cats. How many cats do they have left? "}, {"iIndex": 2, "lAlignments": [58, 91, 119], "lEquations": ["X=((2.0****)-2.0)"], "lSolutions": [2.0], "sQuestion": " <PERSON> was trying to expand his game collection. He bought 2 games from a friend and bought 2 more at a garage sale. If 2 of the games didn't work, how many good games did he end up with? "}, {"iIndex": 3, "lAlignments": [30, 48, 98], "lEquations": ["X=((42.0****)-9.0)"], "lSolutions": [40.0], "sQuestion": " The school cafeteria ordered 42 red apples and 7 green apples for students lunches. But, if only 9 students wanted fruit, how many extra did the cafeteria end up with? "}, {"iIndex": 4, "lAlignments": [13, 27, 78], "lEquations": ["X=((36.0+37.0)-70.0)"], "lSolutions": [3.0], "sQuestion": " <PERSON> picked 36 tulips and 37 roses to make flower bouquets. If she only used 70 of the flowers though, how many extra flowers did <PERSON> pick? "}, {"iIndex": 5, "lAlignments": [72, 97, 109], "lEquations": ["X=((29.0+16.0)-38.0)"], "lSolutions": [7.0], "sQuestion": " <PERSON> and her mom were picking carrots from their garden. <PERSON> picked 29 and her mother picked 16. If only 38 of the carrots were good, how many bad carrots did they have? "}, {"iIndex": 6, "lAlignments": [11, 47, 73], "lEquations": ["X=((16.0+28.0)-25.0)"], "lSolutions": [19.0], "sQuestion": " <PERSON> had 16 dollars. For his birthday he got 28 more dollars but spent 25 on a new game. How much money does he have now? "}, {"iIndex": 7, "lAlignments": [31, 58, 97], "lEquations": ["X=((15.0+18.0)-31.0)"], "lSolutions": [2.0], "sQuestion": " While on vacation, <PERSON> took 15 pictures at the zoo and 18 at the museum. If she later deleted 31 of the pictures, how many pictures from her vacation did she still have? "}, {"iIndex": 8, "lAlignments": [43, 73, 116], "lEquations": ["X=((23.0+32.0)-44.0)"], "lSolutions": [11.0], "sQuestion": " <PERSON> bought two coloring books. One had 23 pictures and the other had 32. After one week she had already colored 44 of the pictures. How many pictures does she still have to color? "}, {"iIndex": 9, "lAlignments": [17, 43, 102], "lEquations": ["X=((9.0+21.0)-29.0)"], "lSolutions": [1.0], "sQuestion": " <PERSON> had to wash 9 short sleeve shirts and 21 long sleeve shirts before school. If he had only washed 29 of them by the time school started, how many did he not wash? "}, {"iIndex": 10, "lAlignments": [20, 47, 106], "lEquations": ["X=((39.0+47.0)-20.0)"], "lSolutions": [66.0], "sQuestion": " <PERSON> had to wash 39 short sleeve shirts and 47 long sleeve shirts before school. If he had only washed 20 of them by the time school started, how many did he not wash? "}, {"iIndex": 11, "lAlignments": [57, 72, 107], "lEquations": ["X=((4.0+29.0)-24.0)"], "lSolutions": [9.0], "sQuestion": " For the school bake sale <PERSON> made pastries. She baked 4 cupcakes and 29 cookies. After the sale she had 24 to take back home. How many pastries did she sell? "}, {"iIndex": 12, "lAlignments": [31, 58, 97], "lEquations": ["X=((24.0+12.0)-14.0)"], "lSolutions": [22.0], "sQuestion": " While on vacation, <PERSON><PERSON> took 24 pictures at the zoo and 12 at the museum. If she later deleted 14 of the pictures, how many pictures from her vacation did she still have? "}, {"iIndex": 13, "lAlignments": [14, 27, 77], "lEquations": ["X=((3.0****)-10.0)"], "lSolutions": [2.0], "sQuestion": " Katie picked 3 tulips and 9 roses to make flower bouquets. If she only used 10 of the flowers though, how many extra flowers did Katie pick? "}, {"iIndex": 14, "lAlignments": [10, 31, 80], "lEquations": ["X=((46.0****)-40.0)"], "lSolutions": [15.0], "sQuestion": " <PERSON> had 46 math problems and 9 science problems for homework. If she finished 40 of the problems at school, how many problems did she have to do for homework? "}, {"iIndex": 15, "lAlignments": [9, 27, 77], "lEquations": ["X=((4.0+21.0)-23.0)"], "lSolutions": [2.0], "sQuestion": " <PERSON> had 4 music files and 21 video files on her flash drive. If she deleted 23 of the files, how many files were still on her flash drive? "}, {"iIndex": 16, "lAlignments": [57, 91, 120], "lEquations": ["X=((11.0+22.0)-19.0)"], "lSolutions": [14.0], "sQuestion": " <PERSON> was trying to expand his game collection. He bought 11 games from a friend and bought 22 more at a garage sale. If 19 of the games didn't work, how many good games did he end up with? "}, {"iIndex": 17, "lAlignments": [64, 109, 147], "lEquations": ["X=((40.0+50.0)-4.0)"], "lSolutions": [86.0], "sQuestion": " <PERSON> was playing a trivia game. In the first round she scored 40 points and in the second round she scored 50 points. In the last round she lost 4 points. How many points did she have at the end of the game? "}, {"iIndex": 18, "lAlignments": [24, 62, 106], "lEquations": ["X=((32.0+25.0)-7.0)"], "lSolutions": [50.0], "sQuestion": " At the arcade, <PERSON> won 32 tickets playing 'whack a mole' and 25 tickets playing 'skee ball'. If he spent 7 of his tickets on a hat, how many tickets does <PERSON> have left? "}, {"iIndex": 19, "lAlignments": [74, 99, 111], "lEquations": ["X=((26.0+15.0)-16.0)"], "lSolutions": [25.0], "sQuestion": " <PERSON> and her mom were picking carrots from their garden. <PERSON> picked 26 and her mother picked 15. If only 16 of the carrots were good, how many bad carrots did they have? "}, {"iIndex": 20, "lAlignments": [57, 72, 106], "lEquations": ["X=((7.0****)-8.0)"], "lSolutions": [4.0], "sQuestion": " For the school bake sale Katie made pastries. She baked 7 cupcakes and 5 cookies. After the sale she had 8 to take back home. How many pastries did she sell? "}, {"iIndex": 21, "lAlignments": [40, 70, 113], "lEquations": ["X=((44.0+44.0)-20.0)"], "lSolutions": [68.0], "sQuestion": " <PERSON> bought two coloring books. One had 44 pictures and the other had 44. After one week she had already colored 20 of the pictures. How many pictures does she still have to color? "}, {"iIndex": 22, "lAlignments": [58, 92, 120], "lEquations": ["X=((21.0+8.0)-23.0)"], "lSolutions": [6.0], "sQuestion": " <PERSON> was trying to expand his game collection. He bought 21 games from a friend and bought 8 more at a garage sale. If 23 of the games didn't work, how many good games did he end up with? "}, {"iIndex": 23, "lAlignments": [11, 47, 73], "lEquations": ["X=((11.0+18.0)-10.0)"], "lSolutions": [19.0], "sQuestion": " <PERSON> had 11 dollars. For his birthday he got 18 more dollars but spent 10 on a new game. How much money does he have now? "}, {"iIndex": 24, "lAlignments": [30, 57, 96], "lEquations": ["X=((41.0+29.0)-15.0)"], "lSolutions": [55.0], "sQuestion": " While on vacation, <PERSON> took 41 pictures at the zoo and 29 at the museum. If she later deleted 15 of the pictures, how many pictures from her vacation did she still have? "}, {"iIndex": 25, "lAlignments": [17, 44, 103], "lEquations": ["X=((40.0+23.0)-29.0)"], "lSolutions": [34.0], "sQuestion": " Sam had to wash 40 short sleeve shirts and 23 long sleeve shirts before school. If he had only washed 29 of them by the time school started, how many did he not wash? "}, {"iIndex": 26, "lAlignments": [17, 37, 76], "lEquations": ["X=((12.0+20.0)-20.0)"], "lSolutions": [12.0], "sQuestion": " A pet store had 12 siamese cats and 20 house cats. During a sale they sold 20 cats. How many cats do they have left? "}, {"iIndex": 27, "lAlignments": [70, 95, 106], "lEquations": ["X=((23.0****)-12.0)"], "lSolutions": [16.0], "sQuestion": " <PERSON> and her mom were picking carrots from their garden. <PERSON> picked 23 and her mother picked 5. If only 12 of the carrots were good, how many bad carrots did they have? "}, {"iIndex": 28, "lAlignments": [30, 48, 99], "lEquations": ["X=((43.0+32.0)-2.0)"], "lSolutions": [73.0], "sQuestion": " The school cafeteria ordered 43 red apples and 32 green apples for students lunches. But, if only 2 students wanted fruit, how many extra did the cafeteria end up with? "}, {"iIndex": 29, "lAlignments": [12, 25, 84], "lEquations": ["X=((39.0****)-26.0)"], "lSolutions": [17.0], "sQuestion": " There were 39 girls and 4 boys trying out for the schools basketball team. If only 26 of them got called back, how many students didn't make the cut? "}, {"iIndex": 30, "lAlignments": [17, 37, 76], "lEquations": ["X=((38.0+25.0)-45.0)"], "lSolutions": [18.0], "sQuestion": " A pet store had 38 siamese cats and 25 house cats. During a sale they sold 45 cats. How many cats do they have left? "}, {"iIndex": 31, "lAlignments": [57, 73, 107], "lEquations": ["X=((36.0****)-4.0)"], "lSolutions": [41.0], "sQuestion": " For the school bake sale <PERSON> made pastries. She baked 36 cupcakes and 9 cookies. After the sale she had 4 to take back home. How many pastries did she sell? "}, {"iIndex": 32, "lAlignments": [64, 109, 146], "lEquations": ["X=((17.0****)-16.0)"], "lSolutions": [7.0], "sQuestion": " <PERSON> was playing a trivia game. In the first round she scored 17 points and in the second round she scored 6 points. In the last round she lost 16 points. How many points did she have at the end of the game? "}, {"iIndex": 33, "lAlignments": [41, 71, 114], "lEquations": ["X=((10.0+39.0)-13.0)"], "lSolutions": [36.0], "sQuestion": " <PERSON> bought two coloring books. One had 10 pictures and the other had 39. After one week she had already colored 13 of the pictures. How many pictures does she still have to color? "}, {"iIndex": 34, "lAlignments": [80, 120, 136], "lEquations": ["X=((34.0+33.0)-4.0)"], "lSolutions": [63.0], "sQuestion": " For Halloween <PERSON> and her sister combined the candy they received. <PERSON> had 34 pieces of candy while her sister had 33. If they ate 4 pieces the first night, how many pieces do they have left? "}, {"iIndex": 35, "lAlignments": [72, 97, 109], "lEquations": ["X=((39.0+38.0)-64.0)"], "lSolutions": [13.0], "sQuestion": " <PERSON> and her mom were picking carrots from their garden. <PERSON> picked 39 and her mother picked 38. If only 64 of the carrots were good, how many bad carrots did they have? "}, {"iIndex": 36, "lAlignments": [12, 25, 85], "lEquations": ["X=((30.0+36.0)-10.0)"], "lSolutions": [56.0], "sQuestion": " There were 30 girls and 36 boys trying out for the schools basketball team. If only 10 of them got called back, how many students didn't make the cut? "}, {"iIndex": 37, "lAlignments": [11, 47, 73], "lEquations": ["X=((29.0+20.0)-34.0)"], "lSolutions": [15.0], "sQuestion": " <PERSON> had 29 dollars. For his birthday he got 20 more dollars but spent 34 on a new game. How much money does he have now? "}, {"iIndex": 38, "lAlignments": [18, 45, 104], "lEquations": ["X=((29.0+11.0)-35.0)"], "lSolutions": [5.0], "sQuestion": " <PERSON> had to wash 29 short sleeve shirts and 11 long sleeve shirts before school. If he had only washed 35 of them by the time school started, how many did he not wash? "}, {"iIndex": 39, "lAlignments": [60, 94, 123], "lEquations": ["X=((41.0+14.0)-31.0)"], "lSolutions": [24.0], "sQuestion": " <PERSON> was trying to expand his game collection. He bought 41 games from a friend and bought 14 more at a garage sale. If 31 of the games didn't work, how many good games did he end up with? "}, {"iIndex": 40, "lAlignments": [37, 60, 81], "lEquations": ["X=((13.0+17.0)-15.0)"], "lSolutions": [15.0], "sQuestion": " At the schools book fair <PERSON> bought 13 adventure books and 17 mystery books. If 15 of the books were used, how many new books did he buy? "}, {"iIndex": 41, "lAlignments": [9, 28, 78], "lEquations": ["X=((26.0+36.0)-48.0)"], "lSolutions": [14.0], "sQuestion": " <PERSON> had 26 music files and 36 video files on her flash drive. If she deleted 48 of the files, how many files were still on her flash drive? "}, {"iIndex": 42, "lAlignments": [12, 48, 74], "lEquations": ["X=((35.0+50.0)-84.0)"], "lSolutions": [1.0], "sQuestion": " <PERSON> had 35 dollars. For his birthday he got 50 more dollars but spent 84 on a new game. How much money does he have now? "}, {"iIndex": 43, "lAlignments": [64, 109, 147], "lEquations": ["X=((16.0+33.0)-48.0)"], "lSolutions": [1.0], "sQuestion": " <PERSON> was playing a trivia game. In the first round she scored 16 points and in the second round she scored 33 points. In the last round she lost 48 points. How many points did she have at the end of the game? "}, {"iIndex": 44, "lAlignments": [11, 32, 82], "lEquations": ["X=((43.0+12.0)-44.0)"], "lSolutions": [11.0], "sQuestion": " <PERSON> had 43 math problems and 12 science problems for homework. If she finished 44 of the problems at school, how many problems did she have to do for homework? "}, {"iIndex": 45, "lAlignments": [30, 48, 99], "lEquations": ["X=((33.0+23.0)-21.0)"], "lSolutions": [35.0], "sQuestion": " The school cafeteria ordered 33 red apples and 23 green apples for students lunches. But, if only 21 students wanted fruit, how many extra did the cafeteria end up with? "}, {"iIndex": 46, "lAlignments": [18, 44, 103], "lEquations": ["X=((9.0+27.0)-20.0)"], "lSolutions": [16.0], "sQuestion": " <PERSON> had to wash 9 short sleeve shirts and 27 long sleeve shirts before school. If he had only washed 20 of them by the time school started, how many did he not wash? "}, {"iIndex": 47, "lAlignments": [32, 58, 96], "lEquations": ["X=((6.0****)-11.0)"], "lSolutions": [4.0], "sQuestion": " While on vacation, <PERSON> took 6 pictures at the zoo and 9 at the museum. If she later deleted 11 of the pictures, how many pictures from her vacation did she still have? "}, {"iIndex": 48, "lAlignments": [76, 101, 113], "lEquations": ["X=((17.0+14.0)-24.0)"], "lSolutions": [7.0], "sQuestion": " <PERSON> and her mom were picking carrots from their garden. <PERSON> picked 17 and her mother picked 14. If only 24 of the carrots were good, how many bad carrots did they have? "}, {"iIndex": 49, "lAlignments": [17, 37, 76], "lEquations": ["X=((41.0+28.0)-15.0)"], "lSolutions": [54.0], "sQuestion": " A pet store had 41 siamese cats and 28 house cats. During a sale they sold 15 cats. How many cats do they have left? "}, {"iIndex": 50, "lAlignments": [37, 60, 81], "lEquations": ["X=((13.0+17.0)-15.0)"], "lSolutions": [15.0], "sQuestion": " At the schools book fair <PERSON> bought 13 adventure books and 17 mystery books. If 15 of the books were used, how many new books did he buy? "}, {"iIndex": 51, "lAlignments": [9, 28, 78], "lEquations": ["X=((26.0+36.0)-48.0)"], "lSolutions": [14.0], "sQuestion": " <PERSON> had 26 music files and 36 video files on her flash drive. If she deleted 48 of the files, how many files were still on her flash drive? "}, {"iIndex": 52, "lAlignments": [12, 48, 74], "lEquations": ["X=((35.0+50.0)-84.0)"], "lSolutions": [1.0], "sQuestion": " <PERSON> had 35 dollars. For his birthday he got 50 more dollars but spent 84 on a new game. How much money does he have now? "}, {"iIndex": 53, "lAlignments": [64, 109, 147], "lEquations": ["X=((16.0+33.0)-48.0)"], "lSolutions": [1.0], "sQuestion": " <PERSON> was playing a trivia game. In the first round she scored 16 points and in the second round she scored 33 points. In the last round she lost 48 points. How many points did she have at the end of the game? "}, {"iIndex": 54, "lAlignments": [11, 32, 82], "lEquations": ["X=((43.0+12.0)-44.0)"], "lSolutions": [11.0], "sQuestion": " <PERSON> had 43 math problems and 12 science problems for homework. If she finished 44 of the problems at school, how many problems did she have to do for homework? "}, {"iIndex": 55, "lAlignments": [30, 48, 99], "lEquations": ["X=((33.0+23.0)-21.0)"], "lSolutions": [35.0], "sQuestion": " The school cafeteria ordered 33 red apples and 23 green apples for students lunches. But, if only 21 students wanted fruit, how many extra did the cafeteria end up with? "}, {"iIndex": 56, "lAlignments": [18, 44, 103], "lEquations": ["X=((9.0+27.0)-20.0)"], "lSolutions": [16.0], "sQuestion": " <PERSON> had to wash 9 short sleeve shirts and 27 long sleeve shirts before school. If he had only washed 20 of them by the time school started, how many did he not wash? "}, {"iIndex": 57, "lAlignments": [32, 58, 96], "lEquations": ["X=((6.0****)-11.0)"], "lSolutions": [4.0], "sQuestion": " While on vacation, <PERSON> took 6 pictures at the zoo and 9 at the museum. If she later deleted 11 of the pictures, how many pictures from her vacation did she still have? "}, {"iIndex": 58, "lAlignments": [76, 101, 113], "lEquations": ["X=((17.0+14.0)-24.0)"], "lSolutions": [7.0], "sQuestion": " <PERSON> and her mom were picking carrots from their garden. <PERSON> picked 17 and her mother picked 14. If only 24 of the carrots were good, how many bad carrots did they have? "}, {"iIndex": 59, "lAlignments": [17, 37, 76], "lEquations": ["X=((41.0+28.0)-15.0)"], "lSolutions": [54.0], "sQuestion": " A pet store had 41 siamese cats and 28 house cats. During a sale they sold 15 cats. How many cats do they have left? "}, {"iIndex": 60, "lAlignments": [14, 27, 78], "lEquations": ["X=((4.0+11.0)-11.0)"], "lSolutions": [4.0], "sQuestion": " <PERSON> picked 4 tulips and 11 roses to make flower bouquets. If she only used 11 of the flowers though, how many extra flowers did <PERSON> pick? "}, {"iIndex": 61, "lAlignments": [13, 32, 82], "lEquations": ["X=((13.0+30.0)-10.0)"], "lSolutions": [33.0], "sQuestion": " <PERSON> had 13 music files and 30 video files on her flash drive. If she deleted 10 of the files, how many files were still on her flash drive? "}, {"iIndex": 62, "lAlignments": [42, 72, 115], "lEquations": ["X=((16.0+40.0)-33.0)"], "lSolutions": [23.0], "sQuestion": " <PERSON><PERSON> bought two coloring books. One had 16 pictures and the other had 40. After one week she had already colored 33 of the pictures. How many pictures does she still have to color? "}, {"iIndex": 63, "lAlignments": [30, 47, 98], "lEquations": ["X=((8.0+43.0)-42.0)"], "lSolutions": [9.0], "sQuestion": " The school cafeteria ordered 8 red apples and 43 green apples for students lunches. But, if only 42 students wanted fruit, how many extra did the cafeteria end up with? "}, {"iIndex": 64, "lAlignments": [68, 117, 148], "lEquations": ["X=((2.0+27.0)-5.0)"], "lSolutions": [24.0], "sQuestion": " <PERSON> started his own lawn mowing business. In the spring he made 2 dollars mowing lawns and in the summer he made 27 dollars. If he had to spend 5 dollars buying supplies, how much money did he end up with? "}, {"iIndex": 65, "lAlignments": [17, 37, 76], "lEquations": ["X=((36.0+18.0)-26.0)"], "lSolutions": [28.0], "sQuestion": " A pet store had 36 siamese cats and 18 house cats. During a sale they sold 26 cats. How many cats do they have left? "}, {"iIndex": 66, "lAlignments": [74, 99, 111], "lEquations": ["X=((20.0+14.0)-19.0)"], "lSolutions": [15.0], "sQuestion": " <PERSON> and her mom were picking carrots from their garden. <PERSON> picked 20 and her mother picked 14. If only 19 of the carrots were good, how many bad carrots did they have? "}, {"iIndex": 67, "lAlignments": [12, 48, 74], "lEquations": ["X=((30.0+16.0)-38.0)"], "lSolutions": [8.0], "sQuestion": " <PERSON> had 30 dollars. For his birthday he got 16 more dollars but spent 38 on a new game. How much money does he have now? "}, {"iIndex": 68, "lAlignments": [12, 24, 84], "lEquations": ["X=((6.0+48.0)-7.0)"], "lSolutions": [47.0], "sQuestion": " There were 6 girls and 48 boys trying out for the schools basketball team. If only 7 of them got called back, how many students didn't make the cut? "}, {"iIndex": 69, "lAlignments": [55, 71, 106], "lEquations": ["X=((15.0+48.0)-12.0)"], "lSolutions": [51.0], "sQuestion": " For the school bake sale <PERSON> made pastries. She baked 15 cupcakes and 48 cookies. After the sale she had 12 to take back home. How many pastries did she sell? "}, {"iIndex": 70, "lAlignments": [40, 63, 84], "lEquations": ["X=((32.0+37.0)-16.0)"], "lSolutions": [53.0], "sQuestion": " At the schools book fair <PERSON> bought 32 adventure books and 37 mystery books. If 16 of the books were used, how many new books did he buy? "}, {"iIndex": 71, "lAlignments": [31, 58, 96], "lEquations": ["X=((50.0+8.0)-38.0)"], "lSolutions": [20.0], "sQuestion": " While on vacation, <PERSON> took 50 pictures at the zoo and 8 at the museum. If she later deleted 38 of the pictures, how many pictures from her vacation did she still have? "}, {"iIndex": 72, "lAlignments": [30, 48, 99], "lEquations": ["X=((37.0+45.0)-51.0)"], "lSolutions": [31.0], "sQuestion": " The school cafeteria ordered 37 red apples and 45 green apples for students lunches. But, if only 51 students wanted fruit, how many extra did the cafeteria end up with? "}, {"iIndex": 73, "lAlignments": [30, 50, 90], "lEquations": ["X=((35.0+21.0)-34.0)"], "lSolutions": [22.0], "sQuestion": " While shopping, <PERSON> bought 35 green towels and 21 white towels. If she gave her mother 34 of them, how many towels did <PERSON> end up with? "}, {"iIndex": 74, "lAlignments": [12, 25, 85], "lEquations": ["X=((17.0+32.0)-10.0)"], "lSolutions": [39.0], "sQuestion": " There were 17 girls and 32 boys trying out for the schools basketball team. If only 10 of them got called back, how many students didn't make the cut? "}, {"iIndex": 75, "lAlignments": [72, 97, 109], "lEquations": ["X=((38.0+47.0)-71.0)"], "lSolutions": [14.0], "sQuestion": " <PERSON> and her mom were picking carrots from their garden. <PERSON> picked 38 and her mother picked 47. If only 71 of the carrots were good, how many bad carrots did they have? "}, {"iIndex": 76, "lAlignments": [47, 111, 128], "lEquations": ["X=((29.0+20.0)-34.0)"], "lSolutions": [15.0], "sQuestion": " A waiter at 'The Greasy Spoon' restaurant had 29 customers to wait on. During the lunch rush he added another 20 customers. If 34 of the customers didn't leave him a tip, how many customers did leave a tip? "}, {"iIndex": 77, "lAlignments": [17, 44, 103], "lEquations": ["X=((10.0+25.0)-5.0)"], "lSolutions": [30.0], "sQuestion": " <PERSON> had to wash 10 short sleeve shirts and 25 long sleeve shirts before school. If he had only washed 5 of them by the time school started, how many did he not wash? "}, {"iIndex": 78, "lAlignments": [13, 32, 82], "lEquations": ["X=((16.0+48.0)-30.0)"], "lSolutions": [34.0], "sQuestion": " <PERSON> had 16 music files and 48 video files on her flash drive. If she deleted 30 of the files, how many files were still on her flash drive? "}, {"iIndex": 79, "lAlignments": [67, 116, 147], "lEquations": ["X=((4.0+50.0)-4.0)"], "lSolutions": [50.0], "sQuestion": " <PERSON><PERSON><PERSON> started his own lawn mowing business. In the spring he made 4 dollars mowing lawns and in the summer he made 50 dollars. If he had to spend 4 dollars buying supplies, how much money did he end up with? "}, {"iIndex": 80, "lAlignments": [30, 47, 98], "lEquations": ["X=((6.0+15.0)-5.0)"], "lSolutions": [16.0], "sQuestion": " The school cafeteria ordered 6 red apples and 15 green apples for students lunches. But, if only 5 students wanted fruit, how many extra did the cafeteria end up with? "}, {"iIndex": 81, "lAlignments": [15, 29, 80], "lEquations": ["X=((39.0+49.0)-81.0)"], "lSolutions": [7.0], "sQuestion": " <PERSON> picked 39 tulips and 49 roses to make flower bouquets. If she only used 81 of the flowers though, how many extra flowers did <PERSON> pick? "}, {"iIndex": 82, "lAlignments": [31, 58, 96], "lEquations": ["X=((49.0+8.0)-38.0)"], "lSolutions": [19.0], "sQuestion": " While on vacation, <PERSON> took 49 pictures at the zoo and 8 at the museum. If she later deleted 38 of the pictures, how many pictures from her vacation did she still have? "}, {"iIndex": 83, "lAlignments": [10, 31, 81], "lEquations": ["X=((18.0+11.0)-24.0)"], "lSolutions": [5.0], "sQuestion": " <PERSON> had 18 math problems and 11 science problems for homework. If she finished 24 of the problems at school, how many problems did she have to do for homework? "}, {"iIndex": 84, "lAlignments": [80, 120, 135], "lEquations": ["X=((10.0****)-9.0)"], "lSolutions": [7.0], "sQuestion": " For Halloween <PERSON> and her sister combined the candy they received. <PERSON> had 10 pieces of candy while her sister had 6. If they ate 9 pieces the first night, how many pieces do they have left? "}, {"iIndex": 85, "lAlignments": [17, 37, 76], "lEquations": ["X=((15.0+49.0)-19.0)"], "lSolutions": [45.0], "sQuestion": " A pet store had 15 siamese cats and 49 house cats. During a sale they sold 19 cats. How many cats do they have left? "}, {"iIndex": 86, "lAlignments": [12, 24, 84], "lEquations": ["X=((9.0+14.0)-2.0)"], "lSolutions": [21.0], "sQuestion": " There were 9 girls and 14 boys trying out for the schools basketball team. If only 2 of them got called back, how many students didn't make the cut? "}, {"iIndex": 87, "lAlignments": [11, 30, 80], "lEquations": ["X=((27.0+42.0)-11.0)"], "lSolutions": [58.0], "sQuestion": " <PERSON> had 27 music files and 42 video files on her flash drive. If she deleted 11 of the files, how many files were still on her flash drive? "}, {"iIndex": 88, "lAlignments": [30, 50, 90], "lEquations": ["X=((40.0+44.0)-65.0)"], "lSolutions": [19.0], "sQuestion": " While shopping, <PERSON> bought 40 green towels and 44 white towels. If she gave her mother 65 of them, how many towels did <PERSON> end up with? "}, {"iIndex": 89, "lAlignments": [47, 111, 128], "lEquations": ["X=((39.0+12.0)-49.0)"], "lSolutions": [2.0], "sQuestion": " A waiter at 'The Greasy Spoon' restaurant had 39 customers to wait on. During the lunch rush he added another 12 customers. If 49 of the customers didn't leave him a tip, how many customers did leave a tip? "}, {"iIndex": 90, "lAlignments": [17, 37, 76], "lEquations": ["X=((19.0+45.0)-56.0)"], "lSolutions": [8.0], "sQuestion": " A pet store had 19 siamese cats and 45 house cats. During a sale they sold 56 cats. How many cats do they have left? "}, {"iIndex": 91, "lAlignments": [47, 111, 128], "lEquations": ["X=((26.0+27.0)-27.0)"], "lSolutions": [26.0], "sQuestion": " A waiter at 'The Greasy Spoon' restaurant had 26 customers to wait on. During the lunch rush he added another 27 customers. If 27 of the customers didn't leave him a tip, how many customers did leave a tip? "}, {"iIndex": 92, "lAlignments": [43, 73, 116], "lEquations": ["X=((24.0+39.0)-4.0)"], "lSolutions": [59.0], "sQuestion": " <PERSON> bought two coloring books. One had 24 pictures and the other had 39. After one week she had already colored 4 of the pictures. How many pictures does she still have to color? "}, {"iIndex": 93, "lAlignments": [30, 49, 89], "lEquations": ["X=((5.0+30.0)-26.0)"], "lSolutions": [9.0], "sQuestion": " While shopping, <PERSON> bought 5 green towels and 30 white towels. If she gave her mother 26 of them, how many towels did <PERSON> end up with? "}, {"iIndex": 94, "lAlignments": [80, 119, 135], "lEquations": ["X=((8.0+23.0)-8.0)"], "lSolutions": [23.0], "sQuestion": " For Halloween <PERSON> and her sister combined the candy they received. <PERSON> had 8 pieces of candy while her sister had 23. If they ate 8 pieces the first night, how many pieces do they have left? "}, {"iIndex": 95, "lAlignments": [57, 91, 120], "lEquations": ["X=((50.0+27.0)-74.0)"], "lSolutions": [3.0], "sQuestion": " <PERSON> was trying to expand his game collection. He bought 50 games from a friend and bought 27 more at a garage sale. If 74 of the games didn't work, how many good games did he end up with? "}, {"iIndex": 96, "lAlignments": [57, 73, 108], "lEquations": ["X=((41.0+31.0)-32.0)"], "lSolutions": [40.0], "sQuestion": " For the school bake sale <PERSON> made pastries. She baked 41 cupcakes and 31 cookies. After the sale she had 32 to take back home. How many pastries did she sell? "}, {"iIndex": 97, "lAlignments": [30, 48, 99], "lEquations": ["X=((25.0+17.0)-10.0)"], "lSolutions": [32.0], "sQuestion": " The school cafeteria ordered 25 red apples and 17 green apples for students lunches. But, if only 10 students wanted fruit, how many extra did the cafeteria end up with? "}, {"iIndex": 98, "lAlignments": [26, 64, 108], "lEquations": ["X=((29.0+17.0)-12.0)"], "lSolutions": [34.0], "sQuestion": " At the arcade, <PERSON> won 29 tickets playing 'whack a mole' and 17 tickets playing 'skee ball'. If he spent 12 of his tickets on a hat, how many tickets does <PERSON> have left? "}, {"iIndex": 99, "lAlignments": [10, 46, 71], "lEquations": ["X=((45.0****)-19.0)"], "lSolutions": [35.0], "sQuestion": " <PERSON> had 45 dollars. For his birthday he got 9 more dollars but spent 19 on a new game. How much money does he have now? "}, {"iIndex": 100, "lAlignments": [37, 86, 62], "lEquations": ["X=((30.0+28.0)-9.0)"], "lSolutions": [49.0], "sQuestion": " For the school bake sale <PERSON> made 30 cupcakes. If she sold 9 of them and then made 28 more, how many cupcakes would she have? "}, {"iIndex": 101, "lAlignments": [27, 110, 55], "lEquations": ["X=((47.0+40.0)-25.0)"], "lSolutions": [62.0], "sQuestion": " For Halloween <PERSON> scored 47 pieces of candy. She ate 25 pieces the first night and then her sister gave her 40 more pieces. How many pieces of candy does <PERSON> have now? "}, {"iIndex": 102, "lAlignments": [11, 79, 32], "lEquations": ["X=((34.0****)-17.0)"], "lSolutions": [24.0], "sQuestion": " <PERSON><PERSON><PERSON> had 34 books. If he sold 17 of them and used the money he earned to buy 7 new books, how many books would <PERSON><PERSON><PERSON> have? "}, {"iIndex": 103, "lAlignments": [28, 102, 82], "lEquations": ["X=((10.0+42.0)-4.0)"], "lSolutions": [48.0], "sQuestion": " In fourth grade there were 10 students at the start of the year. During the year 4 students left and 42 new students came to school. How many students were in fourth grade at the end? "}, {"iIndex": 104, "lAlignments": [12, 86, 57], "lEquations": ["X=((33.0+32.0)-4.0)"], "lSolutions": [61.0], "sQuestion": " <PERSON> had 33 dollars in January. By March he had spent 4 dollars. If he got another 32 dollars from his mom, how much money would he have? "}, {"iIndex": 105, "lAlignments": [15, 70, 37], "lEquations": ["X=((37.0+19.0)-16.0)"], "lSolutions": [40.0], "sQuestion": " A florist had 37 roses. If she sold 16 of them and then later picked 19 more, how many roses would she have? "}, {"iIndex": 106, "lAlignments": [15, 72, 52], "lEquations": ["X=((6.0+18.0)-4.0)"], "lSolutions": [20.0], "sQuestion": " A teacher had 6 worksheets to grade. If she graded 4, but then another 18 were turned in, how many worksheets would she have to grade? "}, {"iIndex": 107, "lAlignments": [18, 82, 59], "lEquations": ["X=((41.0****)-33.0)"], "lSolutions": [10.0], "sQuestion": " A book store had 41 books in the bargin bin. If they sold 33 books, but then put 2 more in the bin, how many books would be in the bin? "}, {"iIndex": 108, "lAlignments": [14, 79, 42], "lEquations": ["X=((19.0+36.0)-14.0)"], "lSolutions": [41.0], "sQuestion": " A waiter had 19 customers to wait on. If 14 customers left and he got another 36 customers, how many customers would he have? "}, {"iIndex": 109, "lAlignments": [24, 109, 65], "lEquations": ["X=((9.0+3.0)-6.0)"], "lSolutions": [6.0], "sQuestion": " At the fair there were 9 people in line for the bumper cars. If 6 of them got tired of waiting and left and 3 more got in line, how many people would be in line? "}, {"iIndex": 110, "lAlignments": [15, 72, 52], "lEquations": ["X=((7.0+46.0)-2.0)"], "lSolutions": [51.0], "sQuestion": " A teacher had 7 worksheets to grade. If she graded 2, but then another 46 were turned in, how many worksheets would she have to grade? "}, {"iIndex": 111, "lAlignments": [11, 88, 53], "lEquations": ["X=((8.0+30.0)-5.0)"], "lSolutions": [33.0], "sQuestion": " <PERSON> had 8 songs on her mp3 player. If she deleted 5 old songs from it and then added 30 new songs, how many songs does she have on her mp3 player? "}, {"iIndex": 112, "lAlignments": [14, 86, 59], "lEquations": ["X=((48.0+15.0)-11.0)"], "lSolutions": [52.0], "sQuestion": " <PERSON> picked 48 carrots from her garden. If she threw out 11 of them and then picked 15 more the next day, how many carrots would she have total? "}, {"iIndex": 113, "lAlignments": [13, 80, 53], "lEquations": ["X=((34.0+13.0)-20.0)"], "lSolutions": [27.0], "sQuestion": " A store had 34 oranges in a bin. If they threw away 20 of the old ones and put 13 new ones in the bin how many would be in the bin? "}, {"iIndex": 114, "lAlignments": [14, 79, 42], "lEquations": ["X=((47.0+20.0)-41.0)"], "lSolutions": [26.0], "sQuestion": " A waiter had 47 customers to wait on. If 41 customers left and he got another 20 customers, how many customers would he have? "}, {"iIndex": 115, "lAlignments": [28, 103, 82], "lEquations": ["X=((33.0+14.0)-18.0)"], "lSolutions": [29.0], "sQuestion": " In fourth grade there were 33 students at the start of the year. During the year 18 students left and 14 new students came to school. How many students were in fourth grade at the end? "}, {"iIndex": 116, "lAlignments": [37, 87, 62], "lEquations": ["X=((26.0+20.0)-20.0)"], "lSolutions": [26.0], "sQuestion": " For the school bake sale Katie made 26 cupcakes. If she sold 20 of them and then made 20 more, how many cupcakes would she have? "}, {"iIndex": 117, "lAlignments": [10, 79, 44], "lEquations": ["X=((5.0****)-2.0)"], "lSolutions": [8.0], "sQuestion": " <PERSON> had 5 dollars. At the store he spent $2 on a new game. If he got another 5 dollars for his allowance, how much money does he have now? "}, {"iIndex": 118, "lAlignments": [42, 109, 88], "lEquations": ["X=((43.0+27.0)-14.0)"], "lSolutions": [56.0], "sQuestion": " <PERSON> was playing a video game and had 43 lives. In a hard part of the game she lost 14 lives. If she got 27 more lives in the next level, how many lives would she have? "}, {"iIndex": 119, "lAlignments": [24, 111, 66], "lEquations": ["X=((12.0+15.0)-10.0)"], "lSolutions": [17.0], "sQuestion": " At the fair there were 12 people in line for the bumper cars. If 10 of them got tired of waiting and left and 15 more got in line, how many people would be in line? "}, {"iIndex": 120, "lAlignments": [14, 78, 42], "lEquations": ["X=((14.0+39.0)-3.0)"], "lSolutions": [50.0], "sQuestion": " A waiter had 14 customers to wait on. If 3 customers left and he got another 39 customers, how many customers would he have? "}, {"iIndex": 121, "lAlignments": [24, 111, 66], "lEquations": ["X=((30.0****)-10.0)"], "lSolutions": [25.0], "sQuestion": " At the fair there were 30 people in line for the bumper cars. If 10 of them got tired of waiting and left and 5 more got in line, how many people would be in line? "}, {"iIndex": 122, "lAlignments": [10, 73, 46], "lEquations": ["X=((34.0+48.0)-3.0)"], "lSolutions": [79.0], "sQuestion": " <PERSON> had 34 coloring books. If she gave away 3 of them, but then bought 48 more, how many would she have total? "}, {"iIndex": 123, "lAlignments": [11, 89, 54], "lEquations": ["X=((11.0+8.0)-9.0)"], "lSolutions": [10.0], "sQuestion": " <PERSON> had 11 songs on her mp3 player. If she deleted 9 old songs from it and then added 8 new songs, how many songs does she have on her mp3 player? "}, {"iIndex": 124, "lAlignments": [15, 73, 53], "lEquations": ["X=((38.0+15.0)-4.0)"], "lSolutions": [49.0], "sQuestion": " A teacher had 38 worksheets to grade. If she graded 4, but then another 15 were turned in, how many worksheets would she have to grade? "}, {"iIndex": 125, "lAlignments": [28, 103, 82], "lEquations": ["X=((40.0+26.0)-14.0)"], "lSolutions": [52.0], "sQuestion": " In fourth grade there were 40 students at the start of the year. During the year 14 students left and 26 new students came to school. How many students were in fourth grade at the end? "}, {"iIndex": 126, "lAlignments": [40, 106, 86], "lEquations": ["X=((43.0+39.0)-8.0)"], "lSolutions": [74.0], "sQuestion": " <PERSON> was playing a video game and had 43 lives. In a hard part of the game she lost 8 lives. If she got 39 more lives in the next level, how many lives would she have? "}, {"iIndex": 127, "lAlignments": [18, 80, 58], "lEquations": ["X=((4.0+10.0)-3.0)"], "lSolutions": [11.0], "sQuestion": " A book store had 4 books in the bargin bin. If they sold 3 books, but then put 10 more in the bin, how many books would be in the bin? "}, {"iIndex": 128, "lAlignments": [24, 85, 48], "lEquations": ["X=((49.0****)-25.0)"], "lSolutions": [30.0], "sQuestion": " At the arcade <PERSON> won 49 tickets. If he spent 25 tickets on a beanie and later won 6 more tickets, how many would he have? "}, {"iIndex": 129, "lAlignments": [38, 87, 63], "lEquations": ["X=((14.0+17.0)-6.0)"], "lSolutions": [25.0], "sQuestion": " For the school bake sale <PERSON> made 14 cupcakes. If she sold 6 of them and then made 17 more, how many cupcakes would she have? "}, {"iIndex": 130, "lAlignments": [12, 75, 48], "lEquations": ["X=((45.0+20.0)-6.0)"], "lSolutions": [59.0], "sQuestion": " <PERSON> had 45 coloring books. If she gave away 6 of them, but then bought 20 more, how many would she have total? "}, {"iIndex": 131, "lAlignments": [9, 87, 52], "lEquations": ["X=((15.0+50.0)-8.0)"], "lSolutions": [57.0], "sQuestion": " <PERSON> had 15 songs on her mp3 player. If she deleted 8 old songs from it and then added 50 new songs, how many songs does she have on her mp3 player? "}, {"iIndex": 132, "lAlignments": [25, 84, 48], "lEquations": ["X=((4.0+47.0)-2.0)"], "lSolutions": [49.0], "sQuestion": " At the arcade <PERSON> won 4 tickets. If he spent 2 tickets on a beanie and later won 47 more tickets, how many would he have? "}, {"iIndex": 133, "lAlignments": [13, 79, 53], "lEquations": ["X=((31.0+38.0)-9.0)"], "lSolutions": [60.0], "sQuestion": " A store had 31 oranges in a bin. If they threw away 9 of the old ones and put 38 new ones in the bin how many would be in the bin? "}, {"iIndex": 134, "lAlignments": [10, 78, 31], "lEquations": ["X=((33.0+23.0)-11.0)"], "lSolutions": [45.0], "sQuestion": " <PERSON> had 33 books. If he sold 11 of them and used the money he earned to buy 23 new books, how many books would <PERSON> have? "}, {"iIndex": 135, "lAlignments": [24, 110, 66], "lEquations": ["X=((10.0****)-2.0)"], "lSolutions": [10.0], "sQuestion": " At the fair there were 10 people in line for the bumper cars. If 2 of them got tired of waiting and left and 2 more got in line, how many people would be in line? "}, {"iIndex": 136, "lAlignments": [26, 100, 50], "lEquations": ["X=((38.0+28.0)-20.0)"], "lSolutions": [46.0], "sQuestion": " The school cafeteria had 38 apples. If they used 20 to make lunch for the students and then bought 28 more, how many apples would they have? "}, {"iIndex": 137, "lAlignments": [12, 77, 39], "lEquations": ["X=((28.0+36.0)-4.0)"], "lSolutions": [60.0], "sQuestion": " <PERSON> had 28 socks. If he threw away 4 old ones that didn't fit and bought 36 new ones, how many socks would he have? "}, {"iIndex": 138, "lAlignments": [28, 110, 56], "lEquations": ["X=((23.0+21.0)-7.0)"], "lSolutions": [37.0], "sQuestion": " For Halloween <PERSON> scored 23 pieces of candy. She ate 7 pieces the first night and then her sister gave her 21 more pieces. How many pieces of candy does <PERSON> have now? "}, {"iIndex": 139, "lAlignments": [41, 107, 87], "lEquations": ["X=((10.0+26.0)-4.0)"], "lSolutions": [32.0], "sQuestion": " <PERSON> was playing a video game and had 10 lives. In a hard part of the game she lost 4 lives. If she got 26 more lives in the next level, how many lives would she have? "}, {"iIndex": 140, "lAlignments": [15, 68, 36], "lEquations": ["X=((6.0+12.0)-5.0)"], "lSolutions": [13.0], "sQuestion": " A florist had 6 roses. If she sold 5 of them and then later picked 12 more, how many roses would she have? "}, {"iIndex": 141, "lAlignments": [28, 111, 56], "lEquations": ["X=((33.0+19.0)-17.0)"], "lSolutions": [35.0], "sQuestion": " For Halloween <PERSON> scored 33 pieces of candy. She ate 17 pieces the first night and then her sister gave her 19 more pieces. How many pieces of candy does <PERSON> have now? "}, {"iIndex": 142, "lAlignments": [26, 100, 50], "lEquations": ["X=((14.0+49.0)-13.0)"], "lSolutions": [50.0], "sQuestion": " The school cafeteria had 14 apples. If they used 13 to make lunch for the students and then bought 49 more, how many apples would they have? "}, {"iIndex": 143, "lAlignments": [12, 80, 33], "lEquations": ["X=((43.0+14.0)-19.0)"], "lSolutions": [38.0], "sQuestion": " <PERSON> had 43 books. If he sold 19 of them and used the money he earned to buy 14 new books, how many books would <PERSON> have? "}, {"iIndex": 144, "lAlignments": [15, 74, 53], "lEquations": ["X=((29.0+29.0)-25.0)"], "lSolutions": [33.0], "sQuestion": " A teacher had 29 worksheets to grade. If she graded 25, but then another 29 were turned in, how many worksheets would she have to grade? "}, {"iIndex": 145, "lAlignments": [28, 103, 82], "lEquations": ["X=((35.0+10.0)-10.0)"], "lSolutions": [35.0], "sQuestion": " In fourth grade there were 35 students at the start of the year. During the year 10 students left and 10 new students came to school. How many students were in fourth grade at the end? "}, {"iIndex": 146, "lAlignments": [37, 86, 62], "lEquations": ["X=((19.0+27.0)-6.0)"], "lSolutions": [40.0], "sQuestion": " For the school bake sale <PERSON> made 19 cupcakes. If she sold 6 of them and then made 27 more, how many cupcakes would she have? "}, {"iIndex": 147, "lAlignments": [13, 80, 53], "lEquations": ["X=((40.0****)-37.0)"], "lSolutions": [10.0], "sQuestion": " A store had 40 oranges in a bin. If they threw away 37 of the old ones and put 7 new ones in the bin how many would be in the bin? "}, {"iIndex": 148, "lAlignments": [26, 87, 50], "lEquations": ["X=((46.0+39.0)-27.0)"], "lSolutions": [58.0], "sQuestion": " At the arcade <PERSON> won 46 tickets. If he spent 27 tickets on a beanie and later won 39 more tickets, how many would he have? "}, {"iIndex": 149, "lAlignments": [14, 86, 59], "lEquations": ["X=((48.0+42.0)-45.0)"], "lSolutions": [45.0], "sQuestion": " <PERSON> picked 48 carrots from her garden. If she threw out 45 of them and then picked 42 more the next day, how many carrots would she have total? "}, {"iIndex": 150, "lAlignments": [9, 74, 36], "lEquations": ["X=((10.0+36.0)-3.0)"], "lSolutions": [43.0], "sQuestion": " <PERSON> had 10 socks. If he threw away 3 old ones that didn't fit and bought 36 new ones, how many socks would he have? "}, {"iIndex": 151, "lAlignments": [11, 85, 59], "lEquations": ["X=((14.0+45.0)-8.0)"], "lSolutions": [51.0], "sQuestion": " <PERSON> had 14 bottles of water in her fridge. If she drank 8 of them and then bought 45 more, how many bottles would she have? "}, {"iIndex": 152, "lAlignments": [15, 73, 53], "lEquations": ["X=((34.0+36.0)-7.0)"], "lSolutions": [63.0], "sQuestion": " A teacher had 34 worksheets to grade. If she graded 7, but then another 36 were turned in, how many worksheets would she have to grade? "}, {"iIndex": 153, "lAlignments": [24, 109, 65], "lEquations": ["X=((7.0+8.0)-4.0)"], "lSolutions": [11.0], "sQuestion": " At the fair there were 7 people in line for the bumper cars. If 4 of them got tired of waiting and left and 8 more got in line, how many people would be in line? "}, {"iIndex": 154, "lAlignments": [11, 73, 46], "lEquations": ["X=((7.0+14.0)-2.0)"], "lSolutions": [19.0], "sQuestion": " <PERSON> had 7 coloring books. If she gave away 2 of them, but then bought 14 more, how many would she have total? "}, {"iIndex": 155, "lAlignments": [24, 85, 48], "lEquations": ["X=((25.0+15.0)-22.0)"], "lSolutions": [18.0], "sQuestion": " At the arcade <PERSON> won 25 tickets. If he spent 22 tickets on a beanie and later won 15 more tickets, how many would he have? "}, {"iIndex": 156, "lAlignments": [11, 89, 54], "lEquations": ["X=((30.0+10.0)-8.0)"], "lSolutions": [32.0], "sQuestion": " <PERSON> had 30 songs on her mp3 player. If she deleted 8 old songs from it and then added 10 new songs, how many songs does she have on her mp3 player? "}, {"iIndex": 157, "lAlignments": [26, 100, 50], "lEquations": ["X=((23.0****)-20.0)"], "lSolutions": [9.0], "sQuestion": " The school cafeteria had 23 apples. If they used 20 to make lunch for the students and then bought 6 more, how many apples would they have? "}, {"iIndex": 158, "lAlignments": [40, 107, 86], "lEquations": ["X=((38.0+32.0)-16.0)"], "lSolutions": [54.0], "sQuestion": " <PERSON> was playing a video game and had 38 lives. In a hard part of the game she lost 16 lives. If she got 32 more lives in the next level, how many lives would she have? "}, {"iIndex": 159, "lAlignments": [14, 85, 59], "lEquations": ["X=((19.0+46.0)-4.0)"], "lSolutions": [61.0], "sQuestion": " <PERSON> picked 19 carrots from her garden. If she threw out 4 of them and then picked 46 more the next day, how many carrots would she have total? "}, {"iIndex": 160, "lAlignments": [11, 86, 56], "lEquations": ["X=((45.0+46.0)-20.0)"], "lSolutions": [71.0], "sQuestion": " <PERSON> had 45 dollars in January. By March he had spent 20 dollars. If he got another 46 dollars from his mom, how much money would he have? "}, {"iIndex": 161, "lAlignments": [26, 99, 50], "lEquations": ["X=((17.0+23.0)-2.0)"], "lSolutions": [38.0], "sQuestion": " The school cafeteria had 17 apples. If they used 2 to make lunch for the students and then bought 23 more, how many apples would they have? "}, {"iIndex": 162, "lAlignments": [40, 107, 86], "lEquations": ["X=((47.0+46.0)-23.0)"], "lSolutions": [70.0], "sQuestion": " <PERSON> was playing a video game and had 47 lives. In a hard part of the game she lost 23 lives. If she got 46 more lives in the next level, how many lives would she have? "}, {"iIndex": 163, "lAlignments": [14, 85, 59], "lEquations": ["X=((12.0+21.0)-2.0)"], "lSolutions": [31.0], "sQuestion": " <PERSON> picked 12 carrots from her garden. If she threw out 2 of them and then picked 21 more the next day, how many carrots would she have total? "}, {"iIndex": 164, "lAlignments": [12, 91, 55], "lEquations": ["X=((34.0+44.0)-14.0)"], "lSolutions": [64.0], "sQuestion": " <PERSON> had 34 songs on her mp3 player. If she deleted 14 old songs from it and then added 44 new songs, how many songs does she have on her mp3 player? "}, {"iIndex": 165, "lAlignments": [9, 75, 29], "lEquations": ["X=((5.0+38.0)-4.0)"], "lSolutions": [39.0], "sQuestion": " <PERSON> had 5 books. If he sold 4 of them and used the money he earned to buy 38 new books, how many books would <PERSON> have? "}, {"iIndex": 166, "lAlignments": [10, 76, 37], "lEquations": ["X=((33.0+13.0)-19.0)"], "lSolutions": [27.0], "sQuestion": " <PERSON> had 33 socks. If he threw away 19 old ones that didn't fit and bought 13 new ones, how many socks would he have? "}, {"iIndex": 167, "lAlignments": [37, 86, 62], "lEquations": ["X=((19.0+10.0)-5.0)"], "lSolutions": [24.0], "sQuestion": " For the school bake sale <PERSON> made 19 cupcakes. If she sold 5 of them and then made 10 more, how many cupcakes would she have? "}, {"iIndex": 168, "lAlignments": [13, 80, 53], "lEquations": ["X=((50.0+24.0)-40.0)"], "lSolutions": [34.0], "sQuestion": " A store had 50 oranges in a bin. If they threw away 40 of the old ones and put 24 new ones in the bin how many would be in the bin? "}, {"iIndex": 169, "lAlignments": [28, 101, 81], "lEquations": ["X=((8.0+8.0)-5.0)"], "lSolutions": [11.0], "sQuestion": " In fourth grade there were 8 students at the start of the year. During the year 5 students left and 8 new students came to school. How many students were in fourth grade at the end? "}, {"iIndex": 170, "lAlignments": [15, 87, 60], "lEquations": ["X=((23.0+47.0)-10.0)"], "lSolutions": [60.0], "sQuestion": " <PERSON> picked 23 carrots from her garden. If she threw out 10 of them and then picked 47 more the next day, how many carrots would she have total? "}, {"iIndex": 171, "lAlignments": [9, 84, 57], "lEquations": ["X=((42.0+30.0)-25.0)"], "lSolutions": [47.0], "sQuestion": " <PERSON> had 42 bottles of water in her fridge. If she drank 25 of them and then bought 30 more, how many bottles would she have? "}, {"iIndex": 172, "lAlignments": [11, 89, 54], "lEquations": ["X=((11.0+24.0)-7.0)"], "lSolutions": [28.0], "sQuestion": " <PERSON> had 11 songs on her mp3 player. If she deleted 7 old songs from it and then added 24 new songs, how many songs does she have on her mp3 player? "}, {"iIndex": 173, "lAlignments": [13, 78, 52], "lEquations": ["X=((5.0+28.0)-2.0)"], "lSolutions": [31.0], "sQuestion": " A store had 5 oranges in a bin. If they threw away 2 of the old ones and put 28 new ones in the bin how many would be in the bin? "}, {"iIndex": 174, "lAlignments": [10, 78, 31], "lEquations": ["X=((48.0+38.0)-19.0)"], "lSolutions": [67.0], "sQuestion": " <PERSON> had 48 books. If he sold 19 of them and used the money he earned to buy 38 new books, how many books would <PERSON> have? "}, {"iIndex": 175, "lAlignments": [28, 102, 82], "lEquations": ["X=((31.0+11.0)-5.0)"], "lSolutions": [37.0], "sQuestion": " In fourth grade there were 31 students at the start of the year. During the year 5 students left and 11 new students came to school. How many students were in fourth grade at the end? "}, {"iIndex": 176, "lAlignments": [37, 87, 62], "lEquations": ["X=((42.0+39.0)-22.0)"], "lSolutions": [59.0], "sQuestion": " For the school bake sale <PERSON> made 42 cupcakes. If she sold 22 of them and then made 39 more, how many cupcakes would she have? "}, {"iIndex": 177, "lAlignments": [15, 69, 37], "lEquations": ["X=((11.0+32.0)-2.0)"], "lSolutions": [41.0], "sQuestion": " A florist had 11 roses. If she sold 2 of them and then later picked 32 more, how many roses would she have? "}, {"iIndex": 178, "lAlignments": [11, 81, 46], "lEquations": ["X=((11.0+14.0)-3.0)"], "lSolutions": [22.0], "sQuestion": " <PERSON> had 11 dollars. At the store he spent $3 on a new game. If he got another 14 dollars for his allowance, how much money does he have now? "}, {"iIndex": 179, "lAlignments": [40, 106, 86], "lEquations": ["X=((14.0+36.0)-4.0)"], "lSolutions": [46.0], "sQuestion": " <PERSON> was playing a video game and had 14 lives. In a hard part of the game she lost 4 lives. If she got 36 more lives in the next level, how many lives would she have? "}, {"iIndex": 180, "lAlignments": [10, 85, 55], "lEquations": ["X=((48.0+21.0)-11.0)"], "lSolutions": [58.0], "sQuestion": " <PERSON> had 48 dollars in January. By March he had spent 11 dollars. If he got another 21 dollars from his mom, how much money would he have? "}, {"iIndex": 181, "lAlignments": [40, 107, 86], "lEquations": ["X=((42.0+24.0)-25.0)"], "lSolutions": [41.0], "sQuestion": " <PERSON> was playing a video game and had 42 lives. In a hard part of the game she lost 25 lives. If she got 24 more lives in the next level, how many lives would she have? "}, {"iIndex": 182, "lAlignments": [11, 74, 47], "lEquations": ["X=((30.0+35.0)-7.0)"], "lSolutions": [58.0], "sQuestion": " <PERSON><PERSON> had 30 coloring books. If she gave away 7 of them, but then bought 35 more, how many would she have total? "}, {"iIndex": 183, "lAlignments": [26, 99, 50], "lEquations": ["X=((12.0+19.0)-8.0)"], "lSolutions": [23.0], "sQuestion": " The school cafeteria had 12 apples. If they used 8 to make lunch for the students and then bought 19 more, how many apples would they have? "}, {"iIndex": 184, "lAlignments": [24, 111, 66], "lEquations": ["X=((31.0+25.0)-25.0)"], "lSolutions": [31.0], "sQuestion": " At the fair there were 31 people in line for the bumper cars. If 25 of them got tired of waiting and left and 25 more got in line, how many people would be in line? "}, {"iIndex": 185, "lAlignments": [15, 70, 37], "lEquations": ["X=((50.0+21.0)-15.0)"], "lSolutions": [56.0], "sQuestion": " A florist had 50 roses. If she sold 15 of them and then later picked 21 more, how many roses would she have? "}, {"iIndex": 186, "lAlignments": [9, 86, 51], "lEquations": ["X=((6.0+20.0)-3.0)"], "lSolutions": [23.0], "sQuestion": " <PERSON> had 6 songs on her mp3 player. If she deleted 3 old songs from it and then added 20 new songs, how many songs does she have on her mp3 player? "}, {"iIndex": 187, "lAlignments": [24, 84, 48], "lEquations": ["X=((11.0+10.0)-5.0)"], "lSolutions": [16.0], "sQuestion": " At the arcade <PERSON> won 11 tickets. If he spent 5 tickets on a beanie and later won 10 more tickets, how many would he have? "}, {"iIndex": 188, "lAlignments": [14, 79, 42], "lEquations": ["X=((33.0+26.0)-31.0)"], "lSolutions": [28.0], "sQuestion": " A waiter had 33 customers to wait on. If 31 customers left and he got another 26 customers, how many customers would he have? "}, {"iIndex": 189, "lAlignments": [28, 102, 82], "lEquations": ["X=((11.0+42.0)-6.0)"], "lSolutions": [47.0], "sQuestion": " In fourth grade there were 11 students at the start of the year. During the year 6 students left and 42 new students came to school. How many students were in fourth grade at the end? "}, {"iIndex": 190, "lAlignments": [12, 77, 39], "lEquations": ["X=((11.0+26.0)-4.0)"], "lSolutions": [33.0], "sQuestion": " <PERSON> had 11 socks. If he threw away 4 old ones that didn't fit and bought 26 new ones, how many socks would he have? "}, {"iIndex": 191, "lAlignments": [13, 80, 53], "lEquations": ["X=((40.0+21.0)-25.0)"], "lSolutions": [36.0], "sQuestion": " A store had 40 oranges in a bin. If they threw away 25 of the old ones and put 21 new ones in the bin how many would be in the bin? "}, {"iIndex": 192, "lAlignments": [26, 85, 49], "lEquations": ["X=((9.0****)-4.0)"], "lSolutions": [9.0], "sQuestion": " At the arcade <PERSON> won 9 tickets. If he spent 4 tickets on a beanie and later won 4 more tickets, how many would he have? "}, {"iIndex": 193, "lAlignments": [28, 101, 81], "lEquations": ["X=((4.0+42.0)-3.0)"], "lSolutions": [43.0], "sQuestion": " In fourth grade there were 4 students at the start of the year. During the year 3 students left and 42 new students came to school. How many students were in fourth grade at the end? "}, {"iIndex": 194, "lAlignments": [14, 86, 59], "lEquations": ["X=((28.0****)-11.0)"], "lSolutions": [26.0], "sQuestion": " <PERSON> picked 28 carrots from her garden. If she threw out 11 of them and then picked 9 more the next day, how many carrots would she have total? "}, {"iIndex": 195, "lAlignments": [11, 79, 32], "lEquations": ["X=((25.0+30.0)-21.0)"], "lSolutions": [34.0], "sQuestion": " <PERSON> had 25 books. If he sold 21 of them and used the money he earned to buy 30 new books, how many books would <PERSON> have? "}, {"iIndex": 196, "lAlignments": [15, 68, 36], "lEquations": ["X=((5.0+34.0)-3.0)"], "lSolutions": [36.0], "sQuestion": " A florist had 5 roses. If she sold 3 of them and then later picked 34 more, how many roses would she have? "}, {"iIndex": 197, "lAlignments": [40, 106, 86], "lEquations": ["X=((10.0+37.0)-6.0)"], "lSolutions": [41.0], "sQuestion": " <PERSON> was playing a video game and had 10 lives. In a hard part of the game she lost 6 lives. If she got 37 more lives in the next level, how many lives would she have? "}, {"iIndex": 198, "lAlignments": [10, 79, 44], "lEquations": ["X=((5.0+26.0)-2.0)"], "lSolutions": [29.0], "sQuestion": " <PERSON> had 5 dollars. At the store he spent $2 on a new game. If he got another 26 dollars for his allowance, how much money does he have now? "}, {"iIndex": 199, "lAlignments": [37, 87, 62], "lEquations": ["X=((28.0+8.0)-25.0)"], "lSolutions": [11.0], "sQuestion": " For the school bake sale <PERSON> made 28 cupcakes. If she sold 25 of them and then made 8 more, how many cupcakes would she have? "}, {"iIndex": 200, "lAlignments": [105, 50, 78], "lEquations": ["X=(5.0*(7.0+3.0))"], "lSolutions": [50.0], "sQuestion": " At the town carnival <PERSON> rode the ferris wheel 7 times and the bumper cars 3 times. If each ride cost 5 tickets, how many tickets did he use? "}, {"iIndex": 201, "lAlignments": [62, 113, 127], "lEquations": ["X=(4.0*(2.0****))"], "lSolutions": [32.0], "sQuestion": " <PERSON> was unboxing some of her old winter clothes. She found 4 boxes of clothing and inside each box there were 2 scarves and 6 mittens. How many pieces of winter clothing did <PERSON> have total? "}, {"iIndex": 202, "lAlignments": [14, 47, 59], "lEquations": ["X=(9.0*(7.0+3.0))"], "lSolutions": [90.0], "sQuestion": " A waiter had 9 tables he was waiting on, with 7 women and 3 men at each table. How many customers total did the waiter have? "}, {"iIndex": 203, "lAlignments": [66, 93, 105], "lEquations": ["X=(3.0*(2.0****))"], "lSolutions": [12.0], "sQuestion": " April's discount flowers was having a sale where each flower was 3 dollars. If <PERSON> bought 2 roses and 2 daisies, how much did she spend? "}, {"iIndex": 204, "lAlignments": [87, 12, 41], "lEquations": ["X=(5.0*(2.0****))"], "lSolutions": [30.0], "sQuestion": " <PERSON> had 2 pages of math homework and 4 pages of reading homework. If each page had 5 problems on it, how many problems did she have to complete total? "}, {"iIndex": 205, "lAlignments": [49, 100, 135], "lEquations": ["X=(5.0*(4.0+3.0))"], "lSolutions": [35.0], "sQuestion": " <PERSON> was playing a video game where she scores 5 points for each treasure she finds. If she found 4 treasures on the first level and 3 on the second, what would her score be? "}, {"iIndex": 206, "lAlignments": [106, 12, 55], "lEquations": ["X=(7.0*(7.0****))"], "lSolutions": [63.0], "sQuestion": " There were 7 friends playing a video game online when 2 more players joined the game. If each player had 7 lives, how many lives did they have total? "}, {"iIndex": 207, "lAlignments": [86, 13, 44], "lEquations": ["X=(9.0*(6.0****))"], "lSolutions": [90.0], "sQuestion": " <PERSON> bought 6 boxes of chocolate candy and 4 boxes of caramel candy. If each box has 9 pieces inside it, how much candy did he have total? "}, {"iIndex": 208, "lAlignments": [17, 48, 62], "lEquations": ["X=(6.0*(6.0****))"], "lSolutions": [48.0], "sQuestion": " A pet store has 6 bird cages. If each cage has 6 parrots and 2 parakeets in it, how many birds does the pet store have total? "}, {"iIndex": 209, "lAlignments": [81, 107, 138], "lEquations": ["X=(9.0*(6.0****))"], "lSolutions": [72.0], "sQuestion": " <PERSON> was organizing her book case making sure each of the shelves had exactly 9 books on it. If she had 6 shelves of mystery books and 2 shelves of picture books, how many books did she have total? "}, {"iIndex": 210, "lAlignments": [63, 114, 128], "lEquations": ["X=(7.0*(3.0****))"], "lSolutions": [49.0], "sQuestion": " <PERSON> was unboxing some of her old winter clothes. She found 7 boxes of clothing and inside each box there were 3 scarves and 4 mittens. How many pieces of winter clothing did <PERSON> have total? "}, {"iIndex": 211, "lAlignments": [17, 48, 62], "lEquations": ["X=(9.0*(2.0****))"], "lSolutions": [36.0], "sQuestion": " A pet store has 9 bird cages. If each cage has 2 parrots and 2 parakeets in it, how many birds does the pet store have total? "}, {"iIndex": 212, "lAlignments": [86, 13, 44], "lEquations": ["X=(8.0*(7.0+3.0))"], "lSolutions": [80.0], "sQuestion": " <PERSON> bought 7 boxes of chocolate candy and 3 boxes of caramel candy. If each box has 8 pieces inside it, how much candy did he have total? "}, {"iIndex": 213, "lAlignments": [94, 36, 49], "lEquations": ["X=(3.0*(2.0****))"], "lSolutions": [21.0], "sQuestion": " At Billy's Restaurant a group with 2 adults and 5 children came in to eat. If each meal cost 3 dollars, how much was the bill? "}, {"iIndex": 214, "lAlignments": [126, 63, 97], "lEquations": ["X=(8.0*(6.0+3.0))"], "lSolutions": [72.0], "sQuestion": " <PERSON> was collecting cans for recycling. On Saturday he filled 6 bags up and on Sunday he filled 3 more bags. If each bag had 8 cans in it, how many cans did he pick up total? "}, {"iIndex": 215, "lAlignments": [153, 44, 86], "lEquations": ["X=(5.0*(5.0****))"], "lSolutions": [50.0], "sQuestion": " While playing a trivia game, <PERSON> answered 5 questions correct in the first half and 5 questions correct in the second half. If each question was worth 5 points, what was his final score? "}, {"iIndex": 216, "lAlignments": [64, 93, 135], "lEquations": ["X=(4.0*(3.0****))"], "lSolutions": [32.0], "sQuestion": " <PERSON>'s favorite band was holding a concert where tickets were 4 dollars each. <PERSON> bought 3 tickets for herself and her friends and 5 extra tickets in case anyone else wanted to go. How much did she spend? "}, {"iIndex": 217, "lAlignments": [113, 54, 78], "lEquations": ["X=(3.0*(5.0****))"], "lSolutions": [30.0], "sQuestion": " <PERSON> was putting his spare change into piles. He had 5 piles of quarters and 5 piles of dimes. If each pile had 3 coins in it, how many coins did he have total? "}, {"iIndex": 218, "lAlignments": [71, 128, 158], "lEquations": ["X=(8.0*(6.0****))"], "lSolutions": [64.0], "sQuestion": " <PERSON> and his friend were buying trick decks from the magic shop for 8 dollars each. How much did they spend if <PERSON> bought 6 decks and his friend bought 2 decks? "}, {"iIndex": 219, "lAlignments": [86, 11, 40], "lEquations": ["X=(9.0*(7.0+3.0))"], "lSolutions": [90.0], "sQuestion": " <PERSON> had 7 pages of math homework and 3 pages of reading homework. If each page had 9 problems on it, how many problems did she have to complete total? "}, {"iIndex": 220, "lAlignments": [48, 99, 134], "lEquations": ["X=(7.0*(2.0****))"], "lSolutions": [56.0], "sQuestion": " <PERSON> was playing a video game where she scores 7 points for each treasure she finds. If she found 2 treasures on the first level and 6 on the second, what would her score be? "}, {"iIndex": 221, "lAlignments": [79, 105, 136], "lEquations": ["X=(9.0*(3.0****))"], "lSolutions": [72.0], "sQuestion": " <PERSON> was organizing her book case making sure each of the shelves had exactly 9 books on it. If she had 3 shelves of mystery books and 5 shelves of picture books, how many books did she have total? "}, {"iIndex": 222, "lAlignments": [106, 12, 55], "lEquations": ["X=(6.0*(2.0****))"], "lSolutions": [24.0], "sQuestion": " There were 2 friends playing a video game online when 2 more players joined the game. If each player had 6 lives, how many lives did they have total? "}, {"iIndex": 223, "lAlignments": [72, 14, 31], "lEquations": ["X=(6.0*(4.0****))"], "lSolutions": [48.0], "sQuestion": " <PERSON> bought 4 new chairs and 4 new tables for her house. If she spent 6 minutes on each piece furniture putting it together, how many minutes did it take her to finish? "}, {"iIndex": 224, "lAlignments": [66, 91, 103], "lEquations": ["X=(3.0*(8.0****))"], "lSolutions": [30.0], "sQuestion": " April's discount flowers was having a sale where each flower was 3 dollars. If <PERSON> bought 8 roses and 2 daisies, how much did she spend? "}, {"iIndex": 225, "lAlignments": [62, 113, 127], "lEquations": ["X=(6.0*(5.0****))"], "lSolutions": [60.0], "sQuestion": " <PERSON> was unboxing some of her old winter clothes. She found 6 boxes of clothing and inside each box there were 5 scarves and 5 mittens. How many pieces of winter clothing did <PERSON> have total? "}, {"iIndex": 226, "lAlignments": [125, 62, 96], "lEquations": ["X=(6.0*(4.0+3.0))"], "lSolutions": [42.0], "sQuestion": " <PERSON> was collecting cans for recycling. On Saturday he filled 4 bags up and on Sunday he filled 3 more bags. If each bag had 6 cans in it, how many cans did he pick up total? "}, {"iIndex": 227, "lAlignments": [65, 104, 137], "lEquations": ["X=(5.0*(7.0****))"], "lSolutions": [45.0], "sQuestion": " <PERSON> was working as a sacker at a grocery store where he made 5 dollars an hour. On Monday he worked 7 hours and on Tuesday he worked 2 hours. How much money did <PERSON> make in those two days? "}, {"iIndex": 228, "lAlignments": [154, 45, 87], "lEquations": ["X=(3.0*(3.0****))"], "lSolutions": [15.0], "sQuestion": " While playing a trivia game, <PERSON> answered 3 questions correct in the first half and 2 questions correct in the second half. If each question was worth 3 points, what was his final score? "}, {"iIndex": 229, "lAlignments": [71, 128, 158], "lEquations": ["X=(9.0*(4.0****))"], "lSolutions": [72.0], "sQuestion": " <PERSON> and his friend were buying trick decks from the magic shop for 9 dollars each. How much did they spend if <PERSON> bought 4 decks and his friend bought 4 decks? "}, {"iIndex": 230, "lAlignments": [68, 122, 152], "lEquations": ["X=(8.0*(3.0****))"], "lSolutions": [64.0], "sQuestion": " <PERSON> and his friend were buying trick decks from the magic shop for 8 dollars each. How much did they spend if <PERSON> bought 3 decks and his friend bought 5 decks? "}, {"iIndex": 231, "lAlignments": [125, 47, 68], "lEquations": ["X=(7.0*(2.0+8.0))"], "lSolutions": [70.0], "sQuestion": " While shopping for music online, <PERSON> bought 2 country albums and 8 pop albums. Each album came with a lyric sheet and had 7 songs. How many songs did <PERSON> buy total? "}, {"iIndex": 232, "lAlignments": [65, 104, 137], "lEquations": ["X=(6.0*(5.0****))"], "lSolutions": [60.0], "sQuestion": " <PERSON> was working as a sacker at a grocery store where he made 6 dollars an hour. On Monday he worked 5 hours and on Tuesday he worked 5 hours. How much money did <PERSON> make in those two days? "}, {"iIndex": 233, "lAlignments": [17, 48, 62], "lEquations": ["X=(6.0*(2.0****))"], "lSolutions": [54.0], "sQuestion": " A pet store has 6 bird cages. If each cage has 2 parrots and 7 parakeets in it, how many birds does the pet store have total? "}, {"iIndex": 234, "lAlignments": [72, 14, 31], "lEquations": ["X=(8.0*(2.0****))"], "lSolutions": [32.0], "sQuestion": " <PERSON> bought 2 new chairs and 2 new tables for her house. If she spent 8 minutes on each piece furniture putting it together, how many minutes did it take her to finish? "}, {"iIndex": 235, "lAlignments": [50, 101, 136], "lEquations": ["X=(8.0*(2.0****))"], "lSolutions": [32.0], "sQuestion": " <PERSON> was playing a video game where she scores 8 points for each treasure she finds. If she found 2 treasures on the first level and 2 on the second, what would her score be? "}, {"iIndex": 236, "lAlignments": [14, 47, 59], "lEquations": ["X=(6.0*(3.0****))"], "lSolutions": [48.0], "sQuestion": " A waiter had 6 tables he was waiting on, with 3 women and 5 men at each table. How many customers total did the waiter have? "}, {"iIndex": 237, "lAlignments": [125, 59, 80], "lEquations": ["X=(7.0*(3.0****))"], "lSolutions": [70.0], "sQuestion": " <PERSON> was selling her necklaces at a garage sale. She sold 3 bead necklaces and 7 gem stone necklaces. If each necklace cost 7 dollars, how much money did she earn? "}, {"iIndex": 238, "lAlignments": [106, 51, 79], "lEquations": ["X=(7.0*(5.0****))"], "lSolutions": [63.0], "sQuestion": " At the town carnival <PERSON> rode the ferris wheel 5 times and the bumper cars 4 times. If each ride cost 7 tickets, how many tickets did he use? "}, {"iIndex": 239, "lAlignments": [106, 12, 55], "lEquations": ["X=(8.0*(5.0****))"], "lSolutions": [56.0], "sQuestion": " There were 5 friends playing a video game online when 2 more players joined the game. If each player had 8 lives, how many lives did they have total? "}, {"iIndex": 240, "lAlignments": [126, 60, 81], "lEquations": ["X=(3.0*(4.0+3.0))"], "lSolutions": [21.0], "sQuestion": " <PERSON> was selling her necklaces at a garage sale. She sold 4 bead necklaces and 3 gem stone necklaces. If each necklace cost 3 dollars, how much money did she earn? "}, {"iIndex": 241, "lAlignments": [65, 104, 137], "lEquations": ["X=(6.0*(3.0****))"], "lSolutions": [48.0], "sQuestion": " <PERSON> was working as a sacker at a grocery store where he made 6 dollars an hour. On Monday he worked 3 hours and on Tuesday he worked 5 hours. How much money did <PERSON> make in those two days? "}, {"iIndex": 242, "lAlignments": [153, 44, 86], "lEquations": ["X=(3.0*(3.0****))"], "lSolutions": [24.0], "sQuestion": " While playing a trivia game, <PERSON> answered 3 questions correct in the first half and 5 questions correct in the second half. If each question was worth 3 points, what was his final score? "}, {"iIndex": 243, "lAlignments": [47, 98, 133], "lEquations": ["X=(4.0*(6.0****))"], "lSolutions": [32.0], "sQuestion": " <PERSON> was playing a video game where she scores 4 points for each treasure she finds. If she found 6 treasures on the first level and 2 on the second, what would her score be? "}, {"iIndex": 244, "lAlignments": [14, 47, 59], "lEquations": ["X=(9.0*(4.0+3.0))"], "lSolutions": [63.0], "sQuestion": " A waiter had 9 tables he was waiting on, with 4 women and 3 men at each table. How many customers total did the waiter have? "}, {"iIndex": 245, "lAlignments": [80, 106, 137], "lEquations": ["X=(6.0*(5.0****))"], "lSolutions": [54.0], "sQuestion": " <PERSON> was organizing her book case making sure each of the shelves had exactly 6 books on it. If she had 5 shelves of mystery books and 4 shelves of picture books, how many books did she have total? "}, {"iIndex": 246, "lAlignments": [126, 48, 69], "lEquations": ["X=(9.0*(6.0****))"], "lSolutions": [72.0], "sQuestion": " While shopping for music online, <PERSON> bought 6 country albums and 2 pop albums. Each album came with a lyric sheet and had 9 songs. How many songs did <PERSON> buy total? "}, {"iIndex": 247, "lAlignments": [95, 37, 50], "lEquations": ["X=(3.0*(2.0****))"], "lSolutions": [18.0], "sQuestion": " At Oliver's Restaurant a group with 2 adults and 4 children came in to eat. If each meal cost 3 dollars, how much was the bill? "}, {"iIndex": 248, "lAlignments": [88, 13, 42], "lEquations": ["X=(3.0*(6.0****))"], "lSolutions": [30.0], "sQuestion": " <PERSON> had 6 pages of math homework and 4 pages of reading homework. If each page had 3 problems on it, how many problems did she have to complete total? "}, {"iIndex": 249, "lAlignments": [66, 93, 105], "lEquations": ["X=(3.0*(5.0****))"], "lSolutions": [27.0], "sQuestion": " April's discount flowers was having a sale where each flower was 3 dollars. If <PERSON> bought 5 roses and 4 daisies, how much did she spend? "}, {"iIndex": 250, "lAlignments": [60, 111, 125], "lEquations": ["X=(8.0*(4.0****))"], "lSolutions": [80.0], "sQuestion": " <PERSON> was unboxing some of her old winter clothes. She found 8 boxes of clothing and inside each box there were 4 scarves and 6 mittens. How many pieces of winter clothing did <PERSON> have total? "}, {"iIndex": 251, "lAlignments": [106, 12, 55], "lEquations": ["X=(3.0*(2.0****))"], "lSolutions": [12.0], "sQuestion": " There were 2 friends playing a video game online when 2 more players joined the game. If each player had 3 lives, how many lives did they have total? "}, {"iIndex": 252, "lAlignments": [86, 13, 44], "lEquations": ["X=(4.0*(2.0****))"], "lSolutions": [28.0], "sQuestion": " <PERSON> bought 2 boxes of chocolate candy and 5 boxes of caramel candy. If each box has 4 pieces inside it, how much candy did he have total? "}, {"iIndex": 253, "lAlignments": [17, 48, 62], "lEquations": ["X=(8.0*(2.0****))"], "lSolutions": [72.0], "sQuestion": " A pet store has 8 bird cages. If each cage has 2 parrots and 7 parakeets in it, how many birds does the pet store have total? "}, {"iIndex": 254, "lAlignments": [63, 91, 133], "lEquations": ["X=(6.0*(8.0****))"], "lSolutions": [60.0], "sQuestion": " <PERSON>'s favorite band was holding a concert where tickets were 6 dollars each. <PERSON> bought 8 tickets for herself and her friends and 2 extra tickets in case anyone else wanted to go. How much did she spend? "}, {"iIndex": 255, "lAlignments": [92, 34, 47], "lEquations": ["X=(8.0*(2.0****))"], "lSolutions": [56.0], "sQuestion": " At Tom's Restaurant a group with 2 adults and 5 children came in to eat. If each meal cost 8 dollars, how much was the bill? "}, {"iIndex": 256, "lAlignments": [79, 105, 136], "lEquations": ["X=(4.0*(5.0+3.0))"], "lSolutions": [32.0], "sQuestion": " <PERSON> was organizing her book case making sure each of the shelves had exactly 4 books on it. If she had 5 shelves of mystery books and 3 shelves of picture books, how many books did she have total? "}, {"iIndex": 257, "lAlignments": [126, 48, 69], "lEquations": ["X=(8.0*(4.0****))"], "lSolutions": [72.0], "sQuestion": " While shopping for music online, <PERSON> bought 4 country albums and 5 pop albums. Each album came with a lyric sheet and had 8 songs. How many songs did <PERSON> buy total? "}, {"iIndex": 258, "lAlignments": [126, 60, 81], "lEquations": ["X=(9.0*(7.0+3.0))"], "lSolutions": [90.0], "sQuestion": " <PERSON> was selling her necklaces at a garage sale. She sold 7 bead necklaces and 3 gem stone necklaces. If each necklace cost 9 dollars, how much money did she earn? "}, {"iIndex": 259, "lAlignments": [50, 101, 136], "lEquations": ["X=(9.0*(5.0****))"], "lSolutions": [63.0], "sQuestion": " <PERSON> was playing a video game where she scores 9 points for each treasure she finds. If she found 5 treasures on the first level and 2 on the second, what would her score be? "}, {"iIndex": 260, "lAlignments": [66, 93, 105], "lEquations": ["X=(6.0*(5.0****))"], "lSolutions": [60.0], "sQuestion": " April's discount flowers was having a sale where each flower was 6 dollars. If <PERSON> bought 5 roses and 5 daisies, how much did she spend? "}, {"iIndex": 261, "lAlignments": [17, 48, 62], "lEquations": ["X=(9.0*(2.0****))"], "lSolutions": [72.0], "sQuestion": " A pet store has 9 bird cages. If each cage has 2 parrots and 6 parakeets in it, how many birds does the pet store have total? "}, {"iIndex": 262, "lAlignments": [114, 55, 79], "lEquations": ["X=(7.0*(3.0+3.0))"], "lSolutions": [42.0], "sQuestion": " <PERSON> was putting his spare change into piles. He had 3 piles of quarters and 3 piles of dimes. If each pile had 7 coins in it, how many coins did he have total? "}, {"iIndex": 263, "lAlignments": [106, 12, 55], "lEquations": ["X=(8.0*(2.0****))"], "lSolutions": [32.0], "sQuestion": " There were 2 friends playing a video game online when 2 more players joined the game. If each player had 8 lives, how many lives did they have total? "}, {"iIndex": 264, "lAlignments": [14, 47, 59], "lEquations": ["X=(7.0*(7.0****))"], "lSolutions": [63.0], "sQuestion": " A waiter had 7 tables he was waiting on, with 7 women and 2 men at each table. How many customers total did the waiter have? "}, {"iIndex": 265, "lAlignments": [51, 102, 137], "lEquations": ["X=(6.0*(3.0****))"], "lSolutions": [48.0], "sQuestion": " <PERSON> was playing a video game where she scores 6 points for each treasure she finds. If she found 3 treasures on the first level and 5 on the second, what would her score be? "}, {"iIndex": 266, "lAlignments": [123, 45, 66], "lEquations": ["X=(3.0*(3.0****))"], "lSolutions": [24.0], "sQuestion": " While shopping for music online, <PERSON> bought 3 country albums and 5 pop albums. Each album came with a lyric sheet and had 3 songs. How many songs did <PERSON> buy total? "}, {"iIndex": 267, "lAlignments": [80, 106, 137], "lEquations": ["X=(7.0*(8.0****))"], "lSolutions": [70.0], "sQuestion": " <PERSON> was organizing her book case making sure each of the shelves had exactly 7 books on it. If she had 8 shelves of mystery books and 2 shelves of picture books, how many books did she have total? "}, {"iIndex": 268, "lAlignments": [127, 61, 82], "lEquations": ["X=(6.0*(3.0+3.0))"], "lSolutions": [36.0], "sQuestion": " <PERSON> was selling her necklaces at a garage sale. She sold 3 bead necklaces and 3 gem stone necklaces. If each necklace cost 6 dollars, how much money did she earn? "}, {"iIndex": 269, "lAlignments": [127, 64, 98], "lEquations": ["X=(5.0*(5.0+3.0))"], "lSolutions": [40.0], "sQuestion": " <PERSON> was collecting cans for recycling. On Saturday he filled 5 bags up and on Sunday he filled 3 more bags. If each bag had 5 cans in it, how many cans did he pick up total? "}, {"iIndex": 270, "lAlignments": [66, 95, 107], "lEquations": ["X=(8.0*(3.0+3.0))"], "lSolutions": [48.0], "sQuestion": " April's discount flowers was having a sale where each flower was 8 dollars. If <PERSON> bought 3 roses and 3 daisies, how much did she spend? "}, {"iIndex": 271, "lAlignments": [63, 102, 135], "lEquations": ["X=(8.0*(8.0****))"], "lSolutions": [80.0], "sQuestion": " <PERSON> was working as a sacker at a grocery store where he made 8 dollars an hour. On Monday he worked 8 hours and on Tuesday he worked 2 hours. How much money did <PERSON> make in those two days? "}, {"iIndex": 272, "lAlignments": [73, 15, 32], "lEquations": ["X=(4.0*(7.0+3.0))"], "lSolutions": [40.0], "sQuestion": " <PERSON> bought 7 new chairs and 3 new tables for her house. If she spent 4 minutes on each piece furniture putting it together, how many minutes did it take her to finish? "}, {"iIndex": 273, "lAlignments": [153, 44, 86], "lEquations": ["X=(8.0*(8.0****))"], "lSolutions": [80.0], "sQuestion": " While playing a trivia game, <PERSON> answered 8 questions correct in the first half and 2 questions correct in the second half. If each question was worth 8 points, what was his final score? "}, {"iIndex": 274, "lAlignments": [17, 48, 62], "lEquations": ["X=(4.0*(8.0****))"], "lSolutions": [40.0], "sQuestion": " A pet store has 4 bird cages. If each cage has 8 parrots and 2 parakeets in it, how many birds does the pet store have total? "}, {"iIndex": 275, "lAlignments": [127, 64, 98], "lEquations": ["X=(4.0*(5.0****))"], "lSolutions": [40.0], "sQuestion": " <PERSON><PERSON><PERSON> was collecting cans for recycling. On Saturday he filled 5 bags up and on Sunday he filled 5 more bags. If each bag had 4 cans in it, how many cans did he pick up total? "}, {"iIndex": 276, "lAlignments": [81, 107, 138], "lEquations": ["X=(8.0*(5.0****))"], "lSolutions": [72.0], "sQuestion": " <PERSON> was organizing her book case making sure each of the shelves had exactly 8 books on it. If she had 5 shelves of mystery books and 4 shelves of picture books, how many books did she have total? "}, {"iIndex": 277, "lAlignments": [114, 55, 79], "lEquations": ["X=(4.0*(2.0+3.0))"], "lSolutions": [20.0], "sQuestion": " <PERSON> was putting his spare change into piles. He had 2 piles of quarters and 3 piles of dimes. If each pile had 4 coins in it, how many coins did he have total? "}, {"iIndex": 278, "lAlignments": [49, 100, 135], "lEquations": ["X=(9.0*(6.0+3.0))"], "lSolutions": [81.0], "sQuestion": " <PERSON> was playing a video game where she scores 9 points for each treasure she finds. If she found 6 treasures on the first level and 3 on the second, what would her score be? "}, {"iIndex": 279, "lAlignments": [125, 47, 68], "lEquations": ["X=(8.0*(3.0****))"], "lSolutions": [64.0], "sQuestion": " While shopping for music online, <PERSON> bought 3 country albums and 5 pop albums. Each album came with a lyric sheet and had 8 songs. How many songs did <PERSON> buy total? "}, {"iIndex": 280, "lAlignments": [61, 112, 126], "lEquations": ["X=(3.0*(3.0****))"], "lSolutions": [21.0], "sQuestion": " <PERSON> was unboxing some of her old winter clothes. She found 3 boxes of clothing and inside each box there were 3 scarves and 4 mittens. How many pieces of winter clothing did <PERSON> have total? "}, {"iIndex": 281, "lAlignments": [70, 126, 156], "lEquations": ["X=(7.0*(3.0****))"], "lSolutions": [35.0], "sQuestion": " <PERSON> and his friend were buying trick decks from the magic shop for 7 dollars each. How much did they spend if <PERSON> bought 3 decks and his friend bought 2 decks? "}, {"iIndex": 282, "lAlignments": [14, 47, 59], "lEquations": ["X=(5.0*(5.0+3.0))"], "lSolutions": [40.0], "sQuestion": " A waiter had 5 tables he was waiting on, with 5 women and 3 men at each table. How many customers total did the waiter have? "}, {"iIndex": 283, "lAlignments": [106, 12, 55], "lEquations": ["X=(6.0*(8.0****))"], "lSolutions": [60.0], "sQuestion": " There were 8 friends playing a video game online when 2 more players joined the game. If each player had 6 lives, how many lives did they have total? "}, {"iIndex": 284, "lAlignments": [125, 62, 96], "lEquations": ["X=(9.0*(3.0****))"], "lSolutions": [63.0], "sQuestion": " <PERSON> was collecting cans for recycling. On Saturday he filled 3 bags up and on Sunday he filled 4 more bags. If each bag had 9 cans in it, how many cans did he pick up total? "}, {"iIndex": 285, "lAlignments": [66, 93, 105], "lEquations": ["X=(6.0*(7.0+3.0))"], "lSolutions": [60.0], "sQuestion": " April's discount flowers was having a sale where each flower was 6 dollars. If <PERSON> bought 7 roses and 3 daisies, how much did she spend? "}, {"iIndex": 286, "lAlignments": [63, 102, 135], "lEquations": ["X=(6.0*(6.0****))"], "lSolutions": [48.0], "sQuestion": " <PERSON> was working as a sacker at a grocery store where he made 6 dollars an hour. On Monday he worked 6 hours and on Tuesday he worked 2 hours. How much money did <PERSON> make in those two days? "}, {"iIndex": 287, "lAlignments": [137, 80, 106], "lEquations": ["X=(6.0*(6.0****))"], "lSolutions": [48.0], "sQuestion": " <PERSON> was organizing her book case making sure each of the shelves had exactly 6 books on it. If she had 2 shelves of mystery books and 6 shelves of picture books, how many books did she have total? "}, {"iIndex": 288, "lAlignments": [124, 46, 67], "lEquations": ["X=(6.0*(2.0+3.0))"], "lSolutions": [30.0], "sQuestion": " While shopping for music online, <PERSON> bought 2 country albums and 3 pop albums. Each album came with a lyric sheet and had 6 songs. How many songs did <PERSON> buy total? "}, {"iIndex": 289, "lAlignments": [112, 53, 77], "lEquations": ["X=(5.0*(2.0****))"], "lSolutions": [30.0], "sQuestion": " <PERSON> was putting his spare change into piles. He had 2 piles of quarters and 4 piles of dimes. If each pile had 5 coins in it, how many coins did he have total? "}, {"iIndex": 290, "lAlignments": [155, 46, 88], "lEquations": ["X=(3.0*(6.0****))"], "lSolutions": [30.0], "sQuestion": " While playing a trivia game, <PERSON> answered 6 questions correct in the first half and 4 questions correct in the second half. If each question was worth 3 points, what was his final score? "}, {"iIndex": 291, "lAlignments": [61, 112, 126], "lEquations": ["X=(4.0*(2.0****))"], "lSolutions": [32.0], "sQuestion": " <PERSON> was unboxing some of her old winter clothes. She found 4 boxes of clothing and inside each box there were 2 scarves and 6 mittens. How many pieces of winter clothing did <PERSON> have total? "}, {"iIndex": 292, "lAlignments": [106, 51, 79], "lEquations": ["X=(3.0*(7.0+3.0))"], "lSolutions": [30.0], "sQuestion": " At the town carnival <PERSON> rode the ferris wheel 7 times and the bumper cars 3 times. If each ride cost 3 tickets, how many tickets did he use? "}, {"iIndex": 293, "lAlignments": [86, 11, 40], "lEquations": ["X=(4.0*(4.0****))"], "lSolutions": [40.0], "sQuestion": " <PERSON> had 4 pages of math homework and 6 pages of reading homework. If each page had 4 problems on it, how many problems did she have to complete total? "}, {"iIndex": 294, "lAlignments": [127, 64, 98], "lEquations": ["X=(4.0*(4.0****))"], "lSolutions": [40.0], "sQuestion": " <PERSON><PERSON><PERSON> was collecting cans for recycling. On Saturday he filled 4 bags up and on Sunday he filled 6 more bags. If each bag had 4 cans in it, how many cans did he pick up total? "}, {"iIndex": 295, "lAlignments": [158, 71, 128], "lEquations": ["X=(6.0*(6.0+3.0))"], "lSolutions": [54.0], "sQuestion": " <PERSON> and his friend were buying trick decks from the magic shop for 6 dollars each. How much did they spend if <PERSON> bought 3 decks and his friend bought 6 decks? "}, {"iIndex": 296, "lAlignments": [72, 14, 31], "lEquations": ["X=(8.0*(4.0****))"], "lSolutions": [48.0], "sQuestion": " <PERSON> bought 4 new chairs and 2 new tables for her house. If she spent 8 minutes on each piece furniture putting it together, how many minutes did it take her to finish? "}, {"iIndex": 297, "lAlignments": [14, 47, 59], "lEquations": ["X=(9.0*(2.0****))"], "lSolutions": [72.0], "sQuestion": " A waiter had 9 tables he was waiting on, with 2 women and 6 men at each table. How many customers total did the waiter have? "}, {"iIndex": 298, "lAlignments": [106, 12, 55], "lEquations": ["X=(3.0*(4.0****))"], "lSolutions": [27.0], "sQuestion": " There were 4 friends playing a video game online when 5 more players joined the game. If each player had 3 lives, how many lives did they have total? "}, {"iIndex": 299, "lAlignments": [125, 47, 68], "lEquations": ["X=(9.0*(6.0****))"], "lSolutions": [72.0], "sQuestion": " While shopping for music online, <PERSON> bought 6 country albums and 2 pop albums. Each album came with a lyric sheet and had 9 songs. How many songs did <PERSON> buy total? "}, {"iIndex": 300, "lAlignments": [29, 41, 91], "lEquations": ["X=((26.0+46.0)/9.0)"], "lSolutions": [8.0], "sQuestion": " The schools debate team had 26 boys and 46 girls on it. If they were split into groups of 9 how many groups could they make? "}, {"iIndex": 301, "lAlignments": [16, 47, 106], "lEquations": ["X=((35.0****)/5.0)"], "lSolutions": [8.0], "sQuestion": " <PERSON> uploaded 35 pictures from her phone and 5 from her camera to facebook. If she sorted the pics into 5 different albums with the same amount of pics in each album, how many pictures were in each of the albums? "}, {"iIndex": 302, "lAlignments": [20, 55, 94], "lEquations": ["X=((21.0+15.0)/6.0)"], "lSolutions": [6.0], "sQuestion": " <PERSON><PERSON><PERSON> had saved up 21 dollars. If he received another 15 dollars for his allowance, how many 6 dollar toys could he buy? "}, {"iIndex": 303, "lAlignments": [77, 107, 43], "lEquations": ["X=((13.0+41.0)/9.0)"], "lSolutions": [6.0], "sQuestion": " <PERSON> was making baggies of cookies with 9 cookies in each bag. If she had 13 chocolate chip cookies and 41 oatmeal cookies, how many baggies could she make? "}, {"iIndex": 304, "lAlignments": [11, 38, 79], "lEquations": ["X=((9.0+18.0)/3.0)"], "lSolutions": [9.0], "sQuestion": " <PERSON> made 9 dollars mowing lawns and 18 dollars weed eating. If he only spent 3 dollar a week, how long would the money last him? "}, {"iIndex": 305, "lAlignments": [34, 53, 113], "lEquations": ["X=((48.0****)/6.0)"], "lSolutions": [9.0], "sQuestion": " There school's baseball team had 48 new players and 6 returning players. If the coach put them into groups with 6 players in each group, how many groups would there be? "}, {"iIndex": 306, "lAlignments": [35, 56, 101], "lEquations": ["X=((41.0+22.0)/9.0)"], "lSolutions": [7.0], "sQuestion": " For a birthday party <PERSON> bought 41 regular sodas and 22 diet sodas. If his fridge would only hold 9 on each shelf, how many shelves would he fill up? "}, {"iIndex": 307, "lAlignments": [135, 170, 71], "lEquations": ["X=((14.0****)/4.0)"], "lSolutions": [4.0], "sQuestion": " <PERSON> and her friends were recycling paper for their class. For every 4 pounds they recycled they earned one point. If <PERSON> recycled 14 pounds and her friends recycled 2 pounds, how many points did they earn? "}, {"iIndex": 308, "lAlignments": [24, 45, 85], "lEquations": ["X=((17.0+15.0)/8.0)"], "lSolutions": [4.0], "sQuestion": " For homework <PERSON> had 17 math problems and 15 spelling problems. If she can finish 8 problems in an hour how long will it take her to finish all the problems? "}, {"iIndex": 309, "lAlignments": [83, 99, 57], "lEquations": ["X=((8.0+10.0)/3.0)"], "lSolutions": [6.0], "sQuestion": " <PERSON> was organizing his baseball cards in a binder with 3 on each page. If he had 8 new cards and 10 old cards to put in the binder, how many pages would he use? "}, {"iIndex": 310, "lAlignments": [29, 41, 91], "lEquations": ["X=((11.0+45.0)/7.0)"], "lSolutions": [8.0], "sQuestion": " The schools debate team had 11 boys and 45 girls on it. If they were split into groups of 7 how many groups could they make? "}, {"iIndex": 311, "lAlignments": [67, 101, 12], "lEquations": ["X=((9.0****)/4.0)"], "lSolutions": [4.0], "sQuestion": " A group of 4 friends went into a restaurant. The chef already had 9 chicken wings cooked but cooked 7 more for the group. If they each got the same amount how many would each person get? "}, {"iIndex": 312, "lAlignments": [39, 56, 17], "lEquations": ["X=((7.0+47.0)/6.0)"], "lSolutions": [9.0], "sQuestion": " A vase can hold 6 flowers. If you had 7 carnations and 47 roses, how many vases would you need to hold the flowers? "}, {"iIndex": 313, "lAlignments": [19, 42, 65], "lEquations": ["X=((9.0+12.0)/3.0)"], "lSolutions": [7.0], "sQuestion": " A pet shelter had 9 puppies when another 12 were brought in. If 3 puppies a day are adopted, how long would it take for all of them to be adopted? "}, {"iIndex": 314, "lAlignments": [126, 154, 86], "lEquations": ["X=((10.0****)/4.0)"], "lSolutions": [3.0], "sQuestion": " <PERSON> was helping the cafeteria workers pick up lunch trays, but he could only carry 4 trays at a time. If he had to pick up 10 trays from one table and 2 trays from another, how many trips will he make? "}, {"iIndex": 315, "lAlignments": [17, 84, 136], "lEquations": ["X=((4.0+10.0)/7.0)"], "lSolutions": [2.0], "sQuestion": " A toy store had 4 giant stuffed bears in stock when they got another shipment with 10 bears in it. The put the bears onto shelves with 7 on each shelf. How many shelves did they use? "}, {"iIndex": 316, "lAlignments": [11, 38, 79], "lEquations": ["X=((6.0+18.0)/3.0)"], "lSolutions": [8.0], "sQuestion": " <PERSON> made 6 dollars mowing lawns and 18 dollars weed eating. If he only spent 3 dollar a week, how long would the money last him? "}, {"iIndex": 317, "lAlignments": [96, 110, 45], "lEquations": ["X=((39.0+33.0)/8.0)"], "lSolutions": [9.0], "sQuestion": " <PERSON>'s old washing machine could only wash 8 pieces of clothing at a time. If she had to wash 39 shirts and 33 sweaters how many loads would she have to do? "}, {"iIndex": 318, "lAlignments": [96, 111, 73], "lEquations": ["X=((2.0****)/4.0)"], "lSolutions": [2.0], "sQuestion": " <PERSON><PERSON>'s class is going on a field trip to the zoo. If each van can hold 4 people and there are 2 students and 6 adults going, how many vans will they need? "}, {"iIndex": 319, "lAlignments": [17, 47, 106], "lEquations": ["X=((2.0****)/3.0)"], "lSolutions": [2.0], "sQuestion": " <PERSON> uploaded 2 pictures from her phone and 4 from her camera to facebook. If she sorted the pics into 3 different albums with the same amount of pics in each album, how many pictures were in each of the albums? "}, {"iIndex": 320, "lAlignments": [40, 78, 149], "lEquations": ["X=((33.0****)/6.0)"], "lSolutions": [7.0], "sQuestion": " While playing at the arcade, <PERSON> won 33 tickets playing 'whack a mole' and 9 tickets playing 'skee ball'. If he was trying to buy candy that cost 6 tickets a piece, how many could he buy? "}, {"iIndex": 321, "lAlignments": [11, 39, 80], "lEquations": ["X=((14.0+26.0)/5.0)"], "lSolutions": [8.0], "sQuestion": " <PERSON> made 14 dollars mowing lawns and 26 dollars weed eating. If he only spent 5 dollar a week, how long would the money last him? "}, {"iIndex": 322, "lAlignments": [133, 167, 70], "lEquations": ["X=((5.0+13.0)/3.0)"], "lSolutions": [6.0], "sQuestion": " <PERSON> and her friends were recycling paper for their class. For every 3 pounds they recycled they earned one point. If <PERSON> recycled 5 pounds and her friends recycled 13 pounds, how many points did they earn? "}, {"iIndex": 323, "lAlignments": [16, 47, 106], "lEquations": ["X=((31.0****)/9.0)"], "lSolutions": [4.0], "sQuestion": " <PERSON> uploaded 31 pictures from her phone and 5 from her camera to facebook. If she sorted the pics into 9 different albums with the same amount of pics in each album, how many pictures were in each of the albums? "}, {"iIndex": 324, "lAlignments": [30, 68, 116], "lEquations": ["X=((11.0****)/8.0)"], "lSolutions": [2.0], "sQuestion": " For Halloween <PERSON> received 11 pieces of candy from neighbors and 5 pieces from her older sister. If she only ate 8 pieces a day, how long would the candy last her? "}, {"iIndex": 325, "lAlignments": [76, 106, 42], "lEquations": ["X=((33.0****)/5.0)"], "lSolutions": [7.0], "sQuestion": " <PERSON> was making baggies of cookies with 5 cookies in each bag. If she had 33 chocolate chip cookies and 2 oatmeal cookies, how many baggies could she make? "}, {"iIndex": 326, "lAlignments": [34, 52, 112], "lEquations": ["X=((4.0****)/5.0)"], "lSolutions": [2.0], "sQuestion": " There school's baseball team had 4 new players and 6 returning players. If the coach put them into groups with 5 players in each group, how many groups would there be? "}, {"iIndex": 327, "lAlignments": [19, 42, 65], "lEquations": ["X=((5.0+35.0)/8.0)"], "lSolutions": [5.0], "sQuestion": " A pet shelter had 5 puppies when another 35 were brought in. If 8 puppies a day are adopted, how long would it take for all of them to be adopted? "}, {"iIndex": 328, "lAlignments": [96, 112, 73], "lEquations": ["X=((12.0+3.0)/5.0)"], "lSolutions": [3.0], "sQuestion": " <PERSON>'s class is going on a field trip to the zoo. If each van can hold 5 people and there are 12 students and 3 adults going, how many vans will they need? "}, {"iIndex": 329, "lAlignments": [125, 153, 85], "lEquations": ["X=((17.0+55.0)/9.0)"], "lSolutions": [8.0], "sQuestion": " <PERSON> was helping the cafeteria workers pick up lunch trays, but he could only carry 9 trays at a time. If he had to pick up 17 trays from one table and 55 trays from another, how many trips will he make? "}, {"iIndex": 330, "lAlignments": [36, 57, 102], "lEquations": ["X=((10.0+22.0)/4.0)"], "lSolutions": [8.0], "sQuestion": " For a birthday party <PERSON> bought 10 regular sodas and 22 diet sodas. If his fridge would only hold 4 on each shelf, how many shelves would he fill up? "}, {"iIndex": 331, "lAlignments": [29, 41, 90], "lEquations": ["X=((28.0****)/4.0)"], "lSolutions": [8.0], "sQuestion": " The schools debate team had 28 boys and 4 girls on it. If they were split into groups of 4 how many groups could they make? "}, {"iIndex": 332, "lAlignments": [76, 105, 42], "lEquations": ["X=((5.0+19.0)/8.0)"], "lSolutions": [3.0], "sQuestion": " <PERSON> was making baggies of cookies with 8 cookies in each bag. If she had 5 chocolate chip cookies and 19 oatmeal cookies, how many baggies could she make? "}, {"iIndex": 333, "lAlignments": [30, 68, 117], "lEquations": ["X=((66.0+15.0)/9.0)"], "lSolutions": [9.0], "sQuestion": " For Halloween <PERSON> received 66 pieces of candy from neighbors and 15 pieces from her older sister. If she only ate 9 pieces a day, how long would the candy last her? "}, {"iIndex": 334, "lAlignments": [126, 154, 86], "lEquations": ["X=((29.0+52.0)/9.0)"], "lSolutions": [9.0], "sQuestion": " <PERSON> was helping the cafeteria workers pick up lunch trays, but he could only carry 9 trays at a time. If he had to pick up 29 trays from one table and 52 trays from another, how many trips will he make? "}, {"iIndex": 335, "lAlignments": [18, 48, 108], "lEquations": ["X=((7.0+13.0)/5.0)"], "lSolutions": [4.0], "sQuestion": " <PERSON> uploaded 7 pictures from her phone and 13 from her camera to facebook. If she sorted the pics into 5 different albums with the same amount of pics in each album, how many pictures were in each of the albums? "}, {"iIndex": 336, "lAlignments": [17, 84, 135], "lEquations": ["X=((5.0****)/6.0)"], "lSolutions": [2.0], "sQuestion": " A toy store had 5 giant stuffed bears in stock when they got another shipment with 7 bears in it. The put the bears onto shelves with 6 on each shelf. How many shelves did they use? "}, {"iIndex": 337, "lAlignments": [11, 39, 80], "lEquations": ["X=((68.0+13.0)/9.0)"], "lSolutions": [9.0], "sQuestion": " <PERSON> made 68 dollars mowing lawns and 13 dollars weed eating. If he only spent 9 dollar a week, how long would the money last him? "}, {"iIndex": 338, "lAlignments": [41, 78, 149], "lEquations": ["X=((3.0****)/4.0)"], "lSolutions": [2.0], "sQuestion": " While playing at the arcade, <PERSON> won 3 tickets playing 'whack a mole' and 5 tickets playing 'skee ball'. If he was trying to buy candy that cost 4 tickets a piece, how many could he buy? "}, {"iIndex": 339, "lAlignments": [73, 96, 112], "lEquations": ["X=((5.0+25.0)/5.0)"], "lSolutions": [6.0], "sQuestion": " <PERSON>'s class is going on a field trip to the zoo. If each van can hold 5 people and there are 25 students and 5 adults going, how many vans will they need? "}, {"iIndex": 340, "lAlignments": [83, 99, 57], "lEquations": ["X=((3.0****)/3.0)"], "lSolutions": [4.0], "sQuestion": " <PERSON> was organizing his baseball cards in a binder with 3 on each page. If he had 3 new cards and 9 old cards to put in the binder, how many pages would he use? "}, {"iIndex": 341, "lAlignments": [12, 67, 101], "lEquations": ["X=((3.0****)/3.0)"], "lSolutions": [3.0], "sQuestion": " A group of 3 friends went into a restaurant. The chef already had 6 chicken wings cooked but cooked 3 more for the group. If they each got the same amount how many would each person get? "}, {"iIndex": 342, "lAlignments": [17, 47, 107], "lEquations": ["X=((5.0+35.0)/8.0)"], "lSolutions": [5.0], "sQuestion": " <PERSON> uploaded 5 pictures from her phone and 35 from her camera to facebook. If she sorted the pics into 8 different albums with the same amount of pics in each album, how many pictures were in each of the albums? "}, {"iIndex": 343, "lAlignments": [17, 84, 136], "lEquations": ["X=((6.0+18.0)/6.0)"], "lSolutions": [4.0], "sQuestion": " A toy store had 6 giant stuffed bears in stock when they got another shipment with 18 bears in it. The put the bears onto shelves with 6 on each shelf. How many shelves did they use? "}, {"iIndex": 344, "lAlignments": [126, 153, 86], "lEquations": ["X=((9.0****)/8.0)"], "lSolutions": [2.0], "sQuestion": " <PERSON> was helping the cafeteria workers pick up lunch trays, but he could only carry 8 trays at a time. If he had to pick up 9 trays from one table and 7 trays from another, how many trips will he make? "}, {"iIndex": 345, "lAlignments": [39, 56, 17], "lEquations": ["X=((6.0+19.0)/5.0)"], "lSolutions": [5.0], "sQuestion": " A vase can hold 5 flowers. If you had 6 carnations and 19 roses, how many vases would you need to hold the flowers? "}, {"iIndex": 346, "lAlignments": [135, 170, 71], "lEquations": ["X=((11.0+16.0)/3.0)"], "lSolutions": [9.0], "sQuestion": " <PERSON> and her friends were recycling paper for their class. For every 3 pounds they recycled they earned one point. If <PERSON> recycled 11 pounds and her friends recycled 16 pounds, how many points did they earn? "}, {"iIndex": 347, "lAlignments": [19, 43, 66], "lEquations": ["X=((17.0+55.0)/8.0)"], "lSolutions": [9.0], "sQuestion": " A pet shelter had 17 puppies when another 55 were brought in. If 8 puppies a day are adopted, how long would it take for all of them to be adopted? "}, {"iIndex": 348, "lAlignments": [34, 53, 114], "lEquations": ["X=((12.0+44.0)/8.0)"], "lSolutions": [7.0], "sQuestion": " There school's baseball team had 12 new players and 44 returning players. If the coach put them into groups with 8 players in each group, how many groups would there be? "}, {"iIndex": 349, "lAlignments": [20, 54, 93], "lEquations": ["X=((3.0+37.0)/8.0)"], "lSolutions": [5.0], "sQuestion": " <PERSON> had saved up 3 dollars. If he received another 37 dollars for his allowance, how many 8 dollar toys could he buy? "}, {"iIndex": 350, "lAlignments": [83, 99, 57], "lEquations": ["X=((3.0****)/3.0)"], "lSolutions": [4.0], "sQuestion": " <PERSON> was organizing his baseball cards in a binder with 3 on each page. If he had 3 new cards and 9 old cards to put in the binder, how many pages would he use? "}, {"iIndex": 351, "lAlignments": [12, 67, 101], "lEquations": ["X=((3.0****)/3.0)"], "lSolutions": [3.0], "sQuestion": " A group of 3 friends went into a restaurant. The chef already had 6 chicken wings cooked but cooked 3 more for the group. If they each got the same amount how many would each person get? "}, {"iIndex": 352, "lAlignments": [17, 47, 107], "lEquations": ["X=((5.0+35.0)/8.0)"], "lSolutions": [5.0], "sQuestion": " <PERSON> uploaded 5 pictures from her phone and 35 from her camera to facebook. If she sorted the pics into 8 different albums with the same amount of pics in each album, how many pictures were in each of the albums? "}, {"iIndex": 353, "lAlignments": [17, 84, 136], "lEquations": ["X=((6.0+18.0)/6.0)"], "lSolutions": [4.0], "sQuestion": " A toy store had 6 giant stuffed bears in stock when they got another shipment with 18 bears in it. The put the bears onto shelves with 6 on each shelf. How many shelves did they use? "}, {"iIndex": 354, "lAlignments": [126, 153, 86], "lEquations": ["X=((9.0****)/8.0)"], "lSolutions": [2.0], "sQuestion": " <PERSON> was helping the cafeteria workers pick up lunch trays, but he could only carry 8 trays at a time. If he had to pick up 9 trays from one table and 7 trays from another, how many trips will he make? "}, {"iIndex": 355, "lAlignments": [39, 56, 17], "lEquations": ["X=((6.0+19.0)/5.0)"], "lSolutions": [5.0], "sQuestion": " A vase can hold 5 flowers. If you had 6 carnations and 19 roses, how many vases would you need to hold the flowers? "}, {"iIndex": 356, "lAlignments": [135, 170, 71], "lEquations": ["X=((11.0+16.0)/3.0)"], "lSolutions": [9.0], "sQuestion": " <PERSON> and her friends were recycling paper for their class. For every 3 pounds they recycled they earned one point. If <PERSON> recycled 11 pounds and her friends recycled 16 pounds, how many points did they earn? "}, {"iIndex": 357, "lAlignments": [19, 43, 66], "lEquations": ["X=((17.0+55.0)/8.0)"], "lSolutions": [9.0], "sQuestion": " A pet shelter had 17 puppies when another 55 were brought in. If 8 puppies a day are adopted, how long would it take for all of them to be adopted? "}, {"iIndex": 358, "lAlignments": [34, 53, 114], "lEquations": ["X=((12.0+44.0)/8.0)"], "lSolutions": [7.0], "sQuestion": " There school's baseball team had 12 new players and 44 returning players. If the coach put them into groups with 8 players in each group, how many groups would there be? "}, {"iIndex": 359, "lAlignments": [20, 54, 93], "lEquations": ["X=((3.0+37.0)/8.0)"], "lSolutions": [5.0], "sQuestion": " <PERSON> had saved up 3 dollars. If he received another 37 dollars for his allowance, how many 8 dollar toys could he buy? "}, {"iIndex": 360, "lAlignments": [19, 42, 65], "lEquations": ["X=((8.0+19.0)/3.0)"], "lSolutions": [9.0], "sQuestion": " A pet shelter had 8 puppies when another 19 were brought in. If 3 puppies a day are adopted, how long would it take for all of them to be adopted? "}, {"iIndex": 361, "lAlignments": [23, 44, 83], "lEquations": ["X=((13.0****)/3.0)"], "lSolutions": [5.0], "sQuestion": " For homework <PERSON> had 13 math problems and 2 spelling problems. If she can finish 3 problems in an hour how long will it take her to finish all the problems? "}, {"iIndex": 362, "lAlignments": [18, 52, 90], "lEquations": ["X=((8.0****)/3.0)"], "lSolutions": [5.0], "sQuestion": " <PERSON> had saved up 8 dollars. If he received another 7 dollars for his allowance, how many 3 dollar toys could he buy? "}, {"iIndex": 363, "lAlignments": [72, 137, 172], "lEquations": ["X=((3.0+24.0)/3.0)"], "lSolutions": [9.0], "sQuestion": " <PERSON> and her friends were recycling paper for their class. For every 3 pounds they recycled they earned one point. If <PERSON> recycled 24 pounds and her friends recycled 3 pounds, how many points did they earn? "}, {"iIndex": 364, "lAlignments": [34, 53, 113], "lEquations": ["X=((31.0****)/7.0)"], "lSolutions": [5.0], "sQuestion": " There school's baseball team had 31 new players and 4 returning players. If the coach put them into groups with 7 players in each group, how many groups would there be? "}, {"iIndex": 365, "lAlignments": [85, 101, 59], "lEquations": ["X=((2.0+10.0)/3.0)"], "lSolutions": [4.0], "sQuestion": " <PERSON> was organizing his baseball cards in a binder with 3 on each page. If he had 2 new cards and 10 old cards to put in the binder, how many pages would he use? "}, {"iIndex": 366, "lAlignments": [11, 38, 78], "lEquations": ["X=((3.0+3.0)/3.0)"], "lSolutions": [2.0], "sQuestion": " <PERSON> made 3 dollars mowing lawns and 3 dollars weed eating. If he only spent 3 dollar a week, how long would the money last him? "}, {"iIndex": 367, "lAlignments": [76, 106, 42], "lEquations": ["X=((23.0+25.0)/6.0)"], "lSolutions": [8.0], "sQuestion": " <PERSON> was making baggies of cookies with 6 cookies in each bag. If she had 23 chocolate chip cookies and 25 oatmeal cookies, how many baggies could she make? "}, {"iIndex": 368, "lAlignments": [29, 40, 90], "lEquations": ["X=((5.0+40.0)/9.0)"], "lSolutions": [5.0], "sQuestion": " The schools debate team had 5 boys and 40 girls on it. If they were split into groups of 9 how many groups could they make? "}, {"iIndex": 369, "lAlignments": [21, 37, 107], "lEquations": ["X=((23.0****)/6.0)"], "lSolutions": [5.0], "sQuestion": " At a company picnic 23 managers and 7 employees decided to start a game of volleyball. If they split into 6 teams how many people would be on each team? "}, {"iIndex": 370, "lAlignments": [34, 54, 99], "lEquations": ["X=((4.0+44.0)/6.0)"], "lSolutions": [8.0], "sQuestion": " For a birthday party <PERSON> bought 4 regular sodas and 44 diet sodas. If his fridge would only hold 6 on each shelf, how many shelves would he fill up? "}, {"iIndex": 371, "lAlignments": [96, 112, 73], "lEquations": ["X=((22.0****)/8.0)"], "lSolutions": [3.0], "sQuestion": " <PERSON>'s class is going on a field trip to the zoo. If each van can hold 8 people and there are 22 students and 2 adults going, how many vans will they need? "}, {"iIndex": 372, "lAlignments": [84, 124, 151], "lEquations": ["X=((5.0****)/5.0)"], "lSolutions": [2.0], "sQuestion": " <PERSON> was helping the cafeteria workers pick up lunch trays, but he could only carry 5 trays at a time. If he had to pick up 5 trays from one table and 5 trays from another, how many trips will he make? "}, {"iIndex": 373, "lAlignments": [34, 52, 112], "lEquations": ["X=((2.0****)/4.0)"], "lSolutions": [2.0], "sQuestion": " There school's baseball team had 2 new players and 6 returning players. If the coach put them into groups with 4 players in each group, how many groups would there be? "}, {"iIndex": 374, "lAlignments": [22, 43, 82], "lEquations": ["X=((18.0****)/4.0)"], "lSolutions": [6.0], "sQuestion": " For homework <PERSON> had 18 math problems and 6 spelling problems. If she can finish 4 problems in an hour how long will it take her to finish all the problems? "}, {"iIndex": 375, "lAlignments": [76, 105, 42], "lEquations": ["X=((2.0+16.0)/3.0)"], "lSolutions": [6.0], "sQuestion": " <PERSON> was making baggies of cookies with 3 cookies in each bag. If she had 2 chocolate chip cookies and 16 oatmeal cookies, how many baggies could she make? "}, {"iIndex": 376, "lAlignments": [96, 110, 45], "lEquations": ["X=((43.0****)/5.0)"], "lSolutions": [9.0], "sQuestion": " <PERSON>'s old washing machine could only wash 5 pieces of clothing at a time. If she had to wash 43 shirts and 2 sweaters how many loads would she have to do? "}, {"iIndex": 377, "lAlignments": [19, 42, 64], "lEquations": ["X=((3.0+3.0)/3.0)"], "lSolutions": [2.0], "sQuestion": " A pet shelter had 3 puppies when another 3 were brought in. If 3 puppies a day are adopted, how long would it take for all of them to be adopted? "}, {"iIndex": 378, "lAlignments": [39, 76, 148], "lEquations": ["X=((2.0+13.0)/3.0)"], "lSolutions": [5.0], "sQuestion": " While playing at the arcade, <PERSON> won 2 tickets playing 'whack a mole' and 13 tickets playing 'skee ball'. If he was trying to buy candy that cost 3 tickets a piece, how many could he buy? "}, {"iIndex": 379, "lAlignments": [67, 101, 12], "lEquations": ["X=((8.0+10.0)/3.0)"], "lSolutions": [6.0], "sQuestion": " A group of 3 friends went into a restaurant. The chef already had 8 chicken wings cooked but cooked 10 more for the group. If they each got the same amount how many would each person get? "}, {"iIndex": 380, "lAlignments": [96, 110, 45], "lEquations": ["X=((19.0+8.0)/9.0)"], "lSolutions": [3.0], "sQuestion": " <PERSON>'s old washing machine could only wash 9 pieces of clothing at a time. If she had to wash 19 shirts and 8 sweaters how many loads would she have to do? "}, {"iIndex": 381, "lAlignments": [19, 53, 91], "lEquations": ["X=((3.0****)/5.0)"], "lSolutions": [2.0], "sQuestion": " <PERSON> had saved up 3 dollars. If he received another 7 dollars for his allowance, how many 5 dollar toys could he buy? "}, {"iIndex": 382, "lAlignments": [12, 40, 81], "lEquations": ["X=((14.0+31.0)/5.0)"], "lSolutions": [9.0], "sQuestion": " <PERSON> made 14 dollars mowing lawns and 31 dollars weed eating. If he only spent 5 dollar a week, how long would the money last him? "}, {"iIndex": 383, "lAlignments": [38, 76, 148], "lEquations": ["X=((26.0+19.0)/9.0)"], "lSolutions": [5.0], "sQuestion": " While playing at the arcade, <PERSON> won 26 tickets playing 'whack a mole' and 19 tickets playing 'skee ball'. If he was trying to buy candy that cost 9 tickets a piece, how many could he buy? "}, {"iIndex": 384, "lAlignments": [125, 153, 85], "lEquations": ["X=((20.0+16.0)/4.0)"], "lSolutions": [9.0], "sQuestion": " <PERSON> was helping the cafeteria workers pick up lunch trays, but he could only carry 4 trays at a time. If he had to pick up 20 trays from one table and 16 trays from another, how many trips will he make? "}, {"iIndex": 385, "lAlignments": [139, 174, 73], "lEquations": ["X=((20.0+16.0)/9.0)"], "lSolutions": [4.0], "sQuestion": " <PERSON> and her friends were recycling paper for their class. For every 9 pounds they recycled they earned one point. If <PERSON> recycled 20 pounds and her friends recycled 16 pounds, how many points did they earn? "}, {"iIndex": 386, "lAlignments": [19, 42, 65], "lEquations": ["X=((2.0+34.0)/4.0)"], "lSolutions": [9.0], "sQuestion": " A pet shelter had 2 puppies when another 34 were brought in. If 4 puppies a day are adopted, how long would it take for all of them to be adopted? "}, {"iIndex": 387, "lAlignments": [21, 36, 107], "lEquations": ["X=((9.0+15.0)/8.0)"], "lSolutions": [3.0], "sQuestion": " At a company picnic 9 managers and 15 employees decided to start a game of volleyball. If they split into 8 teams how many people would be on each team? "}, {"iIndex": 388, "lAlignments": [83, 99, 57], "lEquations": ["X=((8.0+16.0)/3.0)"], "lSolutions": [8.0], "sQuestion": " <PERSON> was organizing his baseball cards in a binder with 3 on each page. If he had 8 new cards and 16 old cards to put in the binder, how many pages would he use? "}, {"iIndex": 389, "lAlignments": [16, 47, 106], "lEquations": ["X=((22.0****)/4.0)"], "lSolutions": [6.0], "sQuestion": " <PERSON> uploaded 22 pictures from her phone and 2 from her camera to facebook. If she sorted the pics into 4 different albums with the same amount of pics in each album, how many pictures were in each of the albums? "}, {"iIndex": 390, "lAlignments": [17, 85, 137], "lEquations": ["X=((17.0+10.0)/9.0)"], "lSolutions": [3.0], "sQuestion": " A toy store had 17 giant stuffed bears in stock when they got another shipment with 10 bears in it. The put the bears onto shelves with 9 on each shelf. How many shelves did they use? "}, {"iIndex": 391, "lAlignments": [131, 166, 69], "lEquations": ["X=((25.0+23.0)/8.0)"], "lSolutions": [6.0], "sQuestion": " <PERSON> and her friends were recycling paper for their class. For every 8 pounds they recycled they earned one point. If <PERSON> recycled 25 pounds and her friends recycled 23 pounds, how many points did they earn? "}, {"iIndex": 392, "lAlignments": [84, 100, 58], "lEquations": ["X=((3.0+42.0)/5.0)"], "lSolutions": [9.0], "sQuestion": " <PERSON> was organizing his baseball cards in a binder with 5 on each page. If he had 3 new cards and 42 old cards to put in the binder, how many pages would he use? "}, {"iIndex": 393, "lAlignments": [33, 53, 98], "lEquations": ["X=((4.0+52.0)/7.0)"], "lSolutions": [8.0], "sQuestion": " For a birthday party <PERSON> bought 4 regular sodas and 52 diet sodas. If his fridge would only hold 7 on each shelf, how many shelves would he fill up? "}, {"iIndex": 394, "lAlignments": [127, 155, 87], "lEquations": ["X=((23.0****)/7.0)"], "lSolutions": [4.0], "sQuestion": " <PERSON> was helping the cafeteria workers pick up lunch trays, but he could only carry 7 trays at a time. If he had to pick up 23 trays from one table and 5 trays from another, how many trips will he make? "}, {"iIndex": 395, "lAlignments": [19, 53, 92], "lEquations": ["X=((4.0+11.0)/5.0)"], "lSolutions": [3.0], "sQuestion": " <PERSON> had saved up 4 dollars. If he received another 11 dollars for his allowance, how many 5 dollar toys could he buy? "}, {"iIndex": 396, "lAlignments": [16, 47, 107], "lEquations": ["X=((30.0+51.0)/9.0)"], "lSolutions": [9.0], "sQuestion": " <PERSON> uploaded 30 pictures from her phone and 51 from her camera to facebook. If she sorted the pics into 9 different albums with the same amount of pics in each album, how many pictures were in each of the albums? "}, {"iIndex": 397, "lAlignments": [96, 112, 73], "lEquations": ["X=((40.0+14.0)/9.0)"], "lSolutions": [6.0], "sQuestion": " <PERSON><PERSON>'s class is going on a field trip to the zoo. If each van can hold 9 people and there are 40 students and 14 adults going, how many vans will they need? "}, {"iIndex": 398, "lAlignments": [30, 67, 116], "lEquations": ["X=((5.0+13.0)/9.0)"], "lSolutions": [2.0], "sQuestion": " For Halloween <PERSON> received 5 pieces of candy from neighbors and 13 pieces from her older sister. If she only ate 9 pieces a day, how long would the candy last her? "}, {"iIndex": 399, "lAlignments": [12, 39, 80], "lEquations": ["X=((5.0+58.0)/7.0)"], "lSolutions": [9.0], "sQuestion": " <PERSON> made 5 dollars mowing lawns and 58 dollars weed eating. If he only spent 7 dollar a week, how long would the money last him? "}, {"iIndex": 400, "lAlignments": [92, 23, 69], "lEquations": ["X=(4.0*(14.0-5.0))"], "lSolutions": [36.0], "sQuestion": " A new building needed 14 windows. The builder had already installed 5 of them. If it takes 4 hours to install each window, how long will it take him to install the rest? "}, {"iIndex": 401, "lAlignments": [81, 22, 57], "lEquations": ["X=(5.0*(16.0-7.0))"], "lSolutions": [45.0], "sQuestion": " A chef needs to cook 16 potatoes. He has already cooked 7. If each potato takes 5 minutes to cook, how long will it take him to cook the rest? "}, {"iIndex": 402, "lAlignments": [90, 12, 49], "lEquations": ["X=(6.0*(14.0-7.0))"], "lSolutions": [42.0], "sQuestion": " <PERSON> bought 14 boxes of chocolate candy and gave 7 to his little brother. If each box has 6 pieces inside it, how many pieces did <PERSON> still have? "}, {"iIndex": 403, "lAlignments": [96, 12, 56], "lEquations": ["X=(5.0*(11.0-5.0))"], "lSolutions": [30.0], "sQuestion": " There were 11 friends playing a video game online when 5 players quit. If each player left had 5 lives, how many lives did they have total? "}, {"iIndex": 404, "lAlignments": [14, 58, 93], "lEquations": ["X=(5.0*(12.0-7.0))"], "lSolutions": [25.0], "sQuestion": " <PERSON> earned 5 dollars for each lawn he mowed. If he had 12 lawns to mow, but forgot to mow 7 of them, how much money did he actually earn? "}, {"iIndex": 405, "lAlignments": [119, 19, 54], "lEquations": ["X=(6.0*(5.0-2.0))"], "lSolutions": [18.0], "sQuestion": " A trivia team had 5 members total, but during a game 2 members didn't show up. If each member that did show up scored 6 points, how many points were scored total? "}, {"iIndex": 406, "lAlignments": [107, 27, 67], "lEquations": ["X=(8.0*(10.0-8.0))"], "lSolutions": [16.0], "sQuestion": " A painter needed to paint 10 rooms in a building. Each room takes 8 hours to paint. If he already painted 8 rooms, how much longer will he take to paint the rest? "}, {"iIndex": 407, "lAlignments": [48, 73, 113], "lEquations": ["X=(3.0*(6.0-2.0))"], "lSolutions": [12.0], "sQuestion": " In a video game, each enemy defeated gives you 3 points. If a level has 6 enemies total and you destroy all but 2 of them, how many points would you earn? "}, {"iIndex": 408, "lAlignments": [14, 69, 97], "lEquations": ["X=(5.0*(11.0-2.0))"], "lSolutions": [45.0], "sQuestion": " <PERSON> earned 5 points for each bag of cans she recycled. If she had 11 bags, but didn't recycle 2 of them, how many points would she have earned? "}, {"iIndex": 409, "lAlignments": [35, 51, 88], "lEquations": ["X=(3.0*(7.0-4.0))"], "lSolutions": [9.0], "sQuestion": " Each chocolate bar in a box cost $3. If a box had 7 bars total and <PERSON> sold all but 4 bars, how much money would she have made? "}, {"iIndex": 410, "lAlignments": [92, 14, 51], "lEquations": ["X=(6.0*(14.0-5.0))"], "lSolutions": [54.0], "sQuestion": " <PERSON><PERSON><PERSON> bought 14 boxes of chocolate candy and gave 5 to his little brother. If each box has 6 pieces inside it, how many pieces did <PERSON><PERSON><PERSON> still have? "}, {"iIndex": 411, "lAlignments": [40, 75, 97], "lEquations": ["X=(3.0*(12.0-7.0))"], "lSolutions": [15.0], "sQuestion": " At a restaurant each adult meal costs $3 and kids eat free. If a group of 12 people came in and 7 were kids, how much would it cost for the group to eat? "}, {"iIndex": 412, "lAlignments": [95, 40, 11], "lEquations": ["X=(8.0*(16.0-7.0))"], "lSolutions": [72.0], "sQuestion": " <PERSON> had 7 action figures, but needed 16 total for a complete collection. If each one costs $8, how much money would he need to finish his collection? "}, {"iIndex": 413, "lAlignments": [48, 73, 114], "lEquations": ["X=(9.0*(11.0-3.0))"], "lSolutions": [72.0], "sQuestion": " In a video game, each enemy defeated gives you 9 points. If a level has 11 enemies total and you destroy all but 3 of them, how many points would you earn? "}, {"iIndex": 414, "lAlignments": [96, 12, 56], "lEquations": ["X=(8.0*(16.0-7.0))"], "lSolutions": [72.0], "sQuestion": " There were 16 friends playing a video game online when 7 players quit. If each player left had 8 lives, how many lives did they have total? "}, {"iIndex": 415, "lAlignments": [92, 23, 69], "lEquations": ["X=(5.0*(10.0-6.0))"], "lSolutions": [20.0], "sQuestion": " A new building needed 10 windows. The builder had already installed 6 of them. If it takes 5 hours to install each window, how long will it take him to install the rest? "}, {"iIndex": 416, "lAlignments": [13, 57, 92], "lEquations": ["X=(9.0*(12.0-8.0))"], "lSolutions": [36.0], "sQuestion": " <PERSON> earned 9 dollars for each lawn he mowed. If he had 12 lawns to mow, but forgot to mow 8 of them, how much money did he actually earn? "}, {"iIndex": 417, "lAlignments": [120, 19, 55], "lEquations": ["X=(8.0*(12.0-4.0))"], "lSolutions": [64.0], "sQuestion": " A trivia team had 12 members total, but during a game 4 members didn't show up. If each member that did show up scored 8 points, how many points were scored total? "}, {"iIndex": 418, "lAlignments": [100, 10, 29], "lEquations": ["X=(7.0*(16.0-8.0))"], "lSolutions": [56.0], "sQuestion": " <PERSON> had 16 video games but 8 of them weren't working. If he wanted to sell the working games for $7 each, how much money could he earn? "}, {"iIndex": 419, "lAlignments": [67, 27, 107], "lEquations": ["X=(7.0*(12.0-5.0))"], "lSolutions": [49.0], "sQuestion": " A painter needed to paint 12 rooms in a building. Each room takes 7 hours to paint. If he already painted 5 rooms, how much longer will he take to paint the rest? "}, {"iIndex": 420, "lAlignments": [95, 12, 55], "lEquations": ["X=(5.0*(8.0-5.0))"], "lSolutions": [15.0], "sQuestion": " There were 8 friends playing a video game online when 5 players quit. If each player left had 5 lives, how many lives did they have total? "}, {"iIndex": 421, "lAlignments": [120, 19, 55], "lEquations": ["X=(3.0*(15.0-6.0))"], "lSolutions": [27.0], "sQuestion": " A trivia team had 15 members total, but during a game 6 members didn't show up. If each member that did show up scored 3 points, how many points were scored total? "}, {"iIndex": 422, "lAlignments": [99, 9, 28], "lEquations": ["X=(7.0*(15.0-6.0))"], "lSolutions": [63.0], "sQuestion": " <PERSON> had 15 video games but 6 of them weren't working. If he wanted to sell the working games for $7 each, how much money could he earn? "}, {"iIndex": 423, "lAlignments": [35, 51, 86], "lEquations": ["X=(6.0*(13.0-6.0))"], "lSolutions": [42.0], "sQuestion": " Each chocolate bar in a box cost $6. If a box had 13 bars total and <PERSON> sold all but 6 bars, how much money would she have made? "}, {"iIndex": 424, "lAlignments": [90, 13, 49], "lEquations": ["X=(4.0*(7.0-3.0))"], "lSolutions": [16.0], "sQuestion": " <PERSON> bought 7 boxes of chocolate candy and gave 3 to his little brother. If each box has 4 pieces inside it, how many pieces did <PERSON> still have? "}, {"iIndex": 425, "lAlignments": [137, 14, 50], "lEquations": ["X=(4.0*(13.0-7.0))"], "lSolutions": [24.0], "sQuestion": " <PERSON> invited 13 friends to a birthday party, but 7 couldn't come. If he wanted to buy enough cupcakes so each person could have exactly 4, how many should he buy? "}, {"iIndex": 426, "lAlignments": [48, 73, 113], "lEquations": ["X=(5.0*(8.0-6.0))"], "lSolutions": [10.0], "sQuestion": " In a video game, each enemy defeated gives you 5 points. If a level has 8 enemies total and you destroy all but 6 of them, how many points would you earn? "}, {"iIndex": 427, "lAlignments": [14, 58, 93], "lEquations": ["X=(9.0*(14.0-8.0))"], "lSolutions": [54.0], "sQuestion": " <PERSON> earned 9 dollars for each lawn he mowed. If he had 14 lawns to mow, but forgot to mow 8 of them, how much money did he actually earn? "}, {"iIndex": 428, "lAlignments": [45, 80, 121], "lEquations": ["X=(2.0*(5.0-3.0))"], "lSolutions": [4.0], "sQuestion": " A magician was selling magic card decks for 2 dollars each. If he started with 5 decks and by the end of the day he had 3 left, how much money did he earn? "}, {"iIndex": 429, "lAlignments": [81, 22, 57], "lEquations": ["X=(6.0*(12.0-6.0))"], "lSolutions": [36.0], "sQuestion": " A chef needs to cook 12 potatoes. He has already cooked 6. If each potato takes 6 minutes to cook, how long will it take him to cook the rest? "}, {"iIndex": 430, "lAlignments": [91, 23, 68], "lEquations": ["X=(6.0*(9.0-6.0))"], "lSolutions": [18.0], "sQuestion": " A new building needed 9 windows. The builder had already installed 6 of them. If it takes 6 hours to install each window, how long will it take him to install the rest? "}, {"iIndex": 431, "lAlignments": [110, 25, 74], "lEquations": ["X=(9.0*(13.0-4.0))"], "lSolutions": [81.0], "sQuestion": " At the fair <PERSON> bought 13 tickets. After riding the ferris wheel he had 4 tickets left. If each ticket cost 9 dollars, how much money did <PERSON> spend riding the ferris wheel? "}, {"iIndex": 432, "lAlignments": [91, 13, 50], "lEquations": ["X=(3.0*(12.0-5.0))"], "lSolutions": [21.0], "sQuestion": " <PERSON> bought 12 boxes of chocolate candy and gave 5 to his little brother. If each box has 3 pieces inside it, how many pieces did <PERSON> still have? "}, {"iIndex": 433, "lAlignments": [10, 39, 93], "lEquations": ["X=(5.0*(7.0-5.0))"], "lSolutions": [10.0], "sQuestion": " <PERSON> had 5 action figures, but needed 7 total for a complete collection. If each one costs $5, how much money would he need to finish his collection? "}, {"iIndex": 434, "lAlignments": [66, 27, 106], "lEquations": ["X=(8.0*(9.0-5.0))"], "lSolutions": [32.0], "sQuestion": " A painter needed to paint 9 rooms in a building. Each room takes 8 hours to paint. If he already painted 5 rooms, how much longer will he take to paint the rest? "}, {"iIndex": 435, "lAlignments": [35, 51, 87], "lEquations": ["X=(4.0*(8.0-3.0))"], "lSolutions": [20.0], "sQuestion": " Each chocolate bar in a box cost $4. If a box had 8 bars total and <PERSON> sold all but 3 bars, how much money would she have made? "}, {"iIndex": 436, "lAlignments": [81, 23, 40], "lEquations": ["X=(3.0*(10.0-5.0))"], "lSolutions": [15.0], "sQuestion": " At lunch a waiter had 10 customers and 5 of them didn't leave a tip. If he got $3 each from the ones who did tip, how much money did he earn? "}, {"iIndex": 437, "lAlignments": [17, 52, 97], "lEquations": ["X=(4.0*(9.0-5.0))"], "lSolutions": [16.0], "sQuestion": " A worksheet had 4 problems on it. If a teacher had 9 worksheets to grade and had already graded 5 of them, how many more problems does she have to grade? "}, {"iIndex": 438, "lAlignments": [136, 14, 49], "lEquations": ["X=(8.0*(9.0-4.0))"], "lSolutions": [40.0], "sQuestion": " <PERSON> invited 9 friends to a birthday party, but 4 couldn't come. If he wanted to buy enough cupcakes so each person could have exactly 8, how many should he buy? "}, {"iIndex": 439, "lAlignments": [45, 80, 122], "lEquations": ["X=(9.0*(12.0-7.0))"], "lSolutions": [45.0], "sQuestion": " A magician was selling magic card decks for 9 dollars each. If he started with 12 decks and by the end of the day he had 7 left, how much money did he earn? "}, {"iIndex": 440, "lAlignments": [80, 22, 56], "lEquations": ["X=(3.0*(9.0-7.0))"], "lSolutions": [6.0], "sQuestion": " A chef needs to cook 9 potatoes. He has already cooked 7. If each potato takes 3 minutes to cook, how long will it take him to cook the rest? "}, {"iIndex": 441, "lAlignments": [45, 80, 122], "lEquations": ["X=(5.0*(14.0-5.0))"], "lSolutions": [45.0], "sQuestion": " A magician was selling magic card decks for 5 dollars each. If he started with 14 decks and by the end of the day he had 5 left, how much money did he earn? "}, {"iIndex": 442, "lAlignments": [35, 51, 87], "lEquations": ["X=(3.0*(9.0-3.0))"], "lSolutions": [18.0], "sQuestion": " Each chocolate bar in a box cost $3. If a box had 9 bars total and <PERSON> sold all but 3 bars, how much money would she have made? "}, {"iIndex": 443, "lAlignments": [67, 27, 107], "lEquations": ["X=(3.0*(12.0-4.0))"], "lSolutions": [24.0], "sQuestion": " A painter needed to paint 12 rooms in a building. Each room takes 3 hours to paint. If he already painted 4 rooms, how much longer will he take to paint the rest? "}, {"iIndex": 444, "lAlignments": [91, 13, 50], "lEquations": ["X=(6.0*(13.0-7.0))"], "lSolutions": [36.0], "sQuestion": " <PERSON> bought 13 boxes of chocolate candy and gave 7 to his little brother. If each box has 6 pieces inside it, how many pieces did <PERSON> still have? "}, {"iIndex": 445, "lAlignments": [40, 75, 97], "lEquations": ["X=(7.0*(13.0-9.0))"], "lSolutions": [28.0], "sQuestion": " At a restaurant each adult meal costs $7 and kids eat free. If a group of 13 people came in and 9 were kids, how much would it cost for the group to eat? "}, {"iIndex": 446, "lAlignments": [119, 19, 54], "lEquations": ["X=(4.0*(7.0-2.0))"], "lSolutions": [20.0], "sQuestion": " A trivia team had 7 members total, but during a game 2 members didn't show up. If each member that did show up scored 4 points, how many points were scored total? "}, {"iIndex": 447, "lAlignments": [101, 11, 30], "lEquations": ["X=(6.0*(10.0-8.0))"], "lSolutions": [12.0], "sQuestion": " <PERSON><PERSON><PERSON> had 10 video games but 8 of them weren't working. If he wanted to sell the working games for $6 each, how much money could he earn? "}, {"iIndex": 448, "lAlignments": [64, 97, 113], "lEquations": ["X=(7.0*(9.0-4.0))"], "lSolutions": [35.0], "sQuestion": " April's discount flowers was having a sale where each rose was 7 dollars. If April started with 9 roses and had 4 roses left, how much money did she earn? "}, {"iIndex": 449, "lAlignments": [80, 23, 39], "lEquations": ["X=(8.0*(9.0-5.0))"], "lSolutions": [32.0], "sQuestion": " At lunch a waiter had 9 customers and 5 of them didn't leave a tip. If he got $8 each from the ones who did tip, how much money did he earn? "}, {"iIndex": 450, "lAlignments": [92, 23, 69], "lEquations": ["X=(8.0*(14.0-8.0))"], "lSolutions": [48.0], "sQuestion": " A new building needed 14 windows. The builder had already installed 8 of them. If it takes 8 hours to install each window, how long will it take him to install the rest? "}, {"iIndex": 451, "lAlignments": [15, 70, 98], "lEquations": ["X=(5.0*(17.0-8.0))"], "lSolutions": [45.0], "sQuestion": " <PERSON> earned 5 points for each bag of cans she recycled. If she had 17 bags, but didn't recycle 8 of them, how many points would she have earned? "}, {"iIndex": 452, "lAlignments": [81, 22, 57], "lEquations": ["X=(8.0*(15.0-6.0))"], "lSolutions": [72.0], "sQuestion": " A chef needs to cook 15 potatoes. He has already cooked 6. If each potato takes 8 minutes to cook, how long will it take him to cook the rest? "}, {"iIndex": 453, "lAlignments": [94, 40, 11], "lEquations": ["X=(6.0*(8.0-3.0))"], "lSolutions": [30.0], "sQuestion": " <PERSON> had 3 action figures, but needed 8 total for a complete collection. If each one costs $6, how much money would he need to finish his collection? "}, {"iIndex": 454, "lAlignments": [13, 57, 92], "lEquations": ["X=(8.0*(15.0-7.0))"], "lSolutions": [64.0], "sQuestion": " <PERSON> earned 8 dollars for each lawn he mowed. If he had 15 lawns to mow, but forgot to mow 7 of them, how much money did he actually earn? "}, {"iIndex": 455, "lAlignments": [96, 12, 56], "lEquations": ["X=(8.0*(10.0-7.0))"], "lSolutions": [24.0], "sQuestion": " There were 10 friends playing a video game online when 7 players quit. If each player left had 8 lives, how many lives did they have total? "}, {"iIndex": 456, "lAlignments": [48, 73, 113], "lEquations": ["X=(8.0*(7.0-2.0))"], "lSolutions": [40.0], "sQuestion": " In a video game, each enemy defeated gives you 8 points. If a level has 7 enemies total and you destroy all but 2 of them, how many points would you earn? "}, {"iIndex": 457, "lAlignments": [67, 27, 107], "lEquations": ["X=(7.0*(11.0-2.0))"], "lSolutions": [63.0], "sQuestion": " A painter needed to paint 11 rooms in a building. Each room takes 7 hours to paint. If he already painted 2 rooms, how much longer will he take to paint the rest? "}, {"iIndex": 458, "lAlignments": [100, 10, 29], "lEquations": ["X=(4.0*(10.0-2.0))"], "lSolutions": [32.0], "sQuestion": " <PERSON> had 10 video games but 2 of them weren't working. If he wanted to sell the working games for $4 each, how much money could he earn? "}, {"iIndex": 459, "lAlignments": [17, 52, 98], "lEquations": ["X=(4.0*(16.0-8.0))"], "lSolutions": [32.0], "sQuestion": " A worksheet had 4 problems on it. If a teacher had 16 worksheets to grade and had already graded 8 of them, how many more problems does she have to grade? "}, {"iIndex": 460, "lAlignments": [17, 52, 98], "lEquations": ["X=(2.0*(14.0-7.0))"], "lSolutions": [14.0], "sQuestion": " A worksheet had 2 problems on it. If a teacher had 14 worksheets to grade and had already graded 7 of them, how many more problems does she have to grade? "}, {"iIndex": 461, "lAlignments": [64, 97, 114], "lEquations": ["X=(4.0*(13.0-4.0))"], "lSolutions": [36.0], "sQuestion": " April's discount flowers was having a sale where each rose was 4 dollars. If April started with 13 roses and had 4 roses left, how much money did she earn? "}, {"iIndex": 462, "lAlignments": [40, 75, 97], "lEquations": ["X=(5.0*(15.0-8.0))"], "lSolutions": [35.0], "sQuestion": " At a restaurant each adult meal costs $5 and kids eat free. If a group of 15 people came in and 8 were kids, how much would it cost for the group to eat? "}, {"iIndex": 463, "lAlignments": [70, 34, 11], "lEquations": ["X=(7.0*(11.0-5.0))"], "lSolutions": [42.0], "sQuestion": " <PERSON> baked 5 brownies, but needed 11 total for her party. If she used 7 cups of flour on each one, how much cups of flour does she still need? "}, {"iIndex": 464, "lAlignments": [66, 27, 106], "lEquations": ["X=(5.0*(6.0-2.0))"], "lSolutions": [20.0], "sQuestion": " A painter needed to paint 6 rooms in a building. Each room takes 5 hours to paint. If he already painted 2 rooms, how much longer will he take to paint the rest? "}, {"iIndex": 465, "lAlignments": [35, 51, 90], "lEquations": ["X=(4.0*(11.0-7.0))"], "lSolutions": [16.0], "sQuestion": " Each chocolate bar in a box cost $4. If a box had 11 bars total and <PERSON> sold all but 7 bars, how much money would she have made? "}, {"iIndex": 466, "lAlignments": [13, 68, 95], "lEquations": ["X=(8.0*(4.0-2.0))"], "lSolutions": [16.0], "sQuestion": " <PERSON> earned 8 points for each bag of cans she recycled. If she had 4 bags, but didn't recycle 2 of them, how many points would she have earned? "}, {"iIndex": 467, "lAlignments": [92, 23, 69], "lEquations": ["X=(4.0*(12.0-6.0))"], "lSolutions": [24.0], "sQuestion": " A new building needed 12 windows. The builder had already installed 6 of them. If it takes 4 hours to install each window, how long will it take him to install the rest? "}, {"iIndex": 468, "lAlignments": [81, 22, 57], "lEquations": ["X=(9.0*(15.0-8.0))"], "lSolutions": [63.0], "sQuestion": " A chef needs to cook 15 potatoes. He has already cooked 8. If each potato takes 9 minutes to cook, how long will it take him to cook the rest? "}, {"iIndex": 469, "lAlignments": [120, 19, 55], "lEquations": ["X=(5.0*(14.0-7.0))"], "lSolutions": [35.0], "sQuestion": " A trivia team had 14 members total, but during a game 7 members didn't show up. If each member that did show up scored 5 points, how many points were scored total? "}, {"iIndex": 470, "lAlignments": [81, 22, 57], "lEquations": ["X=(6.0*(13.0-5.0))"], "lSolutions": [48.0], "sQuestion": " A chef needs to cook 13 potatoes. He has already cooked 5. If each potato takes 6 minutes to cook, how long will it take him to cook the rest? "}, {"iIndex": 471, "lAlignments": [120, 19, 55], "lEquations": ["X=(9.0*(11.0-6.0))"], "lSolutions": [45.0], "sQuestion": " A trivia team had 11 members total, but during a game 6 members didn't show up. If each member that did show up scored 9 points, how many points were scored total? "}, {"iIndex": 472, "lAlignments": [35, 51, 89], "lEquations": ["X=(2.0*(13.0-4.0))"], "lSolutions": [18.0], "sQuestion": " Each chocolate bar in a box cost $2. If a box had 13 bars total and <PERSON> sold all but 4 bars, how much money would she have made? "}, {"iIndex": 473, "lAlignments": [72, 37, 14], "lEquations": ["X=(5.0*(5.0-3.0))"], "lSolutions": [10.0], "sQuestion": " <PERSON> baked 3 brownies, but needed 5 total for her party. If she used 5 cups of flour on each one, how much cups of flour does she still need? "}, {"iIndex": 474, "lAlignments": [17, 52, 98], "lEquations": ["X=(3.0*(15.0-7.0))"], "lSolutions": [24.0], "sQuestion": " A worksheet had 3 problems on it. If a teacher had 15 worksheets to grade and had already graded 7 of them, how many more problems does she have to grade? "}, {"iIndex": 475, "lAlignments": [80, 23, 39], "lEquations": ["X=(9.0*(7.0-4.0))"], "lSolutions": [27.0], "sQuestion": " At lunch a waiter had 7 customers and 4 of them didn't leave a tip. If he got $9 each from the ones who did tip, how much money did he earn? "}, {"iIndex": 476, "lAlignments": [64, 97, 114], "lEquations": ["X=(9.0*(11.0-8.0))"], "lSolutions": [27.0], "sQuestion": " April's discount flowers was having a sale where each rose was 9 dollars. If April started with 11 roses and had 8 roses left, how much money did she earn? "}, {"iIndex": 477, "lAlignments": [90, 12, 49], "lEquations": ["X=(3.0*(14.0-8.0))"], "lSolutions": [18.0], "sQuestion": " <PERSON> bought 14 boxes of chocolate candy and gave 8 to his little brother. If each box has 3 pieces inside it, how many pieces did <PERSON> still have? "}, {"iIndex": 478, "lAlignments": [40, 75, 97], "lEquations": ["X=(2.0*(15.0-9.0))"], "lSolutions": [12.0], "sQuestion": " At a restaurant each adult meal costs $2 and kids eat free. If a group of 15 people came in and 9 were kids, how much would it cost for the group to eat? "}, {"iIndex": 479, "lAlignments": [15, 59, 94], "lEquations": ["X=(4.0*(17.0-9.0))"], "lSolutions": [32.0], "sQuestion": " <PERSON> earned 4 dollars for each lawn he mowed. If he had 17 lawns to mow, but forgot to mow 9 of them, how much money did he actually earn? "}, {"iIndex": 480, "lAlignments": [92, 23, 69], "lEquations": ["X=(8.0*(11.0-4.0))"], "lSolutions": [56.0], "sQuestion": " A new building needed 11 windows. The builder had already installed 4 of them. If it takes 8 hours to install each window, how long will it take him to install the rest? "}, {"iIndex": 481, "lAlignments": [90, 12, 49], "lEquations": ["X=(6.0*(12.0-7.0))"], "lSolutions": [30.0], "sQuestion": " <PERSON> bought 12 boxes of chocolate candy and gave 7 to his little brother. If each box has 6 pieces inside it, how many pieces did <PERSON> still have? "}, {"iIndex": 482, "lAlignments": [10, 39, 94], "lEquations": ["X=(6.0*(11.0-6.0))"], "lSolutions": [30.0], "sQuestion": " <PERSON> had 6 action figures, but needed 11 total for a complete collection. If each one costs $6, how much money would he need to finish his collection? "}, {"iIndex": 483, "lAlignments": [40, 75, 97], "lEquations": ["X=(8.0*(11.0-2.0))"], "lSolutions": [72.0], "sQuestion": " At a restaurant each adult meal costs $8 and kids eat free. If a group of 11 people came in and 2 were kids, how much would it cost for the group to eat? "}, {"iIndex": 484, "lAlignments": [110, 26, 74], "lEquations": ["X=(9.0*(6.0-3.0))"], "lSolutions": [27.0], "sQuestion": " At the fair <PERSON><PERSON><PERSON> bought 6 tickets. After riding the ferris wheel he had 3 tickets left. If each ticket cost 9 dollars, how much money did <PERSON><PERSON><PERSON> spend riding the ferris wheel? "}, {"iIndex": 485, "lAlignments": [15, 38, 74], "lEquations": ["X=(8.0*(17.0-8.0))"], "lSolutions": [72.0], "sQuestion": " <PERSON> baked 8 brownies, but needed 17 total for her party. If she used 8 cups of flour on each one, how much cups of flour does she still need? "}, {"iIndex": 486, "lAlignments": [48, 73, 114], "lEquations": ["X=(7.0*(11.0-8.0))"], "lSolutions": [21.0], "sQuestion": " In a video game, each enemy defeated gives you 7 points. If a level has 11 enemies total and you destroy all but 8 of them, how many points would you earn? "}, {"iIndex": 487, "lAlignments": [45, 80, 122], "lEquations": ["X=(7.0*(16.0-8.0))"], "lSolutions": [56.0], "sQuestion": " A magician was selling magic card decks for 7 dollars each. If he started with 16 decks and by the end of the day he had 8 left, how much money did he earn? "}, {"iIndex": 488, "lAlignments": [96, 12, 56], "lEquations": ["X=(6.0*(13.0-8.0))"], "lSolutions": [30.0], "sQuestion": " There were 13 friends playing a video game online when 8 players quit. If each player left had 6 lives, how many lives did they have total? "}, {"iIndex": 489, "lAlignments": [80, 23, 39], "lEquations": ["X=(3.0*(7.0-5.0))"], "lSolutions": [6.0], "sQuestion": " At lunch a waiter had 7 customers and 5 of them didn't leave a tip. If he got $3 each from the ones who did tip, how much money did he earn? "}, {"iIndex": 490, "lAlignments": [110, 25, 74], "lEquations": ["X=(9.0*(10.0-3.0))"], "lSolutions": [63.0], "sQuestion": " At the fair <PERSON> bought 10 tickets. After riding the ferris wheel he had 3 tickets left. If each ticket cost 9 dollars, how much money did <PERSON> spend riding the ferris wheel? "}, {"iIndex": 491, "lAlignments": [17, 52, 98], "lEquations": ["X=(7.0*(17.0-8.0))"], "lSolutions": [63.0], "sQuestion": " A worksheet had 7 problems on it. If a teacher had 17 worksheets to grade and had already graded 8 of them, how many more problems does she have to grade? "}, {"iIndex": 492, "lAlignments": [119, 19, 54], "lEquations": ["X=(2.0*(9.0-3.0))"], "lSolutions": [12.0], "sQuestion": " A trivia team had 9 members total, but during a game 3 members didn't show up. If each member that did show up scored 2 points, how many points were scored total? "}, {"iIndex": 493, "lAlignments": [100, 10, 29], "lEquations": ["X=(5.0*(15.0-9.0))"], "lSolutions": [30.0], "sQuestion": " <PERSON> had 15 video games but 9 of them weren't working. If he wanted to sell the working games for $5 each, how much money could he earn? "}, {"iIndex": 494, "lAlignments": [15, 59, 93], "lEquations": ["X=(9.0*(6.0-2.0))"], "lSolutions": [36.0], "sQuestion": " <PERSON> earned 9 dollars for each lawn he mowed. If he had 6 lawns to mow, but forgot to mow 2 of them, how much money did he actually earn? "}, {"iIndex": 495, "lAlignments": [40, 75, 96], "lEquations": ["X=(2.0*(9.0-2.0))"], "lSolutions": [14.0], "sQuestion": " At a restaurant each adult meal costs $2 and kids eat free. If a group of 9 people came in and 2 were kids, how much would it cost for the group to eat? "}, {"iIndex": 496, "lAlignments": [72, 36, 13], "lEquations": ["X=(6.0*(16.0-7.0))"], "lSolutions": [54.0], "sQuestion": " <PERSON> baked 7 brownies, but needed 16 total for her party. If she used 6 cups of flour on each one, how much cups of flour does she still need? "}, {"iIndex": 497, "lAlignments": [95, 12, 55], "lEquations": ["X=(3.0*(8.0-3.0))"], "lSolutions": [15.0], "sQuestion": " There were 8 friends playing a video game online when 3 players quit. If each player left had 3 lives, how many lives did they have total? "}, {"iIndex": 498, "lAlignments": [135, 13, 48], "lEquations": ["X=(2.0*(9.0-6.0))"], "lSolutions": [6.0], "sQuestion": " <PERSON> invited 9 friends to a birthday party, but 6 couldn't come. If he wanted to buy enough cupcakes so each person could have exactly 2, how many should he buy? "}, {"iIndex": 499, "lAlignments": [14, 69, 97], "lEquations": ["X=(8.0*(14.0-5.0))"], "lSolutions": [72.0], "sQuestion": " <PERSON> earned 8 points for each bag of cans she recycled. If she had 14 bags, but didn't recycle 5 of them, how many points would she have earned? "}, {"iIndex": 500, "lAlignments": [10, 34, 67], "lEquations": ["X=((57.0-27.0)/6.0)"], "lSolutions": [5.0], "sQuestion": " <PERSON> had 57 dollars. If he spent 27 bucks on a new game, how many 6 dollar toys could he buy with the money he had left? "}, {"iIndex": 501, "lAlignments": [17, 50, 93], "lEquations": ["X=((18.0-3.0)/5.0)"], "lSolutions": [3.0], "sQuestion": " A pet store had 18 puppies. In one day they sold 3 of them and put the rest into cages with 5 in each cage. How many cages did they use? "}, {"iIndex": 502, "lAlignments": [10, 48, 107], "lEquations": ["X=((47.0-17.0)/5.0)"], "lSolutions": [6.0], "sQuestion": " <PERSON> had 47 pieces of clothing to wash. He put 17 of them in one load, but decided to split the rest into 5 equal loads. How many pieces of clothing could go in each of the small loads? "}, {"iIndex": 503, "lAlignments": [15, 54, 149], "lEquations": ["X=((79.0-23.0)/7.0)"], "lSolutions": [8.0], "sQuestion": " <PERSON> bought 79 tickets at the state fair. He spent 23 tickets at the 'dunk a clown' booth and decided to use the rest on rides. If each ride cost 7 tickets, how many rides could he go on? "}, {"iIndex": 504, "lAlignments": [13, 79, 133], "lEquations": ["X=((18.0-8.0)/2.0)"], "lSolutions": [5.0], "sQuestion": " <PERSON> baked 18 cupcakes for her school's bake sale. If her brother, <PERSON>, ate 8 of them how many packages could she make if she put 2 cupcake in each package? "}, {"iIndex": 505, "lAlignments": [11, 49, 95], "lEquations": ["X=((93.0-21.0)/8.0)"], "lSolutions": [9.0], "sQuestion": " <PERSON> had 93 files on her computer. She deleted 21 of them and put the rest into folders with 8 files in each one. How many folders did <PERSON> end up with? "}, {"iIndex": 506, "lAlignments": [16, 49, 94], "lEquations": ["X=((79.0-44.0)/5.0)"], "lSolutions": [7.0], "sQuestion": " <PERSON> uploaded 79 pictures to Facebook. She put 44 pics into one album and put the rest into 5 different albums. How many pictures were in each album? "}, {"iIndex": 507, "lAlignments": [12, 47, 72], "lEquations": ["X=((72.0-32.0)/5.0)"], "lSolutions": [8.0], "sQuestion": " <PERSON> had 72 homework problems. She finished 32 of them but still had 5 pages of problems to do. If each page has the same number of problems on it, how many problems are on each page? "}, {"iIndex": 508, "lAlignments": [19, 56, 130], "lEquations": ["X=((62.0-8.0)/9.0)"], "lSolutions": [6.0], "sQuestion": " The cafeteria had 62 apples. For lunch they handed out 8 to students and decided to use the rest to make pies. If each pie takes 9 apples, how many pies could they make? "}, {"iIndex": 509, "lAlignments": [11, 65, 110], "lEquations": ["X=((101.0-47.0)/6.0)"], "lSolutions": [9.0], "sQuestion": " <PERSON> made 101 dollars mowing lawns over the summer. If he spent 47 dollars buying new mower blades, how many 6 dollar games could he buy with the money he had left? "}, {"iIndex": 510, "lAlignments": [50, 83, 125], "lEquations": ["X=((41.0-11.0)/6.0)"], "lSolutions": [5.0], "sQuestion": " <PERSON>'s team won their dodgeball game and scored 41 points total. If <PERSON> scored 11 of the points and everyone else scored 6 points each, how many players were on her team? "}, {"iIndex": 511, "lAlignments": [60, 108, 166], "lEquations": ["X=((46.0-10.0)/4.0)"], "lSolutions": [9.0], "sQuestion": " <PERSON> is at the library helping put away books. There are 46 book to put away total but a librarian takes 10 of them and leaves <PERSON> with the rest. If he can fit 4 books on a shelf, how many shelves will he need? "}, {"iIndex": 512, "lAlignments": [63, 84, 150], "lEquations": ["X=((41.0-29.0)/4.0)"], "lSolutions": [3.0], "sQuestion": " <PERSON> was planting vegetables in her garden. She started with 41 seeds and planted 29 of them in the big garden and in each of her small gardens put 4 seeds each. How many small gardens did <PERSON> have? "}, {"iIndex": 513, "lAlignments": [13, 66, 111], "lEquations": ["X=((37.0-21.0)/2.0)"], "lSolutions": [8.0], "sQuestion": " <PERSON> made 37 dollars mowing lawns over the summer. If he spent 21 dollars buying new mower blades, how many 2 dollar games could he buy with the money he had left? "}, {"iIndex": 514, "lAlignments": [17, 50, 94], "lEquations": ["X=((81.0-41.0)/8.0)"], "lSolutions": [5.0], "sQuestion": " A pet store had 81 puppies. In one day they sold 41 of them and put the rest into cages with 8 in each cage. How many cages did they use? "}, {"iIndex": 515, "lAlignments": [11, 68, 137], "lEquations": ["X=((65.0-17.0)/8.0)"], "lSolutions": [6.0], "sQuestion": " There are 65 students trying out for the school's trivia teams. If 17 of them didn't get picked for the team and the rest were put into 8 groups, how many students would be in each group? "}, {"iIndex": 516, "lAlignments": [9, 33, 66], "lEquations": ["X=((57.0-49.0)/4.0)"], "lSolutions": [2.0], "sQuestion": " <PERSON> had 57 dollars. If he spent 49 bucks on a new game, how many 4 dollar toys could he buy with the money he had left? "}, {"iIndex": 517, "lAlignments": [15, 107, 81], "lEquations": ["X=((66.0-10.0)/8.0)"], "lSolutions": [7.0], "sQuestion": " <PERSON> picked 66 flowers for her friend’s wedding. She was making bouquets with 8 flowers in each one. If 10 of the flowers wilted before the wedding, how many bouquets could she still make? "}, {"iIndex": 518, "lAlignments": [30, 59, 106], "lEquations": ["X=((108.0-36.0)/9.0)"], "lSolutions": [8.0], "sQuestion": " For Halloween <PERSON> received 108 pieces of candy. She ate 36 pieces then placed the rest into piles with 9 in each pile. How many piles could she make? "}, {"iIndex": 519, "lAlignments": [11, 49, 95], "lEquations": ["X=((80.0-31.0)/7.0)"], "lSolutions": [7.0], "sQuestion": " <PERSON> had 80 files on her computer. She deleted 31 of them and put the rest into folders with 7 files in each one. How many folders did <PERSON> end up with? "}, {"iIndex": 520, "lAlignments": [11, 68, 136], "lEquations": ["X=((36.0-9.0)/3.0)"], "lSolutions": [9.0], "sQuestion": " There are 36 students trying out for the school's trivia teams. If 9 of them didn't get picked for the team and the rest were put into 3 groups, how many students would be in each group? "}, {"iIndex": 521, "lAlignments": [19, 56, 131], "lEquations": ["X=((96.0-42.0)/6.0)"], "lSolutions": [9.0], "sQuestion": " The cafeteria had 96 apples. For lunch they handed out 42 to students and decided to use the rest to make pies. If each pie takes 6 apples, how many pies could they make? "}, {"iIndex": 522, "lAlignments": [17, 50, 94], "lEquations": ["X=((64.0-28.0)/4.0)"], "lSolutions": [9.0], "sQuestion": " A pet store had 64 puppies. In one day they sold 28 of them and put the rest into cages with 4 in each cage. How many cages did they use? "}, {"iIndex": 523, "lAlignments": [55, 67, 109], "lEquations": ["X=((35.0-19.0)/8.0)"], "lSolutions": [2.0], "sQuestion": " <PERSON> was selling his old games. He started out with 35 but sold 19 of them. He packed the rest up putting 8 games into each box. How many boxes did he have to use? "}, {"iIndex": 524, "lAlignments": [12, 65, 110], "lEquations": ["X=((19.0-11.0)/2.0)"], "lSolutions": [4.0], "sQuestion": " <PERSON> made 19 dollars mowing lawns over the summer. If he spent 11 dollars buying new mower blades, how many 2 dollar games could he buy with the money he had left? "}, {"iIndex": 525, "lAlignments": [11, 46, 71], "lEquations": ["X=((40.0-26.0)/2.0)"], "lSolutions": [7.0], "sQuestion": " <PERSON> had 40 homework problems. She finished 26 of them but still had 2 pages of problems to do. If each page has the same number of problems on it, how many problems are on each page? "}, {"iIndex": 526, "lAlignments": [14, 107, 81], "lEquations": ["X=((103.0-47.0)/8.0)"], "lSolutions": [7.0], "sQuestion": " <PERSON> picked 103 flowers for her friend’s wedding. She was making bouquets with 8 flowers in each one. If 47 of the flowers wilted before the wedding, how many bouquets could she still make? "}, {"iIndex": 527, "lAlignments": [11, 49, 108], "lEquations": ["X=((39.0-19.0)/5.0)"], "lSolutions": [4.0], "sQuestion": " <PERSON><PERSON><PERSON> had 39 pieces of clothing to wash. He put 19 of them in one load, but decided to split the rest into 5 equal loads. How many pieces of clothing could go in each of the small loads? "}, {"iIndex": 528, "lAlignments": [19, 48, 103], "lEquations": ["X=((18.0-12.0)/3.0)"], "lSolutions": [2.0], "sQuestion": " A company invited 18 people to a luncheon, but 12 of them didn't show up. If the tables they had held 3 people each, how many tables do they need? "}, {"iIndex": 529, "lAlignments": [58, 106, 162], "lEquations": ["X=((82.0-10.0)/9.0)"], "lSolutions": [8.0], "sQuestion": " <PERSON> is at the library helping put away books. There are 82 book to put away total but a librarian takes 10 of them and leaves <PERSON> with the rest. If he can fit 9 books on a shelf, how many shelves will he need? "}, {"iIndex": 530, "lAlignments": [17, 50, 94], "lEquations": ["X=((78.0-30.0)/8.0)"], "lSolutions": [6.0], "sQuestion": " A pet store had 78 puppies. In one day they sold 30 of them and put the rest into cages with 8 in each cage. How many cages did they use? "}, {"iIndex": 531, "lAlignments": [17, 50, 95], "lEquations": ["X=((33.0-27.0)/3.0)"], "lSolutions": [2.0], "sQuestion": " <PERSON> uploaded 33 pictures to Facebook. She put 27 pics into one album and put the rest into 3 different albums. How many pictures were in each album? "}, {"iIndex": 532, "lAlignments": [50, 83, 125], "lEquations": ["X=((39.0-23.0)/2.0)"], "lSolutions": [8.0], "sQuestion": " <PERSON>'s team won their dodgeball game and scored 39 points total. If <PERSON> scored 23 of the points and everyone else scored 2 points each, how many players were on her team? "}, {"iIndex": 533, "lAlignments": [19, 48, 102], "lEquations": ["X=((47.0-7.0)/5.0)"], "lSolutions": [8.0], "sQuestion": " A company invited 47 people to a luncheon, but 7 of them didn't show up. If the tables they had held 5 people each, how many tables do they need? "}, {"iIndex": 534, "lAlignments": [14, 46, 93], "lEquations": ["X=((44.0-12.0)/8.0)"], "lSolutions": [4.0], "sQuestion": " A waiter had 44 customers in his section. If 12 of them left and the rest of his tables had 8 people at each table, how many tables did he have? "}, {"iIndex": 535, "lAlignments": [13, 95, 157], "lEquations": ["X=((40.0-20.0)/4.0)"], "lSolutions": [5.0], "sQuestion": " A store had 40 coloring books in stock. They ended up putting them on sale and getting rid of 20 of them. The put the ones they still had onto shelves with 4 on each shelf. How many shelves did they use? "}, {"iIndex": 536, "lAlignments": [11, 68, 137], "lEquations": ["X=((64.0-36.0)/4.0)"], "lSolutions": [7.0], "sQuestion": " There are 64 students trying out for the school's trivia teams. If 36 of them didn't get picked for the team and the rest were put into 4 groups, how many students would be in each group? "}, {"iIndex": 537, "lAlignments": [54, 66, 108], "lEquations": ["X=((76.0-46.0)/5.0)"], "lSolutions": [6.0], "sQuestion": " <PERSON><PERSON><PERSON> was selling his old games. He started out with 76 but sold 46 of them. He packed the rest up putting 5 games into each box. How many boxes did he have to use? "}, {"iIndex": 538, "lAlignments": [59, 107, 163], "lEquations": ["X=((34.0-7.0)/3.0)"], "lSolutions": [9.0], "sQuestion": " <PERSON> is at the library helping put away books. There are 34 book to put away total but a librarian takes 7 of them and leaves <PERSON> with the rest. If he can fit 3 books on a shelf, how many shelves will he need? "}, {"iIndex": 539, "lAlignments": [63, 84, 150], "lEquations": ["X=((52.0-28.0)/4.0)"], "lSolutions": [6.0], "sQuestion": " <PERSON> was planting vegetables in her garden. She started with 52 seeds and planted 28 of them in the big garden and in each of her small gardens put 4 seeds each. How many small gardens did <PERSON> have? "}, {"iIndex": 540, "lAlignments": [10, 34, 67], "lEquations": ["X=((83.0-47.0)/4.0)"], "lSolutions": [9.0], "sQuestion": " <PERSON> had 83 dollars. If he spent 47 bucks on a new game, how many 4 dollar toys could he buy with the money he had left? "}, {"iIndex": 541, "lAlignments": [64, 85, 151], "lEquations": ["X=((52.0-40.0)/2.0)"], "lSolutions": [6.0], "sQuestion": " <PERSON> was planting vegetables in her garden. She started with 52 seeds and planted 40 of them in the big garden and in each of her small gardens put 2 seeds each. How many small gardens did <PERSON> have? "}, {"iIndex": 542, "lAlignments": [14, 46, 93], "lEquations": ["X=((21.0-12.0)/3.0)"], "lSolutions": [3.0], "sQuestion": " A waiter had 21 customers in his section. If 12 of them left and the rest of his tables had 3 people at each table, how many tables did he have? "}, {"iIndex": 543, "lAlignments": [11, 68, 137], "lEquations": ["X=((25.0-15.0)/2.0)"], "lSolutions": [5.0], "sQuestion": " There are 25 students trying out for the school's trivia teams. If 15 of them didn't get picked for the team and the rest were put into 2 groups, how many students would be in each group? "}, {"iIndex": 544, "lAlignments": [19, 56, 131], "lEquations": ["X=((47.0-27.0)/4.0)"], "lSolutions": [5.0], "sQuestion": " The cafeteria had 47 apples. For lunch they handed out 27 to students and decided to use the rest to make pies. If each pie takes 4 apples, how many pies could they make? "}, {"iIndex": 545, "lAlignments": [13, 79, 134], "lEquations": ["X=((68.0-32.0)/6.0)"], "lSolutions": [6.0], "sQuestion": " <PERSON> baked 68 cupcakes for her school's bake sale. If her brother, <PERSON>, ate 32 of them how many packages could she make if she put 6 cupcake in each package? "}, {"iIndex": 546, "lAlignments": [13, 96, 158], "lEquations": ["X=((120.0-39.0)/9.0)"], "lSolutions": [9.0], "sQuestion": " A store had 120 coloring books in stock. They ended up putting them on sale and getting rid of 39 of them. The put the ones they still had onto shelves with 9 on each shelf. How many shelves did they use? "}, {"iIndex": 547, "lAlignments": [17, 50, 95], "lEquations": ["X=((25.0-10.0)/5.0)"], "lSolutions": [3.0], "sQuestion": " <PERSON> uploaded 25 pictures to Facebook. She put 10 pics into one album and put the rest into 5 different albums. How many pictures were in each album? "}, {"iIndex": 548, "lAlignments": [11, 46, 71], "lEquations": ["X=((60.0-20.0)/5.0)"], "lSolutions": [8.0], "sQuestion": " <PERSON> had 60 homework problems. She finished 20 of them but still had 5 pages of problems to do. If each page has the same number of problems on it, how many problems are on each page? "}, {"iIndex": 549, "lAlignments": [11, 64, 109], "lEquations": ["X=((42.0-10.0)/8.0)"], "lSolutions": [4.0], "sQuestion": " <PERSON> made 42 dollars mowing lawns over the summer. If he spent 10 dollars buying new mower blades, how many 8 dollar games could he buy with the money he had left? "}, {"iIndex": 550, "lAlignments": [10, 48, 107], "lEquations": ["X=((59.0-32.0)/9.0)"], "lSolutions": [3.0], "sQuestion": " <PERSON> had 59 pieces of clothing to wash. He put 32 of them in one load, but decided to split the rest into 9 equal loads. How many pieces of clothing could go in each of the small loads? "}, {"iIndex": 551, "lAlignments": [13, 66, 110], "lEquations": ["X=((35.0-7.0)/4.0)"], "lSolutions": [7.0], "sQuestion": " <PERSON> made 35 dollars mowing lawns over the summer. If he spent 7 dollars buying new mower blades, how many 4 dollar games could he buy with the money he had left? "}, {"iIndex": 552, "lAlignments": [63, 84, 150], "lEquations": ["X=((42.0-36.0)/2.0)"], "lSolutions": [3.0], "sQuestion": " <PERSON> was planting vegetables in her garden. She started with 42 seeds and planted 36 of them in the big garden and in each of her small gardens put 2 seeds each. How many small gardens did <PERSON> have? "}, {"iIndex": 553, "lAlignments": [11, 68, 136], "lEquations": ["X=((17.0-5.0)/3.0)"], "lSolutions": [4.0], "sQuestion": " There are 17 students trying out for the school's trivia teams. If 5 of them didn't get picked for the team and the rest were put into 3 groups, how many students would be in each group? "}, {"iIndex": 554, "lAlignments": [50, 83, 124], "lEquations": ["X=((12.0-4.0)/4.0)"], "lSolutions": [2.0], "sQuestion": " <PERSON>'s team won their dodgeball game and scored 12 points total. If <PERSON> scored 4 of the points and everyone else scored 4 points each, how many players were on her team? "}, {"iIndex": 555, "lAlignments": [13, 79, 134], "lEquations": ["X=((71.0-43.0)/7.0)"], "lSolutions": [4.0], "sQuestion": " <PERSON> baked 71 cupcakes for her school's bake sale. If her brother, <PERSON>, ate 43 of them how many packages could she make if she put 7 cupcake in each package? "}, {"iIndex": 556, "lAlignments": [16, 49, 94], "lEquations": ["X=((45.0-27.0)/9.0)"], "lSolutions": [2.0], "sQuestion": " <PERSON> uploaded 45 pictures to Facebook. She put 27 pics into one album and put the rest into 9 different albums. How many pictures were in each album? "}, {"iIndex": 557, "lAlignments": [13, 95, 157], "lEquations": ["X=((86.0-37.0)/7.0)"], "lSolutions": [7.0], "sQuestion": " A store had 86 coloring books in stock. They ended up putting them on sale and getting rid of 37 of them. The put the ones they still had onto shelves with 7 on each shelf. How many shelves did they use? "}, {"iIndex": 558, "lAlignments": [19, 56, 130], "lEquations": ["X=((50.0-5.0)/5.0)"], "lSolutions": [9.0], "sQuestion": " The cafeteria had 50 apples. For lunch they handed out 5 to students and decided to use the rest to make pies. If each pie takes 5 apples, how many pies could they make? "}, {"iIndex": 559, "lAlignments": [11, 47, 72], "lEquations": ["X=((101.0-47.0)/6.0)"], "lSolutions": [9.0], "sQuestion": " <PERSON> had 101 homework problems. She finished 47 of them but still had 6 pages of problems to do. If each page has the same number of problems on it, how many problems are on each page? "}, {"iIndex": 560, "lAlignments": [11, 49, 94], "lEquations": ["X=((27.0-9.0)/6.0)"], "lSolutions": [3.0], "sQuestion": " <PERSON> had 27 files on her computer. She deleted 9 of them and put the rest into folders with 6 files in each one. How many folders did <PERSON> end up with? "}, {"iIndex": 561, "lAlignments": [31, 59, 106], "lEquations": ["X=((78.0-30.0)/8.0)"], "lSolutions": [6.0], "sQuestion": " For Halloween <PERSON> received 78 pieces of candy. She ate 30 pieces then placed the rest into piles with 8 in each pile. How many piles could she make? "}, {"iIndex": 562, "lAlignments": [19, 48, 103], "lEquations": ["X=((45.0-35.0)/2.0)"], "lSolutions": [5.0], "sQuestion": " A company invited 45 people to a luncheon, but 35 of them didn't show up. If the tables they had held 2 people each, how many tables do they need? "}, {"iIndex": 563, "lAlignments": [16, 49, 94], "lEquations": ["X=((65.0-17.0)/6.0)"], "lSolutions": [8.0], "sQuestion": " <PERSON> uploaded 65 pictures to Facebook. She put 17 pics into one album and put the rest into 6 different albums. How many pictures were in each album? "}, {"iIndex": 564, "lAlignments": [17, 51, 95], "lEquations": ["X=((102.0-21.0)/9.0)"], "lSolutions": [9.0], "sQuestion": " A pet store had 102 puppies. In one day they sold 21 of them and put the rest into cages with 9 in each cage. How many cages did they use? "}, {"iIndex": 565, "lAlignments": [11, 50, 109], "lEquations": ["X=((120.0-48.0)/9.0)"], "lSolutions": [8.0], "sQuestion": " <PERSON> had 120 pieces of clothing to wash. He put 48 of them in one load, but decided to split the rest into 9 equal loads. How many pieces of clothing could go in each of the small loads? "}, {"iIndex": 566, "lAlignments": [14, 106, 80], "lEquations": ["X=((45.0-35.0)/5.0)"], "lSolutions": [2.0], "sQuestion": " <PERSON> picked 45 flowers for her friend’s wedding. She was making bouquets with 5 flowers in each one. If 35 of the flowers wilted before the wedding, how many bouquets could she still make? "}, {"iIndex": 567, "lAlignments": [14, 80, 135], "lEquations": ["X=((39.0-21.0)/3.0)"], "lSolutions": [6.0], "sQuestion": " <PERSON> baked 39 cupcakes for her school's bake sale. If her brother, <PERSON>, ate 21 of them how many packages could she make if she put 3 cupcake in each package? "}, {"iIndex": 568, "lAlignments": [11, 46, 70], "lEquations": ["X=((55.0-6.0)/7.0)"], "lSolutions": [7.0], "sQuestion": " <PERSON> had 55 homework problems. She finished 6 of them but still had 7 pages of problems to do. If each page has the same number of problems on it, how many problems are on each page? "}, {"iIndex": 569, "lAlignments": [11, 68, 137], "lEquations": ["X=((58.0-10.0)/8.0)"], "lSolutions": [6.0], "sQuestion": " There are 58 students trying out for the school's trivia teams. If 10 of them didn't get picked for the team and the rest were put into 8 groups, how many students would be in each group? "}, {"iIndex": 570, "lAlignments": [14, 106, 80], "lEquations": ["X=((53.0-18.0)/7.0)"], "lSolutions": [5.0], "sQuestion": " <PERSON> picked 53 flowers for her friend’s wedding. She was making bouquets with 7 flowers in each one. If 18 of the flowers wilted before the wedding, how many bouquets could she still make? "}, {"iIndex": 571, "lAlignments": [14, 46, 93], "lEquations": ["X=((22.0-14.0)/4.0)"], "lSolutions": [2.0], "sQuestion": " A waiter had 22 customers in his section. If 14 of them left and the rest of his tables had 4 people at each table, how many tables did he have? "}, {"iIndex": 572, "lAlignments": [53, 65, 107], "lEquations": ["X=((39.0-19.0)/4.0)"], "lSolutions": [5.0], "sQuestion": " <PERSON> was selling his old games. He started out with 39 but sold 19 of them. He packed the rest up putting 4 games into each box. How many boxes did he have to use? "}, {"iIndex": 573, "lAlignments": [13, 79, 134], "lEquations": ["X=((20.0-11.0)/3.0)"], "lSolutions": [3.0], "sQuestion": " <PERSON> baked 20 cupcakes for her school's bake sale. If her brother, <PERSON>, ate 11 of them how many packages could she make if she put 3 cupcake in each package? "}, {"iIndex": 574, "lAlignments": [17, 50, 93], "lEquations": ["X=((13.0-7.0)/2.0)"], "lSolutions": [3.0], "sQuestion": " A pet store had 13 puppies. In one day they sold 7 of them and put the rest into cages with 2 in each cage. How many cages did they use? "}, {"iIndex": 575, "lAlignments": [13, 95, 156], "lEquations": ["X=((27.0-6.0)/7.0)"], "lSolutions": [3.0], "sQuestion": " A store had 27 coloring books in stock. They ended up putting them on sale and getting rid of 6 of them. The put the ones they still had onto shelves with 7 on each shelf. How many shelves did they use? "}, {"iIndex": 576, "lAlignments": [11, 49, 95], "lEquations": ["X=((82.0-37.0)/5.0)"], "lSolutions": [9.0], "sQuestion": " <PERSON> had 82 files on her computer. She deleted 37 of them and put the rest into folders with 5 files in each one. How many folders did <PERSON> end up with? "}, {"iIndex": 577, "lAlignments": [11, 35, 67], "lEquations": ["X=((12.0-8.0)/2.0)"], "lSolutions": [2.0], "sQuestion": " <PERSON><PERSON><PERSON> had 12 dollars. If he spent 8 bucks on a new game, how many 2 dollar toys could he buy with the money he had left? "}, {"iIndex": 578, "lAlignments": [63, 84, 150], "lEquations": ["X=((21.0-12.0)/3.0)"], "lSolutions": [3.0], "sQuestion": " <PERSON> was planting vegetables in her garden. She started with 21 seeds and planted 12 of them in the big garden and in each of her small gardens put 3 seeds each. How many small gardens did <PERSON> have? "}, {"iIndex": 579, "lAlignments": [16, 49, 94], "lEquations": ["X=((51.0-11.0)/8.0)"], "lSolutions": [5.0], "sQuestion": " <PERSON> uploaded 51 pictures to Facebook. She put 11 pics into one album and put the rest into 8 different albums. How many pictures were in each album? "}, {"iIndex": 580, "lAlignments": [19, 48, 103], "lEquations": ["X=((24.0-10.0)/7.0)"], "lSolutions": [2.0], "sQuestion": " A company invited 24 people to a luncheon, but 10 of them didn't show up. If the tables they had held 7 people each, how many tables do they need? "}, {"iIndex": 581, "lAlignments": [51, 85, 127], "lEquations": ["X=((75.0-45.0)/6.0)"], "lSolutions": [5.0], "sQuestion": " <PERSON>'s team won their dodgeball game and scored 75 points total. If <PERSON> scored 45 of the points and everyone else scored 6 points each, how many players were on her team? "}, {"iIndex": 582, "lAlignments": [30, 58, 105], "lEquations": ["X=((54.0-33.0)/7.0)"], "lSolutions": [3.0], "sQuestion": " For Halloween <PERSON> received 54 pieces of candy. She ate 33 pieces then placed the rest into piles with 7 in each pile. How many piles could she make? "}, {"iIndex": 583, "lAlignments": [63, 84, 150], "lEquations": ["X=((56.0-35.0)/3.0)"], "lSolutions": [7.0], "sQuestion": " <PERSON> was planting vegetables in her garden. She started with 56 seeds and planted 35 of them in the big garden and in each of her small gardens put 3 seeds each. How many small gardens did <PERSON> have? "}, {"iIndex": 584, "lAlignments": [13, 95, 157], "lEquations": ["X=((48.0-38.0)/5.0)"], "lSolutions": [2.0], "sQuestion": " A store had 48 coloring books in stock. They ended up putting them on sale and getting rid of 38 of them. The put the ones they still had onto shelves with 5 on each shelf. How many shelves did they use? "}, {"iIndex": 585, "lAlignments": [59, 107, 163], "lEquations": ["X=((14.0-2.0)/3.0)"], "lSolutions": [4.0], "sQuestion": " <PERSON> is at the library helping put away books. There are 14 book to put away total but a librarian takes 2 of them and leaves <PERSON> with the rest. If he can fit 3 books on a shelf, how many shelves will he need? "}, {"iIndex": 586, "lAlignments": [17, 50, 94], "lEquations": ["X=((56.0-24.0)/4.0)"], "lSolutions": [8.0], "sQuestion": " A pet store had 56 puppies. In one day they sold 24 of them and put the rest into cages with 4 in each cage. How many cages did they use? "}, {"iIndex": 587, "lAlignments": [19, 56, 131], "lEquations": ["X=((86.0-30.0)/8.0)"], "lSolutions": [7.0], "sQuestion": " The cafeteria had 86 apples. For lunch they handed out 30 to students and decided to use the rest to make pies. If each pie takes 8 apples, how many pies could they make? "}, {"iIndex": 588, "lAlignments": [11, 49, 108], "lEquations": ["X=((60.0-40.0)/5.0)"], "lSolutions": [4.0], "sQuestion": " <PERSON> had 60 pieces of clothing to wash. He put 40 of them in one load, but decided to split the rest into 5 equal loads. How many pieces of clothing could go in each of the small loads? "}, {"iIndex": 589, "lAlignments": [11, 64, 109], "lEquations": ["X=((69.0-24.0)/5.0)"], "lSolutions": [9.0], "sQuestion": " <PERSON> made 69 dollars mowing lawns over the summer. If he spent 24 dollars buying new mower blades, how many 5 dollar games could he buy with the money he had left? "}, {"iIndex": 590, "lAlignments": [11, 65, 110], "lEquations": ["X=((104.0-41.0)/9.0)"], "lSolutions": [7.0], "sQuestion": " <PERSON> made 104 dollars mowing lawns over the summer. If he spent 41 dollars buying new mower blades, how many 9 dollar games could he buy with the money he had left? "}, {"iIndex": 591, "lAlignments": [31, 59, 106], "lEquations": ["X=((32.0-12.0)/5.0)"], "lSolutions": [4.0], "sQuestion": " For Halloween <PERSON> received 32 pieces of candy. She ate 12 pieces then placed the rest into piles with 5 in each pile. How many piles could she make? "}, {"iIndex": 592, "lAlignments": [53, 65, 107], "lEquations": ["X=((57.0-39.0)/2.0)"], "lSolutions": [9.0], "sQuestion": " <PERSON> was selling his old games. He started out with 57 but sold 39 of them. He packed the rest up putting 2 games into each box. How many boxes did he have to use? "}, {"iIndex": 593, "lAlignments": [17, 50, 94], "lEquations": ["X=((88.0-34.0)/6.0)"], "lSolutions": [9.0], "sQuestion": " A pet store had 88 puppies. In one day they sold 34 of them and put the rest into cages with 6 in each cage. How many cages did they use? "}, {"iIndex": 594, "lAlignments": [11, 49, 95], "lEquations": ["X=((85.0-40.0)/5.0)"], "lSolutions": [9.0], "sQuestion": " <PERSON> had 85 files on her computer. She deleted 40 of them and put the rest into folders with 5 files in each one. How many folders did <PERSON> end up with? "}, {"iIndex": 595, "lAlignments": [11, 35, 68], "lEquations": ["X=((68.0-47.0)/7.0)"], "lSolutions": [3.0], "sQuestion": " <PERSON> had 68 dollars. If he spent 47 bucks on a new game, how many 7 dollar toys could he buy with the money he had left? "}, {"iIndex": 596, "lAlignments": [12, 51, 146], "lEquations": ["X=((40.0-28.0)/4.0)"], "lSolutions": [3.0], "sQuestion": " <PERSON> bought 40 tickets at the state fair. He spent 28 tickets at the 'dunk a clown' booth and decided to use the rest on rides. If each ride cost 4 tickets, how many rides could he go on? "}, {"iIndex": 597, "lAlignments": [19, 56, 131], "lEquations": ["X=((75.0-19.0)/8.0)"], "lSolutions": [7.0], "sQuestion": " The cafeteria had 75 apples. For lunch they handed out 19 to students and decided to use the rest to make pies. If each pie takes 8 apples, how many pies could they make? "}, {"iIndex": 598, "lAlignments": [13, 79, 134], "lEquations": ["X=((38.0-14.0)/8.0)"], "lSolutions": [3.0], "sQuestion": " <PERSON> baked 38 cupcakes for her school's bake sale. If her brother, <PERSON>, ate 14 of them how many packages could she make if she put 8 cupcake in each package? "}, {"iIndex": 599, "lAlignments": [16, 49, 94], "lEquations": ["X=((41.0-37.0)/2.0)"], "lSolutions": [2.0], "sQuestion": " <PERSON> uploaded 41 pictures to Facebook. She put 37 pics into one album and put the rest into 2 different albums. How many pictures were in each album? "}]