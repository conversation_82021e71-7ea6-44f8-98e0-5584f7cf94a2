# Corex

Codes and data for: [Corex: Pushing the Boundaries of Complex Reasoning through Multi-Model Collaboration](https://arxiv.org/abs/2310.00280) (COLM 2024)

<!-- ⚠️ *Under double-blind peer review, full implementations and data will be released later.*🚧 -->


## Introduction

```
pip install -r requirements.txt
```

Use your API KEYs

```python
export OPENAI_API_KEY='OPENAI_API_KEY'
```

or

```python
export ANTHROPIC_API_KEY='ANTHROPIC_API_KEY'
```

## Usage

```python
python corex_{discuss,review,retrieve}.py
```

<!-- 
```python
cd scripts
python run.py --task TASK_NAME --sc-num SC_NUM --complex COMPLEX_BOOL --model MODEL_NAME --system-prompt SYSTEM_PROMPT
```

**Note**: OpenAI and Anthropic has [rate limits](https://platform.openai.com/docs/guides/rate-limits) for users, which will affect the exact time consumption. -->

## Reference

If you are interested in our work, please use the following citation format when referencing our paper:

```bibtex
@article{sun2023corex,
  title={Corex: Pushing the boundaries of complex reasoning through multi-model collaboration},
  author={Sun, Qiushi and Yin, Zhangyue and Li, Xiang and Wu, Zhiyong and Qiu, Xipeng and Kong, Lingpeng},
  journal={arXiv preprint arXiv:2310.00280},
  year={2023}
}
```

---

<sub>Part of the codes in this repository are adapted from (1) [EoT](https://github.com/yinzhangyue/EoT) (2) [PaL](https://github.com/reasoning-machines/pal) (3) [Program of Thoughts](https://github.com/wenhuchen/Program-of-Thoughts) (4) [HugNLP](https://github.com/HugAILab/HugNLP) (5) [OpenICL](https://github.com/Shark-NLP/OpenICL)</sub>
