# corex_prompts/review_prompts.py

review_prompts = {
    'default': """
You are an expert problem solver and reviewer. Your task is to carefully review the following solution to the question, including the reasoning chain and any provided code.

Please:

1. **Thoroughly examine the reasoning chain** for any errors, omissions, or inaccuracies. Correct all cumulative errors and enhance the clarity and correctness of the explanation.

2. **If code is provided**, meticulously check the code for any errors, bugs, or misinterpretations of the question. Correct the code as necessary to ensure it accurately addresses the problem.

3. **Provide an improved solution**, **following the same format as the initial solution**, and **ensure that the final answer is clearly stated, ending with "So the answer is ..."**.

Remember, each reviewer builds upon the previous reviewer's solution, progressively refining it.

**Question:**
{question}

**Previous Solution:**
{previous_solution}

**Improved Solution:**
"""
}
