
import backoff
from openai import OpenAI
from tenacity import retry, stop_after_attempt, wait_random_exponential

class Inference_Model():
    def __init__(self, default_model:str, api_key:str, System_Prompt:str, SC_num:int) -> None:
        self.model_name = default_model
        self.openai_model_list = ["gpt-3.5-turbo-0301", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k-0613", "gpt-4-0314", "gpt-4-0613", "gpt-4o-2024-05-13", "gpt-4o"]
        self.claude_model_list = []  # Initialize empty list for compatibility
        assert self.model_name in self.openai_model_list
        self.api_key = api_key
        self.System_Prompt = System_Prompt
        self.SC_num = SC_num
        self.client = OpenAI(api_key=api_key)

    @retry(wait=wait_random_exponential(min=1, max=20), stop=stop_after_attempt(100))
    def get_info(self, Prompt_question:str, System_Prompt:str = "", model_name:str = "", api_key:str = "", SC_num:int = 0):
        if System_Prompt == "": System_Prompt = self.System_Prompt
        if model_name == "": model_name = self.model_name
        if api_key == "": api_key = self.api_key
        if SC_num == 0: SC_num = self.SC_num
        assert model_name in self.openai_model_list + self.claude_model_list
        assert SC_num > 0

        if model_name in self.openai_model_list:
            response = self.client.chat.completions.create(model = model_name,
            messages=[
                {
                    "role": "system",
                    "content": System_Prompt,
                },
                {
                    "role": "user",
                    "content": Prompt_question,
                },
            ],
            n = SC_num)
            return response.choices, response.usage
        else:
            raise Exception("model_name error")


class Inference_OpenAI_Model():
    def __init__(self, model_name:str, openai_key:str, System_Prompt:str) -> None:
        self.model_name = model_name
        assert self.model_name in ["gpt-3.5-turbo-0301", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k-0613", "gpt-4-0314", "gpt-4-0613","gpt-4o-2024-05-13", "gpt-4o"]
        self.System_Prompt = System_Prompt
        self.client = OpenAI(api_key=openai_key)

    @retry(wait=wait_random_exponential(min=1, max=20), stop=stop_after_attempt(10))
    def get_info(self, Prompt_question:str, System_Prompt:str = ""):
        if System_Prompt == "":
            System_Prompt = self.System_Prompt
        response = self.client.chat.completions.create(model = self.model_name,
        messages=[
            {
                "role": "system",
                "content": System_Prompt,
            },
            {
                "role": "user",
                "content": Prompt_question,
            },
        ])
        return response.choices[0].message.content, response.usage