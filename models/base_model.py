"""
Base model provider interface for Corex multi-model collaboration framework.

This module defines the abstract base class that all model providers must implement
to ensure consistent behavior across different LLM providers (OpenAI, DeepSeek, GLM, Kimi, etc.).
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple, Any
from tenacity import retry, stop_after_attempt, wait_random_exponential
import logging

logger = logging.getLogger(__name__)


class ModelProvider(ABC):
    """
    Abstract base class for all model providers.
    
    This class defines the interface that all model providers must implement
    to ensure consistent behavior across different LLM providers.
    """
    
    def __init__(self, model_name: str, api_key: str, system_prompt: str = ""):
        """
        Initialize the model provider.
        
        Args:
            model_name: The specific model to use (e.g., "gpt-4o", "deepseek-chat")
            api_key: API key for the provider
            system_prompt: Default system prompt for the model
        """
        self.model_name = model_name
        self.api_key = api_key
        self.system_prompt = system_prompt
        self._validate_model()
    
    @property
    @abstractmethod
    def supported_models(self) -> List[str]:
        """Return a list of supported model names for this provider."""
        pass
    
    @property
    @abstractmethod
    def provider_name(self) -> str:
        """Return the name of this provider (e.g., 'openai', 'deepseek')."""
        pass
    
    def _validate_model(self):
        """Validate that the model name is supported by this provider."""
        if self.model_name not in self.supported_models:
            raise ValueError(
                f"Model '{self.model_name}' is not supported by {self.provider_name}. "
                f"Supported models: {self.supported_models}"
            )
    
    @abstractmethod
    def _make_api_call(self, messages: List[Dict[str, str]], **kwargs) -> Any:
        """
        Make the actual API call to the provider.
        
        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            **kwargs: Additional provider-specific parameters
            
        Returns:
            Raw response from the API
        """
        pass
    
    @abstractmethod
    def _extract_content(self, response: Any) -> str:
        """
        Extract the text content from the API response.
        
        Args:
            response: Raw response from the API
            
        Returns:
            The text content of the response
        """
        pass
    
    @abstractmethod
    def _extract_usage(self, response: Any) -> Optional[Dict[str, Any]]:
        """
        Extract usage information from the API response.
        
        Args:
            response: Raw response from the API
            
        Returns:
            Dictionary containing usage information (tokens, cost, etc.) or None
        """
        pass
    
    @retry(wait=wait_random_exponential(min=1, max=20), stop=stop_after_attempt(10))
    def chat(self, messages: List[Dict[str, str]], temperature: float = 0, **kwargs) -> str:
        """
        Send a chat completion request to the model.
        
        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            temperature: Sampling temperature (0-1)
            **kwargs: Additional provider-specific parameters
            
        Returns:
            The text response from the model
        """
        try:
            response = self._make_api_call(messages, temperature=temperature, **kwargs)
            content = self._extract_content(response)
            usage = self._extract_usage(response)
            
            logger.info(f"Response from {self.provider_name}: {content[:100]}...")
            if usage:
                logger.info(f"Usage: {usage}")
                
            return content
        except Exception as e:
            logger.error(f"Error in {self.provider_name} API call: {str(e)}")
            raise
    
    @retry(wait=wait_random_exponential(min=1, max=20), stop=stop_after_attempt(100))
    def chat_with_usage(self, messages: List[Dict[str, str]], temperature: float = 0, **kwargs) -> Tuple[str, Optional[Dict[str, Any]]]:
        """
        Send a chat completion request and return both content and usage information.
        
        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            temperature: Sampling temperature (0-1)
            **kwargs: Additional provider-specific parameters
            
        Returns:
            Tuple of (response_content, usage_info)
        """
        try:
            response = self._make_api_call(messages, temperature=temperature, **kwargs)
            content = self._extract_content(response)
            usage = self._extract_usage(response)
            
            logger.info(f"Response from {self.provider_name}: {content[:100]}...")
            if usage:
                logger.info(f"Usage: {usage}")
                
            return content, usage
        except Exception as e:
            logger.error(f"Error in {self.provider_name} API call: {str(e)}")
            raise
    
    def format_messages(self, user_prompt: str, system_prompt: Optional[str] = None) -> List[Dict[str, str]]:
        """
        Format user prompt and system prompt into the standard message format.
        
        Args:
            user_prompt: The user's input prompt
            system_prompt: Optional system prompt (uses default if not provided)
            
        Returns:
            List of formatted message dictionaries
        """
        messages = []
        
        # Use provided system prompt or fall back to default
        sys_prompt = system_prompt if system_prompt is not None else self.system_prompt
        if sys_prompt:
            messages.append({"role": "system", "content": sys_prompt})
        
        messages.append({"role": "user", "content": user_prompt})
        return messages


class ModelProviderFactory:
    """Factory class for creating model provider instances."""
    
    _providers = {}
    
    @classmethod
    def register_provider(cls, provider_name: str, provider_class: type):
        """Register a new model provider class."""
        cls._providers[provider_name.lower()] = provider_class
    
    @classmethod
    def create_provider(cls, provider_name: str, model_name: str, api_key: str, system_prompt: str = "") -> ModelProvider:
        """
        Create a model provider instance.
        
        Args:
            provider_name: Name of the provider (e.g., 'openai', 'deepseek')
            model_name: Specific model to use
            api_key: API key for the provider
            system_prompt: Default system prompt
            
        Returns:
            ModelProvider instance
        """
        provider_name_lower = provider_name.lower()
        if provider_name_lower not in cls._providers:
            raise ValueError(f"Unknown provider: {provider_name}. Available providers: {list(cls._providers.keys())}")
        
        provider_class = cls._providers[provider_name_lower]
        return provider_class(model_name, api_key, system_prompt)
    
    @classmethod
    def get_available_providers(cls) -> List[str]:
        """Get list of available provider names."""
        return list(cls._providers.keys())
